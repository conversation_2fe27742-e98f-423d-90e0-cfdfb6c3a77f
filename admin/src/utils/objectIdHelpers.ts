/**
 * Utility functions for handling MongoDB ObjectIds
 */

/**
 * Safely extracts an ObjectId from various data formats
 * @param data - The data that might contain an ObjectId (string, object with _id, etc.)
 * @returns The extracted ObjectId as a string, or null if none found
 */
export const extractObjectId = (data: any): string | null => {
  if (!data) return null;
  
  // If data is a string and not empty, return it
  if (typeof data === 'string' && data.trim() !== '') {
    return data;
  }
  
  // If data is an object, check for _id or id properties
  if (typeof data === 'object') {
    if (data._id && typeof data._id === 'string' && data._id.trim() !== '') {
      return data._id;
    }
    if (data.id && typeof data.id === 'string' && data.id.trim() !== '') {
      return data.id;
    }
  }
  
  return null;
};

/**
 * Safely adds an ObjectId field to an object only if the ID is valid
 * @param obj - The object to add the field to
 * @param fieldName - The name of the field to add
 * @param id - The ID value to add
 * @returns The updated object
 */
export const addObjectIdField = (obj: any, fieldName: string, id: string | null): any => {
  if (!id) return obj;
  return {
    ...obj,
    [fieldName]: id
  };
};
