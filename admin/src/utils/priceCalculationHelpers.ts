/**
 * Utility functions for price calculations
 */

/**
 * Updates price and total price in formik for a laundry item
 * @param formik - The formik instance
 * @param index - The index of the item in the items array
 * @param newPrice - The new price value
 * @param quantity - The quantity of the item
 */
export const updateItemPrices = (
  formik: any,
  index: number,
  newPrice: number,
  quantity: number
): void => {
  // Only update if the formik instance is valid
  if (!formik || typeof formik.setFieldValue !== 'function') return;
  
  // Set the price field
  formik.setFieldValue(`items.${index}.price`, newPrice);
  
  // Calculate and set the total price
  const totalPrice = newPrice * quantity;
  formik.setFieldValue(`items.${index}.totalPrice`, totalPrice);
};
