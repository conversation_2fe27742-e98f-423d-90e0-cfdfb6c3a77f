/**
 * Utility functions for inventory management
 */

/**
 * Calculate the total quantity of an inventory item
 * Uses the quantity field if available, otherwise sums cleanQuantity and dirtyQuantity
 * 
 * @param item The inventory item object
 * @returns The total quantity
 */
export const calculateTotalQuantity = (item: any): number => {
  // If quantity is explicitly set and is a number, use it
  if (item && typeof item.quantity === 'number') {
    return item.quantity;
  }
  
  // Otherwise calculate from clean and dirty quantities
  const cleanQuantity = item?.cleanQuantity || 0;
  const dirtyQuantity = item?.dirtyQuantity || 0;
  
  return cleanQuantity + dirtyQuantity;
};
