export const DashboardData = () => {
    const InHouseGuests = {
        mainTitle: "In House Guests",
        actualValue: 100,
        description: "Rooms",
        footerTitle: "Total Pax:",
        footerValue: 120
    }

    const ExpectedArrivals = {
        mainTitle: "Expected Arrivals",
        actualValue: 6,
        description: "Rooms",
        footerTitle: "Total Pax:",
        footerValue: 120
    }

    const ExpectedCheckouts = {
        mainTitle: "Expected Checkouts",
        actualValue: 15,
        description: "Rooms",
        footerTitle: "Total Pax:",
        footerValue: 120
    }

    const NoShow = {
        mainTitle: "No Show",
        actualValue: 9,
        description: "Rooms",
        footerTitle: "Total Pax:",
        footerValue: 120,
        valueColor: "#DD2025"
    }

    const todayCheckout = [{
        room: "201",
        roomType: "Single",
        time:"10:00 AM",
    },{
        room: "200",
        roomType: "Twin",
        time:"10:00 AM",
        },
    {
        room: "204",
        roomType: "Double",
        time:"10:00 AM",
        },
    {
        room: "203",
        roomType: "Standard",
        time:"10:00 AM",
        },
    {
        room: "205",
        roomType: "Single",
        time:"12:00 AM",
        },
    ]

    const RoomService = [
        {
            room: "201",
            item: ['Pizza', "Momo", "Burger"],
            status:"Processing"
        },
        {
            room: "202",
            item: ['Pizza', "Momo", "Burger"],
            status:"Served"
        },
        {
            room: "203",
            item: ['Pizza', "Momo", "Burger"],
            status:"Processing"
        }
    ]

    
     const pieChart = {
  labels: ['Direct Booking', 'Blue', 'Yellow', 'Green', 'Purple', 'Orange'],
  datasets: [
    {
      label: '# of Votes',
      data: [12, 19, 3, 5, 2, 3],
      backgroundColor: [
        "#2A3A6D", 
    "#070C44",
"#163381 ",
"#40528F",
"#1E265E" ,
"#5468B0" 
      ],
    //   borderColor: [
    //     'rgba(255, 99, 132, 1)',
    //     'rgba(54, 162, 235, 1)',
    //     'rgba(255, 206, 86, 1)',
    //     'rgba(75, 192, 192, 1)',
    //     'rgba(153, 102, 255, 1)',
    //     'rgba(255, 159, 64, 1)',
    //   ],
      borderWidth: 1,
    },
  ],
    };
    
    const roomAvailability = [
        {
            status: "Occupied Room",
            value: 280,
            usedColor:"#2A3A6D"
        },
        {
            status: "Reserved",
            value: 100,
            usedColor:"#070C44"
        },
                {
            status: "Available",
            value: 100,
            usedColor:"#40528F"
        },
                {
            status: "Canceled",
            value: 10,
            usedColor:"#1E265E"
        },
                

    ]

    const HouseKeepingStatus = [
        {
            title: 'Clean',
            status: "Completed",
            value: 20,
            color:"#2A3A6D"
        },
        {
            title: 'Cleaning',
            status: "In Progress",
            value: 15,
            color:"#070C44"
        },
        {
            title: 'Dirty',
            status: "In Pending",
            value: 10,
            color:"#1E265E"
        },
    ]


    return {InHouseGuests,ExpectedArrivals,ExpectedCheckouts,NoShow,pieChart,todayCheckout,roomAvailability,RoomService,HouseKeepingStatus}
}

