import {
  ActivityIcon,
  AvailabilityIcon,
  BookingsIcon,
  CabIcon,
  ChannelIcon,
  ComplainIcon,
  DashboardIcon,
  ExpenseIcon,
  FinancialManagementIcon,
  FoodOrderingIcon,
  GuestIcon,
  HotelCongigIcon,
  HouseKeepingIcon,
  HrIcon,
  InventoryIcon,
  LaundaryIcon,
  PermissionIcon,
  PushNotificationIcon,
  ReportsIcon,
  RoomManagementIcon,
  StoreManagementIcon,
  WakeupIcon,
} from "../assets/icons/Icons";
import { ISidebarDashboardRoutes } from "../Interface/global.interface";
// import { FrontendRoutes } from './FrontendRoutes';

export const SidebarDashboardRoutes: ISidebarDashboardRoutes = {
  title: "MAIN",
  routes: [
    {
      id: 1,
      path: "/",
      title: "Dashboard",
      icon: DashboardIcon,
      name: "Dashboard",
    },
    {
      id: 2,
      path: "/booking-management",
      title: "Booking Management",
      icon: BookingsIcon,
      name: "Booking Management",
    },
    {
      id: 3,
      path: "/room-management",
      title: "Room Management",
      icon: RoomManagementIcon,
      name: "Room Management",
    },
    {
      id: 4,
      path: "/guest-management",
      title: "Guest Management",
      icon: GuestIcon,
      name: "Guest Management",
    },
    {
      id: 5,
      path: "/availability-calendar",
      title: "Availability Calendar",
      icon: AvailabilityIcon,
      name: "Availability Calendar",
    },
    {
      id: 6,
      path: "/wakeup-module",
      title: "Wake Up Module ",
      icon: WakeupIcon,
      name: "Wakeup",
    },
  ],
};

export const SidebarHousekeepingRoute: ISidebarDashboardRoutes = {
  title: "Housekeeping",
  routes: [
    {
      id: 7,
      path: "#",
      title: "House Keeping",
      icon: HouseKeepingIcon,
      name: "HouseKeeping",
      children: [
        {
          path: "/service-ticketing",
          title: "Service Ticketing ",
          id: 80000,
          name: "Service Ticketing",
        },
        {
          path: "/room-cleaning",
          title: "Room Cleaning ",
          id: 800001,
          name: "Room Cleaning",
        },
        {
          path: "/room-inspection",
          title: "Room Inspection",
          id: 81111,
          name: "Room Inspection",
        },
        {
          path: "/lost-found",
          title: "Lost & Found",
          id: 83111,
          name: "Lost & Found",
        },
        {
          path: "/maintenance",
          title: "Maintenance",
          id: 85111,
          name: "Maintenance",
        },
      ],
    },
    {
      id: 8,
      path: "#",
      title: "Laundry Management",
      name: "Laundry Management",
      icon: LaundaryIcon,
      children: [
        {
          path: "/laundry",
          title: "Guest Laundry",
          id: 900001,
          name: "Guest Laundry",
        },
        {
          path: "/laundry-category",
          title: "Laundary Category",
          id: 900002,
          name: "Laundary Category",
        },
        {
          path: "/laundry-services",
          title: "Laundary Services",
          id: 900003,
          name: "Laundary Services",
        },
        {
          path: "/laundry-inventory",
          title: "Laundry Inventory",
          id: 900004,
          name: "Laundry Inventory",
        },
      ],
    },
  ],
};

export const SidebarFinanceSection: ISidebarDashboardRoutes = {
  title: "Finance",
  routes: [
    {
      id: 9,
      path: "#",
      title: "Expenses Management",
      name: "Expenses Management",
      icon: ExpenseIcon,
      children: [
        {
          path: "/expense-management/expenses",
          title: "Expenses",
          id: 70000,
          name: "Expenses",
        },
        {
          path: "/expense-management/category",
          title: "Expenses Category",
          id: 71111,
          name: "Expense Category",
        },
      ],
    },
    // {
    //   id: 18,
    //   path: "#",
    //   title: "Financial Management",
    //   icon: ExpenseIcon,
    //   children: [{ path: "/Bank", title: "Bank ", id: 98000 }],
    // },

    {
      id: 999,
      path: "#",
      title: "Financial Management",
      name: "Financial Management",
      icon: FinancialManagementIcon,
      children: [
        { path: "/Bank", title: "Bank ", id: 98000, name: "Bank" },
        {
          path: "/transaction-adjustment",
          title: "Transaction Adjustment",
          id: 700022,
          name: "Transaction Adjustment",
        },
        // { path: "/finance-category", title: "Finance Category", id: 700023 },
      ],
    },
    // Income Management section removed
    // {
    //   id: 12,
    //   path: "#",
    //   title: "Income Management",
    //   icon: InventoryIcon,
    //   name:"Income Management",
    //   children: [
    //     { path: "/income-list",name:"Income list", title: "Income List", id: 100011 },
    //     { path: "/income-category", name:"Income Category",title: "Income Category", id: 100022 },
    //     { path: "/income-type-list",name:"Income Type List", title: "Income Type List", id: 100023 },
    //   ],
    // },
  ],
};

export const SidebarDailyScheduleRoutes: ISidebarDashboardRoutes = {
  title: "Operation",
  routes: [
    {
      id: 10,
      path: "#",
      title: "Store Management",
      name: "Store Management",
      icon: StoreManagementIcon,
      children: [
        {
          path: "/store/dashboard",
          title: "Store Dashboard",
          id: 89999,
          name: "Store Dashboard",
        },
        {
          path: "/store/sub-store",
          name: "Sub Store",
          title: "Sub Store",
          id: 91111,
        },
      ],
    },
    {
      id: 11,
      path: "#",
      title: "Restaurant Management",
      name: "Restaurant Management",
      icon: FoodOrderingIcon,
      children: [
        {
          path: "/restaurant/dashboard",
          title: "Restaurant Dashboard",
          id: 110001,
          name: "Restaurant Dashboard",
        },
        {
          path: "/restaurant/food-ordering",
          title: "Food Ordering",
          id: 110002,
          name: "Food Ordering",
        },
        {
          path: "/restaurant/inventory",
          title: "Restaurant Inventory",
          id: 110003,
          name: "Restaurant Inventory",
        },
      ],
    },

    {
      id: 12,
      path: "#",
      title: "Inventory Management",
      icon: InventoryIcon,
      name: "Inventory Management",
      children: [
        {
          path: "/inventory-management/inventory-list",
          title: "Inventory List",
          name: "Inventory List",
          id: 100002,
        },
        {
          path: "/inventory-management/product-category",
          title: "Product Category",
          name: "Product Category",
          id: 100003,
        },
        {
          path: "/inventory-management/product-list",
          title: "Product List",
          name: "Product List",
          id: 100004,
        },
        {
          path: "/inventory-management/purchase",
          title: "Purchase",
          name: "Purchase",
          id: 100005,
        },
      ],
    },

    {
      id: 13,
      path: "#",
      title: "Activity",
      name: "Activity",
      icon: ActivityIcon,
      children: [
        {
          path: "/activity/activity-list",
          title: "Activity List",
          id: 100031,
          name: "Activity List",
        },
        {
          path: "/activity/activity-booking",
          title: "Activity Booking",
          name: "Activity Booking",
          id: 100032,
        },
      ],
    },

    {
      id: 14,
      path: "#",
      title: "Cab Facility",
      name: "Cab Facility",
      icon: CabIcon,
      children: [
        { path: "/cab-list", title: "Cab List", id: 90000, name: "Cab List" },
        {
          path: "/cab-booking",
          name: "Cab Booking",
          title: "Cab Booking",
          id: 91111,
        },
      ],
    },
    // {
    //   id: 15,
    //   path: "#",
    //   title: "Channel Manager",
    //   icon: ChannelIcon,
    //   children: [
    //     { path: "/channel", title: "Channel", id: 111000 },
    //     { path: "/channel-category", title: "Channel Category", id: 122222000 },
    //   ],
    // },
    {
      id: 16,
      path: "#",
      title: "Reports",
      name: "Report",
      icon: ReportsIcon,
      children: [
        {
          path: "/reports/guest-reports",
          title: "Guest",
          id: 1000050,
          name: "Guest",
        },
        {
          path: "/reports/expenses-reports",
          title: "Expenses",
          id: 1000052,
          name: "Expenses",
        },
      ],
    },
  ],
};

export const SidebarServiceDepartmentsRoutes: ISidebarDashboardRoutes = {
  // title: 'Service Departments',
  routes: [
    {
      id: 17,
      // path: "/channel-manager",
      title: "Channel Manager",
      icon: ChannelIcon,
      children: [
        { path: "/channel", title: "Channel", id: 111000 },
        { path: "/channel-category", title: "Channel Category", id: 122222000 },
      ],
    },
  ],
};

export const HrmsRoutes: ISidebarDashboardRoutes = {
  title: "HRMS",
  routes: [
    // {
    //   id: 18,
    //   path: "/employee-configuration",
    //   title: "Employee Configuration",
    //   icon: HrIcon,
    // },
    {
      id: 19,
      path: "#",
      title: "HR Management",
      name: "HR Management",

      icon: HrIcon,
      children: [
        {
          path: "/hr-management/employee-management",
          title: "All Employees",
          name: "All Employees",
          id: 1110,
        },
        {
          path: "/hr-management/employee-schedule",
          title: "Employee Schedule",
          name: "Employee Schedule",
          id: 111011,
        },
        {
          path: "/hr-management/employee-attendance",
          title: "Employee Attendance",
          name: "employee attendance",
          id: 122222,
        },
        {
          path: "/hr-management/employee-payroll",
          title: "Employee Payroll",
          name: "employee payroll",
          id: 133333,
        },
        {
          path: "/hr-management/employee-configuration",
          title: "Employee Configuration",
          name: "employee config",
          id: 1444,
        },
        {
          path: "/hr-management/leave-tracking",
          title: "Leave Tracking",
          name: "leave tracking",
          id: 15555,
        },
        {
          path: "/user-logs",
          name: "user logs",
          title: "User Logs",
          id: 15855,
        },
      ],
    },
    {
      icon: ComplainIcon,
      id: 1010,
      path: "/customer-complain-box",
      title: "Customer Complaint Box",
      name: "Customer Complaint Box",
    },
    // {
    //   id: 20,
    //   path: "/employee-duty-assign",
    //   title: "Employee Duty Assign",
    //   icon: HrIcon,
    //   children: [
    //     { path: "/assigned-list", title: "Assigned List", id: 1213 },
    //     { path: "/shift-list", title: "Shift List", id: 13134 },
    //     { path: "/roster-list", title: "Roster List", id: 14134 },
    //   ],
    // },
  ],
};
export const SettingRoutes: ISidebarDashboardRoutes = {
  title: "SETTINGS",
  routes: [
    {
      id: 20202,
      path: "/push-notifications",
      title: "Push Notifications",
      icon: PushNotificationIcon,
      name: "Push Notification",
    },
    {
      id: 21,
      path: "#",
      title: "Hotel Configurations",
      icon: HotelCongigIcon,
      name: "Hotel config",
      children: [
        {
          id: 711000,
          path: "/hotel-config/bed-list",
          title: "Bed List",
          name: "Bed List",
        },
        {
          id: 722222000,
          path: "/hotel-config/floor-plan",
          title: "Floor Plan List",
          name: "floor plan list",
        },
        {
          id: 74444000,
          path: "/hotel-config/room-facilities",
          title: "Room Amenities",
          name: "room amenity",
        },
        {
          id: 733333000,
          path: "/hotel-config/room-list",
          title: "Room List",
          name: "room list",
        },

        {
          id: 7444400777,
          path: "/hotel-config/room-services",
          title: "Room Services",
        },
        {
          id: 74444002,
          path: "/hotel-config/room-package",
          title: "Room Package",
        },

        {
          id: 74444001,
          path: "/hotel-config/membership-tiers",
          title: "Membership Tiers",
          name: "Membership Tiers",
        },

        // { id: 74444003, path: "/hotel-config/room-type", title: "Room Type" },

        {
          id: 74444005,
          path: "/hotel-config/price-manager",
          title: "Price Manager",
        },
      ],
    },
    {
      id: 22,
      path: "/permissions",
      title: "Permissions",
      icon: PermissionIcon,
      name: "Permission",
    },
  ],
};
