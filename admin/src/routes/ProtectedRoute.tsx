import { Navigate } from "react-router-dom";
import { useAuth } from "../hooks";
import { useEffect, useState } from "react";
import { onMessageListener } from "../server-action/config/FirebaseConfig";
import { toast } from "react-toastify";

const ProtectedRoute = ({ children, notAllowedRoles }: any) => {
  const { data: authData, isLoading } = useAuth();
  const [fcmToken, _] = useState<string | null>(null);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  useEffect(() => {
    const fcmToken = localStorage.getItem("fcmToken");

    if (fcmToken) {
      const unsubscribe = onMessageListener().then((message: any) => {
        console.log("Foreground Message Received:", message);
        toast.success(message?.data?.title);
      });
      return () => {
        // unsubscribe();
      };
    }
  }, [fcmToken]);

  if (!authData) {
    return <Navigate to="/login" replace />;
  }
  if (authData.user.role === "superadmin") {
    return children;
  }

  if (notAllowedRoles && notAllowedRoles.includes(authData.user.role)) {
    return <Navigate to="/default" replace />;
  }

  return children;
};

export default ProtectedRoute;
