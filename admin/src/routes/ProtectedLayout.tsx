import { Navigate, Outlet, useLocation } from "react-router-dom";
import { useAuth } from "../hooks/";
import { useEffect, useState } from "react";

export const ProtectedLayout = () => {
  const location = useLocation();
  const { data: authData, isLoading } = useAuth();

  console.log(authData, "auth data");

  // Check localStorage directly
  const token = localStorage.getItem("_UPLFMMATRIX");
  const user = localStorage.getItem("user");
  console.log("Token from localStorage:", token ? "exists" : "missing");
  console.log("User from localStorage:", user ? JSON.parse(user) : "missing");

  if (!authData && token && user) {
    console.log("Auth data not found but token and user exist in localStorage");
  }
  const [redirectPath, setRedirectPath] = useState<string | null>(null);

  useEffect(() => {
    if (!isLoading && !authData) {
      const path = location.pathname + location.search;
      setRedirectPath(`/auth-login?redirect=${encodeURIComponent(path)}`);
    }
  }, [isLoading, authData, location]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (redirectPath) {
    return <Navigate to={redirectPath} replace />;
  }

  return authData ? <Outlet /> : null;
};
