import { useState, useEffect } from "react";
import {
  HrmsRoutes,
  SettingRoutes,
  SidebarDailyScheduleRoutes,
  SidebarDashboardRoutes,
  SidebarFinanceSection,
  SidebarHousekeepingRoute,
} from "../routes";
import { SidebarProps } from "../components/SidebarProps";
import { Logo } from "../assets/Svg/logo";
import { useFilteredRoutes } from "../server-action/utils/userFilteredRoutes";

interface sidebarProps {
  miniSidebar: boolean;
  setMiniSidebar: React.Dispatch<React.SetStateAction<boolean>>;
  isCompact: boolean;
  setIsCompact: React.Dispatch<React.SetStateAction<boolean>>;
}

export const SideBar = ({
  miniSidebar,
  setMiniSidebar,
  isCompact,
}: sidebarProps) => {
  const [expandedItems, setExpandedItems] = useState<number[]>([]);

  const toggleExpand = (id: number) => {
    setExpandedItems((prev) =>
      prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]
    );
  };

  const { filteredRoutes: SidebarDashboardRoute } = useFilteredRoutes(
    SidebarDashboardRoutes,
    false
  );
  const { filteredRoutes: SidebarHousekeepingRoutes } = useFilteredRoutes(
    SidebarHousekeepingRoute,
    false
  );
  const { filteredRoutes: SidebarFinanceSections } = useFilteredRoutes(
    SidebarFinanceSection,
    false
  );
  const { filteredRoutes: SidebarDailyScheduleRoute } = useFilteredRoutes(
    SidebarDailyScheduleRoutes,
    false
  );
  const { filteredRoutes: HrmsRoute } = useFilteredRoutes(HrmsRoutes, false);
  const { filteredRoutes: SettingRoute } = useFilteredRoutes(
    SettingRoutes,
    false
  );

  return (
    <>
      {miniSidebar && (
        <div
          className="fixed inset-0 bg-black opacity-50 z-40 md:hidden"
          onClick={() => setMiniSidebar(false)}
        ></div>
      )}

      <div
        className={`fixed overflow-y-scroll left-0 top-0 h-screen bg-[#2A3A6D] text-white transition-all duration-300 z-50 ${
          miniSidebar
            ? isCompact
              ? "w-16" // Compact mode width
              : "w-72" // Full sidebar width
            : "-translate-x-full w-0" // Hidden
        }`}
        id="sidebar"
      >
        <section className="flex flex-col pt-3 text-black">
          <div className="h-[82px] px-1 flex justify-center items-center">
            <div
              className="cursor-pointer"
              onClick={() => setMiniSidebar(false)}
            >
              {Logo()}
            </div>
          </div>

          <SidebarProps
            SidebarRoutes={SidebarDashboardRoute as any}
            expandedItems={expandedItems}
            miniSidebar={miniSidebar}
            setMiniSidebar={setMiniSidebar}
            toggleExpand={toggleExpand}
            isCompact={isCompact}
          />
          <SidebarProps
            SidebarRoutes={SidebarHousekeepingRoutes as any}
            expandedItems={expandedItems}
            miniSidebar={miniSidebar}
            setMiniSidebar={setMiniSidebar}
            toggleExpand={toggleExpand}
            isCompact={isCompact}
          />
          <SidebarProps
            SidebarRoutes={SidebarFinanceSections as any}
            expandedItems={expandedItems}
            miniSidebar={miniSidebar}
            setMiniSidebar={setMiniSidebar}
            toggleExpand={toggleExpand}
            isCompact={isCompact}
          />
          <SidebarProps
            SidebarRoutes={SidebarDailyScheduleRoute as any}
            expandedItems={expandedItems}
            miniSidebar={miniSidebar}
            setMiniSidebar={setMiniSidebar}
            toggleExpand={toggleExpand}
            isCompact={isCompact}
          />
          <SidebarProps
            SidebarRoutes={HrmsRoute as any}
            expandedItems={expandedItems}
            miniSidebar={miniSidebar}
            setMiniSidebar={setMiniSidebar}
            toggleExpand={toggleExpand}
            isCompact={isCompact}
          />
          <SidebarProps
            SidebarRoutes={SettingRoute as any}
            expandedItems={expandedItems}
            miniSidebar={miniSidebar}
            setMiniSidebar={setMiniSidebar}
            toggleExpand={toggleExpand}
            isCompact={isCompact}
          />
        </section>
      </div>
    </>
  );
};
