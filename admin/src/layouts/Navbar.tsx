import { useState, useRef, useEffect } from "react";
import { Icon } from "@iconify/react/dist/iconify.js";
import { Text } from "../components";
import { useAuth } from "../hooks";
import { Link } from "react-router-dom";
import NotificationPopup from "../pages/PushNotification/components/NotificationPopup";

interface NavbarProps {
  setMiniSidebar: React.Dispatch<React.SetStateAction<boolean>>;
  miniSidebar: boolean;
  isCompact: boolean;
  setIsCompact: React.Dispatch<React.SetStateAction<boolean>>;
}

export const Navbar = ({
  miniSidebar,
  setMiniSidebar,
  isCompact,
  setIsCompact,
}: NavbarProps) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isLogoutDialogOpen, setIsLogoutDialogOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isSearchVisible, setIsSearchVisible] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchRef = useRef<HTMLDivElement>(null);

  const user = useAuth().data?.user;

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }

      if (
        searchRef.current &&
        !searchRef.current.contains(event.target as Node) &&
        window.innerWidth < 768
      ) {
        setIsSearchVisible(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleLogout = () => {
    setIsLogoutDialogOpen(true);
    setIsDropdownOpen(false);
    setIsMobileMenuOpen(false);
  };

  const confirmLogout = () => {
    setIsLogoutDialogOpen(false);
    localStorage.removeItem("user");
    window.location.reload();
  };

  const handleSidebarToggle = () => {
    if (window.innerWidth >= 900) {
      // Desktop: Toggle between full and compact modes
      if (miniSidebar && !isCompact) {
        setIsCompact(true); // Switch to compact mode
      } else {
        setIsCompact(false); // Switch to full mode
        setMiniSidebar(true); // Ensure sidebar is visible
      }
    } else {
      // Mobile: Toggle sidebar visibility
      setMiniSidebar((prev) => !prev);
      setIsCompact(false); // Ensure compact mode is off on mobile
    }
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
    if (isSearchVisible) setIsSearchVisible(false);
  };

  const toggleSearch = () => {
    setIsSearchVisible(!isSearchVisible);
    if (isMobileMenuOpen) setIsMobileMenuOpen(false);
  };

  return (
    <div className="relative">
      <div className="flex justify-between w-full ml-[2px] sm:ml-2 rounded-md text-white bg-[#2A3A6D] px-3 sm:px-6 py-3 shadow-sm items-center">
        <div className="flex items-center gap-2 sm:gap-4 text-white">
          <Icon
            icon={
              window.innerWidth >= 900
                ? isCompact
                  ? "octicon:three-bars-16"
                  : "ic:baseline-menu-open"
                : miniSidebar
                ? "ic:baseline-menu-open"
                : "octicon:three-bars-16"
            }
            width="24"
            height="24"
            className="flex-shrink-0 cursor-pointer"
            onClick={handleSidebarToggle}
          />
          <Text
            variant="silver-950"
            size="body-sm-default"
            className="hidden sm:block"
          >
            Dashboard
          </Text>
        </div>

        <div className="flex md:hidden items-center gap-2">
          <button
            onClick={toggleSearch}
            className="p-2 rounded-full hover:bg-[#3A4A7D] transition-colors"
          >
            <Icon icon="lucide:search" width="20" height="20" />
          </button>
          <button
            onClick={toggleMobileMenu}
            className="p-2 rounded-full hover:bg-[#3A4A7D] transition-colors"
          >
            <Icon
              icon={isMobileMenuOpen ? "lucide:x" : "lucide:menu"}
              width="20"
              height="20"
            />
          </button>
        </div>

        <div className="hidden md:flex items-center gap-5 ml-[2px]">
          {/* <div className="relative text-gray-600">
            <input
              className="h-10 px-5 pr-10 text-sm bg-white border-2 border-gray-300 rounded-lg focus:outline-none"
              type="search"
              name="search"
              placeholder="Search"
            />
            <button type="submit" className="absolute top-0 right-0 mt-3 mr-4">
              <Icon icon="lucide:search" className="w-4 h-4 text-gray-600" />
            </button>
          </div> */}

          <section className="flex items-center relative">
            <div className="flex items-center gap-4">
              <NotificationPopup />
            </div>
          </section>

          <div className="relative" ref={dropdownRef}>
            <button
              className="flex gap-5 items-center focus:outline-none"
              onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            >
              <div className="flex gap-3 items-center">
                <img
                  className="w-10 h-10 rounded-full object-cover"
                  src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Nnx8dXNlcnxlbnwwfHwwfHx8MA%3D%3D"
                  alt="Profile"
                />

                <section className="flex flex-col">
                  <Text variant="silver-950" size="body-sm-default">
                    {user?.name || "User Name"}
                  </Text>
                  <Text
                    variant="silver-600"
                    size="body-xs-mid"
                    className="capitalize"
                  >
                    {user?.role || "Role"}
                  </Text>
                </section>
              </div>
              <Icon
                icon="oui:arrow-down"
                fontSize={16}
                className={`transition-transform duration-200 ${
                  isDropdownOpen ? "rotate-180" : ""
                }`}
              />
            </button>

            {isDropdownOpen && (
              <div className="absolute right-0 mt-2 ml-[2px] w-48 bg-white rounded-md shadow-lg py-1 z-20">
                <Link
                  to={"/profile"}
                  className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  <Icon icon="lucide:user" className="mr-2" />
                  My Profile
                </Link>
                <a
                  href="#"
                  className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  <Icon icon="lucide:settings" className="mr-2" />
                  Settings
                </a>
                <div className="border-t border-gray-100"></div>
                <button
                  onClick={handleLogout}
                  className="flex items-center w-full text-left px-4 py-2 text-red hover:bg-gray-100 rounded-sm text-sm text-red-600"
                >
                  <Icon icon="lucide:log-out" className="mr-2" />
                  Logout
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {isSearchVisible && (
        <div
          ref={searchRef}
          className="absolute top-full mt-1 left-2 rounded-md right-0 bg-[#2A3A6D] p-3 z-10 md:hidden shadow-md"
        >
          <div className="relative text-gray-600 w-full">
            <input
              className="h-10 w-full px-5 pr-10 text-sm bg-white border-2 border-gray-300 rounded-lg focus:outline-none"
              type="search"
              name="search"
              placeholder="Search"
              autoFocus
            />
            <button type="submit" className="absolute top-0 right-0 mt-3 mr-4">
              <Icon icon="lucide:search" className="w-4 h-4 text-gray-600" />
            </button>
          </div>
        </div>
      )}

      {isMobileMenuOpen && (
        <div className="absolute top-full left-2 mt-1 rounded-md right-0 bg-[#2A3A6D] p-4 z-10 md:hidden shadow-md">
          <div className="flex items-center gap-3 mb-4 border-b border-[#3A4A7D] pb-4">
            <img
              className="w-10 h-10 rounded-full object-cover"
              src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Nnx8dXNlcnxlbnwwfHwwfHx8MA%3D%3D"
              alt="Profile"
            />
            <div className="flex flex-col">
              <Text
                variant="silver-950"
                size="body-sm-default"
                className="text-white"
              >
                {user?.name || "User Name"}
              </Text>
              <Text
                variant="silver-600"
                size="body-xs-mid"
                className="capitalize text-white"
              >
                {user?.role || "Role"}
              </Text>
            </div>
          </div>

          <div className="flex flex-col space-y-2">
            <a
              href="#"
              className="flex items-center px-2 py-3 text-white hover:bg-[#3A4A7D] rounded-md"
            >
              <Icon icon="lucide:user" className="mr-3" />
              My Profile
            </a>
            <a
              href="#"
              className="flex items-center px-2 py-3 text-white hover:bg-[#3A4A7D] rounded-md"
            >
              <Icon icon="lucide:settings" className="mr-3" />
              Settings
            </a>
            <a
              href="#"
              className="flex items-center px-2 py-3 text-white hover:bg-[#3A4A7D] rounded-md"
            >
              <Icon icon="tdesign:notification" className="mr-3" />
              Notifications
            </a>
            <button
              onClick={handleLogout}
              className="flex items-center text-red px-2 py-3 text-red-400 hover:bg-[#3A4A7D] rounded-md text-left"
            >
              <Icon icon="lucide:log-out" className="mr-3 text-red" />
              Logout
            </button>
          </div>
        </div>
      )}

      {isLogoutDialogOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-4 sm:p-6 max-w-sm w-full">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Confirm Logout
            </h3>
            <p className="text-sm text-gray-500 mb-4">
              Are you sure you want to logout? Any unsaved changes will be lost.
            </p>
            <div className="flex justify-end gap-2">
              <button
                onClick={() => setIsLogoutDialogOpen(false)}
                className="px-3 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors text-sm"
              >
                Cancel
              </button>
              <button
                onClick={confirmLogout}
                className="px-3 py-2 bg-red text-white rounded-md hover:bg-red-700 transition-colors flex items-center text-sm"
              >
                <Icon icon="lucide:log-out" className="mr-2" />
                Logout
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
