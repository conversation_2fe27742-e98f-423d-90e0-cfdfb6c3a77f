import React, { useState, useEffect, useCallback } from "react";
import { debounce } from "lodash";
import DataTableWithShimmer from "./ShimmerLoader";
import Filter from "./Filter";

// Helper function to safely display object values
const safelyDisplayObject = (obj: any): string => {
  if (!obj) return "";
  if (obj.$$typeof || obj._owner || obj._store) {
    if (obj.props) {
      if (typeof obj.props.children === "string") return obj.props.children;
      if (obj.props.value !== undefined) return String(obj.props.value);
      if (obj.props.label !== undefined) return String(obj.props.label);
      if (obj.props.title !== undefined) return String(obj.props.title);
      if (obj.props.name !== undefined) return String(obj.props.name);
    }
    return "[React Component]";
  }
  if (obj.name) return obj.name;
  if (Array.isArray(obj)) {
    return obj
      .map((item) =>
        typeof item === "object" && item !== null
          ? item.name || item.title || item.label || item.id || "Item"
          : String(item)
      )
      .join(", ");
  }
  try {
    const possibleProps = [
      "title",
      "label",
      "value",
      "id",
      "text",
      "description",
    ];
    for (const prop of possibleProps) {
      if (
        obj[prop] !== undefined &&
        obj[prop] !== null &&
        typeof obj[prop] !== "object"
      ) {
        return String(obj[prop]);
      }
    }
    const reactInternalProps = [
      "$$typeof",
      "type",
      "key",
      "ref",
      "props",
      "_owner",
      "_store",
      "_context",
      "Provider",
      "Consumer",
    ];
    const keys = Object.keys(obj).filter(
      (key) => !reactInternalProps.includes(key)
    );
    if (keys.length <= 3 && keys.length > 0) {
      return keys
        .map((key) => {
          const val = obj[key];
          if (val === null || val === undefined) return "";
          if (typeof val === "object") return key;
          return `${val}`;
        })
        .filter(Boolean)
        .join(", ");
    }
    if (keys.length > 0) {
      return `{${keys.join(", ")}}`;
    }
    return "[Component]";
  } catch (e) {
    return "Object";
  }
};

// Define column interface
interface Column {
  key: string;
  title: string;
  render?: (row: Row) => React.ReactNode;
  sortable?: boolean; // Allow specifying sortable columns
}

// Define row interface
interface Row {
  id?: string;
  isActive?: boolean;
  [key: string]: any;
}

// Define filter option interface
interface FilterOption {
  value: string;
  label: string;
}

// Define filter configuration interface
interface FilterConfig {
  label: string;
  key: string;
  options?: FilterOption[];
  type?: string;
}

// Define component props interface
interface MasterTableProps {
  rows: Row[];
  columns: Column[];
  entriesPerPage?: number;
  canSearch?: boolean;
  canSelect?: boolean;
  onDelete?: (selectedIds: (string | number)[]) => void;
  showTopPageSelector?: boolean;
  showToggle?: boolean;
  onToggle?: (row: Row) => void;
  apiPagination?: boolean;
  totalItems?: number;
  onPageChange?: (page: number, pageSize: number) => void;
  onSearch?: (
    searchTerm: string,
    filterValues?: Record<string, string | null>
  ) => void;
  loading?: boolean;
  pagination?: {
    currentPage: number;
    totalPage: number;
    limit: number;
    onClick: (page: number) => void;
  };
  showLimit?: boolean;
  showFilter?: boolean;
  filterConfig?: FilterConfig[];
  sortBy?: string; // Column to sort by (defaults to "createdAt")
  sortOrder?: "asc" | "desc"; // Sort order (defaults to "desc" for newest first)
}

const MasterTable: React.FC<MasterTableProps> = ({
  rows = [],
  columns = [],
  entriesPerPage = 5,
  canSearch = true,
  canSelect = false,
  onDelete,
  apiPagination = false,
  totalItems = 0,
  onPageChange,
  onSearch,
  loading = true,
  showTopPageSelector = true,
  showToggle = false,
  onToggle,
  showLimit = true,
  showFilter = false,
  filterConfig = [],
  sortBy = "createdAt", // Default to sort by creation date
  sortOrder = "asc", // Default to newest first
}) => {
  const [globalFilter, setGlobalFilter] = useState<string>("");
  const [filterValues, setFilterValues] = useState<
    Record<string, string | null>
  >({});
  const [pageIndex, setPageIndex] = useState<number>(0);
  const [pageSize] = useState<number>(entriesPerPage);
  const [filteredRows, setFilteredRows] = useState<Row[]>(rows);
  const [selectedRows, setSelectedRows] = useState<Set<string | number>>(
    new Set()
  );
  const [toggledRows, setToggledRows] = useState<Set<string | number>>(
    new Set()
  );
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<boolean>(false);
  const [isSmallScreen, setIsSmallScreen] = useState<boolean>(false);
  const [sortConfig, setSortConfig] = useState<{
    key: string | null;
    order: "asc" | "desc" | null;
  }>({ key: sortBy || null, order: sortOrder || null }); // Initialize with props

  useEffect(() => {
    const checkScreenSize = () => {
      setIsSmallScreen(window.innerWidth < 768);
    };

    checkScreenSize();
    window.addEventListener("resize", checkScreenSize);
    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  useEffect(() => {
    if (showToggle) {
      const activeRows = rows
        .filter((row) => row.isActive && row.id !== undefined)
        .map((row) => row.id!);
      setToggledRows(new Set(activeRows));
    }
  }, [rows, showToggle]);

  const totalPages = apiPagination
    ? Math.ceil(totalItems / pageSize)
    : Math.ceil(filteredRows.length / pageSize);

  const handlePageChange = (newPage: number) => {
    setPageIndex(newPage);
    if (apiPagination && onPageChange) {
      onPageChange(newPage, pageSize);
    }
  };

  // Sorting handler for column header clicks
  const handleSort = (key: string) => {
    if (!columns.find((col) => col.key === key)?.sortable) return;

    let newOrder: "asc" | "desc" = sortOrder || "asc"; // Use prop if provided, else default to asc
    if (sortConfig.key === key) {
      newOrder = sortConfig.order === "asc" ? "desc" : "asc";
    }

    setSortConfig({ key, order: newOrder });
  };

  useEffect(() => {
    if (apiPagination) {
      setFilteredRows(rows);
      return;
    }

    let filtered = rows;

    if (globalFilter) {
      filtered = filtered.filter((row) =>
        columns.some((column) => {
          const value = row[column.key];
          return (
            value &&
            String(value).toLowerCase().includes(globalFilter.toLowerCase())
          );
        })
      );
    }

    if (filterConfig.length > 0) {
      filterConfig.forEach((config) => {
        const filterValue = filterValues[config.key];
        if (filterValue) {
          filtered = filtered.filter(
            (row) => String(row[config.key]) === filterValue
          );
        }
      });
    }

    // Apply sorting based on sortBy/sortOrder props or sortConfig
    const currentSortKey = sortConfig.key || sortBy;
    const currentSortOrder = sortConfig.order || sortOrder;
    if (
      currentSortKey &&
      currentSortOrder &&
      columns.find((col) => col.key === currentSortKey)
    ) {
      filtered = [...filtered].sort((a, b) => {
        const aValue = a[currentSortKey];
        const bValue = b[currentSortKey];

        if (aValue === null || aValue === undefined) return 1;
        if (bValue === null || bValue === undefined) return -1;

        if (typeof aValue === "string" && typeof bValue === "string") {
          return currentSortOrder === "asc"
            ? aValue.localeCompare(bValue)
            : bValue.localeCompare(aValue);
        }

        return currentSortOrder === "asc"
          ? aValue < bValue
            ? -1
            : 1
          : aValue > bValue
          ? -1
          : 1;
      });
    }

    setFilteredRows(filtered);
    setPageIndex(0);
  }, [
    globalFilter,
    filterValues,
    rows,
    columns,
    apiPagination,
    sortConfig,
    sortBy,
    sortOrder,
  ]);

  const debouncedSearch = useCallback(
    debounce((value: string, filters: Record<string, string | null>) => {
      setGlobalFilter(value);
      if (apiPagination && onSearch) {
        onSearch(value, filters);
      }
    }, 300),
    [apiPagination, onSearch]
  );

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    debouncedSearch(e.target.value, filterValues);
  };

  const handleFilterChange = (key: string, value: string | null) => {
    setFilterValues((prev) => ({ ...prev, [key]: value }));
    if (apiPagination && onSearch) {
      debouncedSearch(globalFilter, { ...filterValues, [key]: value });
    }
  };

  const start = pageIndex * pageSize;
  const end = Math.min(
    (pageIndex + 1) * pageSize,
    apiPagination ? totalItems : filteredRows.length
  );

  const currentPageRows = apiPagination ? rows : filteredRows.slice(start, end);

  useEffect(() => {
    if (pageIndex >= totalPages && totalPages > 0) {
      setPageIndex(totalPages - 1);
    }
  }, [totalPages, pageIndex]);

  const toggleSelectAll = () => {
    if (selectedRows.size === currentPageRows.length) {
      setSelectedRows(new Set());
    } else {
      const newSelected = new Set<string | number>();
      currentPageRows.forEach((row) => {
        if (row.id !== undefined) {
          newSelected.add(row.id);
        }
      });
      setSelectedRows(newSelected);
    }
  };

  const toggleSelectRow = (id: string | number) => {
    const newSelected = new Set(selectedRows);
    if (newSelected.has(id)) {
      newSelected.delete(id);
    } else {
      newSelected.add(id);
    }
    setSelectedRows(newSelected);
  };

  const handleToggle = (row: Row) => {
    if (row.id === undefined) return;
    const newToggledRows = new Set(toggledRows);
    if (newToggledRows.has(row.id)) {
      newToggledRows.delete(row.id);
    } else {
      newToggledRows.add(row.id);
    }
    setToggledRows(newToggledRows);
    onToggle?.({ ...row, isActive: !row.isActive });
  };

  const handleDelete = () => {
    if (onDelete && selectedRows.size > 0) {
      onDelete(Array.from(selectedRows));
      setSelectedRows(new Set());
      setShowDeleteConfirm(false);
    }
  };

  const isAllSelected =
    currentPageRows.length > 0 && selectedRows.size === currentPageRows.length;
  const isSomeSelected =
    selectedRows.size > 0 && selectedRows.size < currentPageRows.length;

  const renderCardView = () => {
    return (
      <div className="grid grid-cols-1 gap-4 p-4">
        {loading ? (
          <DataTableWithShimmer />
        ) : currentPageRows.length === 0 ? (
          <p>No Data found</p>
        ) : (
          currentPageRows.map((row, index) => (
            <div
              key={index}
              className={`p-4 border rounded-lg shadow-sm ${
                selectedRows.has(row.id ?? "") ? "bg-blue-50" : "bg-white"
              }`}
            >
              <div className="flex justify-between mb-2">
                {canSelect && (
                  <input
                    type="checkbox"
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    checked={selectedRows.has(row.id ?? "")}
                    onChange={() => toggleSelectRow(row.id ?? "")}
                  />
                )}
                {showToggle && row.id !== undefined && (
                  <label
                    htmlFor={`toggle-${row.id}`}
                    className="flex cursor-pointer relative"
                  >
                    <input
                      type="checkbox"
                      id={`toggle-${row.id}`}
                      className="sr-only"
                      checked={toggledRows.has(row.id)}
                      onChange={() => handleToggle(row)}
                    />
                    <div
                      className={`toggle-bg border-2 h-[18px] w-[36px] rounded-full transition-colors duration-200 ease-in-out ${
                        toggledRows.has(row.id)
                          ? "bg-blue-600 border-blue-600"
                          : "bg-gray-200 border-gray-200"
                      }`}
                    >
                      <div
                        className={`toggle-dot absolute left-0 top-1/2 -translate-y-1/2 bg-white w-4 h-4 rounded-full transition-transform duration-200 ease-in-out transform ${
                          toggledRows.has(row.id)
                            ? "translate-x-[19px] border-blue-600"
                            : "translate-x-[1px] border-gray-200"
                        }`}
                      />
                    </div>
                  </label>
                )}
              </div>
              <div className="space-y-2">
                {columns.map((column) => (
                  <div key={column.key} className="flex flex-col">
                    <span className="text-xs font-medium text-gray-500 uppercase">
                      {column.title}
                    </span>
                    <span className="text-sm">
                      {column.key === "attachment" &&
                      row[column.key] === "Null" ? (
                        <span className="text-gray-400">Null</span>
                      ) : column.render ? (
                        column.render(row)
                      ) : React.isValidElement(row[column.key]) ? (
                        row[column.key]
                      ) : typeof row[column.key] === "object" &&
                        row[column.key] !== null ? (
                        safelyDisplayObject(row[column.key])
                      ) : (
                        row[column.key]
                      )}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          ))
        )}
      </div>
    );
  };

  return (
    <div
      className={
        "w-full overflow-hidden border border-gray-200 rounded-lg shadow-sm"
      }
    >
      <div className="flex flex-col justify-between gap-3 p-4 bg-white border-b md:flex-row md:items-center">
        {showTopPageSelector && (
          <div className="flex items-center">
            <span className="text-sm font-medium text-gray-600">Page</span>
            <div className="relative mx-2">
              <select
                className="px-3 py-1 pr-8 text-sm bg-white border border-gray-300 rounded appearance-none focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                value={pageIndex + 1}
                onChange={(e) => handlePageChange(Number(e.target.value) - 1)}
              >
                {Array.from({ length: Math.max(1, totalPages) }, (_, i) => (
                  <option key={i} value={i + 1}>
                    {i + 1}
                  </option>
                ))}
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 pointer-events-none">
                <svg
                  className="w-4 h-4 fill-current"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                >
                  <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                </svg>
              </div>
            </div>
            <span className="text-sm text-gray-600">
              of {Math.max(1, totalPages)}
            </span>
          </div>
        )}

        <div className="flex items-center gap-2">
          {canSelect && selectedRows.size > 0 && (
            <button
              onClick={() => setShowDeleteConfirm(true)}
              className="p-1.5 text-red-600 bg-red-50 rounded-full hover:bg-red-100"
              title="Delete selected"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="w-5 h-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                />
              </svg>
            </button>
          )}

          <div className="flex items-center gap-2 w-full md:w-auto">
            {showFilter &&
              filterConfig.map((config) => (
                <Filter
                  key={config.key}
                  label={config.label}
                  options={config.options}
                  type={config.type as "select" | "date" | undefined}
                  selectedValue={filterValues[config.key] || null}
                  onFilterChange={(value) =>
                    handleFilterChange(config.key, value)
                  }
                />
              ))}
            {canSearch && (
              <div className="relative w-full md:w-64">
                <input
                  type="text"
                  onChange={handleSearchChange}
                  className="w-full px-3 py-1.5 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Search..."
                />
                <div className="absolute inset-y-0 flex items-center pointer-events-none left-3">
                  <svg
                    className="w-4 h-4 text-gray-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fillRule="evenodd"
                      d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {isSmallScreen ? (
        renderCardView()
      ) : (
        <div className="overflow-x-auto bg-white master-table-container">
          <table className="min-w-full divide-y divide-gray-200 master-table">
            <thead className="bg-gray-50">
              <tr>
                {canSelect && (
                  <th className="px-3 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        checked={isAllSelected}
                        ref={(input) => {
                          if (input) {
                            input.indeterminate = isSomeSelected;
                          }
                        }}
                        onChange={toggleSelectAll}
                      />
                    </div>
                  </th>
                )}
                {showToggle && (
                  <th className="px-6 py-3 text-xs font-medium tracking-wider text-center text-gray-500 uppercase">
                    Toggle
                  </th>
                )}
                {columns.map((column) => (
                  <th
                    key={column.key}
                    className={`px-6 py-4 h-16 text-xs font-medium tracking-wider text-center text-[#2B2B2B] bg-[#F1F6FD] capitalize ${
                      column.sortable ? "cursor-pointer hover:bg-gray-200" : ""
                    }`}
                    onClick={() => handleSort(column.key)}
                  >
                    <div className="flex text-[13px] items-center uppercase justify-center">
                      {column.title}
                      {column.sortable && (
                        <span className="ml-1">
                          {sortConfig.key === column.key ? (
                            sortConfig.order === "asc" ? (
                              <svg
                                className="w-4 h-4"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth="2"
                                  d="M5 15l7-7 7 7"
                                />
                              </svg>
                            ) : (
                              <svg
                                className="w-4 h-4"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth="2"
                                  d="M19 9l-7 7-7-7"
                                />
                              </svg>
                            )
                          ) : (
                            <svg
                              className="w-4 h-4 text-gray-400"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M7 7l5-5 5 5m0 10l-5 5-5-5"
                              />
                            </svg>
                          )}
                        </span>
                      )}
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td
                    colSpan={
                      columns.length +
                      (canSelect ? 1 : 0) +
                      (showToggle ? 1 : 0)
                    }
                    className="px-6 text-sm text-center text-gray-500"
                  >
                    <DataTableWithShimmer />
                  </td>
                </tr>
              ) : currentPageRows.length === 0 ? (
                <tr>
                  <td
                    colSpan={
                      columns.length +
                      (canSelect ? 1 : 0) +
                      (showToggle ? 1 : 0)
                    }
                    className="px-6 py-4 text-sm text-center text-gray-500"
                  >
                    No data found
                  </td>
                </tr>
              ) : (
                currentPageRows.map((row, index: number) => (
                  <tr
                    key={index}
                    className={`hover:bg-gray-50 ${
                      selectedRows.has(row.id ?? "") ? "bg-blue-50" : ""
                    }`}
                  >
                    {canSelect && (
                      <td className="px-3 py-4 text-sm whitespace-nowrap">
                        <input
                          type="checkbox"
                          className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                          checked={selectedRows.has(row.id ?? "")}
                          onChange={() => toggleSelectRow(row.id ?? "")}
                        />
                      </td>
                    )}
                    {showToggle && row.id !== undefined && (
                      <td className="px-6 py-4 text-sm text-center whitespace-nowrap">
                        <label
                          htmlFor={`toggle-${row.id}`}
                          className="flex cursor-pointer relative justify-center"
                        >
                          <input
                            type="checkbox"
                            id={`toggle-${row.id}`}
                            className="sr-only"
                            checked={toggledRows.has(row.id)}
                            onChange={() => handleToggle(row)}
                          />
                          <div
                            className={`toggle-bg border-2 h-[18px] w-[36px] rounded-full transition-colors duration-200 ease-in-out ${
                              toggledRows.has(row.id)
                                ? "bg-blue-600 border-blue-600"
                                : "bg-gray-200 border-gray-200"
                            }`}
                          >
                            <div
                              className={`toggle-dot absolute left-0 top-1/2 -translate-y-1/2 bg-white w-4 h-4 rounded-full transition-transform duration-200 ease-in-out transform ${
                                toggledRows.has(row.id)
                                  ? "translate-x-[19px] border-blue-600"
                                  : "translate-x-[1px] border-gray-200"
                              }`}
                            />
                          </div>
                        </label>
                      </td>
                    )}
                    {columns.map((column) => (
                      <td
                        key={column.key}
                        className="px-6 py-4 text-[13px] font-normal h-16 text-center capitalize whitespace-nowrap"
                      >
                        {column.key === "attachment" &&
                        row[column.key] === "Null" ? (
                          <span className="text-gray-400">Null</span>
                        ) : column.render ? (
                          column.render(row)
                        ) : React.isValidElement(row[column.key]) ? (
                          row[column.key]
                        ) : typeof row[column.key] === "object" &&
                          row[column.key] !== null ? (
                          safelyDisplayObject(row[column.key])
                        ) : (
                          row[column.key]
                        )}
                      </td>
                    ))}
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      )}

      <div className="px-4 py-3 bg-white border-t border-gray-200 sm:px-6">
        <div className="flex flex-col justify-between gap-3 md:flex-row md:items-center">
          <div className="flex items-center text-sm text-gray-700">
            Showing {currentPageRows.length > 0 ? start + 1 : 0} to {end} of{" "}
            {apiPagination ? totalItems : filteredRows.length}
          </div>
          <div className="flex items-center justify-center space-x-1 overflow-x-auto md:justify-end">
            <button
              onClick={() => handlePageChange(Math.max(pageIndex - 1, 0))}
              disabled={pageIndex === 0}
              className={`relative inline-flex items-center px-2 py-2 rounded-full text-sm font-medium ${
                pageIndex === 0
                  ? "text-gray-300 cursor-not-allowed"
                  : "text-gray-500 hover:bg-gray-100"
              }`}
            >
              <span className="sr-only">Previous</span>
              <svg
                className="w-5 h-5"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
            {Array.from(
              {
                length: Math.min(
                  Math.max(1, totalPages),
                  isSmallScreen ? 3 : 5
                ),
              },
              (_, i) => {
                let pageNum: number;
                const visiblePages = isSmallScreen ? 3 : 5;

                if (totalPages <= visiblePages) {
                  pageNum = i;
                } else if (pageIndex < Math.floor(visiblePages / 2)) {
                  pageNum = i;
                } else if (
                  pageIndex >
                  totalPages - Math.ceil(visiblePages / 2)
                ) {
                  pageNum = totalPages - visiblePages + i;
                } else {
                  pageNum = pageIndex - Math.floor(visiblePages / 2) + i;
                }

                return (
                  <button
                    key={pageNum}
                    onClick={() => handlePageChange(pageNum)}
                    className={`relative inline-flex border border-[#F1F1F1] items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                      pageIndex === pageNum
                        ? "bg-blue-500 text-white bg-[#4A6DAD]"
                        : "text-gray-500 hover:bg-gray-100"
                    }`}
                  >
                    {pageNum + 1}
                  </button>
                );
              }
            )}
            <button
              onClick={() =>
                handlePageChange(Math.min(pageIndex + 1, totalPages - 1))
              }
              disabled={pageIndex === totalPages - 1 || totalPages === 0}
              className={`relative inline-flex items-center px-2 py-2 rounded-full text-sm font-medium ${
                pageIndex === totalPages - 1 || totalPages === 0
                  ? "text-gray-300 cursor-not-allowed"
                  : "text-gray-500 hover:bg-gray-100"
              }`}
            >
              <span className="sr-only">Next</span>
              <svg
                className="w-5 h-5"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {showDeleteConfirm && (
        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div
              className="fixed inset-0 transition-opacity"
              aria-hidden="true"
            >
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>
            <span
              className="hidden sm:inline-block sm:align-middle sm:h-screen"
              aria-hidden="true"
            ></span>
            <div className="inline-block overflow-hidden text-left align-bottom transition-all transform bg-white rounded-lg shadow-xl sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="px-4 pt-5 pb-4 bg-white sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="flex items-center justify-center flex-shrink-0 w-12 h-12 mx-auto bg-red-100 rounded-full sm:mx-0 sm:h-10 sm:w-10">
                    <svg
                      className="w-6 h-6 text-red-600"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                      />
                    </svg>
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 className="text-lg font-medium leading-6 text-gray-900">
                      Delete selected items
                    </h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        Are you sure you want to delete the selected items? This
                        action cannot be undone.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="px-4 py-3 bg-gray-50 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="inline-flex justify-center w-full px-4 py-2 text-base font-medium text-white bg-red-600 border border-transparent rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={handleDelete}
                >
                  Delete
                </button>
                <button
                  type="button"
                  className="inline-flex justify-center w-full px-4 py-2 mt-3 text-base font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={() => setShowDeleteConfirm(false)}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MasterTable;
