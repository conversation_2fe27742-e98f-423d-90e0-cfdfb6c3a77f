"use client";

import React, { useState, useEffect } from "react";
import { Icon } from "@iconify/react";

type FilterField = {
  id: string;
  type: "date" | "select" | "text" | "number";
  label: string;
  placeholder?: string;
  options?: { value: string; label: string }[];
  defaultValue?: any;
};

type FilterValues = {
  [key: string]: any;
};

type GlobalFilterProps = {
  fields: FilterField[];
  onApply: (values: FilterValues) => void;
  onReset?: () => void;
};

const GlobalFilter: React.FC<GlobalFilterProps> = ({
  fields,
  onApply,
  onReset,
}) => {
  const [filterValues, setFilterValues] = useState<FilterValues>({});

  useEffect(() => {
    const initialValues: FilterValues = {};
    fields.forEach((field) => {
      initialValues[field.id] = field.defaultValue || "";
    });
    setFilterValues(initialValues);
  }, [fields]);

  const handleChange = (id: string, value: any) => {
    setFilterValues((prev) => ({
      ...prev,
      [id]: value,
    }));
  };

  const handleApply = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onApply(filterValues);
  };

  const handleReset = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    const initialValues: FilterValues = {};
    fields.forEach((field) => {
      initialValues[field.id] = field.defaultValue || "";
    });
    setFilterValues(initialValues);
    if (onReset) onReset();
  };

  const renderField = (field: FilterField) => {
    switch (field.type) {
      case "date":
        return (
          <div className="relative">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Icon icon="mdi:calendar" className="text-gray-400 text-lg" />
            </div>
            <input
              type="date"
              id={field.id}
              className="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full h-[40px] pl-10 pr-4"
              placeholder={field.placeholder || field.label}
              value={filterValues[field.id] || ""}
              onChange={(e) => handleChange(field.id, e.target.value)}
            />
          </div>
        );
      case "select":
        return (
          <div className="relative">
            <select
              id={field.id}
              className="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full h-[40px] pr-10"
              value={filterValues[field.id] || ""}
              onChange={(e) => handleChange(field.id, e.target.value)}
            >
              <option value="">
                {field.placeholder || "Select an option"}
              </option>
              {field.options?.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <Icon icon="mdi:chevron-down" className="text-gray-400" />
            </div>
          </div>
        );
      case "text":
      case "number":
        return (
          <input
            type={field.type}
            id={field.id}
            className="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full h-[40px] px-4"
            placeholder={field.placeholder || field.label}
            value={filterValues[field.id] || ""}
            onChange={(e) => handleChange(field.id, e.target.value)}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="p-2 rounded-lg border border-gray-200 bg-white mb-4">
      <div className="flex flex-wrap items-end gap-4">
        {fields.map((field) => (
          <div key={field.id} className="min-w-[200px] flex-1">
            {renderField(field)}
          </div>
        ))}
        <div className="flex gap-2 ml-auto mt-2 sm:mt-0">
          <button
            type="button"
            onClick={handleApply}
            className="px-4 py-2 h-[40px] text-sm border border-blue-600 font-medium text-white bg-[#202c4d] rounded-lg hover:border-blue-700 focus:ring-4 focus:ring-blue-300"
          >
            Apply
          </button>
          <button
            type="button"
            onClick={handleReset}
            className="px-4 py-2 h-[40px] text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-gray-200"
          >
            Reset Filters
          </button>
        </div>
      </div>
    </div>
  );
};

export default GlobalFilter;
