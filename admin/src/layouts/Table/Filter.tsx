import React, { useRef, useState } from "react";

// Define interfaces for props
interface FilterOption {
  value: string;
  label: string;
}

interface FilterProps {
  label: string;
  type?: "select" | "date";
  options?: FilterOption[];
  selectedValue: string | null;
  onFilterChange: (value: string | null) => void;
}
import "./FilterStyle.css";

// Iconify Icon Component (simplified for inline SVG usage)
const CalendarIcon = ({ onClick }: { onClick: () => void }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    className="filter-icon"
    onClick={onClick}
    role="button"
    aria-label="Open date picker"
    tabIndex={0}
    onKeyDown={(e) => e.key === "Enter" && onClick()}
  >
    <path d="M19 4h-1V2h-2v2H8V2H6v2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V10h14v10zM5 8V6h14v2H5zm2 4h4v4H7v-4z" />
  </svg>
);

const Filter: React.FC<FilterProps> = ({
  label,
  type = "select",
  options = [],
  selectedValue,
  onFilterChange,
}) => {
  const dateInputRef = useRef<HTMLInputElement>(null);
  // const [isFocused, setIsFocused] = useState(false);

  const handleIconClick = () => {
    if (dateInputRef.current) {
      dateInputRef.current.focus();
      dateInputRef.current.showPicker?.();
    }
  };

  // const handleFocus = () => setIsFocused(true);
  // const handleBlur = () => setIsFocused(false);

  return (
    <div className="filter-container">
      {type === "select" ? (
        <div className="filter-wrapper select-wrapper">
          <select
            id="filter"
            value={selectedValue || ""}
            onChange={(e) =>
              onFilterChange(e.target.value === "" ? null : e.target.value)
            }
            // onFocus={handleFocus}
            // onBlur={handleBlur}
            className="filter-input-select"
          >
            <option value="">{`All ${label}`}</option>
            {options.map((option) => (
              <option key={option?.value} value={option.value}>
                {option?.label}
              </option>
            ))}
          </select>
        </div>
      ) : (
        <div className="filter-wrapper">
          <CalendarIcon onClick={handleIconClick} />
          <div className="filter-input-wrapper">
            <input
              type="date"
              ref={dateInputRef}
              value={selectedValue || ""}
              onChange={(e) =>
                onFilterChange(e.target.value === "" ? null : e.target.value)
              }
              // onFocus={handleFocus}
              // onBlur={handleBlur}
              className={`filter-input ${selectedValue ? "has-value" : ""}`}
            />
            <span className="filter-placeholder">{label}</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default Filter;
