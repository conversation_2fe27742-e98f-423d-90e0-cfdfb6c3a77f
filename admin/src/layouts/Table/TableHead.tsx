import { ITableHeader } from "../../Interface/global.interface";

const TableHead = ({ columns, loading }: ITableHeader) => {
  return (
    <thead>
      {loading ? (
        <div className="w-full h-4 bg-gray-300   rounded-md animate-pulse"></div>
      ) : (
        <tr className="rounded-sm bg-[#EBFEF4]  border border-[#EDECEC] ">
          {columns?.map((item) => (
            <th
              key={item?.key}
              className={`w-auto text-formText font-medium text-[14px]   text-[#2B2B2B] p-3 text-center text-nowrap`}
            >
              <div className="flex bg-[#EBFEF4]   justify-center cursor-pointer ">
                <span className="uppercase  font-medium">{item?.title}</span>
              </div>
            </th>
          ))}
        </tr>
      )}
    </thead>
  );
};

export default TableHead;
