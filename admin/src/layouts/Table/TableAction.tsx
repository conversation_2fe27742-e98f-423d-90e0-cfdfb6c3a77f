import { useState } from "react";
import { DeleteDialog } from "../../components";

interface IProps {
  onShow?: () => void;
  onTransfer?: () => void;
  onReturn?: () => void;
  onAssign?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onThreeDotMenu?: () => void;
  onPrint?: () => void;
  onoption?: () => void;
  onEditModal?: () => void;
  onAccept?: () => void;
  onReject?: () => void;
  onSwitch?: (value: boolean) => void;
  switchStatus?: boolean | string;
  onApiSwitch?: () => void;
  onPaidSwitch?: () => void;
  onPay?: () => void;

  payDisabled?: boolean;
}

export function TableAction({
  switchStatus,
  onShow,
  onTransfer,
  onReturn,
  onAssign,
  onEdit,
  onDelete,
  onSwitch,
  onPrint,
  onEditModal,
  onoption,
  onApiSwitch,
  onPaidSwitch,
  onPay,
  onAccept,
  onReject,
  payDisabled,
}: IProps) {
  const [isActive, setIsActive] = useState(false);
  const [showPopup, setShowPopup] = useState(false);
  const handleToggle = () => {
    if (onSwitch) {
      // Pass the toggled value based on switchStatus
      onSwitch(!(switchStatus === "active" || switchStatus === true));
    }
  };
  const deleteFunction = () => {
    setShowPopup(true);
  };

  return (
    <div className="flex items-center justify-center w-full gap-3">
      {onSwitch && (
        <div className="cursor-pointer" onClick={handleToggle}>
          <div
            className={`w-11 h-[1.6rem] flex items-center rounded-full p-1 transition-all duration-300 ${
              isActive || switchStatus === "active" || switchStatus === true
                ? "bg-[#2A3A6D]"
                : "bg-gray-400"
            }`}
          >
            <div
              className={`bg-white w-4 h-4 rounded-full shadow-md transform transition-transform duration-300 ${
                isActive || switchStatus === "active" || switchStatus === true
                  ? "translate-x-5"
                  : "translate-x-0"
              }`}
            />
          </div>
        </div>
      )}
      {onApiSwitch && (
        <div className="cursor-pointer" onClick={onApiSwitch}>
          <div
            className={`w-11 h-5 flex items-center bg-${
              !!switchStatus ? "secondary" : "gray-400"
            } rounded-full p-1 cursor-pointer transition-all duration-300`}
          >
            <div
              className={`bg-white w-4 h-4 rounded-full shadow-md transform ${
                !!switchStatus ? "translate-x-5" : "translate-x-0"
              } transition-transform duration-300`}
            />
          </div>
        </div>
      )}
      {onPaidSwitch && (
        <div className="cursor-pointer " onClick={onPaidSwitch}>
          <div
            className={`w-11 h-5 flex items-center bg-${
              !!switchStatus ? "green" : "gray-400"
            } rounded-full p-1 cursor-pointer transition-all duration-300`}
          >
            <div
              className={`bg-white w-4 h-4 rounded-full shadow-md transform ${
                !!switchStatus ? "translate-x-5" : "translate-x-0"
              } transition-transform duration-300`}
            />
          </div>
        </div>
      )}
      {onShow && (
        <div className="cursor-pointer " onClick={onShow}>
          <div className="  flex justify-center items-center  h-[1.6rem] p-1 w-[1.6rem] border-[#3498DB] border-1 bg-[#3498DB]  rounded-md">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width={17}
              height={17}
              viewBox="0 0 24 24"
            >
              <g
                fill="none"
                stroke="white"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                color="white"
              >
                <path d="M21.544 11.045c.304.426.456.64.456.955c0 .316-.152.529-.456.955C20.178 14.871 16.689 19 12 19c-4.69 0-8.178-4.13-9.544-6.045C2.152 12.529 2 12.315 2 12c0-.316.152-.529.456-.955C3.822 9.129 7.311 5 12 5c4.69 0 8.178 4.13 9.544 6.045"></path>
                <path d="M15 12a3 3 0 1 0-6 0a3 3 0 0 0 6 0"></path>
              </g>
            </svg>
          </div>
        </div>
      )}
      {onTransfer && (
        <div className="cursor-pointer " onClick={onTransfer}>
          <div>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="26"
              height="24"
              viewBox="0 0 26 24"
              fill="none"
            >
              <rect
                x="0.855469"
                y="0.62793"
                width="24.2903"
                height="22.7443"
                rx="6.18425"
                fill="#3498DB"
              />
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M18.9949 13.1732C19.0354 13.2651 19.0483 13.3668 19.032 13.4659C19.0157 13.565 18.971 13.6573 18.9031 13.7314L15.1081 17.8714C15.0622 17.9215 15.0068 17.962 14.9452 17.9908C14.8836 18.0195 14.8169 18.0358 14.749 18.0387C14.6811 18.0417 14.6133 18.0312 14.5494 18.0079C14.4855 17.9847 14.4268 17.949 14.3767 17.9031C14.3266 17.8572 14.2861 17.8018 14.2573 17.7402C14.2286 17.6786 14.2123 17.6119 14.2094 17.544C14.2064 17.4761 14.2169 17.4082 14.2402 17.3444C14.2634 17.2805 14.2991 17.2218 14.345 17.1717L17.3451 13.899L7.48156 13.899C7.34431 13.899 7.21268 13.8445 7.11563 13.7475C7.01858 13.6504 6.96406 13.5188 6.96406 13.3815C6.96406 13.2443 7.01858 13.1127 7.11563 13.0156C7.21268 12.9186 7.34431 12.864 7.48156 12.864L18.5216 12.864C18.6221 12.864 18.7204 12.8932 18.8046 12.9481C18.8888 13.0029 18.9544 13.0812 18.9949 13.1732ZM19.0391 10.6215C19.0391 10.7588 18.9845 10.8904 18.8875 10.9875C18.7904 11.0845 18.6588 11.139 18.5216 11.139L7.48156 11.139C7.38109 11.1391 7.28278 11.1099 7.19862 11.055C7.11447 11.0001 7.04811 10.9219 7.00765 10.8299C6.96719 10.738 6.95438 10.6362 6.97078 10.5371C6.98717 10.4379 7.03207 10.3457 7.09999 10.2717L10.895 6.1317C10.9878 6.0305 11.117 5.97031 11.2541 5.96435C11.3913 5.9584 11.5252 6.00718 11.6264 6.09996C11.7276 6.19274 11.7878 6.32192 11.7937 6.45909C11.7997 6.59625 11.7509 6.73016 11.6581 6.83136L8.65801 10.104L18.5216 10.104C18.6588 10.104 18.7904 10.1586 18.8875 10.2556C18.9845 10.3527 19.0391 10.4843 19.0391 10.6215Z"
                fill="#FCFCFC"
              />
            </svg>
          </div>
        </div>
      )}

      {switchStatus === "pending" && onAccept && (
        <div className="cursor-pointer" onClick={onAccept}>
          <div className="flex items-center justify-center rounded-md">
            <svg
              width="25"
              height="25"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect
                x="0.25"
                y="0.25"
                width="23"
                height="23"
                rx="3.75"
                stroke="black"
                strokeWidth="0.5"
              />
              <path d="M6 12L10.8 16L17.2 8" stroke="black" strokeWidth="1.6" />
            </svg>
          </div>
        </div>
      )}
      {switchStatus === "pending" && onReject && (
        <div className="cursor-pointer" onClick={onReject}>
          <div className="flex items-center justify-center rounded-md">
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect
                x="0.25"
                y="0.714844"
                width="23.5"
                height="22.57"
                rx="3.75"
                stroke="black"
                strokeWidth="0.5"
              />
              <path
                d="M8 15.5348L15.072 8.46484M8 8.46484L15.072 15.5348"
                stroke="black"
                strokeWidth="1.5"
                strokeLinecap="round"
              />
            </svg>
          </div>
        </div>
      )}
      {switchStatus === "rejected" && onAccept && (
        <div className="cursor-pointer" onClick={onAccept}>
          <div className="flex items-center justify-center rounded-md">
            <svg
              width="25"
              height="25"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect
                x="0.25"
                y="0.25"
                width="23"
                height="23"
                rx="3.75"
                stroke="black"
                strokeWidth="0.5"
              />
              <path d="M6 12L10.8 16L17.2 8" stroke="black" strokeWidth="1.6" />
            </svg>
          </div>
        </div>
      )}
      {switchStatus === "accepted" && onReject && (
        <div className="cursor-pointer" onClick={onReject}>
          <div className="flex items-center justify-center rounded-md">
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect
                x="0.25"
                y="0.714844"
                width="23.5"
                height="22.57"
                rx="3.75"
                stroke="black"
                strokeWidth="0.5"
              />
              <path
                d="M8 15.5348L15.072 8.46484M8 8.46484L15.072 15.5348"
                stroke="black"
                strokeWidth="1.5"
                strokeLinecap="round"
              />
            </svg>
          </div>
        </div>
      )}

      {onReturn && (
        <div className="cursor-pointer " onClick={onReturn}>
          <div>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="25"
              height="24"
              viewBox="0 0 25 24"
              fill="none"
            >
              <rect
                x="0.015625"
                y="0.349121"
                width="24.2903"
                height="22.7443"
                rx="6.18425"
                fill="#3498DB"
              />
              <path
                d="M8.36812 6.20264L5.95312 8.27264L8.36812 10.6876"
                stroke="#FCFCFC"
                strokeWidth="1.38"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M5.95312 8.27197H13.8861C16.2607 8.27197 18.2772 10.2109 18.3697 12.5845C18.4677 15.0926 16.3952 17.242 13.8861 17.242H8.02244"
                stroke="#FCFCFC"
                strokeWidth="1.38"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
        </div>
      )}
      {onAssign && (
        <div className="cursor-pointer " onClick={onAssign}>
          <div>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="25"
              height="24"
              viewBox="0 0 25 24"
              fill="none"
            >
              <rect
                x="0.304688"
                y="0.349121"
                width="24.2903"
                height="22.7443"
                rx="6.18425"
                fill="#3498DB"
              />
              <g clipPath="url(#clip0_2845_38029)">
                <path
                  d="M12.4451 11.3394C14.1752 11.3394 15.5777 9.93691 15.5777 8.20682C15.5777 6.47673 14.1752 5.07422 12.4451 5.07422C10.715 5.07422 9.3125 6.47673 9.3125 8.20682C9.3125 9.93691 10.715 11.3394 12.4451 11.3394Z"
                  fill="#FCFCFC"
                />
                <path
                  d="M9.6914 15.6996C9.70096 15.445 9.78576 15.1991 9.93509 14.9928C10.0844 14.7864 10.2916 14.629 10.5303 14.5404C10.7691 14.4518 11.0288 14.4359 11.2766 14.4949C11.5244 14.5538 11.7491 14.6849 11.9224 14.8716L13.698 16.7806L16.8444 13.2708C15.5448 12.4044 14.0132 11.9521 12.4514 11.9736C11.368 11.9462 10.2918 12.1564 9.29844 12.5895C8.30505 13.0227 7.41859 13.6681 6.7014 14.4806C6.64073 14.5614 6.60839 14.6601 6.6094 14.7612V17.2406C6.60929 17.4799 6.70242 17.7098 6.86903 17.8815C7.03564 18.0533 7.26261 18.1534 7.5018 18.1606H11.444L10.0318 16.6426C9.9149 16.5161 9.82455 16.3675 9.76608 16.2055C9.70761 16.0436 9.68222 15.8715 9.6914 15.6996Z"
                  fill="#FCFCFC"
                />
                <path
                  d="M17.4049 18.1613C17.6441 18.1541 17.8711 18.0541 18.0377 17.8823C18.2043 17.7105 18.2974 17.4806 18.2973 17.2413V15.5117L15.9375 18.1613H17.4049Z"
                  fill="#FCFCFC"
                />
                <path
                  d="M19.7063 12.0064C19.6612 11.9656 19.6084 11.9342 19.551 11.9139C19.4936 11.8937 19.4328 11.885 19.372 11.8884C19.3113 11.8918 19.2518 11.9073 19.1971 11.9339C19.1423 11.9604 19.0934 11.9976 19.0531 12.0432L13.7079 18.0232L11.3159 15.4518C11.2767 15.4059 11.2287 15.3682 11.1749 15.3408C11.121 15.3134 11.0623 15.2969 11.0021 15.2922C10.9419 15.2875 10.8813 15.2947 10.8239 15.3134C10.7664 15.3321 10.7132 15.3619 10.6673 15.4012C10.6242 15.444 10.59 15.4948 10.5666 15.5509C10.5433 15.6069 10.5312 15.6671 10.5312 15.7278C10.5312 15.7885 10.5433 15.8486 10.5666 15.9047C10.59 15.9608 10.6242 16.0116 10.6673 16.0544L13.7401 19.3664L19.7385 12.6412C19.8159 12.5516 19.8557 12.4356 19.8497 12.3173C19.8437 12.1991 19.7923 12.0877 19.7063 12.0064Z"
                  fill="#FCFCFC"
                />
              </g>
              <defs>
                <clipPath id="clip0_2845_38029">
                  <rect
                    width="16.56"
                    height="16.56"
                    fill="white"
                    transform="translate(4.17188 3.44141)"
                  />
                </clipPath>
              </defs>
            </svg>
          </div>
        </div>
      )}
      {onEdit && (
        <div className="cursor-pointer " onClick={onEdit}>
          <div className="  flex justify-center items-center  h-[1.6rem] p-1 w-[1.6rem] border-[#6C757D] border-1 bg-[#6C757D] rounded-md">
            <svg
              width={15}
              height={15}
              viewBox="0 0 18 18"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M8.16699 2.3332H2.33366C1.89163 2.3332 1.46771 2.50879 1.15515 2.82135C0.842587 3.13391 0.666992 3.55784 0.666992 3.99986V15.6665C0.666992 16.1086 0.842587 16.5325 1.15515 16.845C1.46771 17.1576 1.89163 17.3332 2.33366 17.3332H14.0003C14.4424 17.3332 14.8663 17.1576 15.1788 16.845C15.4914 16.5325 15.667 16.1086 15.667 15.6665V9.8332M14.417 1.0832C14.7485 0.751676 15.1982 0.56543 15.667 0.56543C16.1358 0.56543 16.5855 0.751676 16.917 1.0832C17.2485 1.41472 17.4348 1.86436 17.4348 2.3332C17.4348 2.80204 17.2485 3.25168 16.917 3.5832L9.00033 11.4999L5.66699 12.3332L6.50033 8.99986L14.417 1.0832Z"
                stroke="white"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
        </div>
      )}
      {onEditModal && (
        <div className="cursor-pointer " onClick={onEditModal}>
          <div className="  flex justify-center items-center  h-[1.6rem] p-1 w-[1.6rem] border-[#6C757D] border-1 bg-[#6C757D] rounded-full">
            <svg
              width={15}
              height={15}
              viewBox="0 0 18 18"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M8.16699 2.3332H2.33366C1.89163 2.3332 1.46771 2.50879 1.15515 2.82135C0.842587 3.13391 0.666992 3.55784 0.666992 3.99986V15.6665C0.666992 16.1086 0.842587 16.5325 1.15515 16.845C1.46771 17.1576 1.89163 17.3332 2.33366 17.3332H14.0003C14.4424 17.3332 14.8663 17.1576 15.1788 16.845C15.4914 16.5325 15.667 16.1086 15.667 15.6665V9.8332M14.417 1.0832C14.7485 0.751676 15.1982 0.56543 15.667 0.56543C16.1358 0.56543 16.5855 0.751676 16.917 1.0832C17.2485 1.41472 17.4348 1.86436 17.4348 2.3332C17.4348 2.80204 17.2485 3.25168 16.917 3.5832L9.00033 11.4999L5.66699 12.3332L6.50033 8.99986L14.417 1.0832Z"
                stroke="white"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
        </div>
      )}
      {onDelete && (
        <div className="cursor-pointer " onClick={deleteFunction}>
          <div className="  flex justify-center items-center  h-[1.6rem] p-1 w-[1.6rem] border-[#FF474C] border-1 bg-[#FF474C] rounded-md">
            <svg
              width={15}
              height={15}
              viewBox="0 0 16 18"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0.5 4.00033H2.16667M2.16667 4.00033H15.5M2.16667 4.00033V15.667C2.16667 16.109 2.34226 16.5329 2.65482 16.8455C2.96738 17.1581 3.39131 17.3337 3.83333 17.3337H12.1667C12.6087 17.3337 13.0326 17.1581 13.3452 16.8455C13.6577 16.5329 13.8333 16.109 13.8333 15.667V4.00033M4.66667 4.00033V2.33366C4.66667 1.89163 4.84226 1.46771 5.15482 1.15515C5.46738 0.842587 5.89131 0.666992 6.33333 0.666992H9.66667C10.1087 0.666992 10.5326 0.842587 10.8452 1.15515C11.1577 1.46771 11.3333 1.89163 11.3333 2.33366V4.00033M6.33333 8.16699V13.167M9.66667 8.16699V13.167"
                stroke="white"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
        </div>
      )}
      {onoption && (
        <div className="" onClick={onoption}>
          <svg
            width="7"
            height="12"
            viewBox="0 0 2 12"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M0.999373 2.44162C1.14839 2.44162 1.29131 2.38242 1.39668 2.27705C1.50205 2.17168 1.56125 2.02876 1.56125 1.87974C1.56125 1.73073 1.50205 1.58781 1.39668 1.48244C1.29131 1.37707 1.14839 1.31787 0.999373 1.31787C0.850355 1.31787 0.70744 1.37707 0.602069 1.48244C0.496697 1.58781 0.4375 1.73073 0.4375 1.87974C0.4375 2.02876 0.496697 2.17168 0.602069 2.27705C0.70744 2.38242 0.850355 2.44162 0.999373 2.44162ZM0.999373 6.56202C1.14839 6.56202 1.29131 6.50282 1.39668 6.39745C1.50205 6.29208 1.56125 6.14916 1.56125 6.00015C1.56125 5.85113 1.50205 5.70821 1.39668 5.60284C1.29131 5.49747 1.14839 5.43827 0.999373 5.43827C0.850355 5.43827 0.70744 5.49747 0.602069 5.60284C0.496697 5.70821 0.4375 5.85113 0.4375 6.00015C0.4375 6.14916 0.496697 6.29208 0.602069 6.39745C0.70744 6.50282 0.850355 6.56202 0.999373 6.56202ZM0.999373 10.6824C1.14839 10.6824 1.29131 10.6232 1.39668 10.5179C1.50205 10.4125 1.56125 10.2696 1.56125 10.1205C1.56125 9.97153 1.50205 9.82861 1.39668 9.72324C1.29131 9.61787 1.14839 9.55867 0.999373 9.55867C0.850355 9.55867 0.70744 9.61787 0.602069 9.72324C0.496697 9.82861 0.4375 9.97153 0.4375 10.1205C0.4375 10.2696 0.496697 10.4125 0.602069 10.5179C0.70744 10.6232 0.850355 10.6824 0.999373 10.6824Z"
              stroke="black"
              strokeWidth="0.877926"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
      )}
      {onPrint && (
        <div className="cursor-pointer " onClick={onPrint}>
          <div className=" p-1 border-[#2c2c2c] border-1 bg-[#D9D9D9] rounded-md">
            <svg
              width="18"
              height="18"
              viewBox="0 0 18 18"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M4.00033 6.50033V0.666992H14.0003V6.50033M4.00033 14.0003H2.33366C1.89163 14.0003 1.46771 13.8247 1.15515 13.5122C0.842587 13.1996 0.666992 12.7757 0.666992 12.3337V8.16699C0.666992 7.72497 0.842587 7.30104 1.15515 6.98848C1.46771 6.67592 1.89163 6.50033 2.33366 6.50033H15.667C16.109 6.50033 16.5329 6.67592 16.8455 6.98848C17.1581 7.30104 17.3337 7.72497 17.3337 8.16699V12.3337C17.3337 12.7757 17.1581 13.1996 16.8455 13.5122C16.5329 13.8247 16.109 14.0003 15.667 14.0003H14.0003M4.00033 10.667H14.0003V17.3337H4.00033V10.667Z"
                stroke="#1E1E1E"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
        </div>
      )}
      {onPay && (
        <div
          className={payDisabled ? "cursor-not-allowed" : "cursor-pointer"}
          onClick={!payDisabled ? onPay : undefined}
        >
          <div
            className={`flex justify-center items-center px-4 py-2 border-[1.6px] rounded-md text-xs font-medium transition
        ${
          payDisabled
            ? "bg-gray-100 border-gray-300 text-gray-400"
            : "border-[#163381] text-[#2B2B2B] bg-white hover:bg-[#163381] hover:text-white"
        }`}
          >
            Pay
          </div>
        </div>
      )}
      <DeleteDialog
        confirmAction={showPopup}
        onConfirm={() => {
          onDelete?.();
          setShowPopup(false);
        }}
        onClose={() => setShowPopup(false)}
        title="Are you sure?"
      />
    </div>
  );
}
