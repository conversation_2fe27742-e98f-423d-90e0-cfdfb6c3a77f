.filter-container {
  display: inline-flex;
  align-items: center;
  margin-right: 10px;
}

.filter-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  width: 150px;
  height: 35px;
  background-color: #fff;
  border: 1px solid #d1d5db;
  border-radius: 8px;
}

.filter-icon {
  width: 16px;
  height: 16px;
  fill: #6b7280;
  margin-left: 8px;
  cursor: pointer;
}

.filter-input-wrapper {
  position: relative;
  flex: 1;
  height: 100%;
}

.filter-input {
  width: 100%;
  height: 100%;
  padding: 0 0px 0 4px;
  border: none;
  background: transparent;
  font-size: 14px;
  color: #000;
  outline: none;
  appearance: none;
  opacity: 0;
  position: relative;
  z-index: 1;
}

.filter-input:focus,
.filter-input.has-value {
  opacity: 1;
}

.filter-input-select {
  width: 100%;
  height: 100%;
  padding: 0 24px 0 8px;
  border: none;
  background: transparent;
  font-size: 14px;
  color: #374151;
  outline: none;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  cursor: pointer;
  line-height: 36px;
}

.filter-input-select:focus {
  color: #374151;
}

.filter-input-select:not(:focus) {
  color: #374151;
}

/* Apply the dropdown arrow only to select wrappers */
.filter-wrapper.select-wrapper::after {
  content: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" width="16" height="16"><path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/></svg>');
  position: absolute;
  right: 8px;
  pointer-events: none;
  color: #6b7280;
}

.filter-input-select option {
  color: #374151;
  background-color: #fff;
  font-size: 14px;
}

.filter-input-select option[selected] {
  color: #374151;
}

.filter-placeholder {
  position: absolute;
  top: 50%;
  left: 4px;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 14px;
  pointer-events: none;
  z-index: 0;
}

.filter-input:focus + .filter-placeholder,
.filter-input.has-value + .filter-placeholder {
  display: none;
}

.filter-input::-webkit-calendar-picker-indicator {
  opacity: 0;
  position: absolute;
  right: 8px;
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.filter-input::-moz-calendar-picker-indicator {
  opacity: 0;
}

.filter-input::-ms-clear {
  display: none;
}
