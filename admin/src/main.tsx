import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { BrowserRouter, Route, Routes } from "react-router-dom";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import App from "./App.tsx";
import "./index.css";
import { ProtectedLayout, routesConfig } from "./routes";
import { ReactQueryProvider } from "./server-action/API/providers/QuerryClientProviders.tsx";
import { ProtectedLoginPage } from "./routes/ProtectedLoginpage.tsx";
import { IRoutesConfig } from "./Interface/global.interface.ts";
import "./toast.css";
import { ModalProvider } from "./context/ModelContext.tsx";

const renderRoutes = (routes: IRoutesConfig[]) =>
  routes.map(({ path, element, children }) => (
    <Route key={path} path={path} element={element}>
      {children && renderRoutes(children)}
    </Route>
  ));

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <ReactQueryProvider>
      <ModalProvider>
        {" "}
        {/* Wrap the app with ModalProvider */}
        <ReactQueryDevtools initialIsOpen={false} />
        <BrowserRouter>
          <Routes>
            {/* Protected Routes */}
            <Route element={<ProtectedLayout />}>
              <Route path="/" element={<App />}>
                {renderRoutes(routesConfig)}
              </Route>
            </Route>

            {/* Public Routes */}
            <Route path="/auth-login" element={<ProtectedLoginPage />} />
          </Routes>
        </BrowserRouter>
        {/* ToastContainer placed at the end to ensure it renders last */}
        <ToastContainer
          position="top-right"
          autoClose={3000}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick
          rtl={false}
          draggable
          pauseOnHover={false}
          toastClassName="font-semibold"
          theme="colored"
        />
      </ModalProvider>
    </ReactQueryProvider>
  </StrictMode>
);
