import React, { createContext, useContext, useState, useEffect } from "react";

interface ModalContextType {
  modalCount: number;
  openModal: () => void;
  closeModal: () => void;
}

const ModalContext = createContext<ModalContextType>({
  modalCount: 0,
  openModal: () => {},
  closeModal: () => {},
});

export const ModalProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [modalCount, setModalCount] = useState(0);

  useEffect(() => {
    if (modalCount > 0) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = "";
    };
  }, [modalCount]);

  const openModal = () => {
    setModalCount((prev) => prev + 1);
  };

  const closeModal = () => {
    setModalCount((prev) => Math.max(0, prev - 1));
  };

  return (
    <ModalContext.Provider value={{ modalCount, openModal, closeModal }}>
      {children}
    </ModalContext.Provider>
  );
};

export const useModal = () => useContext(ModalContext);
