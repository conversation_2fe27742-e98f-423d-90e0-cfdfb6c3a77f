import { InputField } from "./Input-Field";

interface Field {
  label: string;
  name: string;
  type?: string;
  placeholder?: string;
}
interface FieldArrayComponentProps {
  name: string;
  values: any[];
  push: (obj: any) => void;
  remove: (index: number) => void;
  fields: Field[];
  title: string;
}
const FieldArrayComponent: React.FC<FieldArrayComponentProps> = ({
  name,
  values,
  push,
  remove,
  fields,
  title
}) => {
  return (
    <div>
      {values.map((_, index) => (
        <div key={index} className="mb-6">
          <div className="grid grid-cols-2 gap-5">
            {fields.map((field, fieldIndex) => (
              <InputField
                key={fieldIndex}
                label={field.label}
                name={`${name}.${index}.${field.name}`}
                type={field.type || "text"}
                placeholder={field.placeholder || ""}
              />
            ))}
          </div>
          <div className="flex justify-center gap-4 mt-4">
            {values.length > 1 && (
              <button
                type="button"
                onClick={() => remove(index)}
                className="flex items-center px-4 py-2 transition-colors bg-gray-200 rounded-md hover:bg-gray-300"
              >
                <span className="mr-2">−</span> Remove
              </button>
            )}
            {index === values.length - 1 && (
              <button
                type="button"
                onClick={() => push(Object.fromEntries(fields.map((f) => [f.name, ""])))}
                className="flex items-center px-4 py-2 text-white transition-colors rounded-md bg-[red]"
              >
                <span className="mr-2">+</span> Add {title}
              </button>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};
export default FieldArrayComponent;