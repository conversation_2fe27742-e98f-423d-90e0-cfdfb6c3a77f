import { useRef } from "react";
import { useReactToPrint } from "react-to-print";
interface PrintingWrapperProps {
  children: React.ReactNode;
}
const PrintingWrapper: React.FC<PrintingWrapperProps> = ({ children }) => {
  const contentRef = useRef(null);
  const reactToPrintFn = useReactToPrint({ contentRef });
  return (
    <div ref={contentRef} className="relative">
      <span className="absolute top-2 right-2">
        <button
          onClick={() => reactToPrintFn()}
          className="p-4 bg-gray-200 rounded-md"
        >
          print
        </button>
      </span>
      {children}
    </div>
  );
};

export default PrintingWrapper;
