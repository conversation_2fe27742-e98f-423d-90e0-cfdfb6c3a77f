export function TimeFormater(timeOrDate: string): string {
  // Handle range format like "22:08 - 08:37"
  if (timeOrDate.includes(" - ")) {
    const [start, end] = timeOrDate?.split(" - ").map((part) => part?.trim());
    return `${convertTo12Hour(start)} - ${convertTo12Hour(end)}`;
  }

  // Handle single time or ISO date string
  return convertTo12Hour(timeOrDate);
}

function convertTo12Hour(input: string): string {
  let hours: number, minutes: number;

  // If it's an ISO date string
  if (input?.includes("T")) {
    const date = new Date(input);
    hours = date.getHours();
    minutes = date.getMinutes();
  } else if (/^\d{1,2}:\d{2}$/.test(input)) {
    // If it's a plain "HH:mm" string
    const [h, m] = input.split(":").map(Number);
    hours = h;
    minutes = m;
  } else {
    return "Invalid time";
  }

  const ampm = hours >= 12 ? "PM" : "AM";
  const hour12 = hours % 12 || 12;
  const minPadded = String(minutes).padStart(2, "0");

  return `${hour12}:${minPadded} ${ampm}`;
}
