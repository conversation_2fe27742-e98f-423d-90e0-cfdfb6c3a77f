import { Icon } from "@iconify/react/dist/iconify.js";
import { PopupModal } from "./Modal/Popup-Modal";
import { useOutsideClick } from "../hooks";

export const DeleteDialog = ({
  confirmAction,
  title,
  des,
  onClose,
  onConfirm,
}: {
  confirmAction?: boolean;
  button?: boolean;
  title?: string;
  des?: string;
  onClose?: () => void;
  onConfirm?: () => void;
}) => {
  const modalRef = useOutsideClick(() => {
    onClose?.();
  });

  const handleClose = () => {
    onClose?.();
  };

  return (
    <>
      {confirmAction && (
        <PopupModal onClose={handleClose} classname="w-[28%]" ref={modalRef}>
          <div className="w-full h-full flex flex-col gap-6 p-6 relative">
            <div className="flex justify-center">
              <svg
                width="100"
                height="50"
                viewBox="0 0 24 22"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect
                  x="0.738281"
                  y="0.177734"
                  width="23.1909"
                  height="21.6449"
                  rx="6.18425"
                  fill="#FF474C"
                />
                <path
                  d="M17.8068 7.13574H6.85547M16.7329 8.74622L16.4366 13.1912C16.3225 14.9008 16.2658 15.7557 15.7086 16.2768C15.1514 16.798 14.294 16.7986 12.5804 16.7986H12.0818C10.3683 16.7986 9.51083 16.7986 8.95361 16.2768C8.39638 15.7557 8.33905 14.9008 8.22567 13.1912L7.92934 8.74622"
                  stroke="#FCFCFC"
                  strokeWidth="1.15955"
                  strokeLinecap="round"
                />
                <path
                  d="M8.78906 7.1357H8.85992C9.11918 7.12908 9.3704 7.04437 9.58073 6.89266C9.79106 6.74094 9.95071 6.52929 10.0388 6.28537L10.0607 6.21902L10.1232 6.03156C10.1767 5.87115 10.2037 5.79127 10.2391 5.72299C10.3088 5.58925 10.4089 5.47368 10.5313 5.38553C10.6536 5.29739 10.795 5.23911 10.9439 5.21536C11.0193 5.20313 11.1036 5.20312 11.2724 5.20312H13.3918C13.5606 5.20312 13.645 5.20313 13.7204 5.21536C13.8693 5.23911 14.0106 5.29739 14.133 5.38553C14.2554 5.47368 14.3554 5.58925 14.4251 5.72299C14.4605 5.79127 14.4876 5.87115 14.5411 6.03156L14.6035 6.21902C14.6852 6.4904 14.854 6.72729 15.0839 6.89301C15.3138 7.05873 15.5919 7.14403 15.8752 7.1357"
                  stroke="#FCFCFC"
                  strokeWidth="1.15955"
                />
              </svg>

              <Icon
                icon="mdi:close"
                width="18"
                height="18"
                className="absolute cursor-pointer right-4 top-2"
                onClick={handleClose}
              />
            </div>

            <div className="flex flex-col items-center">
              <h1 className="text-2xl font-semibold text-black/70">
                {title ?? "Delete"}
              </h1>
              <h2 className="text-center text-[#64748B]">
                {des ??
                  "Are you sure you want to Delete? This action cannot be undone"}
              </h2>
            </div>
            <div className="flex gap-4 justify-center items-center">
              <button
                className="bg-[#B0B0B0] w-32 py-2.5 px-2 rounded flex justify-center text-white font-semibold gap-2"
                onClick={handleClose}
              >
                Cancel
              </button>
              <button
                className="bg-[#FF4D4D] w-32 py-2.5 px-2 rounded flex justify-center items-center text-white font-semibold gap-2"
                onClick={onConfirm}
              >
                Confirm
                <Icon icon="mdi:tick" width="22" height="22" />
              </button>
            </div>
          </div>
        </PopupModal>
      )}
    </>
  );
};
