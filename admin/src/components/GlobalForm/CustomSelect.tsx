import React, { useState, useRef, useEffect, useMemo } from "react";
import ReactDOM from "react-dom";

interface SelectOption {
  label: string;
  value: string;
}

interface CustomSelectProps {
  id?: string;
  label?: string;
  value: string;
  options: SelectOption[];
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  isForm?: boolean;
}

export default function CustomSelect({
  id,
  label,
  value,
  options,
  onChange,
  placeholder,
  className = "",
  disabled = false,
  isForm = true,
}: CustomSelectProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [showSearch, setShowSearch] = useState(false);
  const selectRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const pendingKeyRef = useRef<string | null>(null);

  const selectedOption = options.find((option) => option.value === value);

  // Close dropdown on outside click
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        selectRef.current &&
        !selectRef.current.contains(event.target as Node) &&
        (!dropdownRef.current ||
          !dropdownRef.current.contains(event.target as Node))
      ) {
        setIsOpen(false);
        setShowSearch(false);
        setSearchTerm("");
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Focus input and insert first key if available
  useEffect(() => {
    if (showSearch && searchInputRef.current) {
      searchInputRef.current.focus();
      if (pendingKeyRef.current) {
        setSearchTerm(pendingKeyRef.current);
        pendingKeyRef.current = null;
      }
    }
  }, [showSearch]);

  // Debounce searchTerm updates
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);
    return () => clearTimeout(handler);
  }, [searchTerm]);

  // Filter + sort options
  const filteredOptions = useMemo(() => {
    if (!debouncedSearchTerm) return options;
    return [...options].sort((a, b) => {
      const aMatch = a.label
        .toLowerCase()
        .includes(debouncedSearchTerm.toLowerCase())
        ? 0
        : 1;
      const bMatch = b.label
        .toLowerCase()
        .includes(debouncedSearchTerm.toLowerCase())
        ? 0
        : 1;
      return aMatch - bMatch || a.label.localeCompare(b.label);
    });
  }, [options, debouncedSearchTerm]);

  const handleSelect = (optionValue: string) => {
    if (!disabled) {
      onChange(optionValue);
      setIsOpen(false);
      setShowSearch(false);
      setSearchTerm("");
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (!showSearch && e.key.length === 1) {
      pendingKeyRef.current = e.key;
      setShowSearch(true);
    }
  };

  const renderDropdown = () => {
    if (!isOpen || disabled) return null;

    const rect = selectRef.current?.getBoundingClientRect();
    if (!rect) return null;

    return ReactDOM.createPortal(
      <div
        ref={dropdownRef}
        className="absolute bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-y-auto"
        style={{
          top: rect.bottom + window.scrollY + 2,
          left: rect.left + window.scrollX,
          width: rect.width,
          zIndex: 1000,
        }}
      >
        {showSearch && (
          <div className="p-2">
            <input
              ref={searchInputRef}
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search..."
              className="w-full p-2 border border-gray-300 focus:outline-none rounded-md text-sm"
            />
          </div>
        )}

        {filteredOptions.length === 0 ? (
          <div className="px-3 py-2 text-sm text-gray-500">
            No options available
          </div>
        ) : (
          filteredOptions.map((option) => (
            <div
              key={option.value}
              className={`px-3 py-2 text-sm cursor-pointer hover:bg-[#F1F6FD] ${
                option.value === value ? "bg-[#F1F6FD] font-medium" : ""
              }`}
              onClick={() => handleSelect(option.value)}
            >
              {option.label}
            </div>
          ))
        )}
      </div>,
      document.body
    );
  };

  return (
    <div
      id={id}
      className={`relative ${className}`}
      ref={selectRef}
      onKeyDown={handleKeyDown}
      tabIndex={0}
    >
      {label && <label className="block text-sm mb-1">{label}</label>}

      <div
        className={`flex items-center justify-between w-full px-3 ${
          isForm ? "py-3" : "py-2"
        } border border-gray-300 rounded-md bg-white ${
          disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer"
        }`}
        onClick={() => !disabled && setIsOpen(!isOpen)}
      >
        <span
          className={`text-base ${
            !selectedOption ? "text-gray-800" : "text-gray-900"
          }`}
        >
          {selectedOption
            ? selectedOption.label
            : placeholder || "Select an option"}
        </span>
        <svg
          className={`w-5 h-6 text-gray-400 transition-transform ${
            isOpen ? "transform rotate-180" : ""
          }`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </div>

      {renderDropdown()}
    </div>
  );
}
