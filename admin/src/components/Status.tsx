import { reject } from "lodash";

type StatusProps = {
  status: string | boolean;
};

export function Status({ status }: StatusProps) {
  console.log(status, "status");
  const normalizedStatus =
    typeof status === "boolean"
      ? status
        ? "active"
        : "inactive"
      : status?.toString()?.toLowerCase();

  const statusColors: Record<string, string> = {
    active: "border-[#28A745] bg-[#D4EDDA] text-[#155724] uppercase",
    inactive: "border-[#DC3545] bg-[#F8D7DA] text-[#721C24] uppercase",

    pending: "border-[#FFC107] bg-[#FFF3CD] text-[#856404] uppercase",
    accept: "border-[#28A745] bg-[#D4EDDA] text-[#155724] uppercase",
    accepted: "border-[#28A745] bg-[#D4EDDA] text-[#155724] uppercase",
    success: "border-[#28A745] bg-[#D4EDDA] text-[#155724] uppercase",

    completed: "border-[#28A745] bg-[#D4EDDA] text-[#155724] uppercase",
    rejected: "border-[#DC3545] bg-[#F8D7DA] text-[#721C24] uppercase",
    cancelled: "border-[#DC3545] bg-[#F8D7DA] text-[#721C24] uppercase",
    present: "border-[#28A745] bg-[#D4EDDA] text-[#155724] uppercase",
    absent: "border-[#F05D5F] bg-[#FFEBEE] text-[#B71C1C] uppercase",
    paid: "border-[#28A745] bg-[#D4EDDA] text-[#155724] uppercase",
    unpaid: "border-[#DC3545] bg-[#F8D7DA] text-[#721C24] uppercase",
    reported: "border-[#FFC107] bg-[#FFF3CD] text-[#856404] uppercase",
    claimed: "border-[#007BFF] bg-[#CCE5FF] text-[#004085] uppercase",
    discarded: "border-[#6C757D] bg-[#E2E3E5] text-[#383D41]",
    available: "border-[#28A745] bg-[#D4EDDA] text-[#155724] uppercase",
    occupied: "border-[#FF6B6B] bg-[#FFE8E8] text-[#A50000] uppercase",
    reserved: "border-[#FFC107] bg-[#FFF3CD] text-[#856404] uppercase",
    maintenance: "border-[#007BFF] bg-[#CCE5FF] text-[#004085] uppercase",
    cleaning: "border-[#6F42C1] bg-[#E2E3FF] text-[#1A1A8D] uppercase",
    moderate: "border-[#FFC107] bg-[#FFF3CD] text-[#856404] uppercase",
    high: "border-[#DC3545] bg-[#F8D7DA] text-[#721C24] uppercase",
    low: "border-[#28A745] bg-[#D4EDDA] text-[#155724] uppercase",
    admitted: "border-[#20C997] bg-[#D2F4EA] text-[#0F6848] uppercase",
  };

  const statusClass =
    statusColors[normalizedStatus] ||
    "border border-gray-300 bg-gray-100 text-gray-700 uppercase";

  return (
    <div
      className={`px-4 py-1.5 rounded-md text-center border font-medium ${statusClass}`}
    >
      <span className="capitalize">{normalizedStatus}</span>
    </div>
  );
}
