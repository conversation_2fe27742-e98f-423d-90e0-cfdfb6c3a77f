import { Icon } from "@iconify/react";
import { useAuth } from "../hooks";

interface ProfileData {
  DOB: string;
  applyIDS: boolean;
  annualSalary: number;
  basicSalary: number;
  citizenship: string[];
  deduction: any[];
  department: null;
  designation: null;
  documents: any[];
  email: string;
  festivalAllowanceMonth: string;
  gender: string;
  hotel: { id: string; name: string };
  isSuperAdmin: boolean;
  joinDate: string;
  license: string[];
  martialStatus: string;
  overTimePerHr: number;
  paidLeave: number;
  password: string;
  permanentAddress: { district: string; municipality: string };
  phoneNumber: string;
  photo: string[];
  resetPasswordToken: string;
  resetPasswordTokenExpireAt: string;
  resume: string[];
  role: string;
  shiftType: null;
  staffStatus: string;
  temporaryAddress: { district: string; municipality: string };
}

export default function UserProfile() {
  const { data, isLoading } = useAuth();
  const user = data?.user;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  const profileData: ProfileData = {
    DOB: user?.DOB || "N/A",
    applyIDS: user?.applyIDS || false,
    annualSalary: user?.annualSalary || "N/A",
    basicSalary: user?.basicSalary || "N/A",
    citizenship: user?.citizenship || "N/A",
    deduction: user?.deduction || [],
    department: user?.department || null,
    designation: user?.designation || null,
    documents: user?.documents || [],
    email: user?.email || "N/A",
    festivalAllowanceMonth: user?.festivalAllowanceMonth || "N/A",
    gender: user?.gender || "N/A",
    hotel: user?.hotel || {
      id: user?.hotel?._id,
      name: user?.hotel?.name ?? "N/A",
    },
    isSuperAdmin: user?.isSuperAdmin || false,
    joinDate: user?.joinDate || "2023-10-01",
    license: user?.license || ["https://example.com/license1.jpg"],
    martialStatus: user?.martialStatus || "Not specified",
    overTimePerHr: user?.overTimePerHr || 500,
    paidLeave: user?.paidLeave || 20,
    password: user?.password || "********",
    permanentAddress: {
      district: user?.permanentAddress?.district || "Not specified",
      municipality: user?.permanentAddress?.municipality || "Not specified",
    },
    phoneNumber: user?.phoneNumber || "N/A",
    photo: user?.photo || ["N/A"],
    resetPasswordToken: user?.resetPasswordToken || "N/A",
    resetPasswordTokenExpireAt: user?.resetPasswordTokenExpireAt || "N/A",
    resume: user?.resume || ["N/A"],
    role: user?.role || "N/A",
    shiftType: user?.shiftType || null,
    staffStatus: user?.staffStatus || "Active",
    temporaryAddress: {
      district: user?.tempAddress?.district || "Not specified",
      municipality: user?.tempAddress?.municipality || "Not specified",
    },
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return "Not specified";
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    } catch (error) {
      return "Invalid date";
    }
  };

  return (
    <div className="bg-white rounded-lg px-3 shadow-md p-6  mx-auto">
      <div className="flex flex-col md:flex-row items-center md:items-start gap-6 border-b border-gray-200 pb-6 mb-6">
        <div className="relative">
          <div className="w-32 h-32 rounded-full overflow-hidden border-4 border-blue-100 shadow-md">
            <img
              src={
                profileData.photo[0] ||
                "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Nnx8dXNlcnxlbnwwfHwwfHx8MA%3D%3D"
              }
              alt="Profile"
              className="w-full h-full object-cover"
            />
          </div>
          <div className="absolute bottom-0 right-0 bg-blue-500 text-white p-1 rounded-full">
            <Icon icon="lucide:edit" className="w-4 h-4" />
          </div>
        </div>

        <div className="flex-1 text-center md:text-left">
          <div className="flex flex-col md:flex-row md:items-center gap-2 md:gap-4">
            <h1 className="text-2xl font-bold text-gray-800">
              {profileData.email.split("@")[0]}
            </h1>
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue text-white capitalize">
              {profileData.role}
            </span>
            {profileData.staffStatus && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green text-white">
                {profileData.staffStatus}
              </span>
            )}
          </div>

          <div className="mt-2 text-gray-600">
            <p className="flex items-center justify-center md:justify-start gap-1 mb-1">
              <Icon icon="lucide:mail" className="w-4 h-4" />
              {profileData.email}
            </p>
            <p className="flex items-center justify-center md:justify-start gap-1 mb-1">
              <Icon icon="lucide:phone" className="w-4 h-4" />
              {profileData.phoneNumber}
            </p>
            <p className="flex items-center justify-center md:justify-start gap-1">
              <Icon icon="lucide:calendar" className="w-4 h-4" />
              Joined on {formatDate(profileData.joinDate)}
            </p>
          </div>
        </div>

        <div className="flex gap-2">
          <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center gap-1 transition-colors">
            <Icon icon="lucide:edit-3" className="w-4 h-4" />
            Edit Profile
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Personal Information */}
        <div className="bg-gray-100 rounded-lg p-5">
          <div className="flex items-center gap-2 mb-4">
            <Icon icon="lucide:user" className="w-5 h-5 text-blue-500" />
            <h2 className="text-lg font-semibold text-gray-800">
              Personal Information
            </h2>
          </div>

          <div className="space-y-3">
            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Date of Birth</span>
              <span className="font-medium">{formatDate(profileData.DOB)}</span>
            </div>

            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Gender</span>
              <span className="font-medium">{profileData.gender}</span>
            </div>

            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Marital Status</span>
              <span className="font-medium">{profileData.martialStatus}</span>
            </div>

            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Permanent Address</span>
              <span className="font-medium">
                {profileData.permanentAddress?.municipality || "Not specified"},{" "}
                {profileData.permanentAddress?.district || "Not specified"}
              </span>
            </div>

            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Temporary Address</span>
              <span className="font-medium">
                {profileData.temporaryAddress?.municipality || "Not specified"},{" "}
                {profileData.temporaryAddress?.district || "Not specified"}
              </span>
            </div>
          </div>
        </div>

        {/* Employment Details */}
        <div className="bg-gray-100 rounded-lg p-5">
          <div className="flex items-center gap-2 mb-4">
            <Icon icon="lucide:briefcase" className="w-5 h-5 text-blue-500" />
            <h2 className="text-lg font-semibold text-gray-800">
              Employment Details
            </h2>
          </div>

          <div className="space-y-3">
            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Department</span>
              <span className="font-medium">
                {profileData.department || "Not assigned"}
              </span>
            </div>

            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Designation</span>
              <span className="font-medium">
                {profileData.designation || "Not assigned"}
              </span>
            </div>

            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Staff Status</span>
              <span className="font-medium">{profileData.staffStatus}</span>
            </div>

            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Hotel Assignment</span>
              <span className="font-medium">{profileData.hotel.name}</span>
            </div>
          </div>
        </div>

        {/* Salary & Benefits */}
        <div className="bg-gray-100 rounded-lg p-5">
          <div className="flex items-center gap-2 mb-4">
            <Icon icon="lucide:dollar-sign" className="w-5 h-5 text-blue-500" />
            <h2 className="text-lg font-semibold text-gray-800">
              Salary & Benefits
            </h2>
          </div>

          <div className="space-y-3">
            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Basic Salary</span>
              <span className="font-medium">
                Rs.{profileData.basicSalary.toLocaleString()}
              </span>
            </div>

            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Annual Salary</span>
              <span className="font-medium">
                Rs.{profileData.annualSalary.toLocaleString()}
              </span>
            </div>

            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Overtime Rate</span>
              <span className="font-medium">
                Rs.{profileData.overTimePerHr}/hour
              </span>
            </div>

            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Paid Leave</span>
              <span className="font-medium">{profileData.paidLeave} days</span>
            </div>
          </div>
        </div>

        <div className="bg-gray-100 rounded-lg p-5">
          <div className="flex items-center gap-2 mb-4">
            <Icon icon="lucide:file" className="w-5 h-5 text-blue-500" />
            <h2 className="text-lg font-semibold text-gray-800">
              Documents & IDs
            </h2>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Icon icon="lucide:id-card" className="w-5 h-5 text-gray-500" />
                <span className="font-medium">Citizenship</span>
              </div>
              <a
                href={profileData.citizenship[0]}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-500 hover:text-blue-700 flex items-center gap-1"
              >
                <Icon icon="lucide:eye" className="w-4 h-4" />
                View
              </a>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Icon
                  icon="lucide:credit-card"
                  className="w-5 h-5 text-gray-500"
                />
                <span className="font-medium">License</span>
              </div>
              <a
                href={profileData.license[0]}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-500 hover:text-blue-700 flex items-center gap-1"
              >
                <Icon icon="lucide:eye" className="w-4 h-4" />
                View
              </a>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Icon
                  icon="lucide:file-text"
                  className="w-5 h-5 text-gray-500"
                />
                <span className="font-medium">Resume</span>
              </div>
              <a
                href={profileData.resume[0]}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-500 hover:text-blue-700 flex items-center gap-1"
              >
                <Icon icon="lucide:download" className="w-4 h-4" />
                Download
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
