/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, ReactNode } from "react";

interface InventoryItem {
  item?: {
    _id?: string;
    name?: string;
    unit?: string;
  };
  cleanQuantity?: number;
  dirtyQuantity?: number;
  quantity?: number;
  [key: string]: any;
}

interface Store {
  _id?: string;
  id?: string;
  name?: string;
  type?: string;
  [key: string]: any;
}

interface StoreWithItems {
  store: Store;
  items: InventoryItem[];
  [key: string]: any;
}

interface TableColumn {
  key: string;
  title: string;
  render?: (item: any) => ReactNode;
}

interface GroupedInventoryProps {
  storeData: StoreWithItems[];
  isLoading: boolean;
  columns?: TableColumn[];
  renderCustomCell?: (column: TableColumn, item: any) => ReactNode;
}

const GroupedInventory = ({ 
  storeData, 
  isLoading, 
  columns = [
    { key: "item", title: "Item" },
    { key: "clean", title: "Clean" },
    { key: "dirty", title: "Dirty" },
    { key: "total", title: "Total" },
    { key: "unit", title: "Unit" },
  ],
  renderCustomCell
}: GroupedInventoryProps) => {
  const [expandedStores, setExpandedStores] = useState<string[]>([]);

  const toggleStore = (storeId: string) => {
    setExpandedStores((prev) =>
      prev.includes(storeId)
        ? prev.filter(id => id !== storeId)
        : [...prev, storeId]
    );
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-700"></div>
      </div>
    );
  }

  if (!storeData || storeData.length === 0) {
    return (
      <div className="text-center py-8 bg-gray-50 rounded-md">
        <p className="text-gray-500">No inventory items found</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-md shadow overflow-hidden">
      {storeData.map((store) => {
        const storeId = store.store?._id || store.store?.id || "";
        const storeName = store.store?.name || store.store?.type || "Unknown Store";
        const isExpanded = expandedStores.includes(storeId);
        const totalItems = store.items?.length || 0;
        const totalQuantity = store.items?.reduce((sum, item) => sum + (item.quantity || 0), 0) || 0;
        
        return (
          <div key={storeId} className="border-b last:border-b-0">
            {/* Store Header - Clickable */}
            <div 
              className="flex items-center justify-between px-4 py-3 bg-gray-50 cursor-pointer hover:bg-gray-100 transition-colors"
              onClick={() => toggleStore(storeId)}
            >
              <div className="flex items-center space-x-2">
                <span className="h-5 w-5 text-blue-600 inline-flex items-center justify-center">
                  {isExpanded ? '▼' : '▶'}
                </span>
                <h3 className="font-medium text-gray-800">{storeName}</h3>
              </div>
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <span>{totalItems} items</span>
                <span>{totalQuantity} total quantity</span>
              </div>
            </div>
            
            {/* Store Items - Collapsible */}
            {isExpanded && (
              <div className="bg-white">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-100">
                    <tr>
                      {columns.map((column) => (
                        <th 
                          key={column.key} 
                          scope="col" 
                          className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          {column.title}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {store.items && store.items.map((item, index) => (
                      <tr key={`${storeId}-item-${index}`} className="hover:bg-gray-50">
                        {columns.map((column) => (
                          <td key={`${storeId}-item-${index}-${column.key}`} className="px-4 py-3 whitespace-nowrap text-sm text-gray-600">
                            {renderCustomCell ? (
                              renderCustomCell(column, item)
                            ) : (
                              column.render ? (
                                column.render(item)
                              ) : (
                                column.key === "item" ? (
                                  <span className="text-gray-800">{item.item?.name || "Unknown Item"}</span>
                                ) : column.key === "clean" ? (
                                  item.cleanQuantity || 0
                                ) : column.key === "dirty" ? (
                                  item.dirtyQuantity || 0
                                ) : column.key === "total" ? (
                                  item.quantity || 0
                                ) : column.key === "unit" ? (
                                  item.item?.unit || "-"
                                ) : (
                                  (item as any)[column.key] || "-"
                                )
                              )
                            )}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default GroupedInventory;
