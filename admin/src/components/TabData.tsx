import React, { useEffect } from "react";

interface propTypes {
  tabData: {
    title: string;
    value: string;
  }[];
  selectedTabData: string;
  setSelectedTabData: React.Dispatch<React.SetStateAction<string>>;
}

export const TabData = ({
  tabData,
  selectedTabData,
  setSelectedTabData,
}: propTypes) => {
  // Log when props change
  useEffect(() => {
    console.log("TabData component received props:", {
      tabData,
      selectedTabData,
    });
  }, [tabData, selectedTabData]);

  // Handle tab click
  const handleTabClick = (tabValue: string, tabTitle: string) => {
    console.log("Tab clicked:", tabTitle, "Value:", tabValue);
    console.log("Current selectedTabData:", selectedTabData);

    // Only update if it's different
    if (tabValue !== selectedTabData) {
      console.log("Updating selectedTabData to:", tabValue);
      setSelectedTabData(tabValue);
    } else {
      console.log("Tab already selected, no update needed");
    }
  };

  // If no tabs, show a message
  if (!tabData || tabData.length === 0) {
    return (
      <div className="flex place-items-center gap-7 p-5 bg-white shadow-md rounded-lg">
        <p>No activities available</p>
      </div>
    );
  }

  return (
    <div className="flex place-items-center gap-7 p-5 bg-white border rounded-md">
      {tabData.map((tab) => (
        <button
          key={tab.value}
          onClick={() => handleTabClick(tab.value, tab.title)}
          className={`pb-1 font-medium text-xs ${
            selectedTabData === tab.value
              ? "border-b-[3px] border-b-[#163381] bg-[#163381] text-white px-2 py-1 rounded-t"
              : "text-[#000000]"
          }`}
        >
          {tab.title}
        </button>
      ))}
    </div>
  );
};
