import { useCallback, useState, useEffect } from "react";

interface FileUploadProps {
  multiple?: boolean;
  onChange?: (files: (File | string)[]) => void; // Allow both File and string (URL)
  defaultImage?: string[]; // Initial URLs from editData
}

const FileUpload: React.FC<FileUploadProps> = ({
  multiple = false,
  onChange,
  defaultImage = [],
}) => {
  const [files, setFiles] = useState<(File | string)[]>([]);
  const [previews, setPreviews] = useState<string[]>([]);

  // Initialize with defaultImage when component mounts or defaultImage changes
  useEffect(() => {
    if (defaultImage && defaultImage.length > 0) {
      setFiles(defaultImage);
      setPreviews(defaultImage);
    }
  }, [defaultImage]);

  const handleFileChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const selectedFiles = Array.from(e.target.files || []);

      // For new files, maintain any existing string URLs in the files array
      const updatedFiles = multiple
        ? [...files, ...selectedFiles]  // Keep ALL previous files, not just strings
        : selectedFiles;
      setFiles(updatedFiles);

      const newPreviews = selectedFiles.map((file) =>
        URL.createObjectURL(file)
      );

      setPreviews((prevPreviews) => {
        // Clean up any existing object URLs for newly uploaded files
        if (!multiple) {
          prevPreviews.forEach((preview) => {
            if (!defaultImage.includes(preview)) {
              URL.revokeObjectURL(preview);
            }
          });
          return multiple ? [...defaultImage, ...newPreviews] : newPreviews;
        }
        return [...prevPreviews, ...newPreviews];
      });

      onChange?.(updatedFiles);
    },
    [files, multiple, onChange, defaultImage]
  );

  const removeFile = useCallback(
    (index: number) => {
      setFiles((prev) => {
        const newFiles = [...prev];
        newFiles.splice(index, 1);
        onChange?.(newFiles);
        return newFiles;
      });

      setPreviews((prev) => {
        const newPreviews = [...prev];
        const previewUrl = newPreviews[index];

        // Only revoke object URL if it's not from defaultImage
        if (!defaultImage.includes(previewUrl)) {
          URL.revokeObjectURL(previewUrl);
        }

        newPreviews.splice(index, 1);
        return newPreviews;
      });
    },
    [onChange, defaultImage]
  );

  return (
    <div className="w-full max-w-md">
      <div className="mb-4">
        <label
          htmlFor="file-upload"
          className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100"
        >
          <div className="flex flex-col items-center justify-center pt-5 pb-6">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="lucide lucide-arrow-up-from-line"
            >
              <path d="m18 9-6-6-6 6" />
              <path d="M12 3v14" />
              <path d="M5 21h14" />
            </svg>
            <p className="mb-2 text-sm text-gray-500">
              <span className="font-semibold">Click to upload</span> or drag and
              drop
            </p>
            <p className="text-xs text-gray-500">
              {multiple ? "Upload multiple images" : "Upload single image"}
            </p>
          </div>
          <input
            id="file-upload"
            type="file"
            className="hidden"
            accept="image/*"
            multiple={multiple}
            onChange={handleFileChange}
          />
        </label>
      </div>
      {previews.length > 0 && (
        <div className="grid grid-cols-2 gap-4">
          {previews.map((preview, index) => (
            <div key={`${preview}-${index}`} className="relative group">
              <div className="relative aspect-square rounded-lg overflow-hidden border border-gray-200">
                <img
                  src={preview}
                  alt={`Preview ${index + 1}`}
                  className="w-full h-full object-cover"
                />
                <button
                  onClick={() => removeFile(index)}
                  className="absolute top-2 right-2 p-1 bg-red-500 rounded-full text-white opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    width="24"
                    height="24"
                    color="#000000"
                    fill="none"
                  >
                    <path
                      d="M18 6L12 12M12 12L6 18M12 12L18 18M12 12L6 6"
                      stroke="currentColor"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default FileUpload;