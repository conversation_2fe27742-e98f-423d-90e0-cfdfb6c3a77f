interface IActionButtonProps {
  onCancel: () => void;
  onSubmit: () => void;
  loading?: boolean;
  isSubmitting?: boolean;
  submitText?: string;
}

export const ActionButton: React.FC<IActionButtonProps> = ({
  onCancel,
  onSubmit,
  loading = false,
  isSubmitting = false,
  submitText = "Submit",
}) => {
  const isDisabled = loading || isSubmitting;

  const handleSubmitClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault(); // Prevent default form submission
    onSubmit(); // Call the custom submit handler
  };

  return (
    <div className="flex justify-end gap-4 mt-4">
      <button
        type="button"
        className="ml-2 bg-[#FF474C] hover:bg-[#FF474E] text-white font-bold py-2 px-4 rounded"
        onClick={onCancel}
      >
        Cancel
      </button>

      <button
        disabled={isDisabled}
        type="button" // Changed from "submit" to "button" to prevent automatic form submission
        className="bg-[#2A3A6D] text-white font-bold py-2 px-4 rounded disabled:opacity-70"
        onClick={handleSubmitClick}
      >
        {isDisabled ? "Submitting..." : submitText}
      </button>
    </div>
  );
};
