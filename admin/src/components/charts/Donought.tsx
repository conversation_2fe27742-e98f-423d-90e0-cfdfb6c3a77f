import { Chart as ChartJ<PERSON>, Arc<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "chart.js";
import { Doughnut } from "react-chartjs-2";

ChartJS.register(ArcElement, Tooltip, Legend);

interface chartDataTypes {
  title: string;
  status: string;
  value: number;
  color: string;
}

interface propTypes {
  chartData: chartDataTypes[];
}

export const DonoughtChart = ({ chartData }: propTypes) => {
  const data = {
    labels: chartData?.map((item) => item.title),
    datasets: [
      {
        labels: chartData?.map((item) => item.title),
        data: chartData?.map((item) => item.value), // Adjust these values based on your data
        backgroundColor: chartData?.map((item) => item.color), // Your theme colors
        borderWidth: 1,
      },
    ],
  };

  const options = {
    rotation: -90, // Rotate to start from top
    circumference: 180, // Make it a half-circle
    cutout: "60%", // Adjust the thickness of the doughnut
    plugins: {
      legend: {
        display: false, // Hide legend if needed
      },
    },
  };

  return <Doughnut data={data} options={options} />;
};
