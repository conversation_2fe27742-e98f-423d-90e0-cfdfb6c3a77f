import React, { useState } from "react";

interface ButtonProps {
  setItem: () => void;
}

const ToggleButton: React.FC<ButtonProps> = ({ setItem }) => {
  const [toggle, setToggle] = useState<boolean>(false);

  return (
    <div>
      <button
        type="button"
        className="relative rounded-full h-6 w-12 bg-slate-600 border-[1px] border-slate-800 overflow-hidden flex items-center px-[1px]"
        onClick={() => {
          setToggle(!toggle);
          setItem();
        }}
      >
        <div
          className={`absolute rounded-full h-5 w-5 bg-white transition-all ease-in-out duration-300 ${
            toggle ? "translate-x-[24px]" : "left-[1px]"
          } `}
        ></div>
      </button>
    </div>
  );
};

export default ToggleButton;
