import React, { useState, useRef, useEffect } from "react";
import { FormikProps } from "formik";
import { ChevronLeft, ChevronRight, Calendar } from "lucide-react";
import ReactDOM from "react-dom";

// Inline utility function for class names
function classNames(
  ...classes: (string | boolean | undefined | null)[]
): string {
  return classes.filter(Boolean).join(" ");
}

interface CustomDatePickerProps {
  label: string;
  required?: boolean;
  name: string;
  formik: FormikProps<any>;
  value?: string;
  disabled?: boolean;
  onChange?: (e: React.ChangeEvent<any>) => void;
  modalContainer?: HTMLElement; // Modal container to render the portal into
  placeholder?: string;
  [key: string]: any;
}

export const CustomDatePicker: React.FC<CustomDatePickerProps> = ({
  label,
  required = false,
  name,
  formik,
  value,
  disabled = false,
  onChange,
  modalContainer,
  placeholder = "YYYY-MM-DD",
  ...rest
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [currentMonth, setCurrentMonth] = useState<Date>(
    value ? new Date(value) : new Date()
  );
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [searchInput, setSearchInput] = useState<string>("");
  const [calendarPosition, setCalendarPosition] = useState({ top: 0, left: 0 });
  const containerRef = useRef<HTMLDivElement>(null);
  const calendarRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Get current value from formik or props
  const currentDateValue = value !== undefined ? value : formik.values[name];

  // Update currentMonth when value changes
  useEffect(() => {
    if (currentDateValue) {
      const parsedDate = new Date(currentDateValue);
      if (!isNaN(parsedDate.getTime())) {
        setCurrentMonth(parsedDate);
      }
    }
  }, [currentDateValue]);

  // Update calendar position relative to the select field
  const updateCalendarPosition = () => {
    if (!containerRef.current || !isOpen) return;

    const rect = containerRef.current.getBoundingClientRect();
    const modalRect = modalContainer?.getBoundingClientRect() || {
      top: 0,
      left: 0,
    };

    setCalendarPosition({
      top:
        rect.bottom -
        modalRect.top +
        (modalContainer?.scrollTop || window.scrollY) +
        2,
      left:
        rect.left -
        modalRect.left +
        (modalContainer?.scrollLeft || window.scrollX),
    });
  };

  // Update position on open, scroll, and resize
  useEffect(() => {
    if (isOpen) {
      updateCalendarPosition();

      const handleScroll = () => {
        updateCalendarPosition();
      };

      const target = modalContainer || window;
      target.addEventListener("scroll", handleScroll);
      window.addEventListener("resize", updateCalendarPosition);

      return () => {
        target.removeEventListener("scroll", handleScroll);
        window.removeEventListener("resize", updateCalendarPosition);
      };
    }
  }, [isOpen, modalContainer]);

  // Close the date picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node) &&
        (!calendarRef.current ||
          !calendarRef.current.contains(event.target as Node))
      ) {
        setIsOpen(false);
        setIsSearching(false);
        setSearchInput("");
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Focus search input when searching
  useEffect(() => {
    if (isSearching && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isSearching]);

  // Handle key down to activate search
  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (!isSearching && e.key.length === 1) {
      setIsOpen(true);
      setIsSearching(true);
      setSearchInput(e.key);
    }
  };

  // Format date to YYYY-MM-DD string
  const formatDateToString = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  };

  // Format date for display
  const formatDisplayDate = (dateString: string): string => {
    if (!dateString) return "";
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return "";
    return formatDateToString(date);
  };

  // Get days in month
  const daysInMonth = (year: number, month: number): number =>
    new Date(year, month + 1, 0).getDate();

  // Get day of week for first day of month
  const firstDayOfMonth = (year: number, month: number): number =>
    new Date(year, month, 1).getDay();

  // Handle date selection
  const handleDateClick = (day: number) => {
    if (disabled) return;
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    const selectedDate = new Date(year, month, day);
    const formattedDate = formatDateToString(selectedDate);

    formik.setFieldValue(name, formattedDate);
    if (onChange) {
      const syntheticEvent = {
        target: { name, value: formattedDate },
      } as React.ChangeEvent<HTMLInputElement>;
      onChange(syntheticEvent);
    }

    setIsOpen(false);
    setIsSearching(false);
    setSearchInput("");
  };

  // Handle today selection
  const handleTodaySelect = () => {
    if (disabled) return;
    const today = new Date();
    const formattedDate = formatDateToString(today);

    formik.setFieldValue(name, formattedDate);
    setCurrentMonth(today);
    if (onChange) {
      const syntheticEvent = {
        target: { name, value: formattedDate },
      } as React.ChangeEvent<HTMLInputElement>;
      onChange(syntheticEvent);
    }

    setIsOpen(false);
    setIsSearching(false);
    setSearchInput("");
  };

  // Handle clear
  const handleClear = () => {
    if (disabled) return;
    formik.setFieldValue(name, "");
    if (onChange) {
      const syntheticEvent = {
        target: { name, value: "" },
      } as React.ChangeEvent<HTMLInputElement>;
      onChange(syntheticEvent);
    }

    setIsOpen(false);
    setIsSearching(false);
    setSearchInput("");
  };

  // Navigate to previous month
  const previousMonth = () => {
    setCurrentMonth(
      new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1)
    );
  };

  // Navigate to next month
  const nextMonth = () => {
    setCurrentMonth(
      new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1)
    );
  };

  // Handle search input change
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInput(e.target.value);
  };

  // Handle search submission
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (disabled) return;

    const parsedDate = new Date(searchInput);
    if (!isNaN(parsedDate.getTime())) {
      setCurrentMonth(parsedDate);
      formik.setFieldValue(name, formatDateToString(parsedDate));
      if (onChange) {
        const syntheticEvent = {
          target: { name, value: formatDateToString(parsedDate) },
        } as React.ChangeEvent<HTMLInputElement>;
        onChange(syntheticEvent);
      }
    }

    setIsSearching(false);
    setSearchInput("");
    setIsOpen(false);
  };

  // Render calendar days
  const renderCalendar = () => {
    const days = [];
    const totalDays = daysInMonth(
      currentMonth.getFullYear(),
      currentMonth.getMonth()
    );
    const firstDay = firstDayOfMonth(
      currentMonth.getFullYear(),
      currentMonth.getMonth()
    );

    const selectedDate = currentDateValue ? new Date(currentDateValue) : null;
    const selectedDay = selectedDate ? selectedDate.getDate() : null;
    const selectedMonth = selectedDate ? selectedDate.getMonth() : null;
    const selectedYear = selectedDate ? selectedDate.getFullYear() : null;

    const isSelectedDate = (day: number): boolean => {
      return (
        selectedDay === day &&
        selectedMonth === currentMonth.getMonth() &&
        selectedYear === currentMonth.getFullYear()
      );
    };

    const isToday = (day: number): boolean => {
      const today = new Date();
      return (
        day === today.getDate() &&
        currentMonth.getMonth() === today.getMonth() &&
        currentMonth.getFullYear() === today.getFullYear()
      );
    };

    // Previous month days
    for (let i = 0; i < firstDay; i++) {
      const prevMonthDays = daysInMonth(
        currentMonth.getFullYear(),
        currentMonth.getMonth() - 1
      );
      days.push(
        <div
          key={`prev-${i}`}
          className="text-gray-400 p-2 text-center text-xs"
        >
          {prevMonthDays - firstDay + i + 1}
        </div>
      );
    }

    // Current month days
    for (let day = 1; day <= totalDays; day++) {
      days.push(
        <button
          key={`current-${day}`}
          className={classNames(
            "h-7 w-7 flex items-center justify-center rounded-full text-xs",
            isSelectedDate(day) ? "bg-[#2A3A6D] text-white" : "",
            !isSelectedDate(day) && isToday(day)
              ? "border border-[#2A3A6D]"
              : "",
            !isSelectedDate(day) && !isToday(day) ? "hover:bg-gray-100" : "",
            disabled ? "cursor-not-allowed" : ""
          )}
          onClick={() => handleDateClick(day)}
          type="button"
          disabled={disabled}
        >
          {day}
        </button>
      );
    }

    // Next month days
    const totalCells = 42;
    const remainingCells = totalCells - days.length;
    for (let i = 1; i <= remainingCells; i++) {
      days.push(
        <div
          key={`next-${i}`}
          className="text-gray-400 p-2 text-center text-xs"
        >
          {i}
        </div>
      );
    }

    return days;
  };

  // Render calendar dropdown
  const renderCalendarDropdown = () => {
    if (!isOpen || disabled) return null;

    const monthYearString = currentMonth.toLocaleDateString("en-US", {
      month: "long",
      year: "numeric",
    });

    return ReactDOM.createPortal(
      <div
        ref={calendarRef}
        className="absolute w-60 bg-white rounded-md shadow-lg border border-gray-200"
        style={{
          top: calendarPosition.top,
          left: calendarPosition.left,
          zIndex: 1000,
        }}
      >
        {/* Calendar Header */}
        <div className="flex items-center justify-between p-2 border-b">
          <button
            onClick={previousMonth}
            className="p-1 hover:bg-gray-100 rounded-full"
            type="button"
            disabled={disabled}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </button>

          {isSearching ? (
            <form onSubmit={handleSearchSubmit} className="flex-1 mx-2">
              <input
                ref={searchInputRef}
                type="text"
                value={searchInput}
                onChange={handleSearchInputChange}
                placeholder="YYYY-MM-DD"
                className="w-full text-xs border focus:border-none text-center focus:outline-none rounded px-2 py-1"
              />
            </form>
          ) : (
            <button
              onClick={() => setIsSearching(true)}
              className="font-normal text-[13px] hover:bg-gray-100 px-2 py-1 rounded"
              type="button"
            >
              {monthYearString}
            </button>
          )}

          <button
            onClick={nextMonth}
            className="p-1 hover:bg-gray-100 rounded-full"
            type="button"
            disabled={disabled}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </button>
        </div>

        {/* Days of Week */}
        <div className="grid grid-cols-7 text-center py-2 px-1 text-xs text-gray-500">
          {["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"].map((day) => (
            <div
              key={day}
              className="text-center font-medium text-xs text-gray-500"
            >
              {day}
            </div>
          ))}
        </div>

        {/* Calendar Days */}
        <div className="grid grid-cols-7 gap-1 p-2">{renderCalendar()}</div>

        {/* Footer with Today and Clear buttons */}
        <div className="flex justify-between p-1 border-t">
          <button
            onClick={handleTodaySelect}
            className={classNames(
              "px-3 py-1 text-xs rounded-md",
              disabled
                ? "opacity-50 cursor-not-allowed"
                : "hover:bg-gray-100 text-[#2A3A6D]"
            )}
            type="button"
            disabled={disabled}
          >
            Today
          </button>
          <button
            onClick={handleClear}
            className={classNames(
              "px-3 py-1 text-xs rounded-md",
              disabled
                ? "opacity-50 cursor-not-allowed"
                : "hover:bg-gray-100 text-[#2A3A6D]"
            )}
            type="button"
            disabled={disabled}
          >
            Clear
          </button>
        </div>
      </div>,
      modalContainer || document.body
    );
  };

  return (
    <div
      className="flex flex-col w-full max-w-md"
      ref={containerRef}
      onKeyDown={handleKeyDown}
      tabIndex={0}
    >
      <label htmlFor={name} className="mb-1 text-sm">
        {label} {required && <span className="text-red">*</span>}
      </label>

      <div className="relative">
        <div
          className={classNames(
            "flex items-center w-full border rounded-md bg-white p-3",
            disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer",
            formik.touched[name] && formik.errors[name]
              ? "border-red-500"
              : "border-gray-200"
          )}
          onClick={() => !disabled && setIsOpen(!isOpen)}
        >
          <Calendar className="h-4 w-4 text-gray-600 mr-2" />
          <span
            className={classNames(
              "flex-grow",
              !currentDateValue && "text-gray-600"
            )}
          >
            {currentDateValue
              ? formatDisplayDate(currentDateValue)
              : placeholder}
          </span>
          <input
            type="hidden"
            name={name}
            value={currentDateValue}
            onChange={(e) => {
              formik.handleChange(e);
              if (onChange) onChange(e);
            }}
          />
        </div>

        {renderCalendarDropdown()}
      </div>

      {formik.touched[name] && formik.errors[name] && (
        <div className="mt-1 text-xs text-red capitalize">{`${formik.errors[name]}`}</div>
      )}
    </div>
  );
};
