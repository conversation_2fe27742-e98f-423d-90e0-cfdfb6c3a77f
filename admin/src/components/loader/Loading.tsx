import React from "react";

type LoadingProps = {
  size?: "xs" | "sm" | "md" | "lg" | "xl";
  color?: string;
  className?: string;
  value: string;
};

const sizeClasses = {
  xs: "w-4 h-4 border-2",
  sm: "w-6 h-6 border-2",
  md: "w-8 h-8 border-4",
  lg: "w-12 h-12 border-4",
  xl: "w-16 h-16 border-4",
};

const Loading: React.FC<LoadingProps> = ({
  size = "md",
  color = "border-gray-900",
  className = "",
  value,
}) => {
  return (
    <div className="flex flex-col items-center justify-center">
      <span
        className={`animate-spin rounded-full border-t-transparent border-solid ${sizeClasses[size]} ${color} ${className}`}
      />
      <p>{value ?? "Loading..."}</p>
    </div>
  );
};

export default Loading;
