import "./BoxLoader.css";
const BoxLoader = ({
  squareSize = 15,
  size,
  value,
}: {
  squareSize?: number;
  size?: "xs" | "sm" | "md" | "lg" | "xl";
  value?: string;
}) => {
  //squareSize is pixel value
  const sizeClasses = {
    xs: 10,
    sm: 15,
    md: 20,
    lg: 26,
    xl: 30,
  };
  return (
    <div className="flex flex-col items-center">
      <div
        className="loadingspinner"
        style={
          {
            "--square": `${(squareSize = sizeClasses[size ?? "md"])}px`,
          } as React.CSSProperties
        }
      >
        <div id="square1"></div>
        <div id="square2"></div>
        <div id="square3"></div>
        <div id="square4"></div>
        <div id="square5"></div>
      </div>
      <p>{value ?? "Loading..."}</p>
    </div>
  );
};

export default BoxLoader;
