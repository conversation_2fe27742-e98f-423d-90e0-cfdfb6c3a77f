// components/PopupModal.tsx
import React, { useEffect } from "react";
import { createPortal } from "react-dom";
import { useModal } from "../../context/ModelContext";

interface ModalProps {
  children: React.ReactNode;
  classname?: string;
  onClose: () => void;
  modalContainer?: HTMLElement; // Add modalContainer prop
}

export const PopupModal = React.memo(
  React.forwardRef<HTMLDivElement, ModalProps>(
    ({ children, classname, onClose, modalContainer }, ref) => {
      const { openModal, closeModal } = useModal();

      useEffect(() => {
        openModal();
        return () => closeModal();
      }, [openModal, closeModal]);

      return createPortal(
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-50"
            onClick={onClose}
          ></div>

          {/* Modal Content */}
          <main
            ref={ref}
            className={`${classname} fixed top-[50%] left-[50%] z-[999] bg-white border outline-none rounded-lg`}
            style={{ transform: "translate(-50%, -50%)" }}
            onClick={(e) => e.stopPropagation()}
          >
            {children}
          </main>
        </>,
        modalContainer || document.body // Use modalContainer if provided, else document.body
      );
    }
  )
);

PopupModal.displayName = "PopupModal";
