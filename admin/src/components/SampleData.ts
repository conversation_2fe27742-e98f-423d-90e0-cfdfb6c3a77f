export const SampleTableData = [
  {
    tokenId: "Tk1",
    GuestName: "<PERSON>",
    date: "2025-02-14",
    Room: "101/Deluxe",
    checkin: "11/15/24/1200pm",
    checkout: "11/15/24/1200pm",
    status: "PENDING",
  },
  {
    tokenId: "Tk2",
    GuestName: "<PERSON>",
    date: "2025-02-15",
    Room: "101/Deluxe",
    checkin: "11/15/24/1200pm",
    checkout: "11/15/24/1200pm",
    status: "COMPLETED",
  },
  {
    tokenId: "Tk3",
    GuestName: "<PERSON>",
    date: "2025-02-16",
    Room: "101/Deluxe",
    checkin: "11/15/24/1200pm",
    checkout: "11/15/24/1200pm",
    status: "CANCELLED",
  },
  {
    tokenId: "Tk4",
    GuestName: "<PERSON>",
    date: "2025-02-17",
    Room: "101/Deluxe",
    checkin: "11/15/24/1200pm",
    checkout: "11/15/24/1200pm",
    status: "COMPLETED",
  },
  {
    tokenId: "Tk5",
    GuestName: "<PERSON>",
    date: "2025-02-18",
    Room: "101/Deluxe",
    checkin: "11/15/24/1200pm",
    checkout: "11/15/24/1200pm",
    status: "PENDING",
  },
  {
    tokenId: "Tk6",
    GuestName: "Emily Green",
    date: "2025-02-19",
    Room: "101/Deluxe",
    checkin: "11/15/24/1200pm",
    checkout: "11/15/24/1200pm",
    status: "COMPLETED",
  },
];

export const sampleExpenseDate = [
  {
    sn: 1,
    category: "Restaurant Supplies",
    description:
      "Costs for food ingredients, beverages, kitchen, utensils and tableware",
  },
  {
    sn: 2,
    category: "Office Supplies",
    description:
      "Expenses for paper, pens, computers, and other office-related materials",
  },
  {
    sn: 3,
    category: "Utilities",
    description:
      "Monthly costs for electricity, water, gas, and internet services",
  },
  {
    sn: 4,
    category: "Staff Salaries",
    description:
      "Monthly wages and salaries for employees including chefs, waiters, and support staff",
  },
  {
    sn: 5,
    category: "Marketing",
    description:
      "Expenses for online ads, flyers, and other marketing campaigns",
  },
  {
    sn: 6,
    category: "Repairs & Maintenance",
    description:
      "Costs for repairing equipment, furniture, and maintaining the restaurant",
  },
  {
    sn: 7,
    category: "Rent",
    description: "Monthly rent for the restaurant's physical space",
  },
];

export const expenseDummyData = [
  {
    sn: 1,
    date: "28/02/2025",
    title: "Food & Beverage",
    expense_category: "Restaurant Supplies",
    total_cost: "Rs.10,000",
    remarks: "Weekly Food & beverage",
  },
  {
    sn: 2,
    date: "01/03/2025",
    title: "Office Supplies",
    expense_category: "Stationery",
    total_cost: "Rs.2,500",
    remarks: "Purchase of new pens, paper, and folders",
  },
  {
    sn: 3,
    date: "05/03/2025",
    title: "Marketing",
    expense_category: "Advertisements",
    total_cost: "Rs.15,000",
    remarks: "Social media ad campaign",
  },
  {
    sn: 4,
    date: "07/03/2025",
    title: "Transportation",
    expense_category: "Fuel",
    total_cost: "Rs.4,000",
    remarks: "Fuel for company vehicles",
  },
  {
    sn: 5,
    date: "10/03/2025",
    title: "Salaries",
    expense_category: "Employee Payments",
    total_cost: "Rs.50,000",
    remarks: "Monthly salary payment to employees",
  },
  {
    sn: 6,
    date: "12/03/2025",
    title: "Travel",
    expense_category: "Business Trip",
    total_cost: "Rs.20,000",
    remarks: "Flight and accommodation for business trip",
  },
  {
    sn: 7,
    date: "15/03/2025",
    title: "Utilities",
    expense_category: "Electricity",
    total_cost: "Rs.3,500",
    remarks: "Monthly electricity bill",
  },
  {
    sn: 8,
    date: "18/03/2025",
    title: "Software Subscription",
    expense_category: "Software Licenses",
    total_cost: "Rs.8,000",
    remarks: "Annual subscription for design software",
  },
];
