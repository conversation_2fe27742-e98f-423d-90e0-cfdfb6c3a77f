import React, { JSX } from "react";
import Breadcrumbs from "./Breadcrumb";
import { Link } from "react-router-dom";

interface HeaderProps {
  onSearch?: (value: string) => void;
  onAddClick?: () => void;
  route?: string;
  title?: string;
  hideHeader?: boolean;
  showButton?: boolean;
  path?: boolean;
  FilterSection?: JSX.ElementType;
}

const Header: React.FC<HeaderProps> = ({
  // onSearch,
  onAddClick,
  title,
  route,
  path = true,
  showButton = true,
  // hideHeader = false,
  FilterSection,
}) => {
  return (
    <div className="px-1  mb-2 rounded-md ">
      {(path || title) && (
        <div className="flex items-center justify-between w-full py-2 gap-4">
          <div>
            <Breadcrumbs />
          </div>
          {/* {onAddClick && (
             <button
             className="bg-[#163381] flex items-center gap-3 text-[#FCFCFC] px-3 py-3 rounded-md transition"
             onClick={onAddClick}
           >
      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M4.16602 9.99984H15.8327M9.99935 4.1665V15.8332" stroke="#FCFCFC" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
</svg>
              Add {title}
            </button>
          )} */}
          {showButton && title !== "" && (
            <Link
              to={route || "#"}
              className="bg-[#2A3A6D] flex items-center gap-3 text-[#FCFCFC] text-sm px-4 py-2 rounded-md transition"
              onClick={onAddClick}
            >
              <svg
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M4.16602 9.99984H15.8327M9.99935 4.1665V15.8332"
                  stroke="#FCFCFC"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              Add {title}
            </Link>
          )}
        </div>
      )}
      {/* 
      {!hideHeader && (
        <div className="flex items-center justify-between w-full gap-4 p-4 bg-white rounded-lg shadow-sm">
          {onSearch && (
            <div className="relative flex items-center">
              <input
                type="text"
                placeholder="Search name id"
                onChange={(e) => onSearch && onSearch(e.target.value)}
                className="py-2 pl-10 pr-4 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          )}
          {FilterSection && <FilterSection />}
        </div>
      )} */}
    </div>
  );
};

export default Header;
