import React, { useState, useRef, useEffect, useMemo } from "react";
import { FormikProps } from "formik";
import ReactDOM from "react-dom";

interface Option {
  value: string;
  label: string;
}

interface CustomSelectProps {
  label: string;
  required?: boolean;
  name: string;
  placeholder?: string;
  options: Option[];
  formik: FormikProps<any>;
  value?: string;
  disabled?: boolean;
  onChange?: (e: React.ChangeEvent<any>) => void;
  className?: string;
  [key: string]: any;
}

export const CustomSelect: React.FC<CustomSelectProps> = ({
  label,
  required = false,
  name,
  placeholder = "Select an option",
  options = [],
  formik,
  value,
  disabled = false,
  onChange,
  className = "",
  ...rest
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [showSearch, setShowSearch] = useState(false);
  const selectRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const pendingKeyRef = useRef<string | null>(null);

  // Get current value from formik or props
  const currentValue = value !== undefined ? value : formik.values[name];

  // Close dropdown on outside click
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        selectRef.current &&
        !selectRef.current.contains(event.target as Node) &&
        (!dropdownRef.current ||
          !dropdownRef.current.contains(event.target as Node))
      ) {
        setIsOpen(false);
        setShowSearch(false);
        setSearchTerm("");
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Focus input and insert first key if available
  useEffect(() => {
    if (showSearch && searchInputRef.current) {
      searchInputRef.current.focus();
      if (pendingKeyRef.current) {
        setSearchTerm(pendingKeyRef.current);
        pendingKeyRef.current = null;
      }
    }
  }, [showSearch]);

  // Debounce searchTerm updates
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);
    return () => clearTimeout(handler);
  }, [searchTerm]);

  // Filter and sort options
  const filteredOptions = useMemo(() => {
    if (!debouncedSearchTerm) return options;
    return [...options].sort((a, b) => {
      const aMatch = a.label
        .toLowerCase()
        .includes(debouncedSearchTerm.toLowerCase())
        ? 0
        : 1;
      const bMatch = b.label
        .toLowerCase()
        .includes(debouncedSearchTerm.toLowerCase())
        ? 0
        : 1;
      return aMatch - bMatch || a.label.localeCompare(b.label);
    });
  }, [options, debouncedSearchTerm]);

  // Handle selection change
  const handleSelect = (optionValue: string) => {
    if (!disabled) {
      formik.setFieldValue(name, optionValue);
      if (onChange) {
        const syntheticEvent = {
          target: { name, value: optionValue },
        } as unknown as React.ChangeEvent<HTMLSelectElement>;
        onChange(syntheticEvent);
      }
      setIsOpen(false);
      setShowSearch(false);
      setSearchTerm("");
    }
  };

  // Handle key down for search
  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (!showSearch && e.key.length === 1) {
      pendingKeyRef.current = e.key;
      setShowSearch(true);
      setIsOpen(true);
    }
  };

  // Get display text for selected option
  const getDisplayValue = () => {
    const option = options.find((opt) => opt.value === currentValue);
    return option ? (
      <span className="text-gray-900">{option.label}</span>
    ) : (
      <span className="text-gray-800">{placeholder}</span>
    );
  };

  // Render dropdown using portal
  const renderDropdown = () => {
    if (!isOpen || disabled) return null;

    const rect = selectRef.current?.getBoundingClientRect();
    if (!rect) return null;

    return ReactDOM.createPortal(
      <div
        ref={dropdownRef}
        className="absolute bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-y-auto"
        style={{
          top: rect.bottom + window.scrollY + 2,
          left: rect.left + window.scrollX,
          width: rect.width,
          zIndex: 1000,
        }}
      >
        {showSearch && (
          <div className="p-2">
            <input
              ref={searchInputRef}
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search..."
              className="w-full p-2 border border-gray-300 focus:outline-none rounded-md text-sm"
            />
          </div>
        )}
        {filteredOptions.length === 0 ? (
          <div className="px-3 py-2 text-sm text-gray-500">
            No options available
          </div>
        ) : (
          filteredOptions.map((option) => (
            <div
              key={option.value}
              className={`px-3 py-2 text-sm cursor-pointer hover:bg-[#F1F6FD] ${
                option.value === currentValue ? "bg-[#F1F6FD] font-medium" : ""
              }`}
              onClick={() => handleSelect(option.value)}
            >
              {option.label}
            </div>
          ))
        )}
      </div>,
      document.body
    );
  };

  return (
    <div
      className={`relative ${className}`}
      ref={selectRef}
      onKeyDown={handleKeyDown}
      tabIndex={0}
    >
      {/* {label && (
        <label htmlFor={name} className="block text-sm mb-1">
          {label} {required && <span className="text-red-600">*</span>}
        </label>
      )} */}

      <div
        className={`flex items-center justify-between w-full px-3 py-3 border border-gray-300 rounded-md bg-white ${
          disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer"
        } ${
          formik.touched[name] && formik.errors[name] ? "border-red-500" : ""
        }`}
        onClick={() => !disabled && setIsOpen(!isOpen)}
      >
        {getDisplayValue()}
        <svg
          className={`w-5 h-6 text-gray-400 transition-transform ${
            isOpen ? "transform rotate-180" : ""
          }`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </div>

      <select
        name={name}
        value={currentValue || ""}
        onChange={() => {}}
        className="hidden"
        disabled={disabled}
        {...rest}
      >
        <option value="">{placeholder}</option>
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>

      {renderDropdown()}
      {/* 
      {formik.touched[name] && formik.errors[name] && (
        <div className="mt-1 text-xs text-red-600">{`${formik.errors[name]}`}</div>
      )} */}
    </div>
  );
};
