import { Icon } from "@iconify/react/dist/iconify.js";
import { Link, useLocation } from "react-router-dom";
import { Text } from "./Text";

interface _sidebarPropTypes {
  toggleExpand?: (id: number) => void;
  SidebarRoutes: {
    title?: string;
    routes: {
      path?: string;
      id: number;
      icon?: React.ComponentType<{ className: string }>;
      title: string;
      children?: {
        path: string;
        id: number;
        title: string;
      }[];
    }[];
  };
  miniSidebar: boolean;
  expandedItems: number[];
  setMiniSidebar: React.Dispatch<React.SetStateAction<boolean>>;
  isCompact: boolean;
}

export const SidebarProps = ({
  SidebarRoutes,
  toggleExpand,
  miniSidebar,
  expandedItems,
  isCompact,
  setMiniSidebar,
}: _sidebarPropTypes) => {
  const location = useLocation();
  const isActiveRoute = (path: string) =>
    location.pathname === path || location.pathname.startsWith(`${path}/`);

  return (
    <section className="flex flex-col text-white" id="main-menu">
      {miniSidebar && (
        <>
          {!isCompact && SidebarRoutes.title && (
            <section className="my-2 flex items-center gap-1 text-[10px] text-[#898989] w-full px-4">
              <div className="text-start">{SidebarRoutes.title}</div>
              <span className="h-[1px] bg-gray-400 w-full" />
            </section>
          )}
          <div className="flex flex-col gap-1" id="menu">
            {SidebarRoutes.routes.map((route) => {
              const isExpanded = expandedItems.includes(route.id);
              const hasActiveChild = route.children?.some((child) =>
                isActiveRoute(child.path)
              );
              return (
                <div key={route.id} className="mx-2">
                  {route.path && (
                    <Link
                      to={route.path}
                      className={`flex place-items-center group px-3 py-2 justify-between rounded-lg ${
                        isActiveRoute(route.path)
                          ? "bg-[#070C44] text-white"
                          : hasActiveChild
                          ? "bg-white text-black"
                          : "text-white hover:bg-[#070C44] hover:text-white"
                      }`}
                      onClick={() => {
                        if (route.children) toggleExpand?.(route.id);
                        if (isCompact) setMiniSidebar(true); // Expand sidebar when clicking an icon in compact mode
                      }}
                    >
                      <section className="flex gap-2 place-items-center">
                        {route.icon && (
                          <div className="relative flex text-xs gap-2 place-items-center">
                            <route.icon
                              className={`size-5 ${
                                hasActiveChild ? "text-black" : "text-white"
                              }`}
                            />
                          </div>
                        )}
                        {!isCompact && (
                          <p
                            className={
                              isActiveRoute(route.path)
                                ? "text-white text-sm"
                                : hasActiveChild
                                ? "text-black"
                                : "text-white text-sm group-hover:text-white"
                            }
                          >
                            {route.title}
                          </p>
                        )}
                      </section>
                      {!isCompact && route.children && (
                        <Icon
                          icon="lucide:chevron-right"
                          fontSize={16}
                          className={`transform transition-transform duration-300 ${
                            isExpanded ? "rotate-90" : ""
                          } ${hasActiveChild ? "text-black" : "text-white"}`}
                        />
                      )}
                    </Link>
                  )}
                  {!isCompact && route.children && (
                    <div
                      className={`ml-8 flex flex-col overflow-hidden transition-all duration-300 ease-in-out ${
                        isExpanded
                          ? "max-h-96 opacity-100 mt-1"
                          : "max-h-0 opacity-0"
                      }`}
                    >
                      {route.children.map((child) => (
                        <Link
                          to={child.path}
                          key={child.id}
                          className={`relative flex my-1 pl-4 hover:text-white hover:bg-[#070C44] rounded-lg place-items-center ${
                            isActiveRoute(child.path)
                              ? "bg-[#070C44] text-white"
                              : "text-white"
                          }`}
                        >
                          <input
                            type="radio"
                            className="w-3 h-3 border border-gray-400 rounded-full appearance-none checked:border-white checked:bg-transparent checked:ring-2 checked:ring-gray-400"
                          />
                          <Text
                            variant="sidebar"
                            size="body-sm-default"
                            className={`py-2 px-3 rounded ${
                              isActiveRoute(child.path)
                                ? "text-white"
                                : "text-white"
                            }`}
                          >
                            {child.title}
                          </Text>
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </>
      )}
    </section>
  );
};
