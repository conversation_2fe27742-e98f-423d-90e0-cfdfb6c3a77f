import { Form, FormikProvider, useFormik, FieldArray } from "formik";
import { GlobalForm } from "../../../../components/GlobalForm/GlobalFormComponent";
import { ActionButton } from "../../../../components/ActionButton";
import { CustomTabs } from "../../../../components/CustomTab";
import { Icon } from "@iconify/react/dist/iconify.js";
import { useCallback, useMemo, useState } from "react";
import {
  BillingDetails,
  GuestDetails,
  PaymentDetails,
} from "./IncomeListFormData";

const getInitialBillingEntry = () => {
  return BillingDetails.reduce((acc: Record<string, any>, field) => {
    acc[field.field] = "";
    return acc;
  }, {});
};

const getInitialValues = (sections: any) => {
  return sections.reduce((values: any, section: any) => {
    if (section.title === "Billing Details") {
      values.billingEntries = [getInitialBillingEntry()];
    } else {
      section.fields.forEach((field: any) => {
        values[field.field] = "";
      });
    }
    return values;
  }, {});
};

const IncomeForm = () => {
  // console.log(editData);
  const [tab, setTab] = useState("Without VAT Bill");
  const tabOptions = useMemo(() => ["Without VAT Bill", "With VAT Bill"], []);
  const onTabChange = useCallback((status: any) => setTab(status), []);

  const filteredGuestDetails = useMemo(() => {
    return GuestDetails.filter((field) =>
      tab === "With VAT Bill" ? true : field.field !== "vatPanNo"
    );
  }, [tab]);

  const formSections = useMemo(
    () => [
      { id: 1, title: "Guest Details", fields: filteredGuestDetails },
      { id: 2, title: "Billing Details", fields: BillingDetails },
      { id: 3, title: "Payment Details", fields: PaymentDetails },
    ],
    [filteredGuestDetails]
  );

  const formik = useFormik({
    initialValues: getInitialValues(formSections),
    enableReinitialize: true,
    onSubmit: async (values) => {
      console.log("Form Submitted:", values);
    },
  });

  const { handleSubmit, getFieldProps } = formik;

  return (
    <FormikProvider value={formik}>
      <Form onSubmit={handleSubmit}>
        <div className="p-4">
          <div className="flex items-center mb-4 border bg-white p-2 rounded-md">
            <CustomTabs
              tabs={tabOptions}
              defaultTab={tab}
              onTabChange={onTabChange}
            />
          </div>

          {formSections.map(({ title, fields }) => (
            <div key={title} className="mb-6 border p-2 rounded-md">
              <h2 className="pb-2 mb-3 text-lg font-semibold border-b">
                {title}
              </h2>
              {title === "Billing Details" ? (
                <FieldArray name="w">
                  {({ push, remove }) => (
                    <div>
                      {formik.values.billingEntries.map(
                        (_: any, index: any) => (
                          <div
                            key={index}
                            className="mb-4 p-4 border rounded-md relative"
                          >
                            <div className="grid grid-cols-3 gap-4">
                              <GlobalForm
                                formDatails={fields}
                                getFieldProps={(field) => ({
                                  ...formik.getFieldProps(
                                    `billingEntries.${index}.${field}`
                                  ),
                                  value:
                                    formik.values.billingEntries[index][
                                      field
                                    ] || "",
                                })}
                              />
                            </div>
                            {formik.values.billingEntries.length > 1 && (
                              <button
                                type="button"
                                onClick={() => remove(index)}
                                className="absolute top-2 right-2 text-red-500"
                              >
                                <Icon
                                  icon="mdi:delete"
                                  width="20"
                                  height="20"
                                />
                              </button>
                            )}
                          </div>
                        )
                      )}
                      <button
                        type="button"
                        onClick={() => push(getInitialBillingEntry())}
                        className="flex items-center justify-center px-4 py-2 bg-blue-500 text-white bg-blue  rounded-md"
                      >
                        Add
                      </button>
                    </div>
                  )}
                </FieldArray>
              ) : (
                <div className="grid grid-cols-3 gap-4">
                  <GlobalForm
                    formDatails={fields}
                    getFieldProps={getFieldProps}
                  />
                </div>
              )}
            </div>
          ))}
        </div>
        <ActionButton onCancel={() => {}} onSubmit={handleSubmit} />
      </Form>
    </FormikProvider>
  );
};

export default IncomeForm;
