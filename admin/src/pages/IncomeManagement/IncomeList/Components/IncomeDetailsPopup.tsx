import { Icon } from "@iconify/react/dist/iconify.js";
import { PopupModal } from "../../../../components";

// Popup Component for viewing income details
interface IIncomeDetailsPopupProps {
  data: any;
  onClose: () => void;
}
const IncomeDetailsPopup = ({ data, onClose }: IIncomeDetailsPopupProps) => {
  return (
    <PopupModal onClose={onClose}>
      <div className="w-full bg-red-500">
        <div className="relative flex items-center jusify-between bg-[#EBFEF4]">
          <h1 className="w-full p-4 text-center text-semibold">
            View Payment{" "}
          </h1>
          <button
            className="absolute flex items-center justify-center p-1 bg-black rounded-full top-4 right-4"
            onClick={close}
          >
            <Icon
              icon="fluent-mdl2:calculator-multiply"
              width="14"
              height="14"
              className="text-white"
            />
          </button>
        </div>

        <div className="p-6 flex flex-col gap-4">
          <div className="grid grid-cols-3 border  gap-8 p-3 rounded-md">
            <div className="flex items-center space-x-2">
              <p className="font-medium text-gray-500">Date</p>
              <p className="font-medium">{data.date}</p>
            </div>
            <div className="flex items-center space-x-2">
              <p className="text-sm text-gray-500">Payment Method</p>
              <p className="font-medium">{data.paymentMethod}</p>
            </div>
            <div className="flex items-center space-x-2">
              <p className="text-sm text-gray-500">Invoice Number</p>
              <p className="font-medium">{data.invoiceNumber}</p>
            </div>
          </div>

          <div className="grid grid-cols-3 gap-8 border p-3 rounded-md pb-2">
            <div className="flex items-center space-x-2">
              <p className="text-sm text-gray-500 whitespace-nowrap">
                Income Category
              </p>
              <p className="font-medium whitespace-nowrap">
                {data.incomeCategory}
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <p className="text-sm text-gray-500">Remaining</p>
              <p className="font-medium">${data.remaining}</p>
            </div>
            <div className="flex items-center space-x-2">
              <p className="text-sm text-gray-500">Amount</p>
              <p className="font-medium">${data.remaining}</p>
            </div>
          </div>
        </div>
      </div>
    </PopupModal>
  );
};
export default IncomeDetailsPopup;
