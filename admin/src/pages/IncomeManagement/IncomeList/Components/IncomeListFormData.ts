export const GuestDetails = [
  {
    label: "Guest Name",
    field: "categoryName",
    type: "select",
    options: [
      { label: "Xenu Webstudio", value: "xenu" },
      { label: "Marketing", value: "Marketing" },
    ],
  },
  {
    label: "VAT/PAN No",
    field: "vatPanNo",
    type: "text",
  },
  {
    label: "Mobile No",
    field: "mobileNo",
    type: "text",
  },

  { label: "Invoice Date", field: "invoiceDate", type: "date" },
];

export const BillingDetails = [
  {
    label: "Category",
    field: "category",
    type: "text",
  },
  {
    label: "Sub Category",
    field: "subCategory",
    type: "text",
  },
  {
    label: "Unit",
    field: "unit",
    type: "text",
  },
  {
    label: "Price Per Unit",
    field: "pricePerUnit",
    type: "text",
  },
  {
    label: "Income Description",
    field: "incomeDescription",
    type: "text",
  },
];

export const PaymentDetails = [
  {
    label: "Payment Method",
    field: "paymentMethod",
    type: "select",
    options: [
      { label: "Cash", value: "Cash" },
      { label: "Card", value: "Card" },
      { label: "UPI", value: "UPI" },
      { label: "Net Banking", value: "Net Banking" },
    ],
  },
  {
    label: "Total Paid",
    field: "totalPaid",
    type: "text",
  },
  {
    label: "Transaction Id",
    field: "transactionId",
    type: "text",
  },
];
