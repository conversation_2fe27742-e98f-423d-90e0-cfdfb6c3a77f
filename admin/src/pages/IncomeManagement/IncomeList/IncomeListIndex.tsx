import { useMemo, useState } from "react";
import Header from "../../../components/Header";
import MasterTable from "../../../layouts/Table/MasterTable";
import { TableAction } from "../../../layouts/Table/TableAction";
import IncomeForm from "./Components/IncomeForm";
import { useNavigate } from "react-router-dom";
import IncomeDetailsPopup from "./Components/IncomeDetailsPopup";

const IncomeTypeListIndex = () => {
  const [showPopup, setShowPopup] = useState(false);
  // const [editData, setEditData] = useState(null);
  const navigate = useNavigate();
  const [viewData, setViewData] = useState(null);
  const openModal = () => setShowPopup(true);

  const openViewPopup = (data: any) => {
    setViewData(data);
  };

  const closeViewPopup = () => {
    setViewData(null);
  };

  const columns = useMemo(
    () => [
      { title: "SN", key: "index" },
      { title: "Invoice Number", key: "invoiceNumber" },
      { title: "Date", key: "date" },
      { title: "Income Category", key: "incomeCategory" },
      { title: "Payment Method", key: "paymentMethod" },
      { title: "Remaining", key: "remaining" },
      { title: "Actions", key: "action" },
    ],
    []
  );

  const rows = [
    {
      key: "1",
      index: 1,
      invoiceNumber: "INV-001",
      date: "2021-09-01",
      incomeCategory: "Tours and Travels",
      paymentMethod: "Cash",
      remaining: "1000",
      action: (
        <TableAction
          onShow={() => openViewPopup(rows[0])}
          onEdit={() => {
            // setEditData(rows as any);
            navigate("/income-management/income-form");
            setShowPopup(true);
          }}
          onDelete={() => {}}
        />
      ),
    },
    {
      key: "2",
      index: 2,
      invoiceNumber: "INV-002",
      date: "2021-09-02",
      incomeCategory: "Tours and Travels",
      paymentMethod: "Cash",
      remaining: "1000",

      action: (
        <TableAction
          onShow={() => openViewPopup(rows[1])}
          onEdit={() => {
            // setEditData(rows as any);
            navigate("/income-management/income-form");
            setShowPopup(true);
          }}
          onDelete={() => {}}
        />
      ),
    },
  ];

  return (
    <div>
      <Header
        title="New Income"
        onAddClick={openModal}
        route="/income-management/income-form"
      />
      {showPopup && <IncomeForm />}
      {viewData && (
        <IncomeDetailsPopup data={viewData} onClose={closeViewPopup} />
      )}
      <MasterTable
        loading={false}
        columns={columns}
        rows={rows as any}
        // showToggle
      />
    </div>
  );
};

export default IncomeTypeListIndex;
