import { Form, FormikProvider, useFormik } from "formik";
import HeadingPopup from "../../../GuestManagement/components/HeadingPopup";
import { GlobalForm } from "../../../../components/GlobalForm/GlobalFormComponent";
import { ActionButton } from "../../../../components/ActionButton";
import { IncomeCategoryFormData } from "./IncomeCategoryFormData";
interface IIncomeCategoryFormProps {
  onClose: () => void;
  editData: null;
}
const IncomeCategoryForm = ({
  onClose,
  editData,
}: IIncomeCategoryFormProps) => {
  const formik = useFormik({
    initialValues: {},
    enableReinitialize: true,
    onSubmit: async (values: any) => {
      console.log(values, "values");
    },
  });
  const { handleSubmit, getFieldProps } = formik;
  return (
    <HeadingPopup
      onClose={onClose}
      className="w-full max-w-screen-sm"
      heading={`${!editData ? "Add" : "Update"} Income Category`}
    >
      <FormikProvider value={formik}>
        <Form onSubmit={handleSubmit}>
          <div className="grid grid-cols-2 gap-2">
            <GlobalForm
              formDatails={IncomeCategoryFormData}
              getFieldProps={getFieldProps}
            />
          </div>
          <ActionButton onCancel={onClose} onSubmit={handleSubmit} />
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default IncomeCategoryForm;
