import { Form, FormikProvider, useFormik } from "formik";
import HeadingPopup from "../../../GuestManagement/components/HeadingPopup";
import { GlobalForm } from "../../../../components/GlobalForm/GlobalFormComponent";
import { ActionButton } from "../../../../components/ActionButton";
import { IncomeTypeFormData } from "./IncomeTypeFormData";
interface IIncomeCategoryFormProps {
  onClose: () => void;
  editData: null;
}
const IncomeTypeForm = ({ onClose, editData }: IIncomeCategoryFormProps) => {
  const formik = useFormik({
    initialValues: {},
    enableReinitialize: true,
    onSubmit: async (values: any) => {
      console.log(values, "values");
    },
  });
  const { handleSubmit, getFieldProps } = formik;
  return (
    <HeadingPopup
      onClose={onClose}
      className="w-full max-w-screen-sm"
      heading={`${!editData ? "Add" : "Update"} Income Type`}
    >
      <FormikProvider value={formik}>
        <Form onSubmit={handleSubmit}>
          <div className="grid grid-cols-2 gap-2">
            <GlobalForm
              formDatails={IncomeTypeFormData}
              getFieldProps={getFieldProps}
            />
          </div>
          <ActionButton onCancel={onClose} onSubmit={handleSubmit} />
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default IncomeTypeForm;
