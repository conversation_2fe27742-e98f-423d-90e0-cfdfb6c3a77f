import { useCallback, useMemo, useState } from "react";
import { PopupModal } from "../../../components";
import Header from "../../../components/Header";
import MasterTable from "../../../layouts/Table/MasterTable";
import { TableAction } from "../../../layouts/Table/TableAction";
import IncomeTypeForm from "./Components/IncomeTypeForm";

const IncomeTypeListIndex = () => {
  const [showPopup, setShowPopup] = useState(false);
  const [editData, setEditData] = useState(null);

  const openModal = () => setShowPopup(true);
  const closeModal = () => setShowPopup(false);
  const togglePopup = useCallback(() => setShowPopup((prev) => !prev), []);

  const columns = useMemo(
    () => [
      { title: "SN", key: "index" },
      { title: "category Name", key: "name" },
      { title: "Income Type", key: "incomeType" },
      { title: "Price", key: "price" },
      { title: "Unit", key: "unit" },
      { title: "Description", key: "description" },
      { title: "Actions", key: "action" },
    ],
    []
  );

  const rows = [
    {
      key: "1",
      index: 1,
      name: "Tours and Travels",
      incomeType: "Wedding",
      price: "1000",
      unit: "per person",
      description: "lorem ipsum",
      action: (
        <TableAction
          onEdit={() => {
            setEditData(rows as any);
            setShowPopup(true);
          }}
          onDelete={() => {}}
        />
      ),
    },
    {
      key: "2",
      index: 2,
      name: "Tours and Travels",
      incomeType: "Wedding",
      price: "1000",
      unit: "per person",
      description: "lorem ipsum",
      action: (
        <TableAction
          onEdit={() => {
            setEditData(rows as any);
            setShowPopup(true);
          }}
          onDelete={() => {}}
        />
      ),
    },
  ];

  return (
    <div>
      {showPopup && (
        <PopupModal onClose={closeModal}>
          <IncomeTypeForm onClose={togglePopup} editData={editData} />
        </PopupModal>
      )}
      <Header title="Income Type" onAddClick={openModal} />
      <MasterTable
        loading={false}
        columns={columns}
        rows={rows as any}
        // showToggle
      />
    </div>
  );
};

export default IncomeTypeListIndex;
