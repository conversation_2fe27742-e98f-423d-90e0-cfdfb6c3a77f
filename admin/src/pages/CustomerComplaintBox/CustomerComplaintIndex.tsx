// CustomerComplaintIndex.tsx
import { useState } from "react";
import { TableAction } from "../../layouts/Table/TableAction";
import MasterTable from "../../layouts/Table/MasterTable";
import Header from "../../components/Header";
import { PopupModal } from "../../components";
import CustomerComplainForm from "./Components/CustomerComplainForm";
import {
  useDeletecomplain,
  useGetcomplains,
} from "../../server-action/API/complain";
import { Status } from "../../components/Status";

const CustomerComplaintIndex = () => {
  const [showPopup, SetshowPopup] = useState(false);
  const openModal = () => SetshowPopup(true);
  const [editData, setEditData] = useState(null);
  const { data: ComplaintData } = useGetcomplains();
  const closeModal = () => {
    setEditData(null);
    SetshowPopup(false);
  };
  const { mutateAsync: deleteComplaint } = useDeletecomplain();

  const tableData = {
    columns: [
      { title: "Complain Type", key: "complainType" },
      { title: "Room ", key: "room" },
      { title: "Reported Date", key: "reportedDate" },
      { title: "Complain Type", key: "complainType" },
      { title: "Priority", key: "priority" },
      { title: "Action", key: "action" },
    ],
    rows:
      ComplaintData?.map((item: any) => ({
        id: item?._id,
        complainType: item?.complainType,
        room: item?.room?.roomNo,
        reportedDate: item?.date,
        priority: <Status status={item?.priority} />,
        action: (
          <TableAction
            onEdit={() => {
              setEditData(item);
              SetshowPopup(true);
            }}
            onDelete={() => item?._id && deleteComplaint(item._id.toString())}
          />
        ),
      })) || [], // Fallback to empty array if ComplaintData is undefined
  };

  // const filterConfig = [
  //   // {
  //   //   label: "Reported Date",
  //   //   key: "reportedDate",
  //   //   type: "date",
  //   // },
  //   {
  //     label: "Priority",
  //     key: "priority",
  //     type: "select",
  //     options: [
  //       { value: "low", label: "Low" },
  //       { value: "moderate", label: "Moderate" },
  //       { value: "high", label: "High" },
  //     ],
  //   },
  // ];

  return (
    <div>
      {showPopup && (
        <PopupModal onClose={closeModal}>
          <CustomerComplainForm onClose={closeModal} editData={editData} />
        </PopupModal>
      )}
      <Header title="Customer Complain Box" onAddClick={openModal} />

      <MasterTable
        loading={false}
        columns={tableData.columns}
        rows={tableData.rows}
        // canSelect
        onDelete={(selectedIds) => {
          selectedIds.forEach((id) => deleteComplaint(id.toString()));
        }}
      />
    </div>
  );
};

export default CustomerComplaintIndex;
