import { FormikProvider, Form, useFormik } from "formik";
import HeadingPopup from "../../GuestManagement/components/HeadingPopup";
import { GlobalForm } from "../../../components/GlobalForm/GlobalFormComponent";
import { getComplainFormData } from "./CustomerComplainData";
import { ActionButton } from "../../../components/ActionButton";
import {
  useCreatecomplain,
  useUpdatecomplain,
} from "../../../server-action/API/complain";
import { IComplain } from "../../../Interface/complain.interface";
import { ComplainValidationSchema } from "./CustomerComplainValidation";
import { useGetAllRoom } from "../../../server-action/API/Room/room";

interface ICustomerComplaiFormProps {
  editData: IComplain | null;
  onClose: () => void;
}

const CustomerComplainForm = ({
  onClose,
  editData,
}: ICustomerComplaiFormProps) => {
  const { mutateAsync: createComplain } = useCreatecomplain();
  const { mutateAsync: updateComplain } = useUpdatecomplain();
  const { data: room } = useGetAllRoom();

  const ComplainFormData = getComplainFormData(room || ([] as any));

  const formik = useFormik({
    initialValues: {
      date: editData?.date || "",
      room:
        typeof editData?.room === "object" &&
        editData?.room !== null &&
        "_id" in editData.room
          ? (editData.room as { _id: string })._id
          : typeof editData?.room === "string"
          ? editData.room
          : "",
      complainType: editData?.complainType || "",
      priority: editData?.priority || "",
      description: editData?.description || "",
    },
    enableReinitialize: true,
    validationSchema: ComplainValidationSchema,
    onSubmit: async (values) => {
      try {
        if (editData) {
          await updateComplain({
            complainData: values,
            _id: editData?._id as any,
          });
        } else {
          await createComplain(values);
        }
        onClose();
      } catch (error) {
        console.error("Error submitting form:", error);
      }
    },
  });

  const { handleSubmit, getFieldProps } = formik;

  return (
    <HeadingPopup
      onClose={onClose}
      className="w-full max-w-screen-sm "
      heading={`${editData ? "Edit" : "Add"} Customer Complaint Box`}
    >
      <FormikProvider value={formik}>
        <Form onSubmit={handleSubmit}>
          <div className="grid grid-cols-2 gap-2">
            <GlobalForm
              formDatails={ComplainFormData}
              getFieldProps={getFieldProps}
            />
          </div>

          <ActionButton onCancel={onClose} onSubmit={handleSubmit} />
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default CustomerComplainForm;
