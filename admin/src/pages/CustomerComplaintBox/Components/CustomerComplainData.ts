export const getComplainFormData = (room: any[]) => [
  // {
  //   field: "hotel",
  //   label: "Hotel Name",
  //   type: "text",
  //   placeholder: "Select Hotel",
  // },
  {
    field: "date",
    label: "Date",
    type: "date",
    placeholder: "Select Date",
  },
  {
    field: "room",
    label: "Room Type",
    type: "select",
    placeholder: "Select Room No.",
    options:
      room?.map((type) => ({
        value: type?._id,
        label: `${type?.roomNo} (${type?.roomType?.name})`,
      })) || [],
  },

  // {
  //   field: "roomType",
  //   label: "Room Type",
  //   type: "select",
  //   placeholder: "Select Room Type",
  //   options:
  //     roomTypes?.map((type) => ({
  //       value: type?._id,
  //       label: `${type?.name} (${type?.roomType})`,
  //     })) || [],
  // },
  {
    field: "complainType",
    label: "Complaint Type",
    type: "text",
    placeholder: "Add Complaint Type",
  },
  {
    field: "priority",
    label: "Priority",
    type: "select",
    placeholder: "Select Priority",
    options: [
      { value: "high", label: "High" },
      { value: "moderate", label: "Moderate" },
      { value: "low", label: "Low" },
    ],
  },

  // {
  //   field: "description",
  //   label: "Description",
  //   type: "textarea",
  //   placeholder: "Add Description",
  // },

  // {
  //   field: "attachments",
  //   label: "Attachments",
  //   type: "file",
  //   placeholder: "Upload Attachments",
  // },
];
