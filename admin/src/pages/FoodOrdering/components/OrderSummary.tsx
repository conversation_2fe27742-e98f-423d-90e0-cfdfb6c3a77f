import { useState } from "react";
import { IMenuItem } from "../../../Interface/menu.interface";
import { IOrderItem } from "../../../Interface/order.interface";

interface OrderSummaryProps {
  orderItems: {
    menuItem: IMenuItem;
    quantity: number;
    size?: { _id: string; size: string; price: number };
    toppings?: { name: string; price: number }[];
    note?: string;
  }[];
  onRemoveItem: (index: number) => void;
  onUpdateQuantity: (index: number, quantity: number) => void;
  onProceedToCheckout: () => void;
}

const OrderSummary: React.FC<OrderSummaryProps> = ({
  orderItems,
  onRemoveItem,
  onUpdateQuantity,
  onProceedToCheckout,
}) => {
  const calculateItemPrice = (
    menuItem: IMenuItem,
    quantity: number,
    size?: { _id: string; size: string; price: number },
    toppings?: { name: string; price: number }[]
  ) => {
    let price = size ? size.price : menuItem.price;
    if (toppings && toppings.length > 0) {
      toppings.forEach((topping) => {
        price += topping.price;
      });
    }
    return price * quantity;
  };

  const calculateSubtotal = () => {
    return orderItems.reduce(
      (total, { menuItem, quantity, size, toppings }) =>
        total + calculateItemPrice(menuItem, quantity, size, toppings),
      0
    );
  };

  const subtotal = calculateSubtotal();
  const tax = subtotal * 0.05; // Assuming 5% tax
  const total = subtotal + tax;

  return (
    <div className="bg-white rounded-lg shadow p-4">
      <h2 className="text-xl font-bold mb-4">Order Summary</h2>

      {orderItems.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">Your order is empty.</p>
          <p className="text-gray-500 mt-2">
            Add items from the menu to get started.
          </p>
        </div>
      ) : (
        <>
          <div className="divide-y">
            {orderItems.map((item, index) => (
              <div key={index} className="py-4">
                <div className="flex justify-between">
                  <div className="flex-1">
                    <h3 className="font-medium">
                      {item.menuItem.name}
                      {item.size && ` (${item.size.size})`}
                    </h3>
                    {item.toppings && item.toppings.length > 0 && (
                      <p className="text-sm text-gray-600 mt-1">
                        Extra:{" "}
                        {item.toppings
                          .map((t) => `${t.name} +₹${t.price}`)
                          .join(", ")}
                      </p>
                    )}
                    {item.note && (
                      <p className="text-sm text-gray-600 mt-1">
                        Note: {item.note}
                      </p>
                    )}
                  </div>
                  <div className="flex flex-col items-end ml-4">
                    <span className="font-medium">
                      ₹
                      {calculateItemPrice(
                        item.menuItem,
                        item.quantity,
                        item.size,
                        item.toppings
                      )}
                    </span>
                    <div className="flex items-center mt-2">
                      <button
                        type="button"
                        className="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center text-sm"
                        onClick={() =>
                          onUpdateQuantity(
                            index,
                            Math.max(1, item.quantity - 1)
                          )
                        }
                      >
                        -
                      </button>
                      <span className="mx-2 text-sm">{item.quantity}</span>
                      <button
                        type="button"
                        className="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center text-sm"
                        onClick={() =>
                          onUpdateQuantity(index, item.quantity + 1)
                        }
                      >
                        +
                      </button>
                    </div>
                  </div>
                </div>
                <button
                  type="button"
                  className="text-red-600 text-sm mt-2"
                  onClick={() => onRemoveItem(index)}
                >
                  Remove
                </button>
              </div>
            ))}
          </div>

          <div className="border-t pt-4 mt-4">
            <div className="flex justify-between mb-2">
              <span className="text-gray-600">Subtotal</span>
              <span>₹{subtotal.toFixed(2)}</span>
            </div>
            <div className="flex justify-between mb-2">
              <span className="text-gray-600">Tax (5%)</span>
              <span>₹{tax.toFixed(2)}</span>
            </div>
            <div className="flex justify-between font-bold text-lg mt-4">
              <span>Total</span>
              <span>₹{total.toFixed(2)}</span>
            </div>
          </div>

          <button
            type="button"
            className="w-full mt-6 bg-blue-200 text-white py-3 rounded-md hover:bg-blue-700"
            onClick={onProceedToCheckout}
          >
            Proceed to Checkout
          </button>
        </>
      )}
    </div>
  );
};

export default OrderSummary;
