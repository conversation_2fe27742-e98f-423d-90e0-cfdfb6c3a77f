import { useState } from "react";
import { useGetBookings } from "../../../server-action/API/BookingManagement/BookingManagement";
import { IBooking, BookingStatus } from "../../../Interface/booking.interface";
/* eslint-disable @typescript-eslint/no-explicit-any */

interface BookingSelectionProps {
  selectedBooking: IBooking | null;
  onSelectBooking: (booking: IBooking) => void;
}

const BookingSelection: React.FC<BookingSelectionProps> = ({
  selectedBooking,
  onSelectBooking,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const { data: bookings, isLoading } = useGetBookings();

  // Filter active bookings (checked-in)
  const activeBookings = bookings?.data?.filter(
    (booking:any) => booking.status === BookingStatus.CHECKED_IN
  );

  // Filter bookings based on search term
  const filteredBookings = activeBookings?.filter(
    (booking:any) =>
      booking.bookingId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (booking.guest?.name &&
        booking.guest.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (booking.room?.roomNo &&
        booking.room.roomNo.toString().includes(searchTerm))
  );

  return (
    <div className="bg-white rounded-lg shadow p-4">
      <h2 className="text-xl font-bold mb-4">Select a Booking</h2>

      <div className="relative mb-4">
        <input
          type="text"
          placeholder="Search by booking ID, guest name, or room number..."
          className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
        <svg
          className="absolute left-3 top-2.5 h-5 w-5 text-gray-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          ></path>
        </svg>
      </div>

      {isLoading ? (
        <div className="space-y-3">
          {[1, 2, 3, 4].map((i) => (
            <div
              key={i}
              className="p-4 border border-gray-200 rounded-md animate-pulse"
            >
              <div className="h-5 bg-gray-200 rounded w-1/3 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-1"></div>
              <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            </div>
          ))}
        </div>
      ) : filteredBookings && filteredBookings.length > 0 ? (
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {filteredBookings.map((booking:any) => (
            <div
              key={booking._id}
              className={`p-4 border rounded-md cursor-pointer transition-colors ${
                selectedBooking?._id === booking._id
                  ? "border-blue-500 bg-blue-50"
                  : "border-gray-200 hover:border-blue-300 hover:bg-blue-50"
              }`}
              onClick={() => onSelectBooking(booking)}
            >
              <div className="flex justify-between">
                <h3 className="font-medium">
                  Booking ID: {booking.bookingId}
                </h3>
                <span className="text-sm bg-green-100 text-green-800 px-2 py-0.5 rounded">
                  Checked In
                </span>
              </div>
              <p className="text-sm text-gray-600">
                Guest: {booking.guest?.name || "N/A"}
              </p>
              <p className="text-sm text-gray-600">
                Room: {booking.room?.roomNo || "N/A"}
              </p>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <p className="text-gray-500">No active bookings found.</p>
          {searchTerm && (
            <p className="text-gray-500 mt-2">
              Try adjusting your search criteria.
            </p>
          )}
        </div>
      )}
    </div>
  );
};

export default BookingSelection;
