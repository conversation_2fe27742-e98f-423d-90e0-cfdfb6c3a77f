import React from 'react';

interface MembershipDiscountProps {
  membershipDiscount: {
    name: string;
    discountPercentage: number;
    discountAmount: number;
  } | null;
}

const MembershipDiscountInfo: React.FC<MembershipDiscountProps> = ({ membershipDiscount }) => {
  if (!membershipDiscount) {
    return null;
  }

  return (
    <div className="p-3 mb-4 bg-green-50 border border-green-200 rounded-md">
      <h5 className="mb-2 text-sm font-medium text-green-800">Membership Discount Applied</h5>
      <div className="grid grid-cols-3 gap-2">
        <div>
          <p className="text-xs text-green-600">Membership</p>
          <p className="text-sm font-medium">{membershipDiscount.name}</p>
        </div>
        <div>
          <p className="text-xs text-green-600">Discount %</p>
          <p className="text-sm font-medium">{membershipDiscount.discountPercentage}%</p>
        </div>
        <div>
          <p className="text-xs text-green-600">Amount Saved</p>
          <p className="text-sm font-medium text-green-700">{membershipDiscount.discountAmount}</p>
        </div>
      </div>
    </div>
  );
};

export default MembershipDiscountInfo;
