interface Option {
  label: string;
  value: string;
}
export const reservationDetails = (obj: {
  roomCategory: Option[];
  bedCategory: Option[];
  packageType: Option[];
  roomNo: Option[];
  acNon: Option[];
}) => {
  return [
    {
      placeholder: "Enter CheckIn Time",
      label: "Check In",
      name: "checkIn",
      type: "date",
    },
    {
      placeholder: "Enter CheckOut Time",
      label: "Check Out",
      name: "checkOut",
      type: "date",
    },
    {
      placeholder: "Enter Package Type",
      label: "Package Type",
      name: "packageType",
      type: "dropdown",
    },
    {
      placeholder: "Select Room",
      label: "Room Category",
      name: "roomCategory",
      type: "dropdown",
      options: obj.roomCategory,
    },
    {
      placeholder: "Choose one",
      label: "A/c-Non",
      name: "a/cnon",
      type: "dropdown",
      options: obj.acNon,
    },
    {
      placeholder: "Select Bed Category",
      label: "Bed Category",
      name: "bedCategory",
      type: "dropdown",
      options: obj.bedCategory,
    },
    {
      placeholder: "Select Room No",
      label: "Room No",
      name: "roomNo",
      type: "dropdown",
      options: obj.roomNo,
    },
  ];
};
export const customerDetails = (obj: {
  gender: Option[];
  country: Option[];
}) => {
  return [
    {
      label: "Guest Name",
      placeholder: "guestName",
      type: "text",
      name: "guestName",
    },
    {
      label: "Gender",
      placeholder: "Select Gender",
      type: "dropdown",
      name: "gender",
      options: obj.gender,
    },
    {
      name: "dateOfBirth",
      label: "Date of Birth",
      placeholder: "Select Date of Birth",
      type: "date",
    },
    {
      name: "mobileNo",
      label: "Mobile No",
      placeholder: "Enter Mobile No",
      type: "text",
    },
    {
      name: "email",
      label: "Email",
      placeholder: "Enter Email",
      type: "email",
    },
    {
      name: "country",
      label: "Country",
      placeholder: "Select Country",
      type: "dropdown",
      options: obj.country,
    },
    {
      name: "Address",
      label: "Address",
      placeholder: "Enter Address",
      type: "text",
    },
    {
      name: "nationality",
      label: "Nationality",
      placeholder: "Enter Nationality",
      type: "text",
    },
    {
      name: "purposeOfVisit",
      label: "Purpose of Visit",
      placeholder: "Purpose of Visit",
      type: "text",
    },
  ];
};
export const identityDetails = () => {
  return [
    {
      name: "identityTyp",
      label: "Identity Type",
      placeholder: "Select Identity Type",
      type: "dropdown",
    },
    {
      name: "identityNo",
      label: "ID No",
      placeholder: "Enter ID No",
      type: "text",
    },
    {
      name: "uploadPhoto",
      type: "file",
      label: "Upload Photo",
    },
  ];
};
//payment details
export const paymentDetails = () => {
  return [
    {
      name: "paymentType",
      label: "Payment Type",
      placeholder: "Select Payment Type",
      type: "dropdown",
    },
    {
      name: "totalAmount",
      label: "Total Amount",
      placeholder: "Enter Total Amount",
      type: "number",
    },
    {
      name: "paidAmount",
      label: "Paid Amount",
      placeholder: "Enter Paid Amount",
      type: "number",
    },
    {
      name: "paymentMethod",
      label: "Payment Method",
      placeholder: "Select Payment Method",
      type: "dropdown",
    },
  ];
};
