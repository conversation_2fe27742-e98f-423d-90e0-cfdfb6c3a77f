import MasterTable from "../../../layouts/Table/MasterTable"
const SampleTableData = [
    {
        serviceId: "102",
        date: "2/20/2525",
        typeOfService: "Wellness & Spa",
        count: "1",
        amount: "Rs. 1000",
    },
    {
        serviceId: "103",
        date: "2/20/2525",
        typeOfService: "Gym",
        count: "1",
        amount: "Rs. 3000",
    },
];

const ChargeabaleService = () => {
    const tableData = {
        columns: [
            { title: "Service ID", key: "serviceId" },
            { title: "Date", key: "date" },
            { title: "Type of Service", key: "typeOfService" },
            { title: "Count", key: "count" },
            { title: "Amount", key: "amount" },
        ],
    };

    return (
        <div>
            <h1></h1>
            <div className="rounded-md bg-white flex-col flex w-full gap-6 ">
                <h1 className="text-black font-bold mx-6 mt-4">Chargeable Services</h1>
            <MasterTable
                columns={tableData.columns}
                rows={SampleTableData}
                loading={false}
            />
            </div>
        </div>
    );
};

export default ChargeabaleService