import MasterTable from "../../../layouts/Table/MasterTable";
import print from "../../../assets/Svg/Print.svg";
import { IBooking } from "../../../Interface/booking.interface";
import { useState } from "react";
import { PopupModal } from "../../../components";
import moment from "moment";
import get from "lodash/get";
import { useNavigate } from "react-router-dom";
import { FrontendRoutes } from "../../../routes";

interface FoodBeverageProps {
  booking: IBooking;
}

// Sample data for food and beverage
const SampleTableData = [
  {
    date: "11/12/2024",
    description: "Grilled Chicken Breast",
    quantity: "2",
    rate: "850",
    totalAmount: "1700",
  },
  {
    date: "11/12/2024",
    description: "Beef Steak with Pepper Sauce",
    quantity: "1",
    rate: "500",
    totalAmount: "500",
  },
];

const FoodBevarage = ({ booking }: FoodBeverageProps) => {
  const [showPrintModal, setShowPrintModal] = useState(false);
  const navigate = useNavigate();

  // Format the date
  const formattedDate = moment(get(booking, "createdAt", new Date())).format(
    "DD/MM/YYYY"
  );

  // In a real application, you would fetch food and beverage data related to this booking
  // For now, we'll use the sample data but with the booking's date
  const foodBeverageData = SampleTableData.map((item) => ({
    ...item,
    date: formattedDate,
  }));

  const tableData = {
    columns: [
      { title: "Date", key: "date" },
      { title: "Description", key: "description" },
      { title: "Quantity", key: "quantity" },
      { title: "Rate", key: "rate" },
      { title: "Total Amount", key: "totalAmount" },
    ],
  };

  const handleOrderFood = () => {
    // Store the booking ID in session storage to pre-select it in the food ordering page
    sessionStorage.setItem('selectedBookingId', booking._id);
    navigate(FrontendRoutes.FOODORDERING);
  };

  return (
    <div>
      {showPrintModal && (
        <PopupModal onClose={() => setShowPrintModal(false)}>
          <PrintFoodBeverage
            booking={booking}
            foodBeverageData={foodBeverageData}
            onClose={() => setShowPrintModal(false)}
          />
        </PopupModal>
      )}

      <div className="rounded-md bg-white flex-col flex w-full gap-6 ">
        <div className="flex w-full place-items-center justify-between">
          <h1 className="text-black font-bold mx-6 mt-4">Food & Beverage</h1>
          <div className="items-center justify-center flex gap-2">
            <button
              onClick={handleOrderFood}
              className="bg-blue-600 px-4 py-2 text-sm rounded-lg text-white font-semibold flex justify-center items-center gap-2 mt-3"
            >
              <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 5V19M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Order Food
            </button>
            <button
              onClick={() => setShowPrintModal(true)}
              className="bg-[#EBFEF4] px-4 py-2 text-sm rounded-lg text-black font-semibold flex justify-center items-center gap-2 mt-3 mx-3"
            >
              <img src={print} alt="" />
              Print
            </button>
          </div>
        </div>
        <MasterTable
          columns={tableData.columns}
          rows={foodBeverageData}
          loading={false}
        />
      </div>
    </div>
  );
};

export default FoodBevarage;

// Print Food & Beverage Component
interface PrintFoodBeverageProps {
  booking: IBooking;
  foodBeverageData: any[];
  onClose: () => void;
}

const PrintFoodBeverage = ({
  booking,
  foodBeverageData,
  onClose,
}: PrintFoodBeverageProps) => {
  // Calculate total amount
  const totalAmount = foodBeverageData.reduce(
    (sum, item) => sum + parseInt(item.totalAmount || "0", 10),
    0
  );

  return (
    <div className="w-full max-w-4xl bg-white p-8">
      <div className="flex justify-between items-center mb-6 border-b pb-4">
        <h1 className="text-2xl font-bold">Food & Beverage Bill</h1>
        <button
          onClick={onClose}
          className="bg-gray-200 px-4 py-2 rounded-md text-sm"
        >
          Close
        </button>
      </div>

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Booking Information</h2>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p>
              <span className="font-medium">Booking ID:</span> B-
              {get(booking, "_id", "").slice(-5)}
            </p>
            <p>
              <span className="font-medium">Guest Name:</span>{" "}
              {get(booking, "guest.name", "")}
            </p>
          </div>
          <div>
            <p>
              <span className="font-medium">Room:</span>{" "}
              {get(booking, "room.roomNo", "")} /{" "}
              {get(booking, "room.roomType.name", "")}
            </p>
            <p>
              <span className="font-medium">Date:</span>{" "}
              {moment().format("MMM DD, YYYY")}
            </p>
          </div>
        </div>
      </div>

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Food & Beverage Charges</h2>
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-100">
              <th className="border p-2 text-left">Date</th>
              <th className="border p-2 text-left">Description</th>
              <th className="border p-2 text-center">Quantity</th>
              <th className="border p-2 text-right">Rate</th>
              <th className="border p-2 text-right">Total</th>
            </tr>
          </thead>
          <tbody>
            {foodBeverageData.map((item, index) => (
              <tr key={index}>
                <td className="border p-2">{item.date}</td>
                <td className="border p-2">{item.description}</td>
                <td className="border p-2 text-center">{item.quantity}</td>
                <td className="border p-2 text-right">Rs. {item.rate}</td>
                <td className="border p-2 text-right">
                  Rs. {item.totalAmount}
                </td>
              </tr>
            ))}
            <tr>
              <td className="border p-2 font-semibold" colSpan={4}>
                Total
              </td>
              <td className="border p-2 text-right font-semibold">
                Rs. {totalAmount}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div className="text-center mt-8 pt-4 border-t">
        <p className="text-sm text-gray-600">Thank you for dining with us!</p>
        <p className="text-sm text-gray-600">
          For any inquiries, please contact our restaurant at
          <EMAIL>
        </p>
      </div>
    </div>
  );
};
