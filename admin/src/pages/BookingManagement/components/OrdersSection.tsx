import { useState } from "react";
import { IBooking } from "../../../Interface/booking.interface";
import { useGetOrders } from "../../../server-action/API/FoodOrdering/order";
import { Icon } from "@iconify/react/dist/iconify.js";
import moment from "moment";
import { PopupModal } from "../../../components";
import print from "../../../assets/Svg/Print.svg";
import get from "lodash/get";

interface OrdersSectionProps {
  booking: IBooking;
}

const OrdersSection = ({ booking }: OrdersSectionProps) => {
  const [expandedOrders, setExpandedOrders] = useState<string[]>([]);
  const [showPrintModal, setShowPrintModal] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<any>(null);

  // Fetch orders for this booking
  const { data: orders, isLoading } = useGetOrders(booking?._id);

  // Early return if booking is not available
  if (!booking) {
    return (
      <div className="mt-6">
        <div className="rounded-md bg-white flex-col flex w-full gap-6">
          <div className="flex w-full place-items-center justify-between px-6 py-4">
            <h1 className="text-black font-bold">Food & Beverage Orders</h1>
          </div>
          <div className="px-6 pb-6">
            <div className="text-center py-8 text-gray-500">
              Loading booking information...
            </div>
          </div>
        </div>
      </div>
    );
  }

  const toggleOrderExpand = (orderId: string) => {
    setExpandedOrders(prev =>
      prev.includes(orderId)
        ? prev.filter(id => id !== orderId)
        : [...prev, orderId]
    );
  };

  const handlePrintOrder = (order: any) => {
    setSelectedOrder(order);
    setShowPrintModal(true);
  };

  return (
    <div className="mt-6">
      {showPrintModal && selectedOrder && (
        <PopupModal onClose={() => setShowPrintModal(false)}>
          <PrintOrderDetails
            booking={booking}
            order={selectedOrder}
            onClose={() => setShowPrintModal(false)}
          />
        </PopupModal>
      )}

      <div className="rounded-md bg-white flex-col flex w-full gap-6">
        <div className="flex w-full place-items-center justify-between px-6 py-4">
          <h1 className="text-black font-bold">Food & Beverage Orders</h1>
          <div className="text-sm text-gray-500">
            {orders?.length || 0} orders
          </div>
        </div>

        <div className="px-6 pb-6">
          {isLoading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : orders && orders.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {orders.map((order: any) => {
                // Calculate total amount from menu items
                const totalAmount = order.menuItems.reduce(
                  (sum: number, item: any) => {
                    const itemPrice = item.sizePrice || get(item, "item.price", 0);
                    return sum + itemPrice * item.quantity;
                  },
                  0
                );

                const isExpanded = expandedOrders.includes(order._id);

                return (
                  <div key={order._id} className="py-4">
                    {/* Order header - always visible */}
                    <div
                      className="flex items-center justify-between cursor-pointer"
                      onClick={() => toggleOrderExpand(order._id)}
                    >
                      <div className="flex items-center gap-3">
                        <Icon
                          icon={isExpanded ? "mdi:chevron-down" : "mdi:chevron-right"}
                          width="20"
                          height="20"
                          className="text-gray-700"
                        />
                        <div>
                          <h3 className="font-medium text-gray-900">Order #{order._id.slice(-5)}</h3>
                          <p className="text-sm text-gray-500">{moment(order.date).format("MMM DD, YYYY")}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        <span
                          className={`px-2 py-1 rounded-full text-xs ${
                            order.status === "inprogress"
                              ? "bg-yellow-100 text-yellow-800"
                              : order.status === "ready"
                              ? "bg-green-100 text-green-800"
                              : order.status === "cancel"
                              ? "bg-red-100 text-red-800"
                              : "bg-blue-100 text-blue-800"
                          }`}
                        >
                          {order.status}
                        </span>
                        <span className="font-medium">Rs. {totalAmount.toFixed(2)}</span>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handlePrintOrder(order);
                          }}
                          className="text-blue-600 hover:text-blue-800"
                        >
                          <img src={print} alt="Print" className="w-5 h-5" />
                        </button>
                      </div>
                    </div>

                    {/* Order details - visible when expanded */}
                    {isExpanded && (
                      <div className="mt-4 ml-8 bg-gray-50 p-4 rounded-md">
                        <h4 className="font-medium mb-2">Order Items</h4>
                        <table className="w-full text-sm">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left py-2">Item</th>
                              <th className="text-center py-2">Quantity</th>
                              <th className="text-right py-2">Price</th>
                              <th className="text-right py-2">Total</th>
                            </tr>
                          </thead>
                          <tbody>
                            {order.menuItems.map((item: any, idx: number) => {
                              const itemName = typeof item.item === "string"
                                ? item.item
                                : get(item, "item.name", "Unknown Item");
                              const itemPrice = item.sizePrice || get(item, "item.price", 0);
                              const itemTotal = itemPrice * item.quantity;

                              return (
                                <tr key={idx} className="border-b border-gray-200">
                                  <td className="py-2">
                                    {itemName}
                                    {item.note && (
                                      <div className="text-xs text-gray-500 mt-1">
                                        Note: {item.note}
                                      </div>
                                    )}
                                  </td>
                                  <td className="text-center py-2">{item.quantity}</td>
                                  <td className="text-right py-2">Rs. {itemPrice}</td>
                                  <td className="text-right py-2">Rs. {itemTotal}</td>
                                </tr>
                              );
                            })}
                            <tr className="font-medium">
                              <td colSpan={3} className="text-right py-2">Total</td>
                              <td className="text-right py-2">Rs. {totalAmount.toFixed(2)}</td>
                            </tr>
                          </tbody>
                        </table>

                        {order.customerDetails && (
                          <div className="mt-4">
                            <h4 className="font-medium mb-2">Customer Details</h4>
                            <div className="grid grid-cols-2 gap-2 text-sm">
                              {order.customerDetails.name && (
                                <div>
                                  <span className="font-medium">Name:</span> {order.customerDetails.name}
                                </div>
                              )}
                              {order.customerDetails.phone && (
                                <div>
                                  <span className="font-medium">Phone:</span> {order.customerDetails.phone}
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              No orders found for this booking
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OrdersSection;

// Print Order Details Component
interface PrintOrderDetailsProps {
  booking: IBooking;
  order: any;
  onClose: () => void;
}

const PrintOrderDetails = ({
  booking,
  order,
  onClose,
}: PrintOrderDetailsProps) => {
  // Calculate total amount
  const totalAmount = order.menuItems.reduce(
    (sum: number, item: any) => {
      const itemPrice = item.sizePrice || get(item, "item.price", 0);
      return sum + itemPrice * item.quantity;
    },
    0
  );

  return (
    <div className="w-full max-w-4xl bg-white p-8">
      <div className="flex justify-between items-center mb-6 border-b pb-4">
        <h1 className="text-2xl font-bold">Order Details</h1>
        <button
          onClick={onClose}
          className="bg-gray-200 px-4 py-2 rounded-md text-sm"
        >
          Close
        </button>
      </div>

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Booking Information</h2>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p>
              <span className="font-medium">Booking ID:</span> B-
              {get(booking, "_id", "").slice(-5)}
            </p>
            <p>
              <span className="font-medium">Guest Name:</span>{" "}
              {get(booking, "guest.name", "")}
            </p>
            <p>
              <span className="font-medium">Order ID:</span> ORD-
              {order._id.slice(-5)}
            </p>
          </div>
          <div>
            <p>
              <span className="font-medium">Room:</span>{" "}
              {get(booking, "room.roomNo", "")} /{" "}
              {get(booking, "room.roomType.name", "")}
            </p>
            <p>
              <span className="font-medium">Date:</span>{" "}
              {moment(order.date).format("MMM DD, YYYY")}
            </p>
            <p>
              <span className="font-medium">Status:</span> {order.status}
            </p>
          </div>
        </div>
      </div>

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Order Items</h2>
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-100">
              <th className="border p-2 text-left">Item</th>
              <th className="border p-2 text-center">Quantity</th>
              <th className="border p-2 text-right">Price</th>
              <th className="border p-2 text-right">Total</th>
            </tr>
          </thead>
          <tbody>
            {order.menuItems.map((item: any, index: number) => {
              const itemName = typeof item.item === "string"
                ? item.item
                : get(item, "item.name", "Unknown Item");
              const itemPrice = item.sizePrice || get(item, "item.price", 0);
              const itemTotal = itemPrice * item.quantity;

              return (
                <tr key={index}>
                  <td className="border p-2">
                    {itemName}
                    {item.note && (
                      <div className="text-xs text-gray-500 mt-1">
                        Note: {item.note}
                      </div>
                    )}
                  </td>
                  <td className="border p-2 text-center">{item.quantity}</td>
                  <td className="border p-2 text-right">Rs. {itemPrice}</td>
                  <td className="border p-2 text-right">Rs. {itemTotal}</td>
                </tr>
              );
            })}
            <tr>
              <td className="border p-2 font-semibold" colSpan={3}>
                Total
              </td>
              <td className="border p-2 text-right font-semibold">
                Rs. {totalAmount.toFixed(2)}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div className="text-center mt-8 pt-4 border-t">
        <p className="text-sm text-gray-600">Thank you for your order!</p>
        <p className="text-sm text-gray-600">
          For any inquiries, please contact our restaurant at
          <EMAIL>
        </p>
      </div>
    </div>
  );
};
