import get from "lodash/get";
import moment from "moment";
import { useState } from "react";
import { IBooking, BookingStatus } from "../../../Interface/booking.interface";
import RoomBill from "./RoomBill";
import OrdersSection from "./OrdersSection";
import CheckoutForm from "./CheckoutForm";
import BillSummary from "./BillSummary";
import print from "../../../assets/Svg/Print.svg";
import { PopupModal } from "../../../components";
import { useGetAllPackages } from "../../../server-action/API/HotelConfiguration/roompackage";
import { Icon } from "@iconify/react/dist/iconify.js";
interface PropTypes {
  booking: IBooking;
  onBookingUpdate?: () => void;
}

interface MapperTypes {
  guestData?: {
    _id: string;
    label: string;
    value:
      | string
      | number
      | {
          district: string;
          municipality: string;
          tole: string;
          country: string;
        };
  }[];
  roomData?: {
    _id: string;
    label: string;
    value:
      | string
      | number
      | {
          district: string;
          municipality: string;
          tole: string;
          country: string;
        };
  }[];
}

const Folio = ({ booking, onBookingUpdate }: PropTypes) => {
  const [showPrintModal, setShowPrintModal] = useState(false);
  const [showCheckoutModal, setShowCheckoutModal] = useState(false);
  const [showBillModal, setShowBillModal] = useState(false);

  // Use the packages hook
  const {
    data: packages,
    isLoading: packagesLoading,
    error: packagesError,
  } = useGetAllPackages();

  // Get the first package's name
  const packageType = packages && packages.length > 0 ? packages[0].name : "";

  const totalGuests =
    get(booking, "pax.adults", 0) +
    get(booking, "pax.children", 0) +
    (get(booking, "pax.infants", 0) ?? 0);

  console.log(booking, "bookign");

  const guestData = [
    {
      _id: "1",
      label: "Guest Full Name",
      value: get(booking, "guest.name", ""),
    },
    { _id: "2", label: "Email", value: get(booking, "guest.email", "") },
    {
      _id: "3",
      label: "Country",
      value: get(booking, "guest.permanentAddress.country", "Nepal"),
    },
    {
      _id: "4",
      label: "Phone Number",
      value: get(booking, "guest.phoneNumber", ""),
    },
    {
      _id: "8",
      label: "Address",
      value:
        typeof get(booking, "guest.permanentAddress", "") === "object"
          ? `${get(booking, "guest.permanentAddress.tole", "")}, ${get(
              booking,
              "guest.permanentAddress.municipality",
              ""
            )}, ${get(booking, "guest.permanentAddress.district", "")}, ${get(
              booking,
              "guest.permanentAddress.country",
              ""
            )}`
          : get(booking, "guest.permanentAddress", ""),
    },
    {
      _id: "9",
      label: "Purpose",
      value: get(booking, "guest.purpose", "Holiday"),
    },
  ];

  const roomData = [
    { _id: "1", label: "Number of Guest", value: totalGuests },
    { _id: "2", label: "Adults", value: get(booking, "pax.adults", 0) },
    { _id: "3", label: "Kids", value: get(booking, "pax.children", 0) },
    {
      _id: "4",
      label: "Booking Number",
      value: `B-${get(booking, "_id", "").slice(-5)}`,
    },
    {
      _id: "5",
      label: "Check In",
      value: moment(get(booking, "checkIn", moment())).format("MMM-DD-YYYY"),
    },
    {
      _id: "6",
      label: "Room Type",
      value: get(booking, "room.roomType.name", ""),
    },
    {
      _id: "7",
      label: "Nights",
      value: moment(get(booking, "checkOut")).diff(
        moment(get(booking, "checkIn")),
        "days"
      ),
    },
    {
      _id: "8",
      label: "Check Out",
      value: moment(get(booking, "checkOut", moment())).format("MMM-DD-YYYY"),
    },
    {
      _id: "9",
      label: "Facilities",
      value: get(booking, "room.amenities", [])
        .map((amenity) => get(amenity, "name", ""))
        .filter((name) => name)
        .join(", "),
    },
    {
      _id: "10",
      label: "OTA Platform",
      value: get(booking, "otaPlatform", "N/A"),
    },
    {
      _id: "11",
      label: "Package Type",
      value: packagesLoading ? "Loading..." : packageType || "No Package",
    },
  ];

  console.log(booking, "folio booking");

  const handlePrint = () => {
    setShowPrintModal(true);
  };

  // Handle loading and error states
  if (packagesError) {
    console.error("Error loading packages:", packagesError);
  }

  return (
    <div className="flex flex-col w-full h-full gap-4">
      {showPrintModal && (
        <PopupModal onClose={() => setShowPrintModal(false)}>
          <PrintFolio
            booking={booking}
            packageType={packageType}
            onClose={() => setShowPrintModal(false)}
          />
        </PopupModal>
      )}

      {/* Checkout Modal */}
      {showCheckoutModal && (
        <PopupModal onClose={() => setShowCheckoutModal(false)}>
          <CheckoutForm
            booking={booking}
            onClose={() => setShowCheckoutModal(false)}
            onSuccess={() => {
              setShowCheckoutModal(false);
              onBookingUpdate?.();
            }}
          />
        </PopupModal>
      )}

      {/* Bill Summary Modal */}
      {showBillModal && (
        <PopupModal onClose={() => setShowBillModal(false)}>
          <div className="w-full max-w-2xl">
            <div className="bg-white rounded-lg">
              <div className="flex justify-between items-center p-4 border-b border-gray-200 bg-[#EBFEF4]">
                <h2 className="text-lg font-semibold text-gray-900">Full Bill Summary</h2>
                <button onClick={() => setShowBillModal(false)} className="text-gray-400 hover:text-gray-600">
                  <Icon icon="mdi:close" width="24" height="24" />
                </button>
              </div>
              <div className="p-4">
                <BillSummary booking={booking} />
              </div>
            </div>
          </div>
        </PopupModal>
      )}

      <div className="flex justify-between items-center mb-4">
        <div className="flex gap-2">
          <button
            onClick={() => setShowBillModal(true)}
            className="bg-blue-100 hover:bg-blue-200 px-4 py-2 text-sm rounded-lg text-blue-700 font-semibold flex justify-center items-center gap-2 transition-colors"
          >
            <Icon icon="mdi:receipt" width="16" height="16" />
            View Full Bill
          </button>
          <button
            onClick={handlePrint}
            className="bg-[#EBFEF4] hover:bg-green-100 px-4 py-2 text-sm rounded-lg text-black font-semibold flex justify-center items-center gap-2 transition-colors"
          >
            <img src={print} alt="Print" />
            Print Folio
          </button>
        </div>

        {/* Checkout Button - only show if booking can be checked out */}
        {(booking.status === BookingStatus.CHECKED_IN || booking.status === BookingStatus.CONFIRMED) && (
          <button
            onClick={() => setShowCheckoutModal(true)}
            className="bg-[#6047E4] hover:bg-[#5038D3] text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center gap-2 transition-colors"
          >
            <Icon icon="mdi:logout" width="16" height="16" />
            Checkout
          </button>
        )}
      </div>

      <div className="flex flex-col gap-4 md:flex-row">
        <div className="w-full md:w-1/2 h-fit">
          <Guest guestData={guestData} />
        </div>
        <div className="w-full md:w-1/2 h-fit">
          <Rooms roomData={roomData} />
        </div>
      </div>
      <section className="w-full h-full gap-6">
        <div>
          <RoomBill booking={booking} />
        </div>
        <div className="mt-6">
          <OrdersSection booking={booking} />
        </div>
      </section>
    </div>
  );
};

export default Folio;

// Fixed: Updated interface to match actual prop structure
interface GuestProps {
  guestData: {
    _id: string;
    label: string;
    value:
      | string
      | number
      | {
          district: string;
          municipality: string;
          tole: string;
          country: string;
        };
  }[];
}

export const Guest = ({ guestData }: GuestProps) => {
  return (
    <div className="w-full flex flex-col p-5 gap-3 bg-white min-h-[25rem] h-full shadow-sm rounded-md">
      <h1 className="font-semibold text-black">Guest Information</h1>
      <div className="grid lg:grid-cols-3 md:grid-cols-2 gap-y-8">
        {guestData?.map((item) => (
          <div key={item._id} className="flex p-4 flex-col">
            <h3 className="font-medium text-black text-medium">{item.label}</h3>
            <p className="text-sm font-light text-black">
              {typeof item.value === "object" && item.value !== null
                ? `${(item.value as any).tole || "N/A"}, ${
                    (item.value as any).municipality || "N/A"
                  }, ${(item.value as any).district || "N/A"}, ${
                    (item.value as any).country || "N/A"
                  }`
                : String(item.value)}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
};

// Fixed: Updated interface to match actual prop structure
interface RoomsProps {
  roomData: {
    _id: string;
    label: string;
    value:
      | string
      | number
      | {
          district: string;
          municipality: string;
          tole: string;
          country: string;
        };
  }[];
}

export const Rooms = ({ roomData }: RoomsProps) => {
  return (
    <div className="w-full flex flex-col p-5 gap-3 bg-white min-h-[25rem] h-full shadow-sm rounded-md">
      <h1 className="font-bold text-black">Room Information</h1>
      <div className="grid lg:grid-cols-3 md:grid-cols-2 gap-y-8">
        {roomData.map((item) => (
          <div key={item._id} className="flex flex-col">
            <h3 className="font-medium text-black text-medium">{item.label}</h3>
            <p className="text-sm font-light text-black">
              {typeof item.value === "object" && item.value !== null
                ? String(JSON.stringify(item.value))
                : String(item.value)}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
};

// Print Folio Component
const PrintFolio = ({
  booking,
  packageType,
  onClose,
}: {
  booking: IBooking;
  packageType: string;
  onClose: () => void;
}) => {
  const handlePrint = () => {
    window.print();
  };

  return (
    <div className="w-full max-w-4xl bg-white p-8">
      <div className="flex justify-between items-center mb-6 border-b pb-4">
        <h1 className="text-2xl font-bold">Hotel Folio</h1>
        <div className="flex gap-2">
          <button
            onClick={handlePrint}
            className="bg-blue-500 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-600"
          >
            Print
          </button>
          <button
            onClick={onClose}
            className="bg-gray-200 px-4 py-2 rounded-md text-sm hover:bg-gray-300"
          >
            Close
          </button>
        </div>
      </div>

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Booking Information</h2>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p>
              <span className="font-medium">Booking ID:</span> B-
              {get(booking, "_id", "").slice(-5)}
            </p>
            <p>
              <span className="font-medium">Guest Name:</span>{" "}
              {get(booking, "guest.name", "")}
            </p>
            <p>
              <span className="font-medium">Check-in:</span>{" "}
              {moment(get(booking, "checkIn")).format("MMM DD, YYYY")}
            </p>
            <p>
              <span className="font-medium">Check-out:</span>{" "}
              {moment(get(booking, "checkOut")).format("MMM DD, YYYY")}
            </p>
          </div>
          <div>
            <p>
              <span className="font-medium">Room:</span>{" "}
              {get(booking, "room.roomNo", "")} /{" "}
              {get(booking, "room.roomType.name", "")}
            </p>
            <p>
              <span className="font-medium">Nights:</span>{" "}
              {moment(get(booking, "checkOut")).diff(
                moment(get(booking, "checkIn")),
                "days"
              )}
            </p>
            <p>
              <span className="font-medium">Guests:</span>{" "}
              {get(booking, "pax.adults", 0)} Adults,{" "}
              {get(booking, "pax.children", 0)} Children
            </p>
            <p>
              <span className="font-medium">Package:</span>{" "}
              {packageType || "No Package"}
            </p>
            <p>
              <span className="font-medium">Address:</span>{" "}
              {typeof get(booking, "guest.permanentAddress", "") === "object"
                ? `${get(booking, "guest.permanentAddress.tole", "")}, ${get(
                    booking,
                    "guest.permanentAddress.municipality",
                    ""
                  )}, ${get(
                    booking,
                    "guest.permanentAddress.district",
                    ""
                  )}, ${get(booking, "guest.permanentAddress.country", "")}`
                : String(
                    get(booking, "guest.permanentAddress", "") ||
                      get(booking, "guest.address", "")
                  )}
            </p>
            <p>
              <span className="font-medium">Status:</span>{" "}
              {get(booking, "status", "")}
            </p>
          </div>
        </div>
      </div>

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Charges</h2>
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-100">
              <th className="border p-2 text-left">Description</th>
              <th className="border p-2 text-right">Amount</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="border p-2">
                Room Charge (
                {moment(get(booking, "checkOut")).diff(
                  moment(get(booking, "checkIn")),
                  "days"
                )}{" "}
                nights)
              </td>
              <td className="border p-2 text-right">
                Rs. {get(booking, "amount", 0)}
              </td>
            </tr>
            <tr>
              <td className="border p-2 font-semibold">Total</td>
              <td className="border p-2 text-right font-semibold">
                Rs. {get(booking, "amount", 0)}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Payments</h2>
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-100">
              <th className="border p-2 text-left">Date</th>
              <th className="border p-2 text-left">Method</th>
              <th className="border p-2 text-right">Amount</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="border p-2">
                {moment(get(booking, "createdAt")).format("MMM DD, YYYY")}
              </td>
              <td className="border p-2">
                {get(booking, "paymentMethod", "Cash")}
              </td>
              <td className="border p-2 text-right">
                Rs. {get(booking, "amountPaid", 0)}
              </td>
            </tr>
            <tr>
              <td className="border p-2 font-semibold" colSpan={2}>
                Balance Due
              </td>
              <td className="border p-2 text-right font-semibold">
                Rs. {get(booking, "amount", 0) - get(booking, "amountPaid", 0)}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div className="text-center mt-8 pt-4 border-t">
        <p className="text-sm text-gray-600">Thank you for staying with us!</p>
        <p className="text-sm text-gray-600">
          For any inquiries, please contact <NAME_EMAIL>
        </p>
      </div>
    </div>
  );
};
