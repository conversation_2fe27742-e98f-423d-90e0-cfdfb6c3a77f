import { Field, FieldProps, FormikProps } from "formik";

interface Option {
  value: string;
  label: string;
}

interface CustomFormFieldProps {
  label?: string;
  required?: boolean;
  name: string;
  type?: string;
  placeholder?: string;
  options?: Option[];
  formik: FormikProps<any>;
  value?: string | any;
  min?: number | string;
  max?: number | string;
  readonly?: boolean;
  disabled?: boolean;
  onChange?: (e: React.ChangeEvent<any>) => void;
  multiSelect?: boolean;
  accept?: string;
  [key: string]: any;
}

const getNestedProp = (
  obj: Record<string, any> | undefined,
  path: string
): any => {
  if (!obj) return undefined;
  const parts = path
    .replace(/\[(\d+)\]/g, ".$1")
    .split(".")
    .filter(Boolean);
  return parts.reduce((acc: any, part: string) => acc && acc[part], obj);
};

export const CustomFormField: React.FC<CustomFormFieldProps> = ({
  label,
  required = false,
  name,
  type = "text",
  placeholder,
  options = [],
  formik,
  value,
  min,
  max,
  readonly,
  disabled,
  onChange,
  multiSelect = false,
  accept,
  ...rest
}) => {
  const handleChange = (e: React.ChangeEvent<any>) => {
    formik.handleChange(e);
    if (onChange) {
      onChange(e);
    }
  };

  const error = getNestedProp(formik.errors, name);
  const touched = getNestedProp(formik.touched, name);
  const hasError = touched && error;

  if (type === "date") {
    return (
      <div className="flex flex-col w-full">
        {label && (
          <label htmlFor={name} className="mb-1 text-sm flex gap-1">
            {label} {required && <p className="text-red-600">*</p>}
          </label>
        )}
        <input
          id={name}
          name={name}
          type={type}
          value={value ?? formik.values[name] ?? ""}
          onChange={handleChange}
          onBlur={formik.handleBlur}
          className={`p-2 border rounded-md focus:outline-none focus:border-gray-400 ${
            hasError ? "border-red-500" : "border-gray-300"
          }`}
          placeholder={placeholder || label}
          min={min}
          max={max}
          readOnly={readonly}
          disabled={disabled}
          {...rest}
        />
        {hasError && <div className="mt-1 text-xs text-red-600">{error}</div>}
      </div>
    );
  }

  if (type === "dropdown" && multiSelect) {
    return (
      <Field name={name}>
        {({ field, form }: FieldProps) => {
          const selectedValues = field.value || [];
          return (
            <div className="flex flex-col w-full">
              {label && (
                <label htmlFor={name} className="mb-1 text-sm flex gap-1">
                  {label} {required && <p className="text-red-600">*</p>}
                </label>
              )}
              <div
                className={`flex flex-wrap gap-2 p-2 border rounded-lg bg-gray-50 min-h-12 ${
                  hasError ? "border-red-500" : "border-gray-300"
                }`}
              >
                {selectedValues.map((value: string, index: number) => {
                  const option = options.find((opt) => opt.value === value);
                  return (
                    <div
                      key={index}
                      className="flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 rounded-md"
                    >
                      {option?.label || value}
                      <button
                        type="button"
                        onClick={() => {
                          const newValues: string[] = selectedValues.filter(
                            (_: string, i: number) => i !== index
                          );
                          form.setFieldValue(name, newValues);
                          form.setFieldTouched(name, true);
                        }}
                        disabled={disabled}
                        className="text-blue-600 hover:text-blue-800"
                      >
                        ×
                      </button>
                    </div>
                  );
                })}
                <select
                  className="flex-1 min-w-[150px] bg-transparent border-none focus:outline-none text-sm"
                  value=""
                  onChange={(e) => {
                    const val = e.target.value;
                    if (val && !selectedValues.includes(val)) {
                      const newValues = [...selectedValues, val];
                      form.setFieldValue(name, newValues);
                      form.setFieldTouched(name, true);
                    }
                  }}
                  disabled={disabled}
                >
                  <option value="" disabled>
                    {placeholder || "Select an option"}
                  </option>
                  {options.map((opt) => (
                    <option
                      key={opt.value}
                      value={opt.value}
                      disabled={selectedValues.includes(opt.value)}
                    >
                      {opt.label}
                    </option>
                  ))}
                </select>
              </div>
              {hasError && (
                <div className="mt-1 text-xs text-red-600">{error}</div>
              )}
            </div>
          );
        }}
      </Field>
    );
  }

  if (type === "dropdown" && !multiSelect) {
    return (
      <div className="flex flex-col w-full">
        {label && (
          <label htmlFor={name} className="mb-1 text-sm flex gap-1">
            {label} {required && <p className="text-red-600">*</p>}
          </label>
        )}
        <select
          id={name}
          name={name}
          value={value ?? formik.values[name] ?? ""}
          onChange={handleChange}
          onBlur={formik.handleBlur}
          className={`p-2 border rounded-md focus:outline-none focus:border-gray-400 ${
            hasError ? "border-red-500" : "border-gray-300"
          }`}
          disabled={disabled}
          {...rest}
        >
          <option value="" disabled>
            {placeholder || "Select an option"}
          </option>
          {options.map((opt) => (
            <option key={opt.value} value={opt.value}>
              {opt.label}
            </option>
          ))}
        </select>
        {hasError && <div className="mt-1 text-xs text-red-600">{error}</div>}
      </div>
    );
  }

  if (type === "file") {
    return (
      <Field name={name}>
        {({ field, form }: FieldProps) => (
          <div className="flex flex-col w-full">
            {label && (
              <label htmlFor={name} className="mb-1 text-sm flex gap-1">
                {label} {required && <p className="text-red-600">*</p>}
              </label>
            )}
            <div className="flex items-center space-x-2">
              <label
                htmlFor={name}
                className="flex items-center p-2 text-white bg-indigo-500 cursor-pointer rounded-l-md"
              >
                Choose File
              </label>
              <input
                id={name}
                name={name}
                type="file"
                accept={accept}
                onChange={(e) => {
                  const file = e.currentTarget.files?.[0];
                  form.setFieldValue(name, file || null);
                  if (onChange) onChange(e);
                }}
                onBlur={formik.handleBlur}
                className="hidden"
                disabled={disabled}
                {...rest}
              />
              <span className="text-sm text-gray-500">
                {formik.values[name]
                  ? formik.values[name] instanceof File
                    ? formik.values[name].name
                    : formik.values[name]
                  : "No File Chosen"}
              </span>
            </div>
            {hasError && (
              <div className="mt-1 text-xs text-red-600">{error}</div>
            )}
          </div>
        )}
      </Field>
    );
  }

  if (type === "textarea") {
    return (
      <div className="flex flex-col w-full">
        {label && (
          <label htmlFor={name} className="mb-1 text-sm flex gap-1">
            {label} {required && <p className="text-red-600">*</p>}
          </label>
        )}
        <textarea
          id={name}
          name={name}
          placeholder={placeholder}
          value={value ?? formik.values[name] ?? ""}
          onChange={handleChange}
          onBlur={formik.handleBlur}
          className={`p-2 border rounded-md h-24 resize-none focus:outline-none focus:border-gray-400 ${
            hasError ? "border-red-500" : "border-gray-300"
          }`}
          rows={4}
          readOnly={readonly}
          disabled={disabled}
          {...rest}
        />
        {hasError && <div className="mt-1 text-xs text-red-600">{error}</div>}
      </div>
    );
  }

  if (type === "checkbox") {
    return (
      <div className="flex items-center">
        {label && (
          <label htmlFor={name} className="mr-2 text-sm flex gap-1">
            {label} {required && <p className="text-red-600">*</p>}
          </label>
        )}
        <input
          id={name}
          name={name}
          type="checkbox"
          checked={formik.values[name] || false}
          onChange={handleChange}
          onBlur={formik.handleBlur}
          className={`w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 ${
            hasError ? "border-red-500" : ""
          }`}
          disabled={disabled}
          {...rest}
        />
        {hasError && <div className="ml-2 text-xs text-red-600">{error}</div>}
      </div>
    );
  }

  if (type === "number") {
    return (
      <div className="flex flex-col w-full">
        {label && (
          <label htmlFor={name} className="mb-1 text-sm flex gap-1">
            {label} {required && <p className="text-red-600">*</p>}
          </label>
        )}
        <input
          id={name}
          name={name}
          type={type}
          value={value ?? formik.values[name] ?? ""}
          onChange={handleChange}
          onBlur={formik.handleBlur}
          className={`p-2 border rounded-md focus:outline-none focus:border-gray-400 ${
            hasError ? "border-red-500" : "border-gray-300"
          } ${disabled ? "bg-gray-100 cursor-not-allowed" : "bg-white"}`}
          placeholder={placeholder || label}
          min={min}
          max={max}
          readOnly={readonly}
          disabled={disabled}
          {...rest}
        />
        {hasError && <div className="mt-1 text-xs text-red-600">{error}</div>}
      </div>
    );
  }

  // Default input field for other types (e.g., text, email)
  return (
    <div className="flex flex-col w-full">
      {label && (
        <label htmlFor={name} className="mb-1 text-sm flex gap-1">
          {label} {required && <p className="text-red-600">*</p>}
        </label>
      )}
      <input
        id={name}
        name={name}
        type={type}
        value={value ?? formik.values[name] ?? ""}
        onChange={handleChange}
        onBlur={formik.handleBlur}
        className={`p-2 border rounded-md focus:outline-none focus:border-gray-400 ${
          hasError ? "border-red-500" : "border-gray-300"
        } ${disabled ? "bg-gray-100 cursor-not-allowed" : "bg-white"}`}
        placeholder={placeholder || label}
        min={min}
        max={max}
        readOnly={readonly}
        disabled={disabled}
        {...rest}
      />
      {hasError && <div className="mt-1 text-xs text-red-600">{error}</div>}
    </div>
  );
};
