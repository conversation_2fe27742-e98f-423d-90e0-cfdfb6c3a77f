export const getGuestFormData = (users: any[]) => {
  const userOptions = [
    { value: "new", label: "New Guest" },
    ...(users?.map((user: any) => ({
      value: user._id,
      label: `${user.name} ${user.phoneNumber ? `(${user.phoneNumber})` : ""}`,
    })) || []),
  ];

  const countryOptions = [
    { value: "", label: "Select" },
    { value: "Nepal", label: "Nepal" },
    // Add more countries as needed
  ];

  const genderOptions = [
    { value: "", label: "Select" },
    { value: "male", label: "Male" },
    { value: "female", label: "Female" },
    { value: "other", label: "Other" },
  ];

  const identityTypeOptions = [
    { value: "", label: "Select Identity Type" },
    { value: "driving_license", label: "Driving License" },
    { value: "national_identity", label: "National Identity" },
    { value: "passport", label: "Passport" },
    { value: "citizenship", label: "Citizenship" },
    { value: "other", label: "Other" },
  ];

  return [
    // Customer Details
    {
      label: "Guest Selection",
      field: "guest",
      type: "select",
      options: userOptions,
      required: true,
      onChange: (value: string, setFieldValue: any) => {
        if (value !== "new") {
          const selectedUser = users.find((user: any) => user._id === value);
          if (selectedUser) {
            setFieldValue("name", selectedUser.name);
            setFieldValue("phoneNumber", selectedUser.phoneNumber);
            setFieldValue(
              "DOB",
              selectedUser.DOB
                ? new Date(selectedUser.DOB).toISOString().split("T")[0]
                : ""
            );
            setFieldValue("gender", selectedUser.gender);
            setFieldValue("email", selectedUser.email);
            setFieldValue(
              "tempAddress.country",
              selectedUser.tempAddress.country
            );
            setFieldValue(
              "address",
              `${selectedUser.tempAddress.district}, ${selectedUser.tempAddress.municipality}, ${selectedUser.tempAddress.tole}`
            );
          }
        } else {
          setFieldValue("name", "");
          setFieldValue("phoneNumber", "");
          setFieldValue("DOB", "");
          setFieldValue("gender", "");
          setFieldValue("email", "");
          setFieldValue("tempAddress.country", "");
          setFieldValue("address", "");
        }
      },
    },
    {
      label: "Guest Name",
      field: "name",
      type: "text",
      required: true,
      disabled: (values: any) => values.guest !== "new",
    },
    {
      label: "Mobile No.",
      field: "phoneNumber",
      type: "text",
      required: true,
      disabled: (values: any) => values.guest !== "new",
    },
    {
      label: "Date of Birth",
      field: "DOB",
      type: "date",
      placeholder: "mm/dd/yyyy",
      disabled: (values: any) => values.guest !== "new",
    },
    {
      label: "Gender",
      field: "gender",
      type: "select",
      options: genderOptions,
      required: true,
      disabled: (values: any) => values.guest !== "new",
    },
    {
      label: "Email",
      field: "email",
      type: "email",
      placeholder: "<EMAIL>",
      required: true,
      disabled: (values: any) => values.guest !== "new",
    },
    {
      label: "Country",
      field: "tempAddress.country",
      type: "select",
      options: countryOptions,
      required: true,
      disabled: (values: any) => values.guest !== "new",
    },
    {
      label: "Address",
      field: "address",
      type: "text",
      placeholder: "Address",
      disabled: (values: any) => values.guest !== "new",
    },
    {
      label: "Nationality",
      field: "tempAddress.country", // Reusing country field
      type: "select",
      options: countryOptions,
      required: true,
      disabled: (values: any) => values.guest !== "new",
    },
    {
      label: "Purpose of Visit",
      field: "purposeOfVisit",
      type: "text",
      placeholder: "Purpose of Visit",
    },
    // Identity Details
    {
      label: "Identity Type",
      field: "documents[0].IdentityType",
      type: "select",
      options: identityTypeOptions,
    },
    {
      label: "ID No.",
      field: "documents[0].IdNo",
      type: "text",
      placeholder: "ID No.",
    },
    {
      label: "Upload Photo",
      field: "documents[0].images",
      type: "file",
      accept: "image/*",
    },
  ];
};
