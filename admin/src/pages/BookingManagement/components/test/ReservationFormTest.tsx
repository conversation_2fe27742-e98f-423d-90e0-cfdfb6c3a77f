import React from "react";
import { Form, FormikProvider, useFormik } from "formik";
import * as Yup from "yup";

import { useGetAllRoomType } from "../../../../server-action/API/Room/room-type";
import { useGetBeds } from "../../../../server-action/API/HotelConfiguration/bed";
import { useGetAllActivities } from "../../../../server-action/API/activity";
import { useGetAllService } from "../../../../server-action/API/HotelConfiguration/services";
import { useGetAllPackages } from "../../../../server-action/API/HotelConfiguration/roompackage";

import { getReservationFormData } from "./ReserVationFormDataTest";
import {
  useCreateBooking,
  useUpdateBooking,
} from "../../../../server-action/API/BookingManagement/BookingManagement";
import { GlobalForm } from "../../../../components/GlobalForm/GlobalFormComponent";
import { ActionButton } from "../../../../components/ActionButton";

interface ReservationFormProps {
  onClose: () => void;
  editData: any | null;
}

const ReservationValidationSchema = Yup.object().shape({
  guestName: Yup.string().required("Guest name is required"),
  roomType: Yup.string().required("Room type is required"),
  bedType: Yup.string().required("Bed type is required"),
  service: Yup.array().min(1, "At least one service is required"),
  activity: Yup.array().min(1, "At least one activity is required"),
  package: Yup.string().required("Package is required"),
  checkIn: Yup.date().required("Check-in date is required"),
  checkOut: Yup.date()
    .required("Check-out date is required")
    .min(Yup.ref("checkIn"), "Check-out must be after check-in"),
});

const ReservationFormTest = ({ onClose, editData }: ReservationFormProps) => {
  const { data: roomTypes } = useGetAllRoomType();
  const { data: bedTypes } = useGetBeds();
  const { data: services } = useGetAllService();
  const { data: activities } = useGetAllActivities();
  const { data: packages } = useGetAllPackages();

  const { mutateAsync: createReservation } = useCreateBooking();
  const { mutateAsync: updateReservation } = useUpdateBooking();

  const ReservationFormData = getReservationFormData(
    roomTypes ?? [],
    bedTypes ?? [],
    services ?? [],
    activities ?? [],
    packages ?? []
  );

  const formik = useFormik({
    initialValues: {
      guestName: editData?.guestName || "",
      roomType: editData?.roomType?._id || "",
      bedType: editData?.bedType?._id || "",
      service: editData?.service?.map((s: any) => s._id) || [],
      activity: editData?.activity?.map((a: any) => a._id) || [],
      package: editData?.package?._id || "",
      checkIn: editData?.checkIn || "",
      checkOut: editData?.checkOut || "",
    },
    enableReinitialize: true,
    validationSchema: ReservationValidationSchema,
    onSubmit: async (values) => {
      console.log(values, "Reservation data");
      if (editData) {
        await updateReservation({ bookingData: values, _id: editData._id });
      } else {
        await createReservation(values as any);
      }
      onClose();
    },
  });

  const { handleSubmit, getFieldProps } = formik;

  return (
    <FormikProvider value={formik}>
      <Form onSubmit={handleSubmit}>
        <div className="grid grid-cols-4 gap-2">
          <GlobalForm
            formDatails={ReservationFormData as any}
            getFieldProps={getFieldProps}
          />
        </div>
        <ActionButton onCancel={onClose} onSubmit={handleSubmit} />
      </Form>
    </FormikProvider>
  );
};

export default ReservationFormTest;
