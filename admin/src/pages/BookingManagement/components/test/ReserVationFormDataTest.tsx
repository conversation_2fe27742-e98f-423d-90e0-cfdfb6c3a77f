export const getReservationFormData = (
  roomTypes: any[],
  bedTypes: any[],
  activities: any[],
  services: any[],
  roompackage: any[]
) => {
  const packageOptions = [
    { value: "", label: "Select Package" },
    ...(roompackage?.map((pack: any) => ({
      value: pack._id,
      label: pack.name,
    })) || []),
  ];

  const bedOptions = [
    { value: "", label: "Select Bed Category" },
    ...(bedTypes.map((bed: any) => ({
      value: bed._id,
      label: bed.name,
    })) || []),
  ];

  const roomTypeOptions = [
    { value: "", label: "Select Room Category" },
    ...(roomTypes.map((roomType: any) => ({
      value: roomType._id,
      label: roomType.name,
    })) || []),
  ];

  const acOptions = [
    { value: "none", label: "None" },
    { value: "central", label: "Central" },
    { value: "split", label: "Split" },
    { value: "window", label: "Window" },
  ];

  const activityOptions = [
    { value: "", label: "Select Activities" },
    ...(activities.map((activity: any) => ({
      value: activity._id,
      label: activity.name,
    })) || []),
  ];

  const serviceOptions = [
    { value: "", label: "Select Services" },
    ...(services.map((service: any) => ({
      value: service._id,
      label: service.name,
    })) || []),
  ];

  return [
    // Reservation Details

    {
      label: "Check In",
      field: "checkin",
      type: "date",
      required: true,
    },
    {
      label: "Check Out",
      field: "checkout",
      type: "date",
      required: true,
    },
    {
      label: "Expected Check Out",
      field: "expectedCheckout",
      type: "date",
    },
    {
      label: "Reservation Date",
      field: "reservationDate",
      type: "date",
      default: new Date().toISOString().split("T")[0],
    },
    {
      label: "Package Type",
      field: "packageType",
      type: "select",
      options: packageOptions,
    },
    {
      label: "A/C Type",
      field: "ac",
      type: "select",
      options: acOptions,
    },
    {
      label: "Bed Category",
      field: "bedCategory",
      type: "select",
      options: bedOptions,
    },
    {
      label: "Room Category",
      field: "roomCategory",
      type: "select",
      options: roomTypeOptions,
    },
    {
      label: "Room No.",
      field: "roomNo",
      type: "select",
      options: [], // Will be populated dynamically
      required: true,
    },
    {
      label: "Special Requests",
      field: "specialRequests",
      type: "textarea",
      placeholder: "Enter any special requests",
    },
    {
      label: "Booked Activities",
      field: "bookedActivities",
      type: "multi-select",
      options: activityOptions,
    },
    {
      label: "Used Services",
      field: "usedServices",
      type: "multi-select",
      options: serviceOptions,
    },
    // Pax Details
    {
      label: "Adults",
      field: "pax.adults",
      type: "number",
      min: 1,
      max: 10,
      required: true,
    },
    {
      label: "Children",
      field: "pax.children",
      type: "number",
      min: 0,
      max: 9,
    },
    {
      label: "Infants",
      field: "pax.infants",
      type: "number",
      min: 0,
      max: 5,
    },
    // Payment Details
    {
      label: "Payment Type",
      field: "paymentType",
      type: "select",
      options: [
        { value: "partial", label: "Partial" },
        { value: "advance", label: "Advance" },
        { value: "final", label: "Final" },
      ],
      required: true,
    },
    {
      label: "Payment Method",
      field: "paymentMethod",
      type: "select",
      options: [
        { value: "cash", label: "Cash" },
        { value: "online", label: "Digital Wallet" },
        { value: "bank", label: "Bank Transfer" },
      ],
      required: true,
    },
    {
      label: "Membership Discount (%)",
      field: "membershipDiscount",
      type: "number",
      min: 0,
      max: 100,
    },
    {
      label: "Membership Discount Amount",
      field: "membershipDiscountAmount",
      type: "number",
      readOnly: true,
    },
    {
      label: "Original Amount",
      field: "originalAmount",
      type: "number",
      readOnly: true,
    },
    {
      label: "Total Amount",
      field: "totalAmount",
      type: "number",
      readOnly: true,
    },
    {
      label: "Paid Amount",
      field: "paidAmount",
      type: "number",
      min: 0,
    },
    // Additional Details
    {
      label: "Booking Status",
      field: "status",
      type: "select",
      options: [
        { value: "pending", label: "Pending" },
        { value: "confirmed", label: "Confirmed" },
        { value: "checked-in", label: "Checked In" },
        { value: "checked-out", label: "Checked Out" },
        { value: "cancelled", label: "Cancelled" },
        { value: "no-show", label: "No Show" },
      ],
      default: "pending",
    },
    {
      label: "Is Confirmed",
      field: "isConfirmed",
      type: "checkbox",
      default: true,
    },
    {
      label: "Cancellation Date",
      field: "cancellationDate",
      type: "date",
      disabled: (values: any) => values.status !== "cancelled",
    },
    {
      label: "Cancellation Reason",
      field: "cancellationReason",
      type: "textarea",
      disabled: (values: any) => values.status !== "cancelled",
    },
  ];
};
