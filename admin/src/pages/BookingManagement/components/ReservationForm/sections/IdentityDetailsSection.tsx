"use client";

import type React from "react";
import { FieldArray } from "formik";
import type { FormSectionProps } from "../types";

const IdentityDetailsSection: React.FC<FormSectionProps> = ({
  formik,
  getFieldProps,
}) => {
  return (
    <div className="p-4 mt-5 mb-6 border rounded-md">
      <h2 className="pb-2 mb-3 text-lg font-semibold border-b">
        Identity Details
      </h2>

      <div className="mb-4 p-3 bg-blue-50 border-l-4 border-blue-500">
        <p className="text-sm font-medium text-black">
          Optional: Identity Details
        </p>
        <p className="text-xs mt-1 text-black">
          You can add identity documents if available, but they are not
          required.
        </p>
      </div>

      <FieldArray name="documents">
        {({ push, remove }) => (
          <div className="space-y-4">
            {formik.values.documents && formik.values.documents.length > 0 ? (
              formik.values.documents.map((_: any, index: number) => (
                <div key={index} className="p-4 border rounded-lg">
                  <div className="grid grid-cols-3 gap-4 mb-4">
                    <div className="col-span-1">
                      <label className="block mb-1 text-sm font-medium">
                        Identity Type
                      </label>
                      <select
                        {...getFieldProps(`documents[${index}].IdentityType`)}
                        className="w-full p-2 border rounded"
                      >
                        <option value="">Select ID Type</option>
                        <option value="passport">Passport</option>
                        <option value="drivingLicense">Driving License</option>
                        <option value="nationalId">National ID</option>
                        <option value="other">Other</option>
                      </select>
                    </div>

                    <div className="col-span-1">
                      <label className="block mb-1 text-sm font-medium">
                        ID Number
                      </label>
                      <input
                        type="text"
                        {...getFieldProps(`documents[${index}].IdNo`)}
                        className="w-full p-2 border rounded"
                        placeholder="Enter ID number"
                      />
                    </div>

                    <div className="col-span-1">
                      <label className="block mb-1 text-sm font-medium">
                        Upload Images
                      </label>
                      <input
                        type="file"
                        multiple
                        onChange={(event) => {
                          const files = event.currentTarget.files;
                          if (files) {
                            const newImages = [
                              ...(formik.values.documents[index]?.images || []),
                            ];
                            for (let i = 0; i < files.length; i++) {
                              newImages.push(files[i]);
                            }
                            formik.setFieldValue(
                              `documents[${index}].images`,
                              newImages
                            );
                          }
                        }}
                        className="w-full p-2 border rounded"
                      />
                    </div>
                  </div>

                  {formik.values.documents[index]?.images?.length > 0 && (
                    <div className="mt-2">
                      <p className="mb-2 text-sm font-medium">
                        Uploaded Files:
                      </p>
                      <div className="flex flex-wrap gap-2">
                        {formik.values.documents[index].images.map(
                          (image: any, imgIndex: number) => (
                            <div key={imgIndex} className="relative">
                              <div className="p-2 text-xs bg-gray-100 rounded">
                                {image.name || `File ${imgIndex + 1}`}
                                <button
                                  type="button"
                                  className="ml-2 text-red-500"
                                  onClick={() => {
                                    const newImages = [
                                      ...formik.values.documents[index].images,
                                    ];
                                    newImages.splice(imgIndex, 1);
                                    formik.setFieldValue(
                                      `documents[${index}].images`,
                                      newImages
                                    );
                                  }}
                                >
                                  ×
                                </button>
                              </div>
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  )}

                  <button
                    type="button"
                    onClick={() => remove(index)}
                    className="px-3 py-1 mt-2 text-sm text-red-600 border border-red-600 rounded"
                  >
                    Remove Document
                  </button>
                </div>
              ))
            ) : (
              <div className="p-4 text-center bg-gray-50 rounded-lg">
                <p className="text-gray-600">
                  No identity documents added yet.
                </p>
              </div>
            )}

            <button
              type="button"
              onClick={() => push({ IdentityType: "", IdNo: "", images: [] })}
              className="px-4 py-2 mt-4 bg-gray-200 rounded"
            >
              + Add Identity Document
            </button>
          </div>
        )}
      </FieldArray>
    </div>
  );
};

export default IdentityDetailsSection;
