import type React from "react";
import { GlobalForm } from "../../../../../components/GlobalForm/GlobalFormComponent";
import { PaymentDetails } from "../../ReservationFormData";
import type { FormSectionProps } from "../types";

const PaymentDetailsSection: React.FC<FormSectionProps> = ({
  formik,
  getFieldProps,
}) => {
  return (
    <div className="p-4 mt-5 mb-6 border rounded-md">
      <h2 className="pb-2 mb-3 text-lg font-semibold border-b">
        Payment Details
      </h2>
      <div className="grid grid-cols-3 gap-4">
        <GlobalForm
          formDatails={PaymentDetails as any}
          getFieldProps={getFieldProps}
          formik={formik}
        />
      </div>
    </div>
  );
};

export default PaymentDetailsSection;
