import type React from "react";
import { GlobalForm } from "../../../../../components/GlobalForm/GlobalFormComponent";
import CustomerTabs from "../CustomerTabs/CustomerTabs";
import { CustomerDetails } from "../../ReservationFormData";
import type { FormSectionProps } from "../types";
import type { IUser } from "../../../../../Interface/user.interface";
import moment from "moment";
import { get } from "lodash";

interface CustomerDetailsSectionProps extends FormSectionProps {
  isEditMode: boolean;
  onGuestCreated?: (guestId: string, guestData: any) => void;
}

const CustomerDetailsSection: React.FC<CustomerDetailsSectionProps> = ({
  formik,
  getFieldProps,
  isEditMode,
  onGuestCreated,
}) => {
  const handleTabChange = (isExisting: boolean) => {
    formik.setFieldValue("isExistingGuest", isExisting);
    if (!isExisting) {
      formik.setFieldValue("existingGuestId", "");
      const currentValues = { ...formik.values };
      const fieldsToReset = [
        "name",
        "mobileNo",
        "dob",
        "gender",
        "email",
        "permanentAddress.country",
        "tempAddress.district",
      ];
      if (currentValues.existingGuestId) {
        fieldsToReset.forEach((field) => {
          formik.setFieldValue(field, "");
        });
      }
    }
    formik.setErrors({});
  };

  const handleSelectExistingGuest = (guest: IUser) => {
    if (guest) {
      formik.setValues({
        ...formik.values,
        existingGuestId: guest._id,
        name: guest.name || "",
        guestName: guest.name || "",
        mobileNo: guest.phoneNumber || "",
        dob: guest.DOB ? moment(guest.DOB).format("YYYY-MM-DD") : "",
        gender: guest.gender || "",
        email: guest.email || "",
        "permanentAddress.country": guest.permanentAddress?.country || "",
        "tempAddress.district": guest.tempAddress?.district || "",
      });
      formik.setFieldTouched("existingGuestId", true);
      formik.setFieldTouched("name", true);
    }
  };

  return (
    <div className="p-4 mt-5 mb-6 border rounded-md">
      <h2 className="pb-2 mb-3 text-lg font-semibold border-b">
        Customer Details
      </h2>

      {isEditMode ? (
        <>
          <CustomerTabs
            isExistingGuest={true}
            onTabChange={() => {}}
            onSelectExistingGuest={() => {}}
            formik={formik}
            isEditMode={true}
          />
          <div className="mt-4 p-4 border rounded-md bg-blue-50">
            <h3 className="font-medium text-blue-800 mb-3">
              Guest Count Information
            </h3>
            <p className="text-sm text-blue-700 mb-3">
              Please specify the number of adults and children staying in this
              room.
            </p>
            <div className="grid grid-cols-3 gap-4 mt-2">
              {CustomerDetails.filter(
                (field) =>
                  field.field === "numberOfGuest" ||
                  field.field === "adults" ||
                  field.field === "children"
              ).map((field, index) => (
                <div key={index} className="col-span-1">
                  <GlobalForm
                    formDatails={
                      [
                        {
                          ...field,
                          disabled: field.field === "numberOfGuest",
                        },
                      ] as any
                    }
                    getFieldProps={getFieldProps}
                    formik={formik}
                  />
                </div>
              ))}
            </div>
          </div>
        </>
      ) : (
        <>
          <CustomerTabs
            isExistingGuest={formik.values.isExistingGuest}
            onTabChange={handleTabChange}
            onSelectExistingGuest={handleSelectExistingGuest}
            formik={formik}
            isEditMode={false}
            onGuestCreated={onGuestCreated}
          />

          {formik.values.isExistingGuest ? (
            <div>
              <div className="mt-4 p-4 border rounded-md bg-blue-50">
                <h3 className="font-medium text-blue-800 mb-3">
                  Guest Count Information
                </h3>
                <p className="text-sm text-blue-700 mb-3">
                  Please specify the number of adults and children staying in
                  this room.
                </p>
                <div className="grid grid-cols-3 gap-4 mt-2">
                  {CustomerDetails.filter(
                    (field) =>
                      field.field === "numberOfGuest" ||
                      field.field === "adults" ||
                      field.field === "children"
                  ).map((field, index) => (
                    <div key={index} className="col-span-1">
                      <GlobalForm
                        formDatails={[field] as any}
                        getFieldProps={getFieldProps}
                        formik={formik}
                      />
                    </div>
                  ))}
                </div>
              </div>
              {formik.values.existingGuestId && (
                <div className="mt-4 p-4 border rounded bg-gray-50">
                  <h3 className="font-medium text-gray-800 mb-2">
                    Selected Existing Customer
                  </h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">
                        Name:{" "}
                        <span className="font-medium">
                          {formik.values.name}
                        </span>
                      </p>
                      <p className="text-sm text-gray-600">
                        Phone:{" "}
                        <span className="font-medium">
                          {formik.values.mobileNo}
                        </span>
                      </p>
                      <p className="text-sm text-gray-600">
                        Email:{" "}
                        <span className="font-medium">
                          {formik.values.email || "-"}
                        </span>
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">
                        Gender:{" "}
                        <span className="font-medium">
                          {formik.values.gender}
                        </span>
                      </p>
                      <p className="text-sm text-gray-600">
                        Country:{" "}
                        <span className="font-medium">
                          {get(formik.values, "permanentAddress.country", "")}
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-3 gap-4 mt-4">
              <GlobalForm
                formDatails={CustomerDetails as any}
                getFieldProps={getFieldProps}
                formik={formik}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default CustomerDetailsSection;
