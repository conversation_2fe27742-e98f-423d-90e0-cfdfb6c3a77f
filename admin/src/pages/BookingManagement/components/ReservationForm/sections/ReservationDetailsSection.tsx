import type React from "react";
import { GlobalForm } from "../../../../../components/GlobalForm/GlobalFormComponent";
import { Reserve } from "../../ReservationFormData";
import type { FormSectionProps } from "../types";

interface ReservationDetailsSectionProps extends FormSectionProps {
  roomQuery: {
    selectedRoomCategory: string;
    ac: string;
    bedCategory: string;
  };
}

const ReservationDetailsSection: React.FC<ReservationDetailsSectionProps> = ({
  formik,
  getFieldProps,
  roomQuery,
}) => {
  const { ReservationDetails } = Reserve(
    roomQuery.selectedRoomCategory,
    roomQuery.bedCategory,
    roomQuery.ac
  );

  return (
    <div className="p-4 mt-5 mb-6 border rounded-md">
      <h2 className="pb-2 mb-3 text-lg font-semibold border-b">
        Reservation Details
      </h2>
      <div className="grid grid-cols-3 gap-4">
        <GlobalForm
          formDatails={ReservationDetails as any}
          getFieldProps={getFieldProps}
          formik={formik}
        />
      </div>
    </div>
  );
};

export default ReservationDetailsSection;
