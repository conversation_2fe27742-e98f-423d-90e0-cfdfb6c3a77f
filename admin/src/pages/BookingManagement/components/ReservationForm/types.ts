import type { IUser } from "../../../../Interface/user.interface";

export interface FormValues {
  checkin: string;
  checkout: string;
  expectedCheckout: string;
  reservationDate: string;
  isExistingGuest: boolean;
  existingGuestId: string;
  packageType: string;
  ac: string;
  bedCategory: string;
  roomCategory: string;
  roomNo: string;
  name: string;
  guestName: string;
  mobileNo: string;
  dob: string;
  gender: string;
  email: string;
  permanentAddress: {
    country: string;
  };
  tempAddress: {
    district: string;
  };
  numberOfGuest: number;
  adults: number;
  children: number;
  documents: Array<{
    IdentityType: string;
    IdNo: string;
    images: File[];
  }>;
  paymentType: string;
  totalAmount: number;
  paidAmount: number;
  paymentMethod: string;
  status: string;
}

export interface BookingConflictErrorType {
  message: string;
  conflictingBookingId: string | null;
}

export interface CustomerTabsProps {
  isExistingGuest: boolean;
  onTabChange: (isExisting: boolean) => void;
  onSelectExistingGuest: (guest: IUser) => void;
  formik: any;
  isEditMode?: boolean;
  onGuestCreated?: (guestId: string, guestData: any) => void;
}

export interface FormSectionProps {
  formik: any;
  getFieldProps: any;
}
