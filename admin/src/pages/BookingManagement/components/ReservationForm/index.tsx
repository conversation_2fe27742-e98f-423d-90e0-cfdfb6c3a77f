"use client";

import type React from "react";
import { useEffect, useState } from "react";
import { useFormik } from "formik";
import { useParams, useNavigate } from "react-router-dom";
import { useStore } from "@tanstack/react-store";
import { reservationStore } from "../../../../store/reservation";
import {
  useCreateBooking,
  useUpdateBooking,
  useGetBookingById,
} from "../../../../server-action/API/BookingManagement/BookingManagement";
import { useGetUserById } from "../../../../server-action/API/user";
import { useGetRoomById } from "../../../../server-action/API/HotelConfiguration/room";
import { useCreateGuest } from "../../../../server-action/API/Guest/guest";
import { getBookingValidationSchema } from "../ReservationValidationSchema";
import { FormikProvider, Form } from "formik";
import { ActionButton } from "../../../../components/ActionButton";
import { FrontendRoutes } from "../../../../routes";
import { toast } from "react-toastify";

// Import sections
import ReservationDetailsSection from "./sections/ReservationDetailsSection";
import CustomerDetailsSection from "./sections/CustomerDetailsSection";
import IdentityDetailsSection from "./sections/IdentityDetailsSection";
import PaymentDetailsSection from "./sections/PaymentDetailsSection";
import BookingConflictError from "./ReservationConflicterror";

// Import hooks and utilities
import { useFormSubmission } from "./hooks/useFormSubmission";
import { usePriceCalculation } from "./hooks/usePriceCalculaton";
import {
  getAddModeInitialValues,
  getEditModeInitialValues,
  updateBookingStatus,
} from "./utils";
import {
  Reserve,
  CustomerDetails,
  IdentityDetails,
  PaymentDetails,
} from "./../ReservationFormData";
import type { IUser } from "../../../../Interface/user.interface";
import moment from "moment";

const ReservationForm: React.FC = () => {
  const { id, bookingId: urlBookingId } = useParams();
  const navigate = useNavigate();
  const editData = useStore(reservationStore, (state) => state.bookingData);

  // State management
  const [roomQuery, setRoomQuery] = useState({
    selectedRoomCategory: "",
    ac: "",
    bedCategory: "",
  });
  const [selectedUserId, setSelectedUserId] = useState<string>("");
  const [selectedRoomId, setSelectedRoomId] = useState<string>("");
  const [bookingId, setBookingId] = useState<string | null>(null);
  const [isLoadingBookingData, setIsLoadingBookingData] =
    useState<boolean>(false);

  // API hooks
  const { data: fetchedBookingData, isLoading: isLoadingBooking } =
    useGetBookingById(id === "edit" && !editData && bookingId ? bookingId : "");

  const { data: selectedUserData = {} as IUser } =
    useGetUserById(selectedUserId);
  const { data: selectedRoomData } = useGetRoomById(selectedRoomId);

  const {
    mutateAsync: createBooking,
    isPending: isCreating,
    isSuccess: isCreateSuccess,
  } = useCreateBooking();

  const {
    mutateAsync: updateBooking,
    isPending: isUpdating,
    isSuccess: isUpdateSuccess,
  } = useUpdateBooking();

  const { mutateAsync: createGuest, isPending: isCreatingGuest } =
    useCreateGuest();

  const isPending = isCreating || isUpdating || isCreatingGuest;
  const isSuccess = isCreateSuccess || isUpdateSuccess;

  // Form sections configuration
  const { ReservationDetails } = Reserve(
    roomQuery.selectedRoomCategory,
    roomQuery.bedCategory,
    roomQuery.ac
  );

  const formSections = [
    { id: 1, title: "Reservation Details", fields: ReservationDetails },
    { id: 2, title: "Customer Details", fields: CustomerDetails },
    ...(id !== "edit"
      ? [
          { id: 3, title: "Identity Details", fields: IdentityDetails },
          { id: 4, title: "Payment Details", fields: PaymentDetails },
        ]
      : []),
  ];

  // Custom hooks
  const { calculatePrice, membershipDiscount, isCalculatingPrice } =
    usePriceCalculation();
  const {
    handleSubmit,
    submissionError,
    bookingConflictError,
    setBookingConflictError,
  } = useFormSubmission(
    createBooking,
    updateBooking,
    createGuest,
    editData,
    id ?? ""
  );

  // Formik setup
  const formik = useFormik({
    initialValues:
      id === "add"
        ? getAddModeInitialValues(formSections)
        : getEditModeInitialValues(editData),
    enableReinitialize: true,
    validationSchema: getBookingValidationSchema(id === "edit"),
    onSubmit: handleSubmit,
  });

  // Effects for guest count synchronization
  useEffect(() => {
    const adults = Math.max(0, Number(formik.values.adults) || 0);
    const children = Math.max(0, Number(formik.values.children) || 0);
    const currentTotal = Number(formik.values.numberOfGuest) || 0;
    const calculatedTotal = adults + children;

    if (calculatedTotal !== currentTotal) {
      formik.setFieldValue("numberOfGuest", calculatedTotal, false);
      formik.validateField("adults");
      formik.validateField("children");
      formik.validateField("numberOfGuest");
    }
  }, [formik.values.adults, formik.values.children]);

  // Effects for guest count constraints
  useEffect(() => {
    if (Number(formik.values.adults) < 1 && formik.values.adults !== "") {
      formik.setFieldValue("adults", 1, false);
    }

    if (Number(formik.values.children) < 0 && formik.values.children !== "") {
      formik.setFieldValue("children", 0, false);
    }

    const adults = Number(formik.values.adults) || 0;
    const children = Number(formik.values.children) || 0;
    if (adults + children > 10) {
      const newChildren = Math.max(0, 10 - adults);
      if (newChildren !== children) {
        formik.setFieldValue("children", newChildren, false);
      }
    }
  }, [formik.values.adults, formik.values.children]);

  // Effects for price calculation and date validation
  useEffect(() => {
    if (
      formik.values.roomNo &&
      (formik.values.checkin || formik.values.reservationDate) &&
      (formik.values.checkout || formik.values.expectedCheckout)
    ) {
      calculatePrice(formik);
    }

    // Date validation
    if (formik.values.checkin && formik.values.checkout) {
      const checkinDate = new Date(formik.values.checkin);
      const checkoutDate = new Date(formik.values.checkout);

      if (checkoutDate < checkinDate) {
        formik.setFieldError(
          "checkout",
          "Check-out date must be after check-in date"
        );
        formik.setFieldTouched("checkout", true, false);
      } else {
        formik.setFieldError("checkout", undefined);
      }
    }

    if (formik.values.checkin && formik.values.expectedCheckout) {
      const checkinDate = new Date(formik.values.checkin);
      const expectedCheckoutDate = new Date(formik.values.expectedCheckout);

      if (expectedCheckoutDate < checkinDate) {
        formik.setFieldError(
          "expectedCheckout",
          "Expected check-out date must be after check-in date"
        );
        formik.setFieldTouched("expectedCheckout", true, false);
      } else {
        formik.setFieldError("expectedCheckout", undefined);
      }
    }

    // Update booking status
    const newStatus = updateBookingStatus(
      formik.values.checkin || null,
      formik.values.checkout || null
    );
    formik.setFieldValue("status", newStatus);
  }, [
    formik.values.roomNo,
    formik.values.checkin,
    formik.values.checkout,
    formik.values.reservationDate,
    formik.values.expectedCheckout,
    formik.values.existingGuestId,
  ]);

  // Effects for existing guest selection
  useEffect(() => {
    if (
      formik.values.existingGuest &&
      formik.values.existingGuest !== selectedUserId
    ) {
      setSelectedUserId(formik.values.existingGuest);
    }
  }, [formik.values.existingGuest]);

  // Auto-fill guest details when existing guest is selected
  useEffect(() => {
    if (id === "edit") return;

    if (selectedUserData && Object.keys(selectedUserData).length > 0) {
      const userData = selectedUserData as IUser;

      formik.setFieldValue("guestName", userData.name || "");
      formik.setFieldValue("mobileNo", userData.phoneNumber || "");
      formik.setFieldValue(
        "dob",
        userData.DOB ? moment(userData.DOB).format("YYYY-MM-DD") : ""
      );
      formik.setFieldValue("gender", userData.gender || "");
      formik.setFieldValue("email", userData.email || "");
      formik.setFieldValue("country", userData.permanentAddress?.country || "");
      formik.setFieldValue("address", userData.tempAddress?.district || "");
    }
  }, [selectedUserData, id]);

  // Effects for room query updates
  useEffect(() => {
    if (
      formik.values.ac !== roomQuery.ac ||
      formik.values.bedCategory !== roomQuery.bedCategory ||
      formik.values.roomCategory !== roomQuery.selectedRoomCategory
    ) {
      setRoomQuery({
        ac: formik.values.ac,
        bedCategory: formik.values.bedCategory,
        selectedRoomCategory: formik.values.roomCategory,
      });
    }
  }, [formik.values.ac, formik.values.bedCategory, formik.values.roomCategory]);

  // Effects for room selection
  useEffect(() => {
    if (formik.values.roomNo && formik.values.roomNo !== selectedRoomId) {
      setSelectedRoomId(formik.values.roomNo);
    }
  }, [formik.values.roomNo]);

  // Auto-fill room details when room is selected
  useEffect(() => {
    if (selectedRoomData) {
      if (selectedRoomData.roomType?._id) {
        formik.setFieldValue("roomCategory", selectedRoomData.roomType._id);
      }

      if (selectedRoomData.features?.acType) {
        formik.setFieldValue("ac", selectedRoomData.features.acType);
      }

      if (
        selectedRoomData.beds?.types &&
        selectedRoomData.beds.types.length > 0
      ) {
        formik.setFieldValue("bedCategory", selectedRoomData.beds.types[0]._id);
      }
    }
  }, [selectedRoomData]);

  // Effects for booking data loading
  useEffect(() => {
    if (id === "edit" && !editData && urlBookingId) {
      setBookingId(urlBookingId);
    }
  }, [id, editData, urlBookingId]);

  useEffect(() => {
    setIsLoadingBookingData(isLoadingBooking);
    if (id === "edit" && !editData && fetchedBookingData?.data) {
      // Store fetched data in reservation store if needed
    }
  }, [id, editData, fetchedBookingData, isLoadingBooking]);

  useEffect(() => {
    if (isSuccess) {
      formik.resetForm();
      setTimeout(() => {
        navigate(FrontendRoutes.BOOKINGMANAGEMENT);
      }, 1500);
    }
  }, [isSuccess, navigate]);

  const { getFieldProps } = formik;

  return (
    <div className="w-full p-6 mx-auto bg-white rounded-lg shadow-md">
      {isLoadingBookingData && (
        <div className="flex items-center justify-center p-4 mb-4 bg-blue-50 border border-blue-200 rounded-md">
          <div className="w-6 h-6 mr-3 border-t-2 border-b-2 border-blue-500 rounded-full animate-spin"></div>
          <p className="text-blue-700">Loading booking data...</p>
        </div>
      )}

      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit}>
          {bookingConflictError && (
            <BookingConflictError
              message={bookingConflictError.message}
              conflictingBookingId={bookingConflictError.conflictingBookingId}
              onSelectDifferentRoom={() => {
                formik.setFieldValue("roomNo", "");
                setBookingConflictError(null);
                const roomField = document.querySelector('[name="roomNo"]');
                if (roomField) {
                  (roomField as HTMLElement).focus();
                }
              }}
              onSelectDifferentDates={() => {
                formik.setFieldValue("checkin", "");
                formik.setFieldValue("checkout", "");
                setBookingConflictError(null);
                const checkinField = document.querySelector('[name="checkin"]');
                if (checkinField) {
                  (checkinField as HTMLElement).focus();
                }
              }}
            />
          )}

          <div className="max-h-[calc(100vh-200px)] overflow-y-auto p-2">
            {formSections.map(({ title, id: sectionId }) => {
              switch (sectionId) {
                case 1:
                  return (
                    <ReservationDetailsSection
                      key={sectionId}
                      formik={formik}
                      getFieldProps={getFieldProps}
                      roomQuery={roomQuery}
                    />
                  );
                case 2:
                  return (
                    <CustomerDetailsSection
                      key={sectionId}
                      formik={formik}
                      getFieldProps={getFieldProps}
                      isEditMode={id === "edit"}
                      onGuestCreated={(guestId: string, guestData: any) => {
                        console.log(
                          "Guest created in booking form:",
                          guestId,
                          guestData
                        );
                      }}
                    />
                  );
                case 3:
                  return (
                    <IdentityDetailsSection
                      key={sectionId}
                      formik={formik}
                      getFieldProps={getFieldProps}
                    />
                  );
                case 4:
                  return (
                    <PaymentDetailsSection
                      key={sectionId}
                      formik={formik}
                      getFieldProps={getFieldProps}
                    />
                  );
                default:
                  return null;
              }
            })}
          </div>

          {submissionError && (
            <div className="p-3 mb-4 text-sm text-red bg-red-100 rounded-md font-semibold">
              <strong className="text-red">Error:</strong>{" "}
              <span className="text-red">{submissionError}</span>
            </div>
          )}

          <div className="flex justify-between mt-4">
            <button
              type="button"
              onClick={() => navigate(FrontendRoutes.BOOKINGMANAGEMENT)}
              className="px-4 py-2 text-gray-700 bg-gray-200 rounded hover:bg-gray-300"
            >
              Cancel
            </button>

            <ActionButton
              loading={isPending}
              isSubmitting={formik.isSubmitting}
              submitText={
                id === "edit"
                  ? "Update Booking"
                  : !formik.values.isExistingGuest &&
                    !formik.values.existingGuestId
                  ? "Create Guest & Booking"
                  : "Create Booking"
              }
              onCancel={() => navigate(FrontendRoutes.BOOKINGMANAGEMENT)}
              onSubmit={async () => {
                try {
                  if (id === "edit") {
                    const errors = await formik.validateForm();
                    const filteredErrors = Object.keys(errors).reduce(
                      (acc: Record<string, any>, key) => {
                        if (
                          ![
                            "guestName",
                            "mobileNo",
                            "dob",
                            "gender",
                            "email",
                            "country",
                            "address",
                            "documents",
                          ].includes(key)
                        ) {
                          acc[key] = errors[key as string];
                        }
                        return acc;
                      },
                      {} as Record<string, any>
                    );
                    formik.setErrors(filteredErrors);
                  }

                  if (
                    formik.values.isExistingGuest &&
                    formik.values.existingGuestId
                  ) {
                    if (!formik.values.permanentAddress?.country) {
                      formik.setFieldValue("permanentAddress.country", "Nepal");
                    }
                    if (!formik.values.tempAddress?.district) {
                      formik.setFieldValue(
                        "tempAddress.district",
                        "Not specified"
                      );
                    }
                  }

                  await formik.validateForm().then((errors) => {
                    if (id === "edit") {
                      const guestFields = [
                        "guestName",
                        "mobileNo",
                        "dob",
                        "gender",
                        "email",
                        "country",
                        "address",
                        "documents",
                      ];
                      Object.keys(errors).forEach((key) => {
                        if (guestFields.includes(key)) {
                          delete errors[key];
                        }
                      });
                    }

                    if (Object.keys(errors).length === 0) {
                      formik.submitForm();
                    } else {
                      console.error("Form validation failed:", errors);

                      let errorMessage =
                        "Please fix the form errors before submitting";

                      if (errors.checkout) {
                        const checkoutError =
                          typeof errors.checkout === "string"
                            ? errors.checkout
                            : "Check-out date must be after check-in date";

                        formik.setFieldError("checkout", checkoutError);
                        formik.setFieldTouched("checkout", true, false);
                        errorMessage =
                          "Please fix the form errors before submitting";
                      } else if (errors.expectedCheckout) {
                        const expectedCheckoutError =
                          typeof errors.expectedCheckout === "string"
                            ? errors.expectedCheckout
                            : "Expected check-out date must be after check-in date";

                        formik.setFieldError(
                          "expectedCheckout",
                          expectedCheckoutError
                        );
                        formik.setFieldTouched("expectedCheckout", true, false);
                        errorMessage =
                          "Please fix the form errors before submitting";
                      }

                      if (!errors.checkout && !errors.expectedCheckout) {
                        toast.error(errorMessage);
                      }

                      Object.keys(formik.values).forEach((field) => {
                        formik.setFieldTouched(field, true);
                      });

                      if (
                        formik.values.documents &&
                        Array.isArray(formik.values.documents)
                      ) {
                        formik.values.documents.forEach(
                          (_: any, index: number) => {
                            formik.setFieldTouched(
                              `documents[${index}].IdentityType`,
                              true
                            );
                            formik.setFieldTouched(
                              `documents[${index}].IdNo`,
                              true
                            );
                            formik.setFieldTouched(
                              `documents[${index}].images`,
                              true
                            );
                          }
                        );
                      }
                    }
                  });
                } catch (error) {
                  console.error("Error during form submission:", error);
                  toast.error("An error occurred while submitting the form");
                }
              }}
            />
          </div>
        </Form>
      </FormikProvider>
    </div>
  );
};

export default ReservationForm;
