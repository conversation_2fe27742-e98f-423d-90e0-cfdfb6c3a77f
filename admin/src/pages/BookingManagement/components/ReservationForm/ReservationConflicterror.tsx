import { Icon } from "@iconify/react/dist/iconify.js";
// Custom component to display booking conflict errors
interface BookingConflictErrorProps {
  message: string;
  conflictingBookingId?: string | null;
  onSelectDifferentRoom: () => void;
  onSelectDifferentDates: () => void;
}

const BookingConflictError: React.FC<BookingConflictErrorProps> = ({
  message,
  conflictingBookingId,
  onSelectDifferentRoom,
  onSelectDifferentDates,
}) => {
  return (
    <div className="p-4 mb-6 border-l-4 border-red-500 bg-red-50">
      <div className="flex items-start">
        <div className="flex-shrink-0 mt-0.5">
          <Icon icon="mdi:alert-circle" className="w-5 h-5 text-red" />
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium text-red">
            Room Booking Conflict
          </h3>
          <div className="mt-2 text-sm text-red">
            <p className="text-red">{message}</p>
            {conflictingBookingId && (
              <p className="mt-1 text-red">
                Conflicting booking reference:{" "}
                <span className="font-medium text-red">
                  {conflictingBookingId}
                </span>
              </p>
            )}
          </div>
          <div className="mt-4 flex space-x-3">
            <button
              type="button"
              onClick={onSelectDifferentDates}
              className="px-3 py-1.5 text-sm font-medium text-red bg-red-100 hover:bg-red-200 rounded-md"
            >
              Select Different Dates
            </button>
            <button
              type="button"
              onClick={onSelectDifferentRoom}
              className="px-3 py-1.5 text-sm font-medium text-red bg-red-100 hover:bg-red-200 rounded-md"
            >
              Select Different Room
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingConflictError;
