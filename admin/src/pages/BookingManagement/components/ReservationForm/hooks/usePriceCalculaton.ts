"use client";

import { useState } from "react";
import { toast } from "react-toastify";
import { calculateBookingPrice } from "../../../../../server-action/utils/calculateBookingPrice";

export const usePriceCalculation = () => {
  const [membershipDiscount, setMembershipDiscount] = useState<any>(null);
  const [isCalculatingPrice, setIsCalculatingPrice] = useState(false);

  const calculatePrice = async (formik: any) => {
    try {
      if (!formik.values.roomNo) return;
      if (!formik.values.checkin && !formik.values.expectedCheckout) return;

      setIsCalculatingPrice(true);

      const priceData = await calculateBookingPrice({
        roomId: formik.values.roomNo,
        checkInDate: formik.values.checkin || formik.values.reservationDate,
        checkOutDate: formik.values.checkout || formik.values.expectedCheckout,
        userId: formik.values.existingGuestId || undefined,
      });

      formik.setFieldValue("totalAmount", priceData.totalPrice);

      if (!formik.values.paidAmount) {
        formik.setFieldValue("paidAmount", 0);
      }

      if (formik.values.paidAmount > priceData.totalPrice) {
        formik.setFieldValue("paidAmount", priceData.totalPrice);
        toast.info("Paid amount adjusted to match the total amount");
      }

      if (priceData.membershipDiscount) {
        setMembershipDiscount(priceData.membershipDiscount);
        toast.success(
          `${priceData.membershipDiscount.name} membership discount of ${priceData.membershipDiscount.discountPercentage}% applied!`
        );
      } else {
        setMembershipDiscount(null);
      }

      setIsCalculatingPrice(false);
    } catch (error: any) {
      console.error("Error calculating price:", error);

      let errorMessage = "Failed to calculate price";
      if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      toast.error(`Price calculation error: ${errorMessage}`);
      setIsCalculatingPrice(false);
      formik.setFieldValue("totalAmount", 0);
    }
  };

  return {
    calculatePrice,
    membershipDiscount,
    isCalculatingPrice,
  };
};
