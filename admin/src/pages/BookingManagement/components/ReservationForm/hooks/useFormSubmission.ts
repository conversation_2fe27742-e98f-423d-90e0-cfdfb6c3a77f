"use client";

import { useState } from "react";
import { toast } from "react-toastify";
import { useNavigate } from "react-router-dom";
import { formatDate } from "../utils";
import { FrontendRoutes } from "../../../../../routes";

export const useFormSubmission = (
  createBooking: any,
  updateBooking: any,
  createGuest: any,
  editData: any,
  id: string
) => {
  const navigate = useNavigate();
  const [submissionError, setSubmissionError] = useState<string | null>(null);
  const [bookingConflictError, setBookingConflictError] = useState<any>(null);

  const handleSubmit = async (values: any) => {
    try {
      setSubmissionError(null);
      setBookingConflictError(null);

      // Validation checks
      if (!values.checkin && !values.reservationDate) {
        const errorMsg =
          "Either Check-in date or Reservation date is required.";
        setSubmissionError(errorMsg);
        toast.error(errorMsg);
        return;
      }

      if (!values.expectedCheckout) {
        const errorMsg = "Expected checkout date is required.";
        setSubmissionError(errorMsg);
        toast.error(errorMsg);
        return;
      }

      // Date validation
      if (values.checkin && values.checkout) {
        const checkinDate = new Date(values.checkin);
        const checkoutDate = new Date(values.checkout);
        if (checkoutDate < checkinDate) {
          const errorMsg = "Check-out date must be after check-in date";
          setSubmissionError(errorMsg);
          return;
        }
      }

      if (values.checkin && values.expectedCheckout) {
        const checkinDate = new Date(values.checkin);
        const expectedCheckoutDate = new Date(values.expectedCheckout);
        if (expectedCheckoutDate < checkinDate) {
          const errorMsg =
            "Expected check-out date must be after check-in date";
          setSubmissionError(errorMsg);
          return;
        }
      }

      const formData = new FormData();

      // Add basic booking data
      if (values.checkin) {
        formData.append("checkIn", formatDate(values.checkin));
      }
      if (values.checkout) {
        formData.append("checkOut", formatDate(values.checkout));
      }
      formData.append("expectedCheckOut", formatDate(values.expectedCheckout));
      if (values.reservationDate) {
        formData.append("reservationDate", formatDate(values.reservationDate));
      }

      if (
        values.packageType &&
        values.packageType !== "all" &&
        values.packageType !== ""
      ) {
        formData.append("package[package]", values.packageType);
      }

      if (
        values.roomCategory &&
        values.roomCategory !== "all" &&
        values.roomCategory !== ""
      ) {
        formData.append("roomType", values.roomCategory);
      }

      if (!values.roomNo) {
        const errorMsg = "Room number is required. Please select a room.";
        setSubmissionError(errorMsg);
        toast.error(errorMsg);
        return;
      }
      formData.append("room", values.roomNo);

      // Handle guest creation/selection
      let createdGuestViaAPI = false;

      if (id === "edit" && editData?.guest?._id) {
        formData.append("guest", editData.guest._id);
      } else if (values.isExistingGuest && values.existingGuestId) {
        formData.append("guest", values.existingGuestId);
      } else {
        // Create new guest
        if (!values.name || !values.mobileNo || !values.email) {
          const errorMsg = "Guest name, mobile number, and email are required.";
          setSubmissionError(errorMsg);
          toast.error(errorMsg);
          return;
        }

        const guestData = {
          name: values.name.trim(),
          email: values.email.trim(),
          phoneNumber: values.mobileNo.trim(),
          address: values.address || undefined,
          nationality: values.nationality || undefined,
          gender: values.gender || undefined,
          dateOfBirth: values.dob || undefined,
        };

        try {
          const guestResponse = await createGuest(guestData);
          const guestId = guestResponse?.data?._id;

          if (!guestId || typeof guestId !== "string") {
            throw new Error(
              `Guest ID not found or invalid in response. Got: ${guestId}`
            );
          }

          formData.append("guest", guestId);
          createdGuestViaAPI = true;
        } catch (error) {
          console.error("Error creating guest:", error);
          const errorMsg = "Failed to create guest. Please try again.";
          setSubmissionError(errorMsg);
          toast.error(errorMsg);
          return;
        }
      }

      // Add guest count
      formData.append("pax[adults]", String(values.adults || 1));
      formData.append("pax[children]", String(values.children || 0));

      // Add payment details
      if (values.paymentMethod && values.paymentMethod !== "") {
        formData.append("paymentMethod", values.paymentMethod);
      }
      formData.append("amount", String(values.totalAmount || 0));
      formData.append("amountPaid", String(values.paidAmount || 0));

      // Set booking status
      let bookingStatus = values.status || "confirmed";
      if (values.checkin) {
        bookingStatus = "checked-in";
        if (values.checkout) {
          bookingStatus = "checked-out";
        }
      }
      formData.append("status", bookingStatus);

      // Handle documents for new bookings
      if (
        !createdGuestViaAPI &&
        id !== "edit" &&
        values.documents &&
        values.documents.length > 0
      ) {
        const validDocuments = values.documents.filter(
          (doc: any) => doc.IdentityType || doc.IdNo
        );

        if (validDocuments.length > 0) {
          validDocuments.forEach((doc: any, docIndex: number) => {
            formData.append(
              `guest[documents][${docIndex}][IdentityType]`,
              doc.IdentityType || ""
            );
            formData.append(
              `guest[documents][${docIndex}][IdNo]`,
              doc.IdNo?.toString() || ""
            );

            if (doc.images && doc.images.length > 0) {
              doc.images.forEach((image: File) => {
                formData.append(`guest[documents][${docIndex}][images]`, image);
              });
            }
          });
        }
      }

      // Submit the form
      let response;
      if (id === "edit" && editData?._id) {
        response = await updateBooking({
          _id: editData._id,
          bookingData: formData,
        });
      } else {
        response = await createBooking(formData);
      }

      // Navigate on success
      setTimeout(() => {
        navigate(FrontendRoutes.BOOKINGMANAGEMENT);
      }, 1500);
    } catch (error: any) {
      console.error("Error submitting reservation:", error);

      if (error?.isBookingConflict) {
        setBookingConflictError({
          message:
            error.message || "Room is already booked for the selected dates.",
          conflictingBookingId: error.conflictingBookingId || null,
        });
        return;
      }

      let errorMessage = "Failed to process booking. Please try again.";
      if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      setSubmissionError(errorMessage);
      toast.error(errorMessage);
    }
  };

  return {
    handleSubmit,
    submissionError,
    bookingConflictError,
    setBookingConflictError,
  };
};
