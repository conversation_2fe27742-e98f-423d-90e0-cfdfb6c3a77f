import React, { useState, useEffect } from "react";
import { useGetAllUser } from "../../../../../server-action/API/user.tsx";
import { debounce } from "lodash";
import { IUser } from "../../../../../Interface/user.interface";
import { useCreateGuest } from "../../../../../server-action/API/Guest/guest";
import { Icon } from "@iconify/react/dist/iconify.js";

interface CustomerTabsProps {
  isExistingGuest: boolean;
  onTabChange: (isExisting: boolean) => void;
  onSelectExistingGuest: (guest: IUser) => void;
  formik: any;
  isEditMode?: boolean; // Add this to indicate if we're in edit mode
  onGuestCreated?: (guestId: string, guestData: any) => void; // Add callback for guest creation
}

const CustomerTabs: React.FC<CustomerTabsProps> = ({
  isExistingGuest,
  onTabChange,
  onSelectExistingGuest,
  formik,
  isEditMode = false, // Default to false if not provided
  onGuestCreated,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredGuests, setFilteredGuests] = useState<IUser[]>([]);
  const { data: guestsData, isLoading } = useGetAllUser({ role: "guest" });
  const { mutate: createGuest, isPending: isCreatingGuest } = useCreateGuest();

  // Debounced search function
  const debouncedSearch = debounce((term: string) => {
    // Get the guest data array regardless of format
    const guestArray = Array.isArray(guestsData)
      ? guestsData
      : guestsData?.data;

    if (!guestArray || !Array.isArray(guestArray)) {
      console.error("No valid guest data available for search");
      return;
    }

    // If the search term is empty, show all guests
    if (!term.trim()) {
      setFilteredGuests(guestArray);
      return;
    }

    const lowercaseTerm = term.toLowerCase();
    const filtered = guestArray.filter((guest: IUser) => {
      // Check if the guest name contains the search term
      const nameMatch = guest.name?.toLowerCase().includes(lowercaseTerm);

      // Check if the guest email contains the search term
      const emailMatch = guest.email?.toLowerCase().includes(lowercaseTerm);

      // Check if the guest phone number contains the search term
      // Phone number search should be exact match or partial match
      const phoneMatch = guest.phoneNumber?.includes(term);

      // Return true if any of the fields match
      return nameMatch || emailMatch || phoneMatch;
    });

    setFilteredGuests(filtered);
  }, 300);

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const term = e.target.value;
    setSearchTerm(term);
    debouncedSearch(term);
  };

  // Initialize filtered guests when data loads
  useEffect(() => {
    console.log("Guest data received:", guestsData);
    // Check if guestsData is an array directly or has a data property that's an array
    if (Array.isArray(guestsData)) {
      setFilteredGuests(guestsData);
    } else if (guestsData?.data && Array.isArray(guestsData.data)) {
      setFilteredGuests(guestsData.data);
    } else {
      console.error("Guest data is not in expected format:", guestsData);
      setFilteredGuests([]);
    }
  }, [guestsData]);

  // Handle guest selection
  const handleGuestSelect = (guest: IUser) => {
    console.log("Selected guest:", guest);
    onSelectExistingGuest(guest);
    formik.setFieldValue("existingGuestId", guest._id);

    // Also set the existingGuest field for backward compatibility
    // Some components might be using this field instead of existingGuestId
    formik.setFieldValue("existingGuest", guest._id);
  };

  // Handle guest creation from current form data
  const handleCreateGuestFromForm = () => {
    const formValues = formik.values;

    // Validate required fields
    if (!formValues.name || !formValues.name.trim()) {
      formik.setFieldError("name", "Guest name is required");
      formik.setFieldTouched("name", true);
      return;
    }

    if (!formValues.mobileNo || !formValues.mobileNo.trim()) {
      formik.setFieldError("mobileNo", "Phone number is required");
      formik.setFieldTouched("mobileNo", true);
      return;
    }

    if (!formValues.email || !formValues.email.trim()) {
      formik.setFieldError("email", "Email is required");
      formik.setFieldTouched("email", true);
      return;
    }

    // Prepare guest data from form values
    const guestData = {
      name: formValues.name.trim(),
      email: formValues.email.trim(),
      phoneNumber: formValues.mobileNo.trim(),
      address: formValues.address || undefined,
      nationality: formValues.nationality || undefined,
      gender: formValues.gender || undefined,
      dateOfBirth: formValues.dob || undefined,
    };

    console.log("Creating guest with form data:", guestData);

    // Create the guest
    createGuest(guestData, {
      onSuccess: (response) => {
        console.log("Guest created successfully:", response);

        // Extract guest ID safely
        const guestId = response?.data?._id;
        const guestData = response?.data || response;

        if (!guestId) {
          console.error("Guest ID not found in response:", response);
          return;
        }

        // Switch to existing guest tab and select the newly created guest
        onTabChange(true);

        // Set the guest ID in the form and mark as existing guest
        formik.setFieldValue("existingGuestId", guestId);
        formik.setFieldValue("existingGuest", guestId);
        formik.setFieldValue("isExistingGuest", true);

        // Call parent callback if provided
        if (onGuestCreated) {
          onGuestCreated(guestId, guestData);
        }
      },
      onError: (error) => {
        console.error("Error creating guest:", error);
        // Error is already handled by the useCreateGuest hook with toast
      },
    });
  };

  return (
    <div className="mb-6">
      {/* Tabs - Hide tabs in edit mode */}
      {!isEditMode && (
        <div className="flex border-b mb-4">
          <button
            type="button"
            className={`py-2 px-4 font-medium ${
              !isExistingGuest
                ? "text-blue-600 border-b-2 border-blue-600"
                : "text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => onTabChange(false)}
            disabled={isEditMode}
          >
            New
          </button>
          <button
            type="button"
            className={`py-2 px-4 font-medium ${
              isExistingGuest
                ? "text-blue-600 border-b-2 border-blue-600"
                : "text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => onTabChange(true)}
            disabled={isEditMode}
          >
            Old
          </button>
        </div>
      )}

      {/* New Guest Section - Show create button when on new tab */}
      {!isExistingGuest && !isEditMode && (
        <div className="mb-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">
              Guest Information
            </h3>
            <button
              type="button"
              onClick={handleCreateGuestFromForm}
              disabled={isCreatingGuest}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-[#2A3A6D]  focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              {isCreatingGuest && (
                <Icon
                  icon="eos-icons:loading"
                  className="w-4 h-4 mr-2 animate-spin"
                />
              )}
              {!isCreatingGuest && (
                <Icon icon="heroicons:plus" className="w-4 h-4 mr-2" />
              )}
              {isCreatingGuest ? "Creating..." : "Create Guest"}
            </button>
          </div>
          <div className="p-4 border border-dashed border-gray-300 rounded-lg bg-gray-50">
            <p className="text-xs text-gray-500">
              Creating a guest profile will make future bookings easier and send
              a welcome email to the guest.
            </p>
            <div className="mt-3 text-xs text-amber-600">
              <strong>Required fields:</strong> Guest Name, Mobile Number, and
              Email
            </div>
          </div>
        </div>
      )}

      {/* Existing Customer Search - Show search only if not in edit mode */}
      {isExistingGuest && !isEditMode && (
        <div className="mb-4">
          <div className="mb-4">
            <label
              htmlFor="guestSearch"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Search Existing Guest
            </label>
            <div className="relative">
              <input
                type="text"
                id="guestSearch"
                className="w-full p-2 pl-10 border rounded"
                placeholder="Search for a guest..."
                value={searchTerm}
                onChange={handleSearchChange}
              />
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <svg
                  className="w-5 h-5 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  ></path>
                </svg>
              </div>
              {searchTerm && (
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 flex items-center pr-3"
                  onClick={() => {
                    setSearchTerm("");
                    // Get the guest data array regardless of format
                    const guestArray = Array.isArray(guestsData)
                      ? guestsData
                      : guestsData?.data;
                    if (guestArray && Array.isArray(guestArray)) {
                      setFilteredGuests(guestArray);
                    }
                  }}
                >
                  <svg
                    className="w-5 h-5 text-gray-400 hover:text-gray-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M6 18L18 6M6 6l12 12"
                    ></path>
                  </svg>
                </button>
              )}
            </div>
            <p className="mt-1 text-xs text-gray-500">
              Search by name, email, or phone number to find existing guests
            </p>
          </div>

          {/* Guest List */}
          <div className="border rounded max-h-60 overflow-y-auto">
            {isLoading ? (
              <div className="p-4 text-center text-gray-500">
                Loading guests...
              </div>
            ) : filteredGuests.length > 0 ? (
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Name
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Email
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Phone
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Action
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredGuests.map((guest) => (
                    <tr
                      key={guest._id}
                      className={`hover:bg-gray-50 ${
                        formik.values.existingGuestId === guest._id
                          ? "bg-blue-50"
                          : ""
                      }`}
                    >
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {guest.name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {guest.email || "-"}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {guest.phoneNumber || "-"}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          type="button"
                          className="text-blue-600 hover:text-blue-900"
                          onClick={() => handleGuestSelect(guest)}
                        >
                          Select
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            ) : (
              <div className="p-4 text-center text-gray-500">
                {searchTerm
                  ? "No guests found matching your search."
                  : "No guests available."}
              </div>
            )}
          </div>

          {/* Selected Guest Info */}
          {formik.values.existingGuestId && (
            <div className="mt-4 p-4 border rounded bg-blue-50">
              <h3 className="font-medium text-blue-800 mb-2">Selected Guest</h3>
              <p className="text-sm text-blue-700">
                {filteredGuests.find(
                  (g) => g._id === formik.values.existingGuestId
                )?.name || "Guest"}{" "}
                will be assigned to this reservation.
              </p>
              {formik.errors.existingGuestId &&
                formik.touched.existingGuestId && (
                  <p className="mt-1 text-sm text-red-600 font-semibold">
                    {formik.errors.existingGuestId}
                  </p>
                )}
            </div>
          )}
        </div>
      )}

      {/* Display guest info in edit mode */}
      {isEditMode && formik.values.existingGuestId && (
        <div className="mb-4">
          <div className="p-4 border rounded bg-gray-50">
            <h3 className="font-medium text-gray-800 mb-2">
              Guest Information
            </h3>
            <p className="text-sm text-gray-700 mb-1">
              <span className="font-semibold">Name:</span>{" "}
              {formik.values.name || "Not provided"}
            </p>
            <p className="text-sm text-gray-700 mb-1">
              <span className="font-semibold">Email:</span>{" "}
              {formik.values.email || "Not provided"}
            </p>
            <p className="text-sm text-gray-700 mb-1">
              <span className="font-semibold">Phone:</span>{" "}
              {formik.values.mobileNo || "Not provided"}
            </p>
            <p className="text-sm text-gray-700 mb-1">
              <span className="font-semibold">Gender:</span>{" "}
              {formik.values.gender || "Not provided"}
            </p>
            <p className="text-sm text-gray-700">
              <span className="font-semibold">Date of Birth:</span>{" "}
              {formik.values.dob || "Not provided"}
            </p>
            <div className="mt-3 p-2 bg-blue-50 border border-blue-100 rounded">
              <p className="text-xs text-blue-700">
                Guest details cannot be modified when updating a booking. Only
                booking details can be changed.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomerTabs;
