import { useEffect, useState } from "react";
import { FormikProvider, Form, useFormik, FieldArray } from "formik";
import { GlobalForm } from "../../../components/GlobalForm/GlobalFormComponent";
import { ActionButton } from "../../../components/ActionButton";
import {
  Reserve,
  CustomerDetails,
  IdentityDetails,
  PaymentDetails,
} from "./ReservationFormData";
import {
  useCreateBooking,
  useUpdateBooking,
  useGetBookingById,
} from "../../../server-action/API/BookingManagement/BookingManagement";
import { useParams, useNavigate } from "react-router-dom";
import { useStore } from "@tanstack/react-store";
import { reservationStore, setBookingData } from "../../../store/reservation";
import { get } from "lodash";
import moment from "moment";
import { BookingStatus } from "../../../Interface/booking.interface";
import { getBookingValidationSchema } from "./ReservationValidationSchema";
import { toast } from "react-toastify";
import { IUser } from "../../../Interface/user.interface";
import { useGetUserById } from "../../../server-action/API/user";
import { calculateBookingPrice } from "../../../server-action/utils/calculateBookingPrice";
import { FrontendRoutes } from "../../../routes";
import { useGetRoomById } from "../../../server-action/API/HotelConfiguration/room";
import { useCreateGuest } from "../../../server-action/API/Guest/guest";
import { Icon } from "@iconify/react/dist/iconify.js";
import CustomerTabs from "./ReservationForm/CustomerTabs/CustomerTabs";

interface BookingConflictErrorProps {
  message: string;
  conflictingBookingId?: string | null;
  onSelectDifferentRoom: () => void;
  onSelectDifferentDates: () => void;
}

const BookingConflictError: React.FC<BookingConflictErrorProps> = ({
  message,
  conflictingBookingId,
  onSelectDifferentRoom,
  onSelectDifferentDates,
}) => (
  <div className="p-4 mb-6 border-l-4 border-red-500 bg-red-50">
    <div className="flex items-start">
      <Icon icon="mdi:alert-circle" className="w-5 h-5 text-red-500 mt-0.5" />
      <div className="ml-3">
        <h3 className="text-sm font-medium text-red-700">
          Room Booking Conflict
        </h3>
        <div className="mt-2 text-sm text-red-600">
          <p>{message}</p>
          {conflictingBookingId && (
            <p className="mt-1">
              Conflicting booking reference:{" "}
              <span className="font-medium">{conflictingBookingId}</span>
            </p>
          )}
        </div>
        <div className="mt-4 flex space-x-3">
          <button
            type="button"
            onClick={onSelectDifferentDates}
            className="px-3 py-1.5 text-sm font-medium text-red-600 bg-red-100 hover:bg-red-200 rounded-md"
          >
            Select Different Dates
          </button>
          <button
            type="button"
            onClick={onSelectDifferentRoom}
            className="px-3 py-1.5 text-sm font-medium text-red-600 bg-red-100 hover:bg-red-200 rounded-md"
          >
            Select Different Room
          </button>
        </div>
      </div>
    </div>
  </div>
);

const getInitialValues = (sections: any) =>
  sections.reduce((values: Record<string, any>, section: any) => {
    section.fields.forEach((field: any) => {
      if (field.type === "files") values[field.field] = [];
      else if (field.type === "file") values[field.field] = "";
      else if (field.field.includes("permanentAddress"))
        values.permanentAddress = { country: "" };
      else if (field.field.includes("tempAddress"))
        values.tempAddress = { district: "" };
      else values[field.field] = "";
    });
    return values;
  }, {});

const ReservationForm: React.FC = () => {
  const { id, bookingId } = useParams();
  const navigate = useNavigate();
  const editData = useStore(reservationStore, (state) => state.bookingData);
  const [roomQuery, setRoomQuery] = useState({
    selectedRoomCategory: "",
    ac: "",
    bedCategory: "",
  });
  const [selectedUserId, setSelectedUserId] = useState<string>("");
  const [selectedRoomId, setSelectedRoomId] = useState<string>("");
  const [membershipDiscount, setMembershipDiscount] = useState<any>(null);
  const [isCalculatingPrice, setIsCalculatingPrice] = useState(false);
  const [submissionError, setSubmissionError] = useState<string | null>(null);
  const [bookingConflictError, setBookingConflictError] = useState<{
    message: string;
    conflictingBookingId: string | null;
  } | null>(null);
  const [isLoadingBookingData, setIsLoadingBookingData] =
    useState<boolean>(false);

  const { data: fetchedBookingData, isLoading: isLoadingBooking } =
    useGetBookingById(id === "edit" && !editData && bookingId ? bookingId : "");
  const { data: selectedUserData = {} as IUser } =
    useGetUserById(selectedUserId);
  const { data: selectedRoomData } = useGetRoomById(selectedRoomId);
  const { mutateAsync: createBooking, isPending: isCreating } =
    useCreateBooking();
  const { mutateAsync: updateBooking, isPending: isUpdating } =
    useUpdateBooking();
  const { mutateAsync: createGuest, isPending: isCreatingGuest } =
    useCreateGuest();

  const isPending = isCreating || isUpdating || isCreatingGuest;

  const { ReservationDetails } = Reserve(
    roomQuery.selectedRoomCategory,
    roomQuery.bedCategory,
    roomQuery.ac
  );
  const formSections = [
    { id: 1, title: "Reservation Details", fields: ReservationDetails },
    { id: 2, title: "Customer Details", fields: CustomerDetails },
    ...(id !== "edit"
      ? [
          { id: 3, title: "Identity Details", fields: IdentityDetails },
          { id: 4, title: "Payment Details", fields: PaymentDetails },
        ]
      : []),
  ];

  const formik = useFormik({
    initialValues:
      id === "add"
        ? {
            ...getInitialValues(formSections),
            isExistingGuest: false,
            existingGuestId: "",
            name: "",
            guestName: "",
            expectedCheckout: "",
            reservationDate: "",
            totalAmount: 0,
            paidAmount: 0,
            adults: 1,
            children: 0,
            status: BookingStatus.CONFIRMED,
            documents: [{ IdentityType: "", IdNo: "", images: [] }],
          }
        : {
            checkin: moment(get(editData, "checkIn")).format("YYYY-MM-DD"),
            checkout: get(editData, "checkOut")
              ? moment(get(editData, "checkOut")).format("YYYY-MM-DD")
              : "",
            expectedCheckout: get(editData, "expectedCheckOut")
              ? moment(get(editData, "expectedCheckOut")).format("YYYY-MM-DD")
              : "",
            reservationDate: get(editData, "reservationDate")
              ? moment(get(editData, "reservationDate")).format("YYYY-MM-DD")
              : "",
            isExistingGuest: false,
            existingGuestId: get(editData, "guest._id") || "",
            packageType: get(editData, "package.package") || "",
            ac: "",
            bedCategory: "",
            roomCategory: get(editData, "roomType._id") || "",
            roomNo: get(editData, "room._id"),
            name: get(editData, "guest.name"),
            guestName: get(editData, "guest.name"),
            mobileNo: get(editData, "guest.phoneNumber"),
            dob: moment(get(editData, "guest.DOB")).format("YYYY-MM-DD"),
            gender: get(editData, "guest.gender"),
            email: get(editData, "guest.email"),
            permanentAddress: {
              country: get(editData, "guest.permanentAddress.country"),
            },
            tempAddress: {
              district: get(editData, "guest.tempAddress.district"),
            },
            numberOfGuest:
              get(editData, "pax.adults") + get(editData, "pax.children", 0),
            adults: get(editData, "pax.adults", 1),
            children: get(editData, "pax.children", 0),
            documents: get(editData, "guest.documents", []),
            paymentType: get(editData, "paymentType"),
            totalAmount: get(editData, "amount"),
            paidAmount: get(editData, "amountPaid", 0),
            paymentMethod: get(editData, "paymentMethod"),
            status: get(editData, "status", BookingStatus.CONFIRMED),
          },
    enableReinitialize: true,
    validationSchema: getBookingValidationSchema(id === "edit"),
    onSubmit: async (values) => {
      try {
        setSubmissionError(null);
        setBookingConflictError(null);

        // Display all form values in a toast for debugging
        toast.info(
          <div>
            <h3>Form Data:</h3>
            <pre style={{ fontSize: "12px", whiteSpace: "pre-wrap" }}>
              {JSON.stringify(values, null, 2)}
            </pre>
          </div>,
          { autoClose: 10000 } // Keep toast visible for 10 seconds
        );

        const formData = new FormData();
        const formatDate = (date: any) =>
          date ? moment(date).format("YYYY-MM-DD") : "";

        if (values.isExistingGuest && values.existingGuestId) {
          values.permanentAddress = {
            country: values.permanentAddress?.country || "Nepal",
          };
          values.tempAddress = {
            district: values.tempAddress?.district || "Not specified",
          };
        }

        if (values.checkin)
          formData.append("checkIn", formatDate(values.checkin));
        if (values.checkout)
          formData.append("checkOut", formatDate(values.checkout));
        formData.append(
          "expectedCheckOut",
          formatDate(values.expectedCheckout)
        );
        if (values.reservationDate)
          formData.append(
            "reservationDate",
            formatDate(values.reservationDate)
          );
        if (values.packageType && values.packageType !== "all")
          formData.append("package[package]", values.packageType);
        if (values.roomCategory && values.roomCategory !== "all")
          formData.append("roomType", values.roomCategory);
        formData.append("room", values.roomNo);

        let createdGuestViaAPI = false;
        if (id === "edit" && editData?.guest?._id) {
          formData.append("guest", editData.guest._id);
        } else if (values.isExistingGuest && values.existingGuestId) {
          formData.append("guest", values.existingGuestId);
        } else {
          const guestData = {
            name: values.name.trim(),
            email: values.email?.trim(),
            phoneNumber: values.mobileNo.trim(),
            address: values.address,
            nationality: values.nationality,
            gender: values.gender,
            documents: values.documents || [],
            dateOfBirth: values.dob,
          };
          const guestResponse = await createGuest(guestData);
          const guestId = guestResponse?.data?._id;
          if (!guestId) throw new Error("Failed to create guest");
          formData.append("guest", guestId);
          createdGuestViaAPI = true;
        }

        formData.append("pax[adults]", String(values.adults || 1));
        formData.append("pax[children]", String(values.children || 0));
        if (values.paymentMethod)
          formData.append("paymentMethod", values.paymentMethod);
        formData.append("amount", String(values.totalAmount || 0));
        formData.append("amountPaid", String(values.paidAmount || 0));

        const status = values.checkin
          ? values.checkout
            ? BookingStatus.CHECKED_OUT
            : BookingStatus.CHECKED_IN
          : BookingStatus.CONFIRMED;
        formData.append("status", status);

        if (
          !createdGuestViaAPI &&
          id !== "edit" &&
          values.documents?.length > 0
        ) {
          const validDocuments = values.documents.filter(
            (doc: any) => doc.IdentityType || doc.IdNo
          );
          if (validDocuments.length > 0) {
            validDocuments.forEach((doc: any, docIndex: number) => {
              formData.append(
                `guest[documents][${docIndex}][IdentityType]`,
                doc.IdentityType || ""
              );
              formData.append(
                `guest[documents][${docIndex}][IdNo]`,
                doc.IdNo?.toString() || ""
              );
              doc.images?.forEach((image: File) => {
                formData.append(`guest[documents][${docIndex}][images]`, image);
              });
            });
          }
        }

        const response =
          id === "edit" && editData?._id
            ? await updateBooking({ _id: editData._id, bookingData: formData })
            : await createBooking(formData);

        toast.success(
          id === "edit"
            ? "Booking updated successfully"
            : "Booking created successfully"
        );
        setTimeout(() => navigate(FrontendRoutes.BOOKINGMANAGEMENT), 1500);
      } catch (error: any) {
        const errorMessage =
          error?.response?.data?.message ||
          error?.message ||
          "Failed to process booking";
        setSubmissionError(errorMessage);
        if (error?.isBookingConflict) {
          setBookingConflictError({
            message:
              error.message || "Room is already booked for the selected dates.",
            conflictingBookingId: error.conflictingBookingId || null,
          });
          formik.setFieldError(
            "roomNo",
            "This room is already booked for these dates"
          );
          formik.setFieldError(
            "checkin",
            "Date conflict with existing booking"
          );
          formik.setFieldError(
            "checkout",
            "Date conflict with existing booking"
          );
          formik.setFieldTouched("roomNo", true);
          formik.setFieldTouched("checkin", true);
          formik.setFieldTouched("checkout", true);
        } else {
          toast.error(
            `Failed to ${
              id === "edit" ? "update" : "create"
            } booking: ${errorMessage}`
          );
        }
      }
    },
  });

  const updateBookingStatus = (
    checkin: string | null,
    checkout: string | null
  ) => {
    const status = checkin
      ? checkout
        ? BookingStatus.CHECKED_OUT
        : BookingStatus.CHECKED_IN
      : BookingStatus.CONFIRMED;
    formik.setFieldValue("status", status);
  };

  const calculatePrice = async () => {
    if (
      !formik.values.roomNo ||
      (!formik.values.checkin && !formik.values.reservationDate)
    )
      return;
    try {
      setIsCalculatingPrice(true);
      const priceData = await calculateBookingPrice({
        roomId: formik.values.roomNo,
        checkInDate: formik.values.checkin || formik.values.reservationDate,
        checkOutDate: formik.values.checkout || formik.values.expectedCheckout,
        userId: formik.values.existingGuestId || undefined,
      });
      formik.setFieldValue("totalAmount", priceData.totalPrice);
      formik.setFieldValue(
        "paidAmount",
        Math.min(formik.values.paidAmount || 0, priceData.totalPrice)
      );
      setMembershipDiscount(priceData.membershipDiscount);
      if (priceData.membershipDiscount) {
        toast.success(
          `${priceData.membershipDiscount.name} membership discount of ${priceData.membershipDiscount.discountPercentage}% applied!`
        );
      }
    } catch (error: any) {
      toast.error(
        `Price calculation error: ${
          error?.response?.data?.message ||
          error?.message ||
          "Failed to calculate price"
        }`
      );
      formik.setFieldValue("totalAmount", 0);
    } finally {
      setIsCalculatingPrice(false);
    }
  };

  useEffect(() => {
    const adults = Math.max(1, Number(formik.values.adults) || 1);
    const children = Math.max(0, Number(formik.values.children) || 0);
    const total = adults + children;
    if (total > 10) {
      formik.setFieldValue("children", Math.max(0, 10 - adults));
    } else {
      formik.setFieldValue("numberOfGuest", total);
    }
  }, [formik.values.adults, formik.values.children]);

  useEffect(() => {
    if (
      formik.values.roomNo &&
      (formik.values.checkin || formik.values.reservationDate)
    ) {
      calculatePrice();
    }
    updateBookingStatus(
      formik.values.checkin || null,
      formik.values.checkout || null
    );
  }, [
    formik.values.roomNo,
    formik.values.checkin,
    formik.values.checkout,
    formik.values.reservationDate,
    formik.values.expectedCheckout,
    formik.values.existingGuestId,
  ]);

  useEffect(() => {
    if (
      formik.values.existingGuest &&
      formik.values.existingGuest !== selectedUserId
    ) {
      setSelectedUserId(formik.values.existingGuest);
    }
  }, [formik.values.existingGuest]);

  useEffect(() => {
    if (
      id !== "edit" &&
      selectedUserData &&
      Object.keys(selectedUserData).length > 0
    ) {
      const userData = selectedUserData as IUser;
      formik.setValues({
        ...formik.values,
        guestName: userData.name || "",
        mobileNo: userData.phoneNumber || "",
        dob: userData.DOB ? moment(userData.DOB).format("YYYY-MM-DD") : "",
        gender: userData.gender || "",
        email: userData.email || "",
        permanentAddress: { country: userData.permanentAddress?.country || "" },
        tempAddress: { district: userData.tempAddress?.district || "" },
      });
    }
  }, [selectedUserData, id]);

  useEffect(() => {
    if (
      formik.values.ac !== roomQuery.ac ||
      formik.values.bedCategory !== roomQuery.bedCategory ||
      formik.values.roomCategory !== roomQuery.selectedRoomCategory
    ) {
      setRoomQuery({
        ac: formik.values.ac,
        bedCategory: formik.values.bedCategory,
        selectedRoomCategory: formik.values.roomCategory,
      });
    }
  }, [formik.values.ac, formik.values.bedCategory, formik.values.roomCategory]);

  useEffect(() => {
    if (formik.values.roomNo && formik.values.roomNo !== selectedRoomId) {
      setSelectedRoomId(formik.values.roomNo);
    }
  }, [formik.values.roomNo]);

  useEffect(() => {
    if (selectedRoomData) {
      formik.setFieldValue(
        "roomCategory",
        selectedRoomData.roomType?._id || ""
      );
      formik.setFieldValue("ac", selectedRoomData.features?.acType || "");
      formik.setFieldValue(
        "bedCategory",
        selectedRoomData.beds?.types?.[0]?._id || ""
      );
    }
  }, [selectedRoomData]);

  useEffect(() => {
    setIsLoadingBookingData(isLoadingBooking);
    if (id === "edit" && !editData && fetchedBookingData?.data) {
      setBookingData(fetchedBookingData.data);
    }
  }, [id, editData, fetchedBookingData, isLoadingBooking]);

  const { handleSubmit, getFieldProps } = formik;

  return (
    <div className="w-full mx-auto bg-white rounded-lg shadow-md">
      {isLoadingBookingData && (
        <div className="flex items-center justify-center p-4 mb-4 bg-blue-50 border border-blue-200 rounded-md">
          <div className="w-6 h-6 mr-3 border-t-2 border-b-2 border-blue-500 rounded-full animate-spin"></div>
          <p className="text-blue-700">Loading booking data...</p>
        </div>
      )}

      <FormikProvider value={formik}>
        <Form onSubmit={handleSubmit}>
          {bookingConflictError && (
            <BookingConflictError
              message={bookingConflictError.message}
              conflictingBookingId={bookingConflictError.conflictingBookingId}
              onSelectDifferentRoom={() => {
                formik.setFieldValue("roomNo", "");
                setBookingConflictError(null);
                document.querySelector('[name="roomNo"]');
              }}
              onSelectDifferentDates={() => {
                formik.setFieldValue("checkin", "");
                formik.setFieldValue("checkout", "");
                setBookingConflictError(null);
                document.querySelector('[name="checkin"]');
              }}
            />
          )}

          <div className="max-h-[calc(100vh-160px)] overflow-y-auto">
            {formSections.map(({ title, fields, id: sectionId }) => (
              <div key={title} className="p-4 mt-5 mb-6 border rounded-md">
                <h2 className="pb-2 mb-3 text-lg font-semibold border-b">
                  {title}
                </h2>
                {sectionId === 2 ? (
                  <div>
                    {id === "add" ? (
                      <>
                        <CustomerTabs
                          isExistingGuest={formik.values.isExistingGuest}
                          onTabChange={(isExisting) => {
                            formik.setFieldValue("isExistingGuest", isExisting);
                            if (!isExisting) {
                              formik.setFieldValue("existingGuestId", "");
                              [
                                "name",
                                "mobileNo",
                                "dob",
                                "gender",
                                "email",
                                "permanentAddress.country",
                                "tempAddress.district",
                              ].forEach((field) =>
                                formik.setFieldValue(field, "")
                              );
                            }
                            formik.setErrors({});
                          }}
                          onSelectExistingGuest={(guest: IUser) => {
                            if (guest) {
                              formik.setValues({
                                ...formik.values,
                                existingGuestId: guest._id,
                                name: guest.name || "",
                                guestName: guest.name || "",
                                mobileNo: guest.phoneNumber || "",
                                dob: guest.DOB
                                  ? moment(guest.DOB).format("YYYY-MM-DD")
                                  : "",
                                gender: guest.gender || "",
                                email: guest.email || "",
                                permanentAddress: {
                                  country:
                                    guest.permanentAddress?.country || "",
                                },
                                tempAddress: {
                                  district: guest.tempAddress?.district || "",
                                },
                              });
                            }
                          }}
                          formik={formik}
                          isEditMode={id !== "add"}
                        />
                        {formik.values.isExistingGuest ? (
                          <div>
                            <div className="mt-4 p-4 border rounded-md bg-blue-50">
                              <h3 className="font-medium text-blue-800 mb-3">
                                Guest Count Information
                              </h3>
                              <p className="text-sm text-blue-700 mb-3">
                                Please specify the number of adults and children
                                staying in this room.
                              </p>
                              <div className="grid grid-cols-4 gap-4 mt-2">
                                {CustomerDetails.filter((field) =>
                                  [
                                    "numberOfGuest",
                                    "adults",
                                    "children",
                                  ].includes(field.field)
                                ).map((field, index) => (
                                  <div key={index} className="col-span-1">
                                    <GlobalForm
                                      formDatails={[field] as any}
                                      getFieldProps={getFieldProps}
                                      formik={formik}
                                    />
                                  </div>
                                ))}
                              </div>
                            </div>
                            {formik.values.existingGuestId && (
                              <div className="mt-4 p-4 border rounded bg-gray-50">
                                <h3 className="font-medium text-gray-800 mb-2">
                                  Selected Existing Customer
                                </h3>
                                <div className="grid grid-cols-2 gap-4">
                                  <div>
                                    <p className="text-sm text-gray-600">
                                      Name:{" "}
                                      <span className="font-medium">
                                        {formik.values.name}
                                      </span>
                                    </p>
                                    <p className="text-sm text-gray-600">
                                      Phone:{" "}
                                      <span className="font-medium">
                                        {formik.values.mobileNo}
                                      </span>
                                    </p>
                                    <p className="text-sm text-gray-600">
                                      Email:{" "}
                                      <span className="font-medium">
                                        {formik.values.email || "-"}
                                      </span>
                                    </p>
                                  </div>
                                  <div>
                                    <p className="text-sm text-gray-600">
                                      Gender:{" "}
                                      <span className="font-medium">
                                        {formik.values.gender}
                                      </span>
                                    </p>
                                    <p className="text-sm text-gray-600">
                                      Country:{" "}
                                      <span className="font-medium">
                                        {formik.values.permanentAddress
                                          ?.country || ""}
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="grid grid-cols-3 gap-4 mt-4">
                            <GlobalForm
                              formDatails={fields as any}
                              getFieldProps={getFieldProps}
                              formik={formik}
                            />
                          </div>
                        )}
                      </>
                    ) : (
                      <>
                        <CustomerTabs
                          isExistingGuest={true}
                          onTabChange={() => {}}
                          onSelectExistingGuest={() => {}}
                          formik={formik}
                          isEditMode={true}
                        />
                        <div className="mt-4 p-4 border rounded-md bg-blue-50">
                          <h3 className="font-medium text-blue-800 mb-3">
                            Guest Count Information
                          </h3>
                          <p className="text-sm text-blue-700 mb-3">
                            Please specify the number of adults and children
                            staying in this room.
                          </p>
                          <div className="grid grid-cols-4 gap-4 mt-2">
                            {CustomerDetails.filter((field) =>
                              ["numberOfGuest", "adults", "children"].includes(
                                field.field
                              )
                            ).map((field, index) => (
                              <div key={index} className="col-span-1">
                                <GlobalForm
                                  formDatails={
                                    [
                                      {
                                        ...field,
                                        disabled:
                                          field.field === "numberOfGuest",
                                      },
                                    ] as any
                                  }
                                  getFieldProps={getFieldProps}
                                  formik={formik}
                                />
                              </div>
                            ))}
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                ) : sectionId === 3 && id !== "edit" ? (
                  <>
                    <div className="mb-4 p-3 bg-blue-50 border-l-4 border-blue-500">
                      <p className="text-sm font-medium text-black">
                        Optional: Identity Details
                      </p>
                      <p className="text-xs mt-1 text-black">
                        You can add identity documents if available.
                      </p>
                    </div>
                    <FieldArray name="documents">
                      {({ push, remove }) => (
                        <div className="space-y-4">
                          {formik.values.documents?.length > 0 ? (
                            formik.values.documents.map(
                              (_: {}, index: number) => (
                                <div
                                  key={index}
                                  className="p-4 border rounded-lg"
                                >
                                  <div className="grid grid-cols-4 gap-4 mb-4">
                                    <div className="col-span-1">
                                      <label className="block mb-1 text-sm font-medium">
                                        Identity Type
                                      </label>
                                      <select
                                        {...getFieldProps(
                                          `documents[${index}].IdentityType`
                                        )}
                                        className="w-full p-2 border rounded"
                                      >
                                        <option value="">Select ID Type</option>
                                        <option value="passport">
                                          Passport
                                        </option>
                                        <option value="drivingLicense">
                                          Driving License
                                        </option>
                                        <option value="nationalId">
                                          National ID
                                        </option>
                                        <option value="other">Other</option>
                                      </select>
                                    </div>
                                    <div className="col-span-1">
                                      <label className="block mb-1 text-sm font-medium">
                                        ID Number
                                      </label>
                                      <input
                                        type="text"
                                        {...getFieldProps(
                                          `documents[${index}].IdNo`
                                        )}
                                        className="w-full p-2 border rounded"
                                        placeholder="Enter ID number"
                                      />
                                    </div>
                                    <div className="col-span-1">
                                      <label className="block mb-1 text-sm font-medium">
                                        Upload Images
                                      </label>
                                      <input
                                        type="file"
                                        multiple
                                        onChange={(event) => {
                                          const files =
                                            event.currentTarget.files;
                                          if (files) {
                                            const newImages = [
                                              ...(formik.values.documents[index]
                                                ?.images || []),
                                            ];
                                            for (
                                              let i = 0;
                                              i < files.length;
                                              i++
                                            )
                                              newImages.push(files[i]);
                                            formik.setFieldValue(
                                              `documents[${index}].images`,
                                              newImages
                                            );
                                          }
                                        }}
                                        className="w-full p-2 border rounded"
                                      />
                                    </div>
                                  </div>
                                  {formik.values.documents[index]?.images
                                    ?.length > 0 && (
                                    <div className="mt-2">
                                      <p className="mb-2 text-sm font-medium">
                                        Uploaded Files:
                                      </p>
                                      <div className="flex flex-wrap gap-2">
                                        {formik.values.documents[
                                          index
                                        ].images.map(
                                          (image: any, imgIndex: number) => (
                                            <div
                                              key={imgIndex}
                                              className="p-2 text-xs bg-gray-100 rounded"
                                            >
                                              {image.name ||
                                                `File ${imgIndex + 1}`}
                                              <button
                                                type="button"
                                                className="ml-2 text-red-500"
                                                onClick={() => {
                                                  const newImages = [
                                                    ...formik.values.documents[
                                                      index
                                                    ].images,
                                                  ];
                                                  newImages.splice(imgIndex, 1);
                                                  formik.setFieldValue(
                                                    `documents[${index}].images`,
                                                    newImages
                                                  );
                                                }}
                                              >
                                                ×
                                              </button>
                                            </div>
                                          )
                                        )}
                                      </div>
                                    </div>
                                  )}
                                  <button
                                    type="button"
                                    onClick={() => remove(index)}
                                    className="px-3 py-1 mt-2 text-sm text-red-600 border border-red-600 rounded"
                                  >
                                    Remove Document
                                  </button>
                                </div>
                              )
                            )
                          ) : (
                            <div className="p-4 text-center bg-gray-50 rounded-lg">
                              <p className="text-gray-600">
                                No identity documents added yet.
                              </p>
                            </div>
                          )}
                          <button
                            type="button"
                            onClick={() =>
                              push({ IdentityType: "", IdNo: "", images: [] })
                            }
                            className="px-4 py-2 mt-4 bg-gray-200 rounded"
                          >
                            + Add Identity Document
                          </button>
                        </div>
                      )}
                    </FieldArray>
                  </>
                ) : (
                  <div className="grid grid-cols-4 gap-4">
                    <GlobalForm
                      formDatails={fields as any}
                      getFieldProps={getFieldProps}
                      formik={formik}
                    />
                  </div>
                )}
              </div>
            ))}
          </div>

          {submissionError && (
            <div className="p-4 mb-4 text-sm text-red-600 bg-red-100 rounded-md">
              <strong>Error:</strong> {submissionError}
            </div>
          )}

          <div className="flex justify-end">
            <ActionButton
              loading={isPending}
              isSubmitting={formik.isSubmitting}
              submitText={
                id === "edit"
                  ? "Update Booking"
                  : !formik.values.isExistingGuest &&
                    !formik.values.existingGuestId
                  ? "Create Guest & Booking"
                  : "Create Booking"
              }
              onCancel={() => navigate(FrontendRoutes.BOOKINGMANAGEMENT)}
              onSubmit={async () => {
                const errors = await formik.validateForm();
                if (id === "edit") {
                  const filteredErrors = Object.keys(errors).reduce(
                    (acc: Record<string, any>, key) => {
                      if (
                        ![
                          "guestName",
                          "mobileNo",
                          "dob",
                          "gender",
                          "email",
                          "country",
                          "address",
                          "documents",
                        ].includes(key)
                      ) {
                        acc[key] = errors[key];
                      }
                      return acc;
                    },
                    {}
                  );
                  formik.setErrors(filteredErrors);
                }

                if (Object.keys(errors).length === 0) {
                  formik.submitForm();
                } else {
                  toast.error("Please fix the form errors before submitting");
                  Object.keys(formik.values).forEach((field) =>
                    formik.setFieldTouched(field, true)
                  );
                  formik.values.documents?.forEach((_: any, index: number) => {
                    formik.setFieldTouched(
                      `documents[${index}].IdentityType`,
                      true
                    );
                    formik.setFieldTouched(`documents[${index}].IdNo`, true);
                    formik.setFieldTouched(`documents[${index}].images`, true);
                  });
                }
              }}
            />
          </div>
        </Form>
      </FormikProvider>
    </div>
  );
};

export default ReservationForm;
