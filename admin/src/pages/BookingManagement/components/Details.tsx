import eye from "../../../assets/Svg/Eye.svg";
import { useState } from "react";
import { PopupModal } from "../../../components";
import { IBooking } from "../../../Interface/booking.interface";
import get from "lodash/get";
import { useGetAllPackages } from "../../../server-action/API/HotelConfiguration/roompackage";

interface propTypes {
  booking: IBooking;
}

const Details = ({ booking }: propTypes) => {
  const [showPopup, setShowPopup] = useState(false);

  // Use the packages hook
  const {
    data: packages,
    isLoading: packagesLoading,
    error: packagesError,
  } = useGetAllPackages();

  // Get the first package's name
  const packageType = packages && packages.length > 0 ? packages[0].name : "";

  const totalGuests =
    get(booking, "pax.adults", 0) +
    get(booking, "pax.children", 0) +
    (get(booking, "pax.infants", 0) ?? 0);

  const guestData = [
    { _id: "1", label: "Guest Full Name", value: "Starlight Haven" },
    { _id: "2", label: "Email", value: get(booking, "guest.email", "") },
    { _id: "3", label: "Country", value: "Nepal" },
    {
      _id: "4",
      label: "Phone Number",
      value: get(booking, "guest.phoneNumber", ""),
    },
    { _id: "5", label: "Nationality", value: "Nepali" },
    {
      _id: "8",
      label: "Address",
      value: get(booking, "guest.tempAddress.district", "ktm"),
    },
    // { _id: "9", label: "Purpose", value: "Holiday" },
    { _id: "9", label: "status", value: get(booking, "status", "") },
    {
      _id: "10",
      label: "Document",
      value: get(booking, "guest.documents") || "not uploaded",
    },
  ];

  console.log(booking, "details");

  const roomData = [
    { _id: "1", label: "Number of Guest", value: totalGuests },
    { _id: "2", label: "Adults", value: get(booking, "pax.adults", 0) },
    { _id: "3", label: "Kids", value: get(booking, "pax.children", 0) },
    { _id: "4", label: "Booking Number", value: get(booking, "bookingId", "") },
    {
      _id: "5",
      label: "Check In",
      value: (get(booking, "checkIn", "") as any)?.split("T")[0],
    },
    {
      _id: "6",
      label: "Room Type",
      value: get(booking, "room.roomType.name", ""),
    },
    { _id: "7", label: "Nights", value: get(booking, "durationNights", 0) },
    {
      _id: "8",
      label: "Check Out",
      value: (get(booking, "checkOut", "") as any)?.split("T")[0],
    },

    {
      _id: "9",
      label: "Facilities",
      value: get(booking, "room.amenities", [])
        .map((amenity) => get(amenity, "name", ""))
        .filter((name) => name) // Remove empty strings
        .join(", "),
    },

    { _id: "10", label: "OTA Platform", value: "Booking.com" },
    {
      _id: "11",
      label: "Package Type",
      value: packagesLoading ? "Loading..." : packageType || "No Package",
    },
    { _id: "12", label: "status", value: get(booking, "status", "") },
  ];

  if (packagesError) {
    console.error("Error loading packages:", packagesError);
  }

  return (
    <div>
      <div className="flex flex-col gap-4 md:flex-row">
        <div className="w-full md:w-1/2 h-fit">
          <Guest guestData={guestData} />
        </div>
        <div className="w-full md:w-1/2 h-fit">
          <Rooms roomData={roomData} />
        </div>
      </div>
    </div>
  );
};

export default Details;

interface mapperTypes {
  [key: string]: {
    _id: string;
    label: string;
    value?: any;
  }[];
}

export const Guest = ({ guestData }: mapperTypes) => {
  const [showPopup, setShowPopup] = useState(false);

  return (
    <div className="w-full flex flex-col p-5 gap-5 bg-white min-h-[28.9rem] h-full shadow-sm rounded-md">
      {showPopup && (
        <PopupModal onClose={() => setShowPopup(false)}>
          <div>
            <img
              src={guestData.find((item) => item.label === "Document")?.value}
              alt="Guest Document"
              className="w-full h-full "
            />
          </div>
        </PopupModal>
      )}
      <h1 className="font-bold text-black">Guest Information</h1>
      <div className="flex gap-4">
        <h1>Payment Status</h1>
        <button className="bg-[#FFC107] px-4 text-sm rounded-lg">
          {guestData.find((item) => item.label === "status")?.value}
        </button>
      </div>
      <div className="grid lg:grid-cols-3 md:grid-cols-2 gap-y-8">
        {guestData
          .filter(
            (item) => item.label !== "status" && item.label !== "Document"
          )
          .map((item) => (
            <div key={item._id} className="flex flex-col">
              <h1 className="font-semibold text-black">{item.label}</h1>
              <h1 className="text-black font-light text-[16px]">
                {item.value}
              </h1>
            </div>
          ))}
      </div>
      <div className="flex flex-col gap-3">
        <h1 className="font-semibold text-black">Document</h1>
        <div className="flex items-center gap-5">
          <img
            src={guestData.find((item) => item.label === "Document")?.value}
            alt="Guest Document"
            className="w-[5rem] h-full "
          />
          <div
            className="w-8 h-8 rounded-lg bg-[#729A48] flex justify-center items-center"
            onClick={() => setShowPopup(true)}
          >
            <img src={eye} alt="" />
          </div>
        </div>
      </div>
    </div>
  );
};

export const Rooms = ({ roomData }: mapperTypes) => {
  return (
    <div className="w-full flex flex-col p-5 gap-5 bg-white min-h-[28.9rem] h-full shadow-sm rounded-md">
      <h1 className="font-bold text-black">Room Information</h1>
      <div className="flex gap-4">
        <h1>Booking Status</h1>
        <button className="bg-[#28A745] px-4 text-sm rounded-lg text-white">
          {roomData.find((item) => item.label === "status")?.value}
        </button>
      </div>
      <div className="grid lg:grid-cols-3 md:grid-cols-2 gap-y-8">
        {roomData
          .filter(
            (item) => item.label !== "status" && item.label !== "Document"
          )
          .map((item) => (
            <div key={item._id} className="flex flex-col">
              <h1 className="font-semibold text-black">{item.label}</h1>
              <h1 className="text-black font-light text-[16px]">
                {item.value}
              </h1>
            </div>
          ))}
      </div>
    </div>
  );
};
