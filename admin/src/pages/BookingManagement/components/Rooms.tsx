import MasterTable from "../../../layouts/Table/MasterTable";
import { Formik, Form } from "formik";
import { DropdownField, InputField } from "../../../components";
import { IBooking } from "../../../Interface/booking.interface";
import { useState } from "react";

interface propTypes {
  booking: IBooking;
}
const Rooms = ({ booking }: propTypes) => {
  const [value, setValue] = useState("");

  const SampleTableData = [
    {
      date: (booking?.checkIn as any)?.split("T")[0],
      remaining: `${booking?.room?.roomNo} - ${booking?.room?.floor} Floor `,
    },
  ];

  const tableData = {
    columns: [
      { title: "Date", key: "date" },
      { title: "Room - Floor", key: "remaining" },
    ],
  };
  return (
    <div className="flex flex-col w-full gap-4">
      <div className="flex items-center justify-between w-full p-4 bg-white shadow-sm rounded-xl">
        <div className="">
          <Formik
            initialValues={{ date: "" }}
            onSubmit={(values) => {
              console.log(values);
            }}
          >
            <Form className="flex gap-4">
              <InputField
                label="Select Date"
                name="date"
                type="date"
                placeholder="Enter your username"
              />
              <DropdownField
              setValue={setValue}
              value={value}
                label="Select Date"
                name="date"
                options={[
                  { label: "First Floor", value: "First Floor" },
                  { label: "Second Floor", value: "Second Floor" },
                  { label: "Third Floor", value: "Third Floor" },
                ]}
              />
            </Form>
          </Formik>
        </div>
        <div className="">
          <button
            onClick={() => {}}
            className="px-4 py-2 text-sm text-black bg-white border rounded-lg shadow-sm"
          >
            Room Update
          </button>
        </div>
      </div>
      <MasterTable
        columns={tableData?.columns}
        rows={SampleTableData}
        loading={false}
      />
    </div>
  );
};

export default Rooms;
