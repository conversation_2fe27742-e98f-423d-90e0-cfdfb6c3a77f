import React, { useState } from "react";
import { Form, FormikProvider, useFormik } from "formik";
import * as Yup from "yup";
import moment from "moment";
import { get } from "lodash";
import { IBooking, BookingStatus, PaymentMethod } from "../../../Interface/booking.interface";
import { IRoom, RoomStatus } from "../../../Interface/room.interface";
import { useUpdateBooking } from "../../../server-action/API/BookingManagement/BookingManagement";
import { useUpdateRoom } from "../../../server-action/API/HotelConfiguration/room";
import { useGetOrders } from "../../../server-action/API/FoodOrdering/order";
import { FormField } from "./ReservationCustomForm";
import { Icon } from "@iconify/react/dist/iconify.js";
import cross from "../../../assets/Svg/Cross.svg";
import BillSummary from "./BillSummary";

interface CheckoutFormProps {
  booking: IBooking | IRoom; // Can be either booking or room
  onClose: () => void;
  onSuccess?: () => void;
  isRoomCheckout?: boolean; // Flag to indicate room checkout
}

const CheckoutValidationSchema = Yup.object().shape({
  actualCheckOut: Yup.string().required("Checkout date is required"),
  additionalPayment: Yup.number()
    .min(0, "Additional payment cannot be negative")
    .nullable()
    .transform((value, originalValue) => originalValue === "" ? 0 : value),
  paymentMethod: Yup.string().required("Payment method is required"),
  notes: Yup.string(),
});

const CheckoutForm: React.FC<CheckoutFormProps> = ({ booking, onClose, onSuccess, isRoomCheckout = false }) => {
  const [showWarning, setShowWarning] = useState(false);
  const updateBookingMutation = useUpdateBooking();
  const updateRoomMutation = useUpdateRoom();
  const { data: orders } = useGetOrders(isRoomCheckout ? "" : booking._id);

  // Calculate total bill amount
  const calculateTotalAmount = () => {
    if (isRoomCheckout) {
      // For room checkout, use room base price
      const roomCharges = get(booking, "roomPrice.base", 0);
      const tax = roomCharges * 0.10; // 10% tax
      return roomCharges + tax;
    }

    const roomCharges = get(booking, "amount", 0);

    const serviceCharges = (booking as IBooking).usedServices?.reduce(
      (total, service) => total + service.price,
      0
    ) || 0;

    const activityCharges = (booking as IBooking).bookedActivities?.reduce(
      (total, activity) => total + (get(activity, "price", 0) || 0),
      0
    ) || 0;

    const orderCharges = orders?.reduce((total, order: any) => {
      return total + order.menuItems.reduce((orderTotal: number, item: any) => {
        const itemPrice = item.sizePrice || get(item, "item.price", 0);
        return orderTotal + (itemPrice * item.quantity);
      }, 0);
    }, 0) || 0;

    const subtotal = roomCharges + serviceCharges + activityCharges + orderCharges;
    const tax = subtotal * 0.10; // 10% tax
    return subtotal + tax;
  };

  const totalAmount = calculateTotalAmount();
  const currentPaidAmount = get(booking, "amountPaid", 0);

  const formik = useFormik({
    initialValues: {
      actualCheckOut: moment().format("YYYY-MM-DD"),
      additionalPayment: 0, // New payment amount to add
      paymentMethod: get(booking, "paymentMethod", "cash") as PaymentMethod,
      notes: "",
    },
    validationSchema: CheckoutValidationSchema,
    onSubmit: async (values) => {
      try {
        // Calculate final total paid amount (existing + new payment)
        const finalTotalPaid = currentPaidAmount + values.additionalPayment;

        if (isRoomCheckout) {
          // For room checkout, update room status to cleaning
          const roomUpdateData = {
            status: RoomStatus.CLEANING,
            lastCleaned: values.actualCheckOut,
            notes: values.notes,
          };

          await updateRoomMutation.mutateAsync({
            roomData: roomUpdateData,
            _id: booking._id!,
          });
        } else {
          // Check if total paid amount is less than total bill
          if (finalTotalPaid < totalAmount) {
            setShowWarning(true);
            return;
          }

          // Prepare checkout data for booking
          const checkoutData = {
            checkOut: values.actualCheckOut,
            amountPaid: finalTotalPaid, // Total cumulative amount
            paymentMethod: values.paymentMethod,
            status: BookingStatus.CHECKED_OUT,
            ...(values.notes && { notes: values.notes }),
          };

          await updateBookingMutation.mutateAsync({
            bookingData: checkoutData,
            _id: booking._id,
          });
        }

        onSuccess?.();
        onClose();
      } catch (error) {
        console.error("Error during checkout:", error);
      }
    },
  });

  const handleForceCheckout = async () => {
    try {
      if (isRoomCheckout) {
        // For room checkout, update room status to cleaning
        const roomUpdateData = {
          status: RoomStatus.CLEANING,
          lastCleaned: formik.values.actualCheckOut,
          notes: formik.values.notes,
        };

        await updateRoomMutation.mutateAsync({
          roomData: roomUpdateData,
          _id: booking._id!,
        });
      } else {
        const finalTotalPaid = currentPaidAmount + formik.values.additionalPayment;

        const checkoutData = {
          checkOut: formik.values.actualCheckOut,
          amountPaid: finalTotalPaid, // Total cumulative amount
          paymentMethod: formik.values.paymentMethod,
          status: BookingStatus.CHECKED_OUT,
          ...(formik.values.notes && { notes: formik.values.notes }),
        };

        await updateBookingMutation.mutateAsync({
          bookingData: checkoutData,
          _id: booking._id,
        });
      }

      onSuccess?.();
      onClose();
    } catch (error) {
      console.error("Error during force checkout:", error);
    }
  };

  const formFields = [
    {
      label: "Checkout Date",
      field: "actualCheckOut",
      type: "date",
      required: true,
    },
    {
      label: "Additional Payment",
      field: "additionalPayment",
      type: "number",
      required: false,
    },
    {
      label: "Payment Method",
      field: "paymentMethod",
      type: "select",
      required: true,
      options: [
        { value: "cash", label: "Cash" },
        { value: "online", label: "Online" },
      ],
    },
    {
      label: "Notes",
      field: "notes",
      type: "textarea",
      required: false,
    },
  ];

  return (
    <div className="w-full max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-lg">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200 bg-[#EBFEF4]">
          <h2 className="text-xl font-semibold text-gray-900">
            {isRoomCheckout ? `Checkout Room ${get(booking, "roomNo", "")}` : "Checkout Booking"}
          </h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <img src={cross} alt="Close" className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Left Column - Bill Summary */}
            <div>
              <BillSummary booking={booking as IBooking} />
            </div>

            {/* Right Column - Checkout Form */}
            <div>
              <FormikProvider value={formik}>
                <Form className="space-y-4">
                  {formFields.map((field) => (
                    <FormField
                      key={field.field}
                      label={field.label}
                      name={field.field}
                      type={field.type}
                      options={field.options}
                      required={field.required}
                      formik={formik}
                    />
                  ))}

                  {/* Payment Summary */}
                  <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600">{isRoomCheckout ? "Room Total:" : "Total Bill Amount:"}:</span>
                      <span className="font-semibold">Rs. {totalAmount.toLocaleString()}</span>
                    </div>
                    {!isRoomCheckout && (
                      <>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Already Paid:</span>
                          <span className="font-semibold text-green-600">Rs. {currentPaidAmount.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Additional Payment:</span>
                          <span className="font-semibold text-blue-600">Rs. {formik.values.additionalPayment.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between border-t pt-2">
                          <span className="text-gray-600">Total Paid Amount:</span>
                          <span className="font-semibold text-green-600">
                            Rs. {(currentPaidAmount + formik.values.additionalPayment).toLocaleString()}
                          </span>
                        </div>
                        <div className="flex justify-between border-t pt-2">
                          <span className="text-gray-600">Remaining Balance:</span>
                          <span className={`font-semibold ${
                            (totalAmount - (currentPaidAmount + formik.values.additionalPayment)) > 0 ? 'text-red-600' : 'text-green-600'
                          }`}>
                            Rs. {(totalAmount - (currentPaidAmount + formik.values.additionalPayment)).toLocaleString()}
                          </span>
                        </div>
                      </>
                    )}
                    {isRoomCheckout && (
                      <>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Current Status:</span>
                          <span className="font-semibold capitalize">{get(booking, "status", "N/A")}</span>
                        </div>
                        <div className="flex justify-between border-t pt-2">
                          <span className="text-gray-600">After Checkout:</span>
                          <span className="font-semibold text-blue-600">Cleaning</span>
                        </div>
                      </>
                    )}
                  </div>

                  {/* Warning Message */}
                  {showWarning && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                      <div className="flex items-start">
                        <Icon icon="mdi:warning" className="text-yellow-600 mt-0.5 mr-2" width="20" height="20" />
                        <div>
                          <h3 className="text-yellow-800 font-medium">Payment Warning</h3>
                          <p className="text-yellow-700 text-sm mt-1">
                            The total paid amount (Rs. {(currentPaidAmount + formik.values.additionalPayment).toLocaleString()}) is less than the total bill
                            (Rs. {totalAmount.toLocaleString()}). There will be a remaining balance of Rs. {(totalAmount - (currentPaidAmount + formik.values.additionalPayment)).toLocaleString()}.
                          </p>
                          <div className="mt-3 flex gap-2">
                            <button
                              type="button"
                              onClick={handleForceCheckout}
                              className="bg-yellow-600 text-white px-3 py-1 rounded text-sm hover:bg-yellow-700"
                              disabled={isRoomCheckout ? updateRoomMutation.isPending : updateBookingMutation.isPending}
                            >
                              Proceed Anyway
                            </button>
                            <button
                              type="button"
                              onClick={() => setShowWarning(false)}
                              className="bg-gray-300 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-400"
                            >
                              Cancel
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="flex justify-end gap-3 pt-4">
                    <button
                      type="button"
                      onClick={onClose}
                      className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-6 py-2 bg-[#6047E4] text-white rounded-md hover:bg-[#5038D3] disabled:opacity-50"
                      disabled={isRoomCheckout ? updateRoomMutation.isPending : updateBookingMutation.isPending}
                    >
                      {(isRoomCheckout ? updateRoomMutation.isPending : updateBookingMutation.isPending) ? "Processing..." : "Complete Checkout"}
                    </button>
                  </div>
                </Form>
              </FormikProvider>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutForm;
