import type React from "react";
import { useState } from "react";
import CustomSelect from "../../../components/GlobalForm/CustomSelect";
import CustomDatePicker from "../../../components/GlobalForm/CustomDatePicker";

interface FilterOption {
  label: string;
  value: string;
}

interface BookingFilterProps {
  onSearch: (value: string) => void;
  onApplyFilters: (filters: {
    status: string;
    reservationDate: string;
    checkIn: string;
    checkOut: string;
    minAmount: string;
    maxAmount: string;
  }) => void;
  statusOptions: FilterOption[];
}

const BookingFilter = ({
  onSearch,
  onApplyFilters,
  statusOptions,
}: BookingFilterProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [reservationDate, setReservationDate] = useState("");
  const [checkIn, setCheckIn] = useState("");
  const [checkOut, setCheckOut] = useState("");
  const [minAmount, setMinAmount] = useState("");
  const [maxAmount, setMaxAmount] = useState("");

  const allStatusOptions = [{ label: "All", value: "all" }, ...statusOptions];

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    onSearch(value); // Trigger search filtering immediately
  };

  const handleResetFilters = () => {
    setSearchTerm("");
    setSelectedStatus("all");
    setReservationDate("");
    setCheckIn("");
    setCheckOut("");
    setMinAmount("");
    setMaxAmount("");
    onSearch(""); // Reset search
    onApplyFilters({
      status: "all",
      reservationDate: "",
      checkIn: "",
      checkOut: "",
      minAmount: "",
      maxAmount: "",
    }); // Reset other filters
  };

  const handleApplyFilters = () => {
    onApplyFilters({
      status: selectedStatus,
      reservationDate,
      checkIn,
      checkOut,
      minAmount,
      maxAmount,
    }); // Apply status, date, and amount filters
  };

  return (
    <div className="flex flex-col md:flex-row md:items-end gap-4">
      {/* Status Filter */}
      <div className="w-full md:w-48">
        <CustomSelect
          label="Booking Status"
          value={selectedStatus}
          options={allStatusOptions}
          onChange={(val) => setSelectedStatus(val)}
          className="w-full"
          isForm={false}
        />
      </div>

      {/* Reservation Date */}
      <div className="w-full md:w-48">
        <CustomDatePicker
          value={reservationDate}
          onChange={(e) => setReservationDate(e.target.value)}
          label="Reservation Date"
          isForm={false}
        />
      </div>

      {/* Check In Date */}
      <div className="w-full md:w-48">
        <CustomDatePicker
          value={checkIn}
          onChange={(e) => setCheckIn(e.target.value)}
          label="Check In"
          isForm={false}
        />
      </div>

      {/* Check Out Date */}
      {/* <div className="w-full md:w-48">
        <CustomDatePicker
          value={checkOut}
          onChange={(e) => setCheckOut(e.target.value)}
          label="Check Out"
          isForm={false}
        />
      </div> */}

      {/* Buttons */}
      <div className="flex gap-2">
        <button
          onClick={handleResetFilters}
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100 focus:outline-none"
        >
          Reset
        </button>
        <button
          onClick={handleApplyFilters}
          className="px-4 py-2 bg-[#2A3A6D] text-white rounded-md focus:outline-none"
        >
          Apply Filters
        </button>
      </div>

      {/* Search */}
      <div className="flex-1 flex justify-end mt-6 md:mt-0">
        <div className="relative w-full md:w-64">
          <input
            type="text"
            placeholder="Search..."
            value={searchTerm}
            onChange={handleSearchChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 pl-10"
          />
          <svg
            className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
      </div>
    </div>
  );
};

export default BookingFilter;
