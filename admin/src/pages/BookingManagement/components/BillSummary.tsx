import React from "react";
import { get } from "lodash";
import { IBooking } from "../../../Interface/booking.interface";
import { useGetOrders } from "../../../server-action/API/FoodOrdering/order";

interface BillSummaryProps {
  booking: IBooking;
}

interface BillBreakdown {
  roomCharges: number;
  serviceCharges: number;
  activityCharges: number;
  orderCharges: number;
  tax: number;
  discount: number;
  subtotal: number;
  totalAmount: number;
  amountPaid: number;
  remainingBalance: number;
}

const BillSummary: React.FC<BillSummaryProps> = ({ booking }) => {
  const { data: orders } = useGetOrders(booking._id);

  const calculateBillBreakdown = (): BillBreakdown => {
    // Room charges
    const roomCharges = get(booking, "amount", 0);

    // Service charges
    const serviceCharges = booking.usedServices?.reduce(
      (total, service) => total + service.price,
      0
    ) || 0;

    // Activity charges
    const activityCharges = booking.bookedActivities?.reduce(
      (total, activity) => total + (get(activity, "price", 0) || 0),
      0
    ) || 0;

    // Order charges from food & beverage
    const orderCharges = orders?.reduce((total, order: any) => {
      return total + order.menuItems.reduce((orderTotal: number, item: any) => {
        const itemPrice = item.sizePrice || get(item, "item.price", 0);
        return orderTotal + (itemPrice * item.quantity);
      }, 0);
    }, 0) || 0;

    // Calculate subtotal
    const subtotal = roomCharges + serviceCharges + activityCharges + orderCharges;

    // Tax calculation (10% as shown in RoomBill component)
    const tax = subtotal * 0.10;

    // Discount (if any package discount exists)
    const discount = 0; // Can be enhanced to include package discounts

    // Total amount
    const totalAmount = subtotal + tax - discount;

    // Amount paid
    const amountPaid = get(booking, "amountPaid", 0);

    // Remaining balance
    const remainingBalance = totalAmount - amountPaid;

    return {
      roomCharges,
      serviceCharges,
      activityCharges,
      orderCharges,
      tax,
      discount,
      subtotal,
      totalAmount,
      amountPaid,
      remainingBalance,
    };
  };

  const billBreakdown = calculateBillBreakdown();

  const formatCurrency = (amount: number) => `Rs. ${amount.toLocaleString()}`;

  return (
    <div className="bg-white rounded-lg shadow-sm border">
      <div className="p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Bill Summary</h2>

        <div className="space-y-3">
          {/* Room Charges */}
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Room Charges</span>
            <span className="font-medium">{formatCurrency(billBreakdown.roomCharges)}</span>
          </div>

          {/* Service Charges */}
          {billBreakdown.serviceCharges > 0 && (
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Service Charges</span>
              <span className="font-medium">{formatCurrency(billBreakdown.serviceCharges)}</span>
            </div>
          )}

          {/* Activity Charges */}
          {billBreakdown.activityCharges > 0 && (
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Activity Charges</span>
              <span className="font-medium">{formatCurrency(billBreakdown.activityCharges)}</span>
            </div>
          )}

          {/* Food & Beverage Charges */}
          {billBreakdown.orderCharges > 0 && (
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Food & Beverage</span>
              <span className="font-medium">{formatCurrency(billBreakdown.orderCharges)}</span>
            </div>
          )}

          {/* Subtotal */}
          <div className="flex justify-between items-center pt-2 border-t border-gray-200">
            <span className="text-gray-600">Subtotal</span>
            <span className="font-medium">{formatCurrency(billBreakdown.subtotal)}</span>
          </div>

          {/* Tax */}
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Tax (10%)</span>
            <span className="font-medium">{formatCurrency(billBreakdown.tax)}</span>
          </div>

          {/* Discount */}
          {billBreakdown.discount > 0 && (
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Discount</span>
              <span className="font-medium text-green-600">-{formatCurrency(billBreakdown.discount)}</span>
            </div>
          )}

          {/* Total Amount */}
          <div className="flex justify-between items-center pt-3 border-t-2 border-gray-300">
            <span className="text-lg font-semibold text-gray-900">Total Amount</span>
            <span className="text-lg font-bold text-gray-900">{formatCurrency(billBreakdown.totalAmount)}</span>
          </div>

          {/* Amount Paid */}
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Total Paid</span>
            <span className="font-medium text-green-600">{formatCurrency(billBreakdown.amountPaid)}</span>
          </div>

          {/* Remaining Balance */}
          <div className="flex justify-between items-center pb-2">
            <span className="text-gray-600 font-medium">Outstanding Balance</span>
            <span className={`font-bold text-lg ${
              billBreakdown.remainingBalance > 0 ? 'text-red-600' : 'text-green-600'
            }`}>
              {billBreakdown.remainingBalance > 0
                ? formatCurrency(billBreakdown.remainingBalance)
                : 'PAID IN FULL'
              }
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BillSummary;
