import MasterTable from "../../../layouts/Table/MasterTable";
import house from "../../../assets/images/image.png";
import print from "../../../assets/Svg/Print.svg";
export const PrintLayout = ({ onClose }: { onClose: () => void }) => {
  return (
    <div className="w-full min-w-[40rem] h-[60rem] py-2 px-6 overflow-scroll">
      <div className="w-full h-fit flex justify-between ">
        <img src={house} alt="" className="w-46 h-16" />
        <div className="w-full h-full relative ">
          <div className="absolute right-0 -top-5 flex justify-center items-end p-2 w-[6rem] h-[5rem] bg-[#EBFEF4]">
            <h1 className="font-semibold">INVOICE</h1>
          </div>
        </div>
      </div>

      <div className="flex justify-between mt-4 items-center ">
        <div className="flex flex-col gap-2">
          <h1 className="text-black font-semibold">
            Invoice Number: <span className="font-normal">81</span>
          </h1>
          <h1 className="text-black font-semibold">
            Date: <span className="font-normal">81/121/12</span>
          </h1>
          <h1 className="text-black font-semibold">
            Payment Method: <span className="font-normal">Cash</span>
          </h1>
        </div>
        <div>
          <button className="bg-[#EBFEF4] px-4 py-2 text-sm rounded-lg text-black font-semibold flex justify-center items-center gap-2">
            <img src={print} alt="" />
            Print
          </button>
        </div>
      </div>

      <div className="mt-4">
        <div className="w-full flex justify-center items-center rounded-lg p-4 h-12 bg-[#EBFEF4]">
          <h1 className="font-semibold">Invoice Details</h1>
        </div>
      </div>

      <section>
        <PaymentBillingInfo />
      </section>

      <section>
        <InvoiceTable />
      </section>

      <section className="w-full flex justify-end mt-4">
        <div className="flex-col space-y-2 w-fit text-right text-sm">
          <h1 className="text-black font-semibold">Subtotal: Rs.2200</h1>
          <h1 className="text-black font-semibold">VAT(10%): Rs.2200</h1>
          <h1 className="text-black font-semibold">Total Amount: Rs.2200</h1>
          <h1 className="text-black font-semibold">Amount Paid: Rs.2200</h1>
          <h1 className="text-black font-semibold">Balance Due: Rs.2200</h1>
        </div>
      </section>

      <section className="border-t mt-4 border-black py-4">
        <div className="flex flex-col gap-1">
          <h1>Terms and Conditions</h1>
          <p>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
            eiusmod tempor incididunt ut labore et dolore magna aliqua.
          </p>
          <div className="flex justify-end">
            <button
              className="bg-[#6047E4] text-white px-4 py-2 text-sm rounded-lg font-semibold"
              onClick={onClose}
            >
              Close
            </button>
          </div>
        </div>
      </section>
    </div>
  );
};

export const InvoiceTable = () => {
  const tableData = [
    {
      details: "Room Charges (2 Nights)",
      price: "$150",
      qty: "1",
      total: "$300",
    },
    {
      details: "Breakfast (per day)",
      price: "$15",
      qty: "2",
      total: "$30",
    },
  ];
  const columns = [
    { title: "Details", key: "details" },
    { title: "Price", key: "price" },
    { title: "Qty", key: "qty" },
    { title: "Total", key: "total" },
  ];

  return <MasterTable columns={columns} rows={tableData} loading={false} />;
};

export const PaymentBillingInfo = () => {
  const data = {
    paymentTo: [
      { label: "Hotel Name:", value: "House Guest Hotel" },
      {
        label: "Address:",
        value: [
          "1234 Maple Street Apt 5B",
          "Springfield, IL 62704 United States",
        ],
      },
      { label: "Email:", value: "<EMAIL>" },
      { label: "Phone Number:", value: "+977-9812345678" },
    ],
    billTo: [
      { label: "Guest Name:", value: "John Doe" },
      {
        label: "Address:",
        value: [
          "1234 Maple Street Apt 5B",
          "Springfield, IL 62704 United States",
        ],
      },
      { label: "Email:", value: "<EMAIL>" },
      { label: "Phone Number:", value: "+977-9812345678" },
    ],
  };

  return (
    <div className="w-full px-4 py-6">
      <div className="grid grid-cols-2 gap-8">
        <div className="col-span-1">
          <h2 className="font-bold mb-2 text-sm">Payment To</h2>

          <div className="space-y-2">
            {data.paymentTo.map((item, index) => (
              <div key={index}>
                <span className="font-semibold text-sm">{item.label} </span>
                {Array.isArray(item.value) ? (
                  <div className="text-sm">
                    <span>{item.value[0]}</span>
                    <div>{item.value[1]}</div>
                  </div>
                ) : (
                  <span className="text-sm">{item.value}</span>
                )}
              </div>
            ))}
          </div>
        </div>

        <div className="col-span-1">
          <h2 className="font-bold mb-2 text-sm">Bill To</h2>

          <div className="space-y-2">
            {data.billTo.map((item, index) => (
              <div key={index}>
                <span className="font-semibold text-sm">{item.label} </span>
                {Array.isArray(item.value) ? (
                  <div className="text-sm">
                    <span>{item.value[0]}</span>
                    <div>{item.value[1]}</div>
                  </div>
                ) : (
                  <span className="text-sm">{item.value}</span>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
