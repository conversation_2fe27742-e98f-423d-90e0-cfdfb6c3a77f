import { Form, Formik } from "formik";
import { useState } from "react";
import cross from "../../../assets/Svg/Cross.svg";
import { InputField, PopupModal } from "../../../components";
import MasterTable from "../../../layouts/Table/MasterTable";
import { IBooking } from "../../../Interface/booking.interface";
import moment from "moment";
import get from "lodash/get";
import { DateForamter } from "../../../components/DateFormater";

interface RoomBillProps {
  booking: IBooking;
}

const RoomBill = ({ booking }: RoomBillProps) => {
  const [showPopup, setShowPopup] = useState(false);

  // Calculate the number of nights (used for display or calculations if needed)
  const nights = moment(get(booking, "checkOut")).diff(
    moment(get(booking, "checkIn")),
    "days"
  );

  // Log nights for debugging
  console.log(`Booking for ${nights} nights`);

  // Calculate the room price
  const roomPrice = get(booking, "amount", 0);

  // Get room information
  const roomNo = get(booking, "room.roomNo", "");
  const roomFloor = get(booking, "room.floor", "");
  const roomFloorDisplay = roomFloor
    ? `${roomNo} - ${roomFloor} Floor`
    : roomNo;

  const tableData = {
    columns: [
      { title: "Bill Number", key: "billNumber" },
      { title: "Date", key: "date" },
      { title: "Room Floor", key: "roomFloor" },
      { title: "Tax", key: "tax" },
      { title: "Price", key: "price" },
    ],
    rows: [
      {
        key: 1,
        billNumber: `B-${get(booking, "_id", "").slice(-5)}`,
        date: DateForamter(booking?.checkIn),
        roomFloor: roomFloorDisplay,
        tax: "10%",
        price: `Rs. ${roomPrice}`,
      },
    ],
  };

  return (
    <div>
      {showPopup && (
        <PopupModal onClose={() => setShowPopup(false)}>
          <EditForm onclose={() => setShowPopup(false)} booking={booking} />
        </PopupModal>
      )}
      <div className="flex flex-col w-full gap-6 bg-white rounded-md ">
        <h1 className="mx-6 mt-4 font-bold text-black">Room Bill</h1>
        <MasterTable
          columns={tableData.columns}
          rows={tableData.rows}
          loading={false}
        />
      </div>
    </div>
  );
};

export default RoomBill;

interface EditFormProps {
  onclose: () => void;
  booking?: IBooking;
}

export const EditForm = ({ onclose, booking }: EditFormProps) => {
  // Get room rate from booking if available
  const roomRate = booking ? get(booking, "amount", 0) : 0;

  return (
    <div className="w-full min-w-[30rem] h-fit">
      <div className="w-full h-12 flex justify-between bg-[#EBFEF4] items-center px-4">
        <div className="flex items-center justify-center flex-1 w-full">
          <h1 className="font-semibold text-black text-medium">
            Update Room Bill
          </h1>
        </div>
        <div onClick={onclose}>
          <img src={cross} alt="" />
        </div>
      </div>
      <Formik
        initialValues={{
          roomrate: roomRate.toString(),
          tax: "10%",
        }}
        onSubmit={(values) => {
          console.log(values);
          // Here you would update the booking with the new values
          onclose();
        }}
      >
        <Form className="p-4">
          <div className="grid grid-cols-2 gap-4">
            <InputField label="Room Rate" name="roomrate" placeholder="2000" />
            <InputField label="Tax" name="tax" placeholder="10%" />
          </div>

          <div className="flex items-end justify-end w-full h-full mt-4">
            <div className="flex gap-2">
              <button
                type="submit"
                className="bg-[#6047E4] px-6 py-2 text-sm rounded-lg text-white font-semibold flex justify-center items-center gap-2"
              >
                Update
              </button>
            </div>
          </div>
        </Form>
      </Formik>
    </div>
  );
};
