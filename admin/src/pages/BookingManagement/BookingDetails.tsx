import moment from "moment";
import { useState } from "react";
import { useParams } from "react-router-dom";
import { CustomTabs } from "../../components/CustomTab";
import { PopupModal } from "../../components";
import { useGetBookingById } from "../../server-action/API/BookingManagement/BookingManagement";
import { BookingStatus } from "../../Interface/booking.interface";
import Details from "./components/Details";
import Folio from "./components/Folio";
import Payment from "./components/Payment";
import CheckoutForm from "./components/CheckoutForm";
import { Icon } from "@iconify/react/dist/iconify.js";

const tabs = ["Folio", "Details", "Payments"];

export function getStatus(checkIn: string, checkOut: string): string {
  const today = moment().startOf("day");
  const checkInDate = moment(checkIn).startOf("day");
  const checkOutDate = moment(checkOut).startOf("day");

  if (today.isSameOrAfter(checkOutDate)) {
    return "checkout";
  } else if (today.isSameOrAfter(checkInDate) && today.isBefore(checkOutDate)) {
    return "checkin";
  } else {
    return "upcoming";
  }
}

const BookingDetails = () => {
  const { id } = useParams();
  const [selectedTab, setSelectedTab] = useState("Folio");
  const [showCheckoutModal, setShowCheckoutModal] = useState(false);
  const { data: bookingData, refetch } = useGetBookingById(id ?? "");

  const renderTabContent = () => {
    if (!bookingData?.data) {
      return <div>Loading booking details...</div>;
    }

    switch (selectedTab) {
      case "Folio":
        return <Folio booking={bookingData.data} onBookingUpdate={refetch} />;
      case "Details":
        return <Details booking={bookingData.data} />;
      case "Payments":
        return <Payment booking={bookingData.data} />;
      default:
        return null;
    }
  };

  const status = bookingData?.data ? getStatus(
    bookingData.data.checkIn as string,
    bookingData.data.checkOut as string
  ) : "unknown";

  const booking = bookingData?.data;
  const canCheckout = booking &&
    (booking.status === BookingStatus.CHECKED_IN || booking.status === BookingStatus.CONFIRMED) &&
    status !== "checkout";

  const handleCheckoutSuccess = () => {
    refetch();
    setShowCheckoutModal(false);
  };

  return (
    <div className="flex flex-col w-full h-full gap-4">
      {/* Checkout Modal */}
      {showCheckoutModal && booking && (
        <PopupModal onClose={() => setShowCheckoutModal(false)}>
          <CheckoutForm
            booking={booking}
            onClose={() => setShowCheckoutModal(false)}
            onSuccess={handleCheckoutSuccess}
          />
        </PopupModal>
      )}

      <div className="w-full p-4 bg-white shadow-sm rounded-xl">
        <CustomTabs
          tabs={tabs}
          defaultTab="Folio"
          onTabChange={(tab: any) => setSelectedTab(tab)}
        />
      </div>

      <section>
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center gap-4">
            <h1 className="text-sm font-semibold">Status</h1>
            <button
              className={`h-fit ${
                status === "checkin" ? "bg-[#28A745]" :
                status === "checkout" ? "bg-blue-600" : "bg-red-500"
              } py-1 px-2 text-[12px] rounded-md tracking-widest text-white`}
            >
              {status === "checkout" ? "CHECKED OUT" : status.toUpperCase()}
            </button>
          </div>

          {/* Checkout Button */}
          {canCheckout && (
            <button
              onClick={() => setShowCheckoutModal(true)}
              className="flex items-center gap-2 bg-[#6047E4] hover:bg-[#5038D3] text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
            >
              <Icon icon="mdi:logout" width="16" height="16" />
              Checkout
            </button>
          )}
        </div>
      </section>

      <div className="flex-1">{renderTabContent()}</div>
    </div>
  );
};

export default BookingDetails;
