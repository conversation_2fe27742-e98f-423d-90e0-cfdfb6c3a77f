import React from "react";

interface ImageModalProps {
  selectedImage: string | null;
  onClose: () => void;
  downloadable?: boolean;
  printable?: boolean;
  closable?: boolean;
}

const ImageModal: React.FC<ImageModalProps> = ({
  selectedImage,
  onClose,
  downloadable = false,
  printable = false,
  closable = true,
}) => {
  if (!selectedImage) return null;

  const handlePrint = () => {
    const printWindow = window.open("", "_blank");
    if (!printWindow) return;

    printWindow.document.write(`
      <html>
        <head>
          <title>Print Image</title>
          <style>
            @page { margin: 0; }
            body { display: flex; align-items: center; justify-content: center; margin: 0; }
            img { max-width: 100%; max-height: 100%; }
          </style>
        </head>
        <body>
          <img src="${selectedImage}" onload="window.print(); window.onafterprint = window.close();" />
        </body>
      </html>
    `);
    printWindow.document.close();
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      onClick={onClose}
    >
      <div
        className="bg-white max-h-[80vh] p-2 rounded-md shadow-lg max-w-lg w-full border-b-2 relative"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex justify-end items-center gap-2 my-1">
          {printable && (
            <button onClick={handlePrint}>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
              >
                <path
                  fill="currentColor"
                  d="M16 8V5H8v3H6V3h12v5zM4 10h16zm14 2.5q.425 0 .713-.288T19 11.5t-.288-.712T18 10.5t-.712.288T17 11.5t.288.713t.712.287M16 19v-4H8v4zm2 2H6v-4H2v-6q0-1.275.875-2.137T5 8h14q1.275 0 2.138.863T22 11v6h-4zm2-6v-4q0-.425-.288-.712T19 10H5q-.425 0-.712.288T4 11v4h2v-2h12v2z"
                />
              </svg>
            </button>
          )}

          {downloadable && (
            <a
              href={selectedImage}
              download={`image_${new Date().toISOString().slice(0, 10)}.jpg`}
              className="pl-2 py-1 text-lg rounded-md"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
              >
                <path
                  fill="darkblue"
                  d="m12 16l-5-5l1.4-1.45l2.6 2.6V4h2v8.15l2.6-2.6L17 11zm-6 4q-.825 0-1.412-.587T4 18v-3h2v3h12v-3h2v3q0 .825-.587 1.413T18 20z"
                />
              </svg>
            </a>
          )}

          {closable && (
            <button
              className="px-2 py-1 text-base rounded-md"
              onClick={onClose}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
              >
                <path
                  fill="red"
                  d="M6.4 19L5 17.6l5.6-5.6L5 6.4L6.4 5l5.6 5.6L17.6 5L19 6.4L13.4 12l5.6 5.6l-1.4 1.4l-5.6-5.6z"
                />
              </svg>
            </button>
          )}
        </div>

        <div className="max-h-[70vh] p-2 m-2 overflow-auto flex items-center justify-center">
          <img
            src={selectedImage}
            alt="Selected Document"
            className="max-w-full max-h-full rounded-md object-contain"
          />
        </div>
      </div>
    </div>
  );
};

export default ImageModal;
