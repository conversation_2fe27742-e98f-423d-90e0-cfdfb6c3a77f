import MasterTable from "../../../layouts/Table/MasterTable";
import { TableAction } from "../../../layouts/Table/TableAction";
import Header from "../../../components/Header";
import {
  useGetAllBank,
  useDeletBankModule,
} from "../../../server-action/API/Bank/FinanceCategory/BankCategory";
import get from "lodash/get";
import CategoryBankForm from "./Component/BankForm";
import BankTransactionList from "./Component/BankTransactionList";
import BankTransactionForm from "./Component/BankTransactionForm";
import { useState } from "react";
import { PopupModal } from "../../../components";
import { CustomTabs } from "../../../components/CustomTab";
import BankForm from "./Component/BankForm";

interface BankData {
  bankId?: string;
  bankName: string;
  branchName?: string;
  accountHolderName?: string;
  accountNumber?: string;
  balance?: number;
  _id: string;
}

interface FormState {
  state: boolean;
  edit: boolean;
  editData?: BankData | null;
}

interface CategoryDetailProps {
  onSearch?: (searchTerm: string) => void;
  canSearch?: boolean;
}

const BankIndex: React.FC<CategoryDetailProps> = () => {
  const [showPopup, setShowPopup] = useState<string>("");
  const [selectedTab, setSelectedTab] = useState<string>("Bank Accounts");

  const { data = [], isLoading, isSuccess } = useGetAllBank();
  const { mutate: deleteModule } = useDeletBankModule();
  const [formState, setFormState] = useState<FormState>({
    state: false,
    edit: true,
    editData: null,
  });

  // Define tabs
  const tabs = ["Bank Accounts", "Bank Transactions"];

  const tableData = {
    columns: [
      // { title: "Bank ID", key: "bankId" },
      { title: "Bank Name", key: "bankName" },
      { title: "Bank Branch", key: "branchName" },
      { title: "Account No.", key: "accountNumber" },
      { title: "Account Name", key: "accountHolderName" },
      { title: "Current Balance", key: "currentBalance" },
      { title: "Actions", key: "action" },
    ],
    rows: isSuccess
      ? data?.map((item) => ({
          id: item._id,
          // bankId: get(item, "bankId", "-"),
          bankName: get(item, "bankName", "-"),
          branchName: get(item, "branchName", "-"),
          accountHolderName: get(item, "accountHolderName", "-"),
          accountNumber: get(item, "accountNumber", "-"),
          currentBalance: get(item, "currentBalance"),
          action: (
            <div className="relative">
              <TableAction
                onShow={() => {
                  setFormState({
                    edit: false,
                    state: false,
                    editData: item,
                  });
                  setShowPopup("view");
                }}
                onEdit={() =>
                  setFormState({
                    edit: true,
                    state: true,
                    editData: item,
                  })
                }
                onDelete={() => deleteModule(item._id)}
              />
            </div>
          ),
        }))
      : [],
  };

  return (
    <div className="">
      <Header
        title={
          selectedTab === "Bank Accounts" ? "New Bank" : "Bank Transactions"
        }
        onAddClick={() => {
          if (selectedTab === "Bank Accounts") {
            setShowPopup("New Bank");
          } else {
            setShowPopup("New Transaction");
          }
        }}
      />

      {showPopup === "New Bank" && (
        <CategoryBankForm close={() => setShowPopup("")} />
      )}

      {showPopup === "New Transaction" && (
        <BankTransactionForm close={() => setShowPopup("")} />
      )}

      {showPopup === "view" && formState.editData && (
        <PopupModal onClose={() => setShowPopup("")}>
          <div className="w-full bg-white">
            <div className="relative flex items-center justify-between bg-[#EBFEF4]">
              <h1 className="w-full p-4 text-center font-semibold text-lg">
                Bank Details
              </h1>
              <button
                className="absolute flex items-center justify-center p-1 bg-black rounded-full top-4 right-4"
                onClick={() => setShowPopup("")}
              >
                <svg
                  width="14"
                  height="14"
                  viewBox="0 0 14 14"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="text-white"
                >
                  <path
                    d="M1 1L13 13M1 13L13 1"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                  />
                </svg>
              </button>
            </div>

            <div className="p-8 flex justify-center">
              <div className="border rounded-lg p-8 w-full max-w-4xl shadow-md bg-white">
                <div className="mb-6 pb-5 border-b">
                  <div className="flex items-center space-x-3">
                    <p className="text-base text-gray-500">Date :</p>
                    <p className="font-medium text-base">
                      {get(formState.editData, "date", "-")}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-40 gap-y-6 mb-6 pb-5 border-b">
                  <div className="flex items-center space-x-2">
                    <p className="text-base text-gray-500">Bank Name :</p>
                    <p className="font-medium text-base">
                      {get(formState.editData, "bankName", "-")}
                    </p>
                  </div>

                  <div className="flex items-center space-x-2">
                    <p className="text-base text-gray-500">Bank Branch :</p>
                    <p className="font-medium text-base">
                      {get(formState.editData, "branchName", "-")}
                    </p>
                  </div>

                  <div className="flex items-center space-x-2">
                    <p className="text-base text-gray-500">
                      Account Holder Name :
                    </p>
                    <span className="inline-flex items-center px-3 py-1.5 text-sm font-semibold text-yellow-800 bg-yellow-100 rounded-full">
                      {get(formState.editData, "accountHolderName", "-")}
                    </span>
                  </div>

                  <div className="flex items-start space-x-3">
                    <p className="text-base text-gray-500">
                      Account Holder Number :
                    </p>
                    <p className="font-medium text-base">
                      {get(formState.editData, "accountNumber", "-")}
                    </p>
                  </div>
                  <div className="flex items-start space-x-3">
                    <p className="text-base text-gray-500">Phone Number :</p>
                    <p className="font-medium text-base">
                      {get(formState.editData, "phoneNumber", "-")}
                    </p>
                  </div>
                  <div className="flex items-start space-x-3">
                    <p className="text-base text-gray-500">Opening Balance :</p>
                    <p className="font-medium text-base">
                      {get(formState.editData, "currentBalance", "-")}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </PopupModal>
      )}

      <div className="w-full p-4 bg-white rounded-md  shadow-sm mb-2">
        <CustomTabs
          tabs={tabs}
          defaultTab={tabs[0]}
          onTabChange={setSelectedTab}
        />
      </div>

      {/* Tab content */}
      <div className="flex-1">
        {selectedTab === "Bank Accounts" ? (
          <div className="bg-white">
            <MasterTable
              columns={tableData.columns}
              rows={tableData.rows}
              loading={isLoading}
            />
          </div>
        ) : (
          <BankTransactionList />
        )}
      </div>

      {formState.state && (
        <BankForm
          close={() =>
            setFormState({ state: false, edit: false, editData: null })
          }
          edit={formState.edit}
          editData={formState.editData}
        />
      )}
    </div>
  );
};

export default BankIndex;
