import React, { useState } from "react";

import { Form, FormikProvider, useFormik } from "formik";
import { PopupModal } from "../../../../components";
import { Icon } from "@iconify/react/dist/iconify.js";
import get from "lodash/get";
import { FormField } from "../../../BookingManagement/components/ReservationCustomForm";
import {
  useCreateBankModule,
  useUpdateBankModule,
} from "../../../../server-action/API/Bank/FinanceCategory/BankCategory";
import * as Yup from "yup";
interface ServiceTicketingFormProps {
  close: () => void;
  edit?: boolean;
  editData?: any;
}
const BankValidationSchema = Yup.object().shape({
  bankName: Yup.string().required("Bank Name is required"),
  branchName: Yup.string().required("Branch Name is required"),
  accountHolderName: Yup.string().required("Account Holder Name is required"),
  accountNumber: Yup.string()
    .min(6, "Account Number is too short")
    .required("Account Number is required"),
  phoneNumber: Yup.string().required("Phone Number is required"),
  currentBalance: Yup.number()
    .typeError("Opening Balance must be a number")
    .required("Opening Balance is required")
    .min(0, "Balance cannot be negative"),
  // accountQr: Yup.mixed().nullable(), // optional or file upload handling
});

export interface FinanceData {
  _id?: string;
  name?: string;
  date?: string;
  time?: string;
  transactionType?: string;
  description?: string;
  bank?: string;
  accountNumber?: string;
  fromBank?: string;
  fromAccountNumber?: string;
  amount?: string;
  createdBy?: string;
  serviceCharge?: string;
}

const BankForm: React.FC<ServiceTicketingFormProps> = ({
  close,
  edit = false,
  editData,
}) => {
  const [formError, setFormError] = useState<string | null>(null);
  const { mutate: createBank } = useCreateBankModule();
  const { mutate: updateBank } = useUpdateBankModule();

  const formik = useFormik({
    initialValues: {
      // date: edit ? get(editData, "date", "") : "",
      bankName: edit ? get(editData, "bankName", "") : "",
      branchName: edit ? get(editData, "branchName", "") : "",
      accountNumber: edit ? get(editData, "accountNumber", "") : "",
      accountHolderName: edit ? get(editData, "accountHolderName", "") : "",
      phoneNumber: edit ? get(editData, "phoneNumber", "") : "",
      currentBalance: edit ? get(editData, "currentBalance", "") : "",
      // accountQr: edit ? get(editData, "accountQr", "") : "",
    },
    enableReinitialize: true,
    validationSchema: BankValidationSchema,
    onSubmit: async (values) => {
      setFormError(null); // Clear previous errors

      if (edit) {
        updateBank(
          { id: editData._id, body: values },
          {
            onSuccess: () => {
              close();
            },
            onError: (error: any) => {
              console.log("Form update error:", error);

              // Check for duplicate bank error
              if (
                (typeof error === "string" &&
                  error.includes("bank already exists")) ||
                (error?.message &&
                  error.message.includes("bank already exists")) ||
                error?.isDuplicate
              ) {
                setFormError(
                  "This bank account already exists. Please use a different bank name or account number."
                );

                // Set field errors to highlight the problematic fields
                formik.setFieldError(
                  "bankName",
                  "Bank with this name and account number already exists"
                );
                formik.setFieldError(
                  "accountNumber",
                  "Bank with this name and account number already exists"
                );
              }
            },
          }
        );
      } else {
        createBank(values, {
          onSuccess: () => {
            close();
          },
          onError: (error: any) => {
            console.log("Form create error:", error);

            // Check for duplicate bank error
            if (
              (typeof error === "string" &&
                error.includes("bank already exists")) ||
              (error?.message &&
                error.message.includes("bank already exists")) ||
              error?.isDuplicate
            ) {
              setFormError(
                "This bank account already exists. Please use a different bank name or account number."
              );

              // Set field errors to highlight the problematic fields
              formik.setFieldError(
                "bankName",
                "Bank with this name and account number already exists"
              );
              formik.setFieldError(
                "accountNumber",
                "Bank with this name and account number already exists"
              );
            }
          },
        });
      }
    },
  });

  return (
    <PopupModal onClose={close} classname="w-full max-w-screen-sm">
      <div className="relative flex items-center jusify-between bg-[#F1F6FD]">
        <h1 className="w-full p-4 text-center text-semibold">
          {edit ? "Edit Bank" : "Add New Bank"}
        </h1>
        <button
          className="absolute flex items-center justify-center p-1 bg-black rounded-full top-4 right-4"
          onClick={close}
        >
          <Icon
            icon="fluent-mdl2:calculator-multiply"
            width="14"
            height="14"
            className="text-white"
          />
        </button>
      </div>
      <div className="p-4">
        <FormikProvider value={formik}>
          <Form onSubmit={formik.handleSubmit}>
            {formError && (
              <div className="p-3 mb-4 text-sm text-red-700 bg-red-100 border border-red-200 rounded-md">
                {formError}
              </div>
            )}
            <div className="flex items-center justify-between gap-4 mt-4">
              <FormField
                name="bankName"
                label="Bank Name"
                type="text"
                placeholder="xyz bank"
                formik={formik}
              />

              <FormField
                name="branchName"
                label="Bank Branch"
                formik={formik}
                type="text"
                placeholder="lalitpur"
              />
            </div>
            <div className="flex items-center justify-between gap-4 mt-4">
              <FormField
                name="accountHolderName"
                label="Account Holder Name"
                type="text"
                placeholder="Jhon Doe"
                formik={formik}
              />

              <FormField
                name="accountNumber"
                label="Account Holder Number"
                formik={formik}
                type="number"
                placeholder="XXXXXXXXXX"
              />
            </div>
            <div className="flex items-center justify-between gap-4 mt-4">
              <FormField
                name="phoneNumber"
                label="Phone Number"
                formik={formik}
                type="number"
                placeholder="**********"
              />
              <FormField
                name="currentBalance"
                label="Opening Balance"
                formik={formik}
                type="text"
                placeholder="Rs 0"
              />
            </div>

            <div className="flex items-center justify-end mt-4">
              <button
                type="submit"
                className="py-2 px-6 text-white rounded-md bg-[#163381]"
                disabled={formik.isSubmitting}
              >
                {edit ? "Update Bank" : "Add Bank"}
              </button>
            </div>
          </Form>
        </FormikProvider>
      </div>
    </PopupModal>
  );
};

export default BankForm;
