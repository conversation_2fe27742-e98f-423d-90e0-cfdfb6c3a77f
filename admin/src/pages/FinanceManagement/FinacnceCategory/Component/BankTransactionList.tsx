import { useState } from "react";
import MasterTable from "../../../../layouts/Table/MasterTable";
import { TableAction } from "../../../../layouts/Table/TableAction";
import { PopupModal } from "../../../../components";
import { Icon } from "@iconify/react";
import { Form, FormikProvider, useFormik } from "formik";
import { FormField } from "../../../BookingManagement/components/ReservationCustomForm";
import {
  useGetBankTransactions,
  useDeleteBankTransaction,
  IBankTransaction,
} from "../../../../server-action/API/Bank/Transaction/bankTransactionApi";
import { useGetAllBank } from "../../../../server-action/API/Bank/FinanceCategory/BankCategory";
import get from "lodash/get";
import moment from "moment";
import { DateForamter } from "../../../../components/DateFormater";
import { TimeFormater } from "../../../../components/TimeFormater";

interface BankTransactionListProps {
  onTransactionSelect?: (transaction: IBankTransaction) => void;
}

const BankTransactionList: React.FC<BankTransactionListProps> = ({
  onTransactionSelect,
}) => {
  const [showDetails, setShowDetails] = useState(false);
  const [selectedTransaction, setSelectedTransaction] =
    useState<IBankTransaction | null>(null);

  // State for filtering and pagination
  const [filters, setFilters] = useState({
    bank: "",
    transactionType: "",
    date: "",
    page: 1,
    limit: 10,
  });

  // Get bank options for filter
  const { data: banksData } = useGetAllBank();
  const bankOptions = banksData
    ? banksData.map((bank: any) => ({
        label: bank.bankName,
        value: bank._id,
      }))
    : [];

  // Transaction type options
  const transactionTypeOptions = [
    { label: "All Types", value: "" },
    { label: "Deposit", value: "deposit" },
    { label: "Withdraw", value: "withdraw" },
    { label: "Transfer", value: "transfer" },
  ];

  // Get bank transactions with filters
  const {
    data: transactionsData,
    isLoading,
    isSuccess,
  } = useGetBankTransactions(filters);

  // Delete transaction mutation
  const { mutate: deleteTransaction } = useDeleteBankTransaction();

  // Handle pagination
  const handlePageChange = (page: number) => {
    setFilters((prev) => ({
      ...prev,
      page,
    }));
  };

  // Extract transactions and pagination from the response
  const transactions = transactionsData?.transactions || [];
  const pagination = transactionsData?.pagination || {
    total: 0,
    page: 1,
    limit: 10,
    pages: 0,
  };

  // Filter form
  const filterFormik = useFormik({
    initialValues: {
      bank: "",
      transactionType: "",
      date: "",
    },
    onSubmit: (values) => {
      setFilters((prev) => ({
        ...prev,
        bank: values.bank,
        transactionType: values.transactionType,
        date: values.date,
        page: 1, // Reset to first page on filter change
      }));
    },
  });

  // Reset filters
  const resetFilters = () => {
    filterFormik.resetForm();
    setFilters({
      bank: "",
      transactionType: "",
      date: "",
      page: 1,
      limit: 10,
    });
  };

  // View transaction details
  const viewTransactionDetails = (transaction: IBankTransaction) => {
    setSelectedTransaction(transaction);
    setShowDetails(true);
    if (onTransactionSelect) {
      onTransactionSelect(transaction);
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "NPR",
    }).format(amount);
  };

  // Table data
  const tableData = {
    columns: [
      {
        title: "Transaction No",
        key: "transactionNo",
      },
      {
        title: "Date",
        key: "date",
      },
      {
        title: "Type",
        key: "transactionType",
      },
      {
        title: "From Bank",
        key: "fromBank",
      },
      {
        title: "To Bank",
        key: "toBank",
      },
      {
        title: "Description",
        key: "description",
      },
      {
        title: "Amount(NPR)",
        key: "amount",
      },
      {
        title: "Closing Balance",
        key: "closingBalance",
      },
      {
        title: "Actions",
        key: "action",
      },
    ],
    rows: isSuccess
      ? transactions.map((item: IBankTransaction) => {
          return {
            id: item._id,
            transactionNo: get(item, "transactionNo", "-"),
            date: `${DateForamter(item?.date)} (${TimeFormater(item?.time)}) `,
            transactionType: (
              <span
                className={`px-2 py-1 rounded-full text-xs font-medium ${
                  item.transactionType === "deposit"
                    ? "bg-green-100 text-green-800"
                    : item.transactionType === "withdraw"
                    ? "bg-red-100 text-red-800"
                    : "bg-blue-100 text-blue-800"
                }`}
              >
                {item.transactionType.charAt(0).toUpperCase() +
                  item.transactionType.slice(1)}
              </span>
            ),
            fromBank:
              typeof item.bank === "object" && item.bank
                ? get(item, "bank.bankName", "-")
                : "-",
            toBank:
              item.transactionType === "transfer" &&
              typeof item.to === "object" &&
              item.to
                ? get(item, "to.bankName", "-")
                : item.transactionType === "deposit"
                ? "→ Bank Account"
                : item.transactionType === "withdraw"
                ? "Cash/External"
                : "-",
            description: get(item, "description", "-"),
            amount: formatCurrency(item.transactionAmount),
            closingBalance: formatCurrency(item.closingBalance),
            action: (
              <TableAction
                onShow={() => viewTransactionDetails(item)}
                onDelete={() => deleteTransaction(item._id)}
              />
            ),
          };
        })
      : [],
  };

  return (
    <div className="flex flex-col gap-2">
      {/* Filter Form */}
      <div className="bg-white p-4 border rounded-lg flex justify-between shadow-sm">
        {/* <h3 className="text-lg font-medium mb-4">Filter Transactions</h3> */}
        <FormikProvider value={filterFormik}>
          <Form onSubmit={filterFormik.handleSubmit} className="flex w-full">
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <FormField
                name="bank"
                label="Bank"
                type="dropdown"
                options={[{ label: "All Banks", value: "" }, ...bankOptions]}
                formik={filterFormik}
              />
              <FormField
                name="transactionType"
                label="Transaction Type"
                type="dropdown"
                options={transactionTypeOptions}
                formik={filterFormik}
              />
              <FormField
                name="date"
                label="Date"
                type="date"
                formik={filterFormik}
              />
            </div>
            <div className="flex-1 items-center mt-4 gap-2 md:w-auto flex">
              <button
                type="button"
                onClick={resetFilters}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Reset
              </button>
              <button
                type="submit"
                className="px-4 py-2 whitespace-nowrap  bg-[#2A3A6D] text-white rounded-md hover:bg-[#2A3A6D]"
              >
                Apply Filters
              </button>
            </div>
          </Form>
        </FormikProvider>
      </div>

      {/* Transactions Table */}
      <div className="">
        {transactions.length > 0 ? (
          <>
            <MasterTable
              columns={tableData?.columns}
              rows={tableData.rows}
              loading={isLoading}
              pagination={{
                currentPage: pagination.page,
                totalPage: pagination.pages,
                limit: pagination.limit,
                onClick: () => handlePageChange(pagination.page + 1),
              }}
            />
            <div className="mt-4 text-sm text-gray-500 text-right">
              Showing {transactions.length} of {pagination.total} transactions
            </div>
          </>
        ) : (
          <div className="p-8 text-center">
            <p className="text-lg text-gray-500">No bank transactions found.</p>
            <p className="text-sm text-gray-400 mt-2">
              Try adjusting your filters or add a new bank transaction.
            </p>
          </div>
        )}
      </div>

      {/* Transaction Details Modal */}
      {showDetails && selectedTransaction && (
        <PopupModal onClose={() => setShowDetails(false)}>
          <div className="w-full bg-white">
            <div className="relative flex items-center justify-between bg-[#EBFEF4]">
              <h1 className="w-full p-4 text-center font-semibold text-lg">
                Bank Transaction Details
              </h1>
              <button
                className="absolute flex items-center justify-center p-1 bg-black rounded-full top-4 right-4"
                onClick={() => setShowDetails(false)}
              >
                <Icon
                  icon="fluent-mdl2:calculator-multiply"
                  width="14"
                  height="14"
                  className="text-white"
                />
              </button>
            </div>

            <div className="p-8 flex justify-center">
              <div className="border rounded-lg p-8 w-full max-w-4xl shadow-md bg-white">
                <div className="mb-6 pb-5 border-b">
                  <div className="flex items-center space-x-3">
                    <p className="text-base text-gray-500">Transaction Date:</p>
                    <p className="font-medium text-base">
                      {selectedTransaction.date} {selectedTransaction.time}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-40 gap-y-6 mb-6 pb-5 border-b">
                  <div className="flex items-center space-x-2">
                    <p className="text-base text-gray-500">Transaction Type:</p>
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium ${
                        selectedTransaction.transactionType === "deposit"
                          ? "bg-green-100 text-green-800"
                          : selectedTransaction.transactionType === "withdraw"
                          ? "bg-red-100 text-red-800"
                          : "bg-blue-100 text-blue-800"
                      }`}
                    >
                      {selectedTransaction.transactionType
                        .charAt(0)
                        .toUpperCase() +
                        selectedTransaction.transactionType.slice(1)}
                    </span>
                  </div>

                  <div className="flex items-center space-x-2">
                    <p className="text-base text-gray-500">Transaction No:</p>
                    <p className="font-medium text-base">
                      {selectedTransaction.transactionNo || "-"}
                    </p>
                  </div>

                  <div className="flex items-center space-x-2">
                    <p className="text-base text-gray-500">From Bank:</p>
                    <p className="font-medium text-base">
                      {typeof selectedTransaction.bank === "object" &&
                      selectedTransaction.bank
                        ? selectedTransaction.bank.bankName || "-"
                        : "-"}
                    </p>
                  </div>

                  <div className="flex items-center space-x-2">
                    <p className="text-base text-gray-500">To:</p>
                    <p className="font-medium text-base">
                      {selectedTransaction.transactionType === "transfer" &&
                      typeof selectedTransaction.to === "object" &&
                      selectedTransaction.to
                        ? selectedTransaction.to.bankName || "-"
                        : selectedTransaction.transactionType === "deposit"
                        ? "Bank Account"
                        : selectedTransaction.transactionType === "withdraw"
                        ? "Cash/External"
                        : "-"}
                    </p>
                  </div>

                  <div className="flex items-start space-x-3">
                    <p className="text-base text-gray-500">Amount:</p>
                    <p className="font-medium text-base">
                      {formatCurrency(selectedTransaction.transactionAmount)}
                    </p>
                  </div>

                  <div className="flex items-start space-x-3">
                    <p className="text-base text-gray-500">Closing Balance:</p>
                    <p className="font-medium text-base">
                      {formatCurrency(selectedTransaction.closingBalance)}
                    </p>
                  </div>

                  {selectedTransaction.serviceCharge &&
                    Number(selectedTransaction.serviceCharge) > 0 && (
                      <div className="flex items-start space-x-3">
                        <p className="text-base text-gray-500">
                          Service Charge:
                        </p>
                        <p className="font-medium text-base">
                          {formatCurrency(
                            Number(selectedTransaction.serviceCharge)
                          )}
                        </p>
                      </div>
                    )}

                  <div className="flex items-start space-x-3">
                    <p className="text-base text-gray-500">Description:</p>
                    <p className="font-medium text-base">
                      {selectedTransaction.description || "-"}
                    </p>
                  </div>

                  <div className="flex items-start space-x-3">
                    <p className="text-base text-gray-500">Reference:</p>
                    <p className="font-medium text-base">
                      {selectedTransaction.transactionReference || "-"}
                    </p>
                  </div>

                  {selectedTransaction.createdBy && (
                    <div className="flex items-start space-x-3">
                      <p className="text-base text-gray-500">Created By:</p>
                      <p className="font-medium text-base">
                        {typeof selectedTransaction.createdBy === "object" &&
                        selectedTransaction.createdBy
                          ? selectedTransaction.createdBy.name || "-"
                          : "-"}
                      </p>
                    </div>
                  )}

                  {selectedTransaction.createdAt && (
                    <div className="flex items-start space-x-3">
                      <p className="text-base text-gray-500">Created At:</p>
                      <p className="font-medium text-base">
                        {moment(selectedTransaction.createdAt).format(
                          "YYYY-MM-DD HH:mm"
                        )}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </PopupModal>
      )}
    </div>
  );
};

export default BankTransactionList;
