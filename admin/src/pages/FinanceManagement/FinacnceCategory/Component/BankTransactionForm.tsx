import React, { useState, useEffect } from "react";
import { Form, FormikProvider, useFormik } from "formik";
import { PopupModal } from "../../../../components";
import { Icon } from "@iconify/react";
import { FormField } from "../../../BookingManagement/components/ReservationCustomForm";
import {
  useCreateBankTransaction,
  CreateBankTransactionData
} from "../../../../server-action/API/Bank/Transaction/bankTransactionApi";
import { useGetAllBank } from "../../../../server-action/API/Bank/FinanceCategory/BankCategory";
import { useGetAllUser } from "../../../../server-action/API/user";
import * as Yup from "yup";
import moment from "moment";
import get from "lodash/get";

interface BankTransactionFormProps {
  close: () => void;
  edit?: boolean;
  editData?: any;
}

const BankTransactionForm: React.FC<BankTransactionFormProps> = ({
  close,
  edit = false,
  editData,
}) => {
  const [selectedTransactionType, setSelectedTransactionType] = useState<'deposit' | 'withdraw' | 'transfer'>(
    edit ? get(editData, "transactionType", "deposit") as 'deposit' | 'withdraw' | 'transfer' : "deposit"
  );

  // API hooks
  const { mutate: createTransaction, isPending } = useCreateBankTransaction();
  const { data: banks = [], isSuccess: hasBanks } = useGetAllBank();
  const { data: users = [], isSuccess: hasUsers } = useGetAllUser();

  // Bank options for dropdown
  const bankOptions = hasBanks
    ? banks.map((bank: any) => ({
        label: bank.bankName,
        value: bank._id,
      }))
    : [];

  // User options for dropdown
  const userOptions = hasUsers
    ? users.map((user: any) => ({
        label: user?.name || "Unnamed User",
        value: user._id,
      }))
    : [];

  // Transaction type options
  const transactionTypeOptions = [
    { label: "Deposit", value: "deposit" },
    { label: "Withdraw", value: "withdraw" },
    { label: "Transfer", value: "transfer" },
  ];

  // Validation schema
  const validationSchema = Yup.object({
    bank: Yup.string().required("Bank is required"),
    date: Yup.string().required("Date is required"),
    time: Yup.string().required("Time is required"),
    transactionType: Yup.string().required("Transaction type is required"),
    transactionNo: Yup.string().required("Transaction number is required"),
    transactionAmount: Yup.number()
      .required("Amount is required")
      .positive("Amount must be positive"),
    description: Yup.string(),
  });

  // Handle form submission with proper value extraction
  const handleSubmit = async (values: any) => {
    try {
      // Create a clean object with primitive values for submission
      const cleanValues = {
        ...values,
        // Ensure we're sending string values, not objects
        bank: values.bank,
        to: values.to || undefined,
        createdBy: values.createdBy || undefined,
        // Convert amount to number
        transactionAmount: Number(values.transactionAmount),
        serviceCharge: values.serviceCharge ? Number(values.serviceCharge) : 0,
      };

      console.log('Submitting transaction:', cleanValues);
      await createTransaction(cleanValues);
      close();
    } catch (error) {
      console.error("Error creating transaction:", error);
    }
  };

  // Form initialization
  const formik = useFormik<CreateBankTransactionData & { createdBy?: string }>({
    initialValues: {
      bank: edit ? (typeof editData.bank === 'object' ? get(editData, "bank._id", "") : editData.bank || "") : "",
      date: edit ? get(editData, "date", "") : moment().format("YYYY-MM-DD"),
      time: edit ? get(editData, "time", "") : moment().format("HH:mm"),
      description: edit ? get(editData, "description", "") : "",
      to: edit ? (typeof editData.to === 'object' ? get(editData, "to._id", "") : editData.to || "") : "",
      transactionType: selectedTransactionType,
      transactionNo: edit ? get(editData, "transactionNo", "") : `TXN${Date.now().toString().slice(-6)}`,
      transactionReference: edit ? get(editData, "transactionReference", "") : "",
      transactionAmount: edit ? get(editData, "transactionAmount", "") : "",
      serviceCharge: edit ? get(editData, "serviceCharge", 0) : 0,
      createdBy: edit ? (typeof editData.createdBy === 'object' ? get(editData, "createdBy._id", "") : editData.createdBy || "") : "",
    },
    validationSchema,
    onSubmit: handleSubmit,
  });

  // Handle transaction type change
  const handleTransactionTypeChange = (value: 'deposit' | 'withdraw' | 'transfer') => {
    // Update both state and formik values
    setSelectedTransactionType(value);
    formik.setFieldValue("transactionType", value, true);

    // Force formik to validate and update UI
    setTimeout(() => {
      formik.validateForm();
      formik.setSubmitting(false);
    }, 0);

    console.log('Transaction type changed to:', value);
  };

  // Keep formik values and state in sync
  useEffect(() => {
    if (formik.values.transactionType !== selectedTransactionType) {
      setSelectedTransactionType(formik.values.transactionType);
    }
  }, [formik.values.transactionType]);

  return (
    <PopupModal onClose={close} classname="w-full max-w-screen-md">
      <div className="relative flex items-center justify-between bg-[#EBFEF4]">
        <h1 className="w-full p-4 text-center font-semibold">
          {edit ? "Edit Bank Transaction" : "Add Bank Transaction"}
        </h1>
        <button
          className="absolute flex items-center justify-center p-1 bg-black rounded-full top-4 right-4"
          onClick={close}
        >
          <Icon
            icon="fluent-mdl2:calculator-multiply"
            width="14"
            height="14"
            className="text-white"
          />
        </button>
      </div>
      <div className="p-4">
        <FormikProvider value={formik}>
          <Form onSubmit={formik.handleSubmit}>
            {/* Date and Time */}
            <div className="flex items-center justify-between gap-4 border-2 rounded-md p-4 mb-4">
              <FormField
                name="date"
                label="Date"
                type="date"
                formik={formik}
              />
              <FormField
                name="time"
                label="Time"
                type="time"
                formik={formik}
              />
              <FormField
                name="transactionType"
                label="Transaction Type"
                type="dropdown"
                options={transactionTypeOptions}
                value={formik.values.transactionType}
                onChange={(value) => {
                  if (typeof value === 'string') {
                    handleTransactionTypeChange(value);
                  } else if (value && typeof value === 'object' && 'value' in value) {
                    handleTransactionTypeChange(value.value as 'deposit' | 'withdraw' | 'transfer');
                  } else if (value && value.target && value.target.value !== undefined) {
                    // Handle standard event object
                    handleTransactionTypeChange(value.target.value);
                  }
                }}
                formik={formik}
              />
            </div>

            {/* Bank Selection */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 border-2 rounded-md p-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Source</label>
                <FormField
                  name="bank"
                  label={formik.values.transactionType === "deposit" ? "Deposit To Bank" :
                         formik.values.transactionType === "withdraw" ? "Withdraw From Bank" :
                         "Transfer From Bank"}
                  type="dropdown"
                  options={bankOptions}
                  onChange={(option) => {
                    if (option && typeof option === 'object' && 'value' in option) {
                      formik.setFieldValue('bank', option.value);
                    } else if (typeof option === 'string') {
                      formik.setFieldValue('bank', option);
                    }
                  }}
                  formik={formik}
                />
              </div>

              {formik.values.transactionType === "transfer" ? (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Destination</label>
                  <FormField
                    name="to"
                    label="Transfer To Bank"
                    type="dropdown"
                    options={bankOptions}
                    onChange={(option) => {
                      if (option && typeof option === 'object' && 'value' in option) {
                        formik.setFieldValue('to', option.value);
                      } else if (typeof option === 'string') {
                        formik.setFieldValue('to', option);
                      }
                    }}
                    formik={formik}
                  />
                </div>
              ) : (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {formik.values.transactionType === "deposit" ? "Source" : "Destination"}
                  </label>
                  <div className="p-2 border rounded-md bg-gray-50">
                    <p className="text-gray-600">
                      {formik.values.transactionType === "deposit" ?
                        "External Source (Cash/Check/etc.)" :
                        "External Destination (Cash/Check/etc.)"}
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Transaction Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 border-2 rounded-md p-4 mb-4">
              <FormField
                name="transactionAmount"
                label="Amount"
                type="number"
                placeholder="Enter amount"
                formik={formik}
              />

              <FormField
                name="serviceCharge"
                label="Service Charge (if any)"
                type="number"
                placeholder="Enter service charge"
                formik={formik}
              />

              <FormField
                name="transactionNo"
                label="Transaction No"
                type="text"
                placeholder="Enter transaction number"
                formik={formik}
              />

              <FormField
                name="transactionReference"
                label="Reference"
                type="text"
                placeholder="Enter reference"
                formik={formik}
              />

              <FormField
                name="createdBy"
                label="Created By"
                type="dropdown"
                options={userOptions}
                onChange={(option) => {
                  if (option && typeof option === 'object' && 'value' in option) {
                    formik.setFieldValue('createdBy', option.value);
                  } else if (typeof option === 'string') {
                    formik.setFieldValue('createdBy', option);
                  }
                }}
                formik={formik}
              />

              <div className="col-span-2">
                <FormField
                  name="description"
                  label="Description"
                  type="textarea"
                  placeholder="Enter transaction description"
                  formik={formik}
                />
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex items-center justify-end mt-4">
              <button
                type="submit"
                className="py-2 px-6 text-white rounded-md bg-[#163381]"
                disabled={isPending}
              >
                {isPending ? "Processing..." : (edit ? "Update" : "Submit")}
              </button>
            </div>
          </Form>
        </FormikProvider>
      </div>
    </PopupModal>
  );
};

export default BankTransactionForm;
