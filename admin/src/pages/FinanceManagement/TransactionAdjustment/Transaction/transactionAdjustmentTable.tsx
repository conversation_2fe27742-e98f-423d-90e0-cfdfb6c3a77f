import { useState } from "react";
import MasterTable from "../../../../layouts/Table/MasterTable";
import { TableAction } from "../../../../layouts/Table/TableAction";
import {
  useGetAllFinancialTransactions,
  useDeleteFinancialTransaction,
  useGetTransactionSummary,
  useGetCategoryBreakdown,
  IFinancialTransaction,
} from "../../../../server-action/API/financialTransactionApi";
import get from "lodash/get";
import FinancialTransactionForm from "../Component/FinancialTransactionForm";
import { PopupModal } from "../../../../components";
import { IMAGE_URL } from "../../../../constant/constant";
import { DateForamter } from "../../../../components/DateFormater";
import { FormField } from "../../../BookingManagement/components/ReservationCustomForm";
import { CustomSelect } from "../../../../components/SelectFormFields";

interface FormState {
  state: boolean;
  edit: boolean;
  editData?: any;
}

const TransactionAdjustmentTable = () => {
  const [showPopup, setShowPopup] = useState<string>("");

  // State for filtering transactions
  const [filters, setFilters] = useState({
    transactionType: "",
    date: "",
    category: "",
    paymentMethod: "",
    status: "",
    page: 1,
    limit: 10,
  });

  // Financial Transaction API hooks with filters
  const {
    data: financialTransactionsData,
    isLoading,
    isSuccess,
  } = useGetAllFinancialTransactions(filters);
  const { mutate: deleteFinancialTransaction } =
    useDeleteFinancialTransaction();
  const { data: summary } = useGetTransactionSummary();
  const { data: categoryBreakdown } = useGetCategoryBreakdown();

  // Extract transactions and pagination from the response
  const transactions = financialTransactionsData?.transactions || [];
  const pagination = financialTransactionsData?.pagination || {
    total: 0,
    page: 1,
    limit: 10,
    pages: 0,
  };

  const [formState, setFormState] = useState<FormState>({
    state: false,
    edit: true,
    editData: null,
  });

  // Debug data
  console.log("Financial transactions data:", transactions);
  console.log("Summary data:", summary);
  console.log("Category breakdown data:", categoryBreakdown);

  // Handle filter changes
  const handleFilterChange = (name: string, value: any) => {
    setFilters((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setFilters((prev) => ({
      ...prev,
      page,
    }));
  };

  const tableData = {
    columns: [
      { title: "Reference", key: "reference" },
      { title: "Date", key: "date" },
      { title: "Type", key: "transactionType" },
      { title: "Description", key: "description" },
      { title: "Amount", key: "amount" },
      { title: "Payment Method", key: "paymentMethod" },
      { title: "Created By", key: "createdBy" },
      { title: "Actions", key: "action" },
    ],
    rows: isSuccess
      ? transactions?.map((item: IFinancialTransaction) => {
          return {
            id: item._id,
            reference: get(item, "reference", "-"),
            date: DateForamter(item?.date),
            transactionType: get(item, "transactionType", "-"),
            description: get(item, "description", "-"),
            amount: get(item, "amount", "-"),
            paymentMethod: get(item, "paymentMethod", "-"),
            createdBy: item.createdBy ? item.createdBy.name : "-",
            action: (
              <TableAction
                onShow={() => {
                  setFormState({
                    edit: false,
                    state: false,
                    editData: item,
                  });
                  setShowPopup("view");
                }}
                onEdit={() =>
                  setFormState({ edit: true, state: true, editData: item })
                }
                onDelete={() => deleteFinancialTransaction(item._id || "")}
              />
            ),
          };
        })
      : [],
  };

  // Financial transactions summary section
  const renderSummary = () => {
    // Always show summary, even if data is not available
    const totalIncome = summary?.totalIncome || 0;
    const totalExpense = summary?.totalExpense || 0;
    const netBalance = summary?.netBalance || 0;

    return (
      <div className="p-4 mb-2 bg-white rounded-md border">
        <h3 className="mb-2 text-lg font-semibold">Financial Summary</h3>
        <div className="grid grid-cols-3 gap-4">
          <div className="p-3 bg-green-100 rounded-md">
            <p className="text-sm text-gray-600">Total Income</p>
            <p className="text-xl font-bold text-green-600">
              Rs.{totalIncome.toFixed(2)}
            </p>
          </div>
          <div className="p-3 bg-red-100 rounded-md">
            <p className="text-sm text-gray-600">Total Expenses</p>
            <p className="text-xl font-bold text-red-600">
              Rs.{totalExpense.toFixed(2)}
            </p>
          </div>
          <div className="p-3 bg-blue-100 rounded-md">
            <p className="text-sm text-gray-600">Net Balance</p>
            <p className="text-xl font-bold text-blue-600">
              Rs.{netBalance.toFixed(2)}
            </p>
          </div>
        </div>
      </div>
    );
  };

  // Render filter controls
  const renderFilters = () => {
    return (
      <div className="p-4 mb-2 bg-white  rounded-md border">
        <h3 className="mb-2 text-lg font-semibold">Filter Transactions</h3>
        <div className="grid grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Transaction Type
            </label>
            <select
              className="w-full p-2 border border-gray-300 rounded-md"
              value={filters.transactionType}
              onChange={(e) =>
                handleFilterChange("transactionType", e.target.value)
              }
            >
              <option value="">All Types</option>
              <option value="income">Income</option>
              <option value="expense">Expense</option>
            </select>
            {/* <CustomSelect /> */}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Date
            </label>
            <input
              type="date"
              className="w-full p-2 border border-gray-300 rounded-md"
              value={filters.date}
              onChange={(e) => handleFilterChange("date", e.target.value)}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Payment Method
            </label>
            <select
              className="w-full p-2 border border-gray-300 rounded-md"
              value={filters.paymentMethod}
              onChange={(e) =>
                handleFilterChange("paymentMethod", e.target.value)
              }
            >
              <option value="">All Methods</option>
              <option value="cash">Cash</option>
              <option value="bank_transfer">Bank Transfer</option>
              <option value="credit_card">Credit Card</option>
              <option value="debit_card">Debit Card</option>
              <option value="mobile_payment">Mobile Payment</option>
              <option value="other">Other</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              className="w-full p-2 border border-gray-300 rounded-md"
              value={filters.status}
              onChange={(e) => handleFilterChange("status", e.target.value)}
            >
              <option value="">All Status</option>
              <option value="completed">Completed</option>
              <option value="pending">Pending</option>
              <option value="failed">Failed</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>

          <div className="flex items-end">
            <button
              className="p-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
              onClick={() =>
                setFilters({
                  transactionType: "",
                  date: "",
                  category: "",
                  paymentMethod: "",
                  status: "",
                  page: 1,
                  limit: 10,
                })
              }
            >
              Reset Filters
            </button>
          </div>
        </div>
      </div>
    );
  };

  // Category breakdown section
  const renderCategoryBreakdown = () => {
    if (
      !categoryBreakdown ||
      !Array.isArray(categoryBreakdown) ||
      categoryBreakdown.length === 0
    )
      return null;

    return (
      <div className="p-4 mb-4 bg-gray-50 rounded-md">
        <h3 className="mb-2 text-lg font-semibold">Category Breakdown</h3>
        <div className="grid grid-cols-4 gap-4">
          {categoryBreakdown.map((item: any, index: number) => (
            <div
              key={index}
              className={`p-3 rounded-md ${
                item.type === "income" ? "bg-green-50" : "bg-red-50"
              }`}
            >
              <p className="text-sm font-medium">{item.category}</p>
              <p
                className={`text-lg font-bold ${
                  item.type === "income" ? "text-green-600" : "text-red-600"
                }`}
              >
                ${item.amount.toFixed(2)}
              </p>
              <p className="text-xs text-gray-500">
                {item.percentage.toFixed(1)}%
              </p>
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div>
      {showPopup === "Transaction Adjustment" && (
        <FinancialTransactionForm close={() => setShowPopup("")} />
      )}
      {showPopup === "view" && formState?.editData && (
        <PopupModal onClose={() => setShowPopup("")}>
          <div className="w-full bg-white">
            <div className="relative flex items-center justify-between bg-[#EBFEF4]">
              <h1 className="w-full p-4 text-center font-semibold text-lg">
                {formState.editData.transactionType === "income"
                  ? "Income"
                  : "Expense"}{" "}
                Transaction Details
              </h1>
              <button
                className="absolute flex items-center justify-center p-1 bg-black rounded-full top-4 right-4"
                onClick={() => setShowPopup("")}
              >
                <svg
                  width="14"
                  height="14"
                  viewBox="0 0 14 14"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="text-white"
                >
                  <path
                    d="M1 1L13 13M1 13L13 1"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                  />
                </svg>
              </button>
            </div>

            <div className="p-8 flex justify-center">
              <div className="border rounded-lg p-8 w-full max-w-4xl shadow-md bg-white">
                {/* Transaction header with type and status */}
                <div className="mb-6 pb-5 border-b flex justify-between items-center">
                  <div className="flex items-center space-x-3">
                    <p className="text-base text-gray-500">Transaction Date:</p>
                    <p className="font-medium text-base">
                      {new Date(
                        get(formState.editData, "date", "-")
                      ).toLocaleDateString()}
                    </p>
                  </div>
                  <div>
                    <span
                      className={`inline-flex items-center px-3 py-1.5 text-sm font-semibold rounded-full ${
                        get(formState.editData, "status", "") === "completed"
                          ? "text-green-800 bg-green-100"
                          : get(formState.editData, "status", "") === "pending"
                          ? "text-yellow-800 bg-yellow-100"
                          : get(formState.editData, "status", "") === "failed"
                          ? "text-red-800 bg-red-100"
                          : "text-gray-800 bg-gray-100"
                      }`}
                    >
                      {get(formState.editData, "status", "-")}
                    </span>
                  </div>
                </div>

                {/* Transaction details */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4 mb-6 pb-5 border-b">
                  {/* Basic transaction info */}
                  <div className="flex items-center space-x-2">
                    <p className="text-base text-gray-500 w-32">
                      Transaction Type:
                    </p>
                    <span
                      className={`inline-flex items-center px-3 py-1.5 text-sm font-semibold rounded-full ${
                        get(formState.editData, "transactionType", "") ===
                        "income"
                          ? "text-green-800 bg-green-100"
                          : "text-red-800 bg-red-100"
                      }`}
                    >
                      {get(formState.editData, "transactionType", "-")}
                    </span>
                  </div>

                  <div className="flex items-center space-x-2">
                    <p className="text-base text-gray-500 w-32">Reference:</p>
                    <p className="font-medium text-base">
                      {get(formState.editData, "reference", "-")}
                    </p>
                  </div>

                  <div className="flex items-center space-x-2">
                    <p className="text-base text-gray-500 w-32">Category:</p>
                    <p className="font-medium text-base">
                      {typeof get(formState.editData, "category", "-") ===
                      "object"
                        ? get(formState.editData, "category.name", "-")
                        : get(formState.editData, "category", "-")}
                    </p>
                  </div>

                  <div className="flex items-center space-x-2">
                    <p className="text-base text-gray-500 w-32">
                      Payment Method:
                    </p>
                    <p className="font-medium text-base">
                      {get(formState.editData, "paymentMethod", "-")}
                    </p>
                  </div>

                  <div className="flex items-start space-x-2">
                    <p className="text-base text-gray-500 w-32">Amount:</p>
                    <p
                      className={`font-medium text-base ${
                        get(formState.editData, "transactionType", "") ===
                        "income"
                          ? "text-green-600"
                          : "text-red-600"
                      }`}
                    >
                      $
                      {parseFloat(get(formState.editData, "amount", 0)).toFixed(
                        2
                      )}
                    </p>
                  </div>

                  {/* Show bank info if available */}
                  {get(formState.editData, "bank", null) && (
                    <div className="flex items-center space-x-2">
                      <p className="text-base text-gray-500 w-32">Bank:</p>
                      <p className="font-medium text-base">
                        {get(formState.editData, "bank", "-")}
                      </p>
                    </div>
                  )}

                  {/* Show guest info if available */}
                  {get(formState.editData, "guest", null) && (
                    <div className="flex items-center space-x-2">
                      <p className="text-base text-gray-500 w-32">Guest:</p>
                      <p className="font-medium text-base">
                        {get(formState.editData, "guest.name", "-")}
                      </p>
                    </div>
                  )}

                  {/* Show booking info if available */}
                  {get(formState.editData, "booking", null) && (
                    <div className="flex items-center space-x-2">
                      <p className="text-base text-gray-500 w-32">
                        Booking ID:
                      </p>
                      <p className="font-medium text-base">
                        {get(formState.editData, "booking.bookingId", "-")}
                      </p>
                    </div>
                  )}

                  {/* Created by info */}
                  <div className="flex items-start space-x-2">
                    <p className="text-base text-gray-500 w-32">Created By:</p>
                    <p className="font-medium text-base">
                      {typeof get(formState.editData, "createdBy", "-") ===
                      "object"
                        ? get(formState.editData, "createdBy.name", "-")
                        : get(formState.editData, "createdBy", "-")}
                    </p>
                  </div>

                  {/* Created at info */}
                  <div className="flex items-start space-x-2">
                    <p className="text-base text-gray-500 w-32">Created At:</p>
                    <p className="font-medium text-base">
                      {get(formState.editData, "createdAt", "-")
                        ? new Date(
                            get(formState.editData, "createdAt", "-")
                          ).toLocaleString()
                        : "-"}
                    </p>
                  </div>
                </div>

                {/* Description and notes section */}
                <div className="mb-6 pb-5 border-b">
                  <div className="mb-4">
                    <p className="text-base text-gray-500 mb-2">Description:</p>
                    <p className="font-medium text-base bg-gray-50 p-3 rounded">
                      {get(formState.editData, "description", "-")}
                    </p>
                  </div>

                  {get(formState.editData, "notes", "") && (
                    <div>
                      <p className="text-base text-gray-500 mb-2">
                        Additional Notes:
                      </p>
                      <p className="font-medium text-base bg-gray-50 p-3 rounded">
                        {get(formState.editData, "notes", "-")}
                      </p>
                    </div>
                  )}
                </div>

                {/* Attachments section */}
                {get(formState.editData, "attachments", []).length > 0 && (
                  <div>
                    <p className="text-base text-gray-500 mb-2">Attachments:</p>
                    <div className="flex flex-wrap gap-2">
                      {get(formState.editData, "attachments", []).map(
                        (attachment: string, index: number) => (
                          <div key={index} className="w-24 h-24 relative">
                            <img
                              src={`${IMAGE_URL}${attachment}`}
                              alt={`Attachment ${index + 1}`}
                              className="w-full h-full object-cover rounded-md"
                            />
                          </div>
                        )
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </PopupModal>
      )}

      {/* Financial Summary, Filters and Category Breakdown */}
      {renderSummary()}
      {renderFilters()}
      {renderCategoryBreakdown()}

      <div>
        {transactions.length > 0 ? (
          <>
            <MasterTable
              columns={tableData.columns}
              rows={tableData.rows}
              loading={isLoading}
              pagination={{
                currentPage: pagination.page,
                totalPage: pagination.pages,
                limit: pagination.limit,
                onClick: () => handlePageChange(pagination.page + 1),
              }}
            />
            <div className="mt-4 text-sm text-gray-500 text-right">
              Showing {transactions.length} of {pagination.total} transactions
            </div>
          </>
        ) : (
          <div className="p-8 text-center">
            <p className="text-lg text-gray-500">No transaction data found.</p>
            <p className="text-sm text-gray-400 mt-2">
              Try adding a new transaction or check your API connection.
            </p>
          </div>
        )}
        {formState.state && (
          <FinancialTransactionForm
            close={() =>
              setFormState({ state: false, edit: false, editData: null })
            }
            edit={formState.edit}
            editData={formState.editData}
          />
        )}
      </div>
    </div>
  );
};

export default TransactionAdjustmentTable;
