import MasterTable from "../../../../layouts/Table/MasterTable";
import { TableAction } from "../../../../layouts/Table/TableAction";
import { useState } from "react";
import {
  useGetAllFinancialTransactions,
  useDeleteFinancialTransaction,
  IFinancialTransaction,
} from "../../../../server-action/API/financialTransactionApi";
import get from "lodash/get";
import FinancialTransactionForm from "../Component/FinancialTransactionForm";
import { PopupModal } from "../../../../components";
import { DateForamter } from "../../../../components/DateFormater";

interface FormState {
  state: boolean;
  edit: boolean;
  editData?: any;
}

const CashTransaction = () => {
  const [showPopup, setShowPopup] = useState<string>("");

  // State for filtering and pagination
  const [filters, setFilters] = useState({
    paymentMethod: "cash",
    page: 1,
    limit: 10,
  });

  // Financial Transaction API hooks for cash transactions
  const {
    data: financialTransactionsData,
    isLoading,
    isSuccess,
  } = useGetAllFinancialTransactions(filters);

  // Handle pagination
  const handlePageChange = (page: number) => {
    setFilters((prev) => ({
      ...prev,
      page,
    }));
  };

  // Extract transactions and pagination from the response
  const transactions = financialTransactionsData?.transactions || [];
  const pagination = financialTransactionsData?.pagination || {
    total: 0,
    page: 1,
    limit: 10,
    pages: 0,
  };

  const { mutate: deleteFinancialTransaction } =
    useDeleteFinancialTransaction();

  const [formState, setFormState] = useState<FormState>({
    state: false,
    edit: true,
    editData: null,
  });

  const tableData = {
    columns: [
      {
        title: "Reference",
        key: "reference",
      },
      {
        title: "Date",
        key: "date",
      },
      {
        title: "Type",
        key: "transactionType",
      },
      {
        title: "Description",
        key: "description",
      },
      {
        title: "Amount(NPR)",
        key: "amount",
      },
      {
        title: "Payment Method",
        key: "paymentMethod",
      },
      {
        title: "Created By",
        key: "createdBy",
      },
      {
        title: "Actions",
        key: "action",
      },
    ],
    rows: isSuccess
      ? transactions.map((item: IFinancialTransaction) => {
          return {
            id: item._id,
            reference: get(item, "reference", "-"),
            date: DateForamter(item?.date),
            transactionType: get(item, "transactionType", "-"),
            description: get(item, "description", "-"),
            amount: `Rs. ${get(item, "amount", "-")}`,
            paymentMethod: get(item, "paymentMethod", "-"),
            createdBy: item.createdBy ? item.createdBy.name : "-",
            action: (
              <TableAction
                onShow={() => {
                  setFormState({
                    edit: false,
                    state: false,
                    editData: item,
                  });
                  setShowPopup("view");
                }}
                onEdit={() =>
                  setFormState({
                    edit: true,
                    state: true,
                    editData: item,
                  })
                }
                onDelete={() => deleteFinancialTransaction(item._id || "")}
              />
            ),
          };
        })
      : [],
  };

  return (
    <div>
      {showPopup === "Cash Transaction" && (
        <FinancialTransactionForm close={() => setShowPopup("")} />
      )}
      {showPopup === "view" && formState.editData && (
        <PopupModal onClose={() => setShowPopup("")}>
          <div className="w-full bg-white">
            <div className="relative flex items-center justify-between bg-[#EBFEF4]">
              <h1 className="w-full p-4 text-center font-semibold text-lg">
                Cash Transaction
              </h1>
              <button
                className="absolute flex items-center justify-center p-1 bg-black rounded-full top-4 right-4"
                onClick={() => setShowPopup("")}
              >
                <svg
                  width="14"
                  height="14"
                  viewBox="0 0 14 14"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="text-white"
                >
                  <path
                    d="M1 1L13 13M1 13L13 1"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                  />
                </svg>
              </button>
            </div>

            <div className="p-8 flex justify-center">
              <div className="border rounded-lg p-8 w-full max-w-4xl shadow-md bg-white">
                <div className="mb-6 pb-5 border-b">
                  <div className="flex items-center space-x-3">
                    <p className="text-base text-gray-500">Date :</p>
                    <p className="font-medium text-base">
                      {formState.editData.hasOwnProperty("transactionType")
                        ? new Date(formState.editData.date).toLocaleDateString()
                        : `${get(formState.editData, "date", "-")} - ${get(
                            formState.editData,
                            "time",
                            "-"
                          )}`}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-40 gap-y-6 mb-6 pb-5 border-b">
                  <div className="flex items-center space-x-2">
                    <p className="text-base text-gray-500">Transaction Type:</p>
                    <p className="font-medium text-base">
                      {get(formState.editData, "transactionType", "-")}
                    </p>
                  </div>

                  <div className="flex items-center space-x-2">
                    <p className="text-base text-gray-500">Category:</p>
                    <p className="font-medium text-base">
                      {typeof formState.editData.category === "object"
                        ? formState.editData.category.name
                        : formState.editData.category}
                    </p>
                  </div>

                  <div className="flex items-center space-x-2">
                    <p className="text-base text-gray-500">Payment Method:</p>
                    <span className="inline-flex items-center px-3 py-1.5 text-sm font-semibold text-yellow-800 bg-yellow-100 rounded-full">
                      {get(formState.editData, "paymentMethod", "-")}
                    </span>
                  </div>

                  <div className="flex items-start space-x-3">
                    <p className="text-base text-gray-500">Amount:</p>
                    <p className="font-medium text-base">
                      {get(formState.editData, "amount", "-")}
                    </p>
                  </div>

                  <div className="flex items-start space-x-3">
                    <p className="text-base text-gray-500">Description:</p>
                    <p className="font-medium text-base">
                      {get(formState.editData, "description", "-")}
                    </p>
                  </div>

                  <div className="flex items-start space-x-3">
                    <p className="text-base text-gray-500">Reference:</p>
                    <p className="font-medium text-base">
                      {get(formState.editData, "reference", "-")}
                    </p>
                  </div>

                  <div className="flex items-start space-x-3">
                    <p className="text-base text-gray-500">Created By:</p>
                    <p className="font-medium text-base">
                      {formState.editData.createdBy
                        ? formState.editData.createdBy.name
                        : "-"}
                    </p>
                  </div>

                  {formState.editData.attachments &&
                    formState.editData.attachments.length > 0 && (
                      <div className="flex items-start space-x-3 col-span-2">
                        <p className="text-base text-gray-500">Attachments:</p>
                        <div className="flex flex-wrap gap-2">
                          {formState.editData.attachments.map(
                            (attachment: string, index: number) => (
                              <a
                                key={index}
                                href={attachment}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:underline"
                              >
                                View Attachment {index + 1}
                              </a>
                            )
                          )}
                        </div>
                      </div>
                    )}
                </div>
              </div>
            </div>
          </div>
        </PopupModal>
      )}
      <div>
        {transactions.length > 0 ? (
          <>
            <MasterTable
              columns={tableData?.columns}
              rows={tableData.rows}
              loading={isLoading}
              pagination={{
                currentPage: pagination.page,
                totalPage: pagination.pages,
                limit: pagination.limit,
                onClick: () => handlePageChange(pagination.page + 1),
              }}
            />
            <div className="mt-4 text-sm text-gray-500 text-right">
              Showing {transactions.length} of {pagination.total} transactions
            </div>
          </>
        ) : (
          <div className="p-8 text-center">
            <p className="text-lg text-gray-500">
              No cash transaction data found.
            </p>
            <p className="text-sm text-gray-400 mt-2">
              Try adding a new cash transaction or check your API connection.
            </p>
          </div>
        )}
        {formState.state && (
          <FinancialTransactionForm
            close={() =>
              setFormState({ state: false, edit: false, editData: null })
            }
            edit={formState.edit}
            editData={formState.editData}
          />
        )}
      </div>
    </div>
  );
};
export default CashTransaction;
