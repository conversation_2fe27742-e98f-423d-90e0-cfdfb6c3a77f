import { Form, FormikProvider, useFormik } from "formik";
import { PopupModal } from "../../../../components";
import { Icon } from "@iconify/react/dist/iconify.js";
import { FormField } from "../../../BookingManagement/components/ReservationCustomForm";
import { galleryIcon } from "../../../../components/svgExports";
import moment from "moment";
import { get } from "lodash";
import * as Yup from "yup";
import {
  useCreateFinancialTransaction,
  useUpdateFinancialTransaction,
} from "../../../../server-action/API/financialTransactionApi";
import { IMAGE_URL } from "../../../../constant/constant";
import { useGetAllCategory } from "../../../../server-action/API/expenseApi";

interface TransactionFormProps {
  close: () => void;
  edit?: boolean;
  editData?: any;
}

const TransactionValidationSchema = Yup.object().shape({
  date: Yup.string().required("Date is required"),
  amount: Yup.number()
    .required("Amount is required")
    .min(1, "Amount must be greater than 0"),
  transactionType: Yup.string().required("Transaction type is required"),
  category: Yup.string().required("Category is required"),
  description: Yup.string().optional(),
  paymentMethod: Yup.string().required("Payment method is required"),
  status: Yup.string().required("Status is required"),
  reference: Yup.string().optional(),
  notes: Yup.string().optional(),
  attachments: Yup.array().max(5, "You can upload up to 5 files only"),
});

const FinancialTransactionForm: React.FC<TransactionFormProps> = ({
  close,
  edit = false,
  editData,
}) => {
  const { mutate: createTransaction, isPending: isCreating } =
    useCreateFinancialTransaction();
  const { mutate: updateTransaction, isPending: isUpdating } =
    useUpdateFinancialTransaction();
  const { data: categories = [] } = useGetAllCategory();

  const formik = useFormik({
    initialValues: {
      date: edit
        ? moment(get(editData, "date", "")).format("YYYY-MM-DD")
        : moment().format("YYYY-MM-DD"),
      amount: edit ? get(editData, "amount", "") : "",
      transactionType: edit ? get(editData, "transactionType", "") : "expense",
      category: edit ? get(editData, "category", "") : "",
      description: edit ? get(editData, "description", "") : "",
      paymentMethod: edit ? get(editData, "paymentMethod", "") : "cash",
      status: edit ? get(editData, "status", "") : "completed",
      reference: edit ? get(editData, "reference", "") : "",
      notes: edit ? get(editData, "notes", "") : "",
      attachments: edit ? get(editData, "attachments", []) : [],
    },
    validationSchema: TransactionValidationSchema,

    onSubmit: async (values) => {
      const formData = new FormData();

      // Append form fields to FormData
      formData.append("date", values.date);
      formData.append("amount", values.amount.toString());
      formData.append("transactionType", values.transactionType);
      formData.append("category", values.category);
      formData.append("description", values.description);
      formData.append("paymentMethod", values.paymentMethod);
      formData.append("status", values.status);

      // Add optional fields if they have values
      if (values.reference) {
        formData.append("reference", values.reference);
      }

      if (values.notes) {
        formData.append("notes", values.notes);
      }

      // Removed expense data appending as per requirement

      // Append attachments to FormData (if there are any)
      if (values.attachments?.length > 0) {
        values.attachments.forEach((file: File) => {
          formData.append("attachments", file);
        });
      }

      // Use the correct mutation (create or update)
      if (edit) {
        // If editing, send the FormData to update the transaction
        await updateTransaction({ id: editData._id, body: formData });
      } else {
        // If creating, send the FormData to create the transaction
        await createTransaction(formData);
      }

      close(); // Close the modal after success
    },
  });

  return (
    <PopupModal onClose={close} classname="w-full max-w-screen-sm h-[90vh]">
      <div className="relative flex items-center jusify-between bg-[#F1F6FD]">
        <h1 className="w-full p-4 text-center text-semibold">
          {edit ? "Edit Financial Transaction" : "Add Financial Transaction"}
        </h1>
        <button
          className="absolute flex items-center justify-center p-1 bg-black rounded-full top-4 right-4"
          onClick={close}
        >
          <Icon
            icon="fluent-mdl2:calculator-multiply"
            width="14"
            height="14"
            className="text-white"
          />
        </button>
      </div>
      <div className="p-4">
        <FormikProvider value={formik}>
          <Form onSubmit={formik.handleSubmit}>
            <div className="grid grid-cols-2 gap-2">
              <FormField
                name="date"
                label="Date"
                type="date"
                placeholder=""
                formik={formik}
              />
              <FormField
                name="amount"
                label="Amount"
                type="number"
                placeholder="Enter Amount"
                formik={formik}
              />
              <FormField
                name="transactionType"
                label="Transaction Type"
                type="dropdown"
                placeholder="Select Transaction Type"
                options={[
                  { label: "Income", value: "income" },
                  { label: "Expense", value: "expense" },
                ]}
                formik={formik}
              />
              <FormField
                name="category"
                label="Category"
                type="dropdown"
                placeholder="Select Category"
                options={categories.map((category: any) => ({
                  label: category.name,
                  value: category._id,
                }))}
                formik={formik}
              />
              <FormField
                name="paymentMethod"
                label="Payment Method"
                type="dropdown"
                placeholder="Select Payment Method"
                options={[
                  { label: "Cash", value: "cash" },
                  { label: "Bank Transfer", value: "bank_transfer" },
                  { label: "Credit Card", value: "credit_card" },
                  { label: "Debit Card", value: "debit_card" },
                  { label: "Mobile Payment", value: "mobile_payment" },
                  { label: "Other", value: "other" },
                ]}
                formik={formik}
              />
              <FormField
                name="status"
                label="Status"
                type="dropdown"
                placeholder="Select Status"
                options={[
                  { label: "Completed", value: "completed" },
                  { label: "Pending", value: "pending" },
                  { label: "Failed", value: "failed" },
                  { label: "Cancelled", value: "cancelled" },
                ]}
                formik={formik}
              />
              <FormField
                name="reference"
                label="Reference Number"
                type="text"
                placeholder="Enter Reference Number"
                formik={formik}
              />
              <div className="col-span-2">
                <FormField
                  name="description"
                  label="Description"
                  type="textarea"
                  placeholder="Enter Description"
                  formik={formik}
                />
              </div>
              <div className="col-span-2">
                <FormField
                  name="notes"
                  label="Additional Notes"
                  type="textarea"
                  placeholder="Enter Additional Notes"
                  formik={formik}
                />
              </div>

              <div className="flex flex-col">
                <div>
                  <label className="mb-1 text-sm">Upload Attachments</label>
                  <div className="flex items-center w-full space-x-2 border-2 rounded-md">
                    <label
                      htmlFor="transaction-form"
                      className="flex items-center gap-1 p-2 text-white bg-[#163381] cursor-pointer rounded-l-md"
                    >
                      {galleryIcon({ color: "white", size: "18" })}
                      Choose File
                    </label>
                    <input
                      name="file"
                      id="transaction-form"
                      multiple={true}
                      type="file"
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                        const files = e.target.files
                          ? Array.from(e.target.files)
                          : [];
                        formik.setFieldValue("attachments", files);
                      }}
                      className="hidden"
                    />
                    <span className="text-sm text-gray-500">
                      {formik.values.attachments?.length} files selected
                    </span>
                  </div>
                </div>
                {formik.values.attachments?.length > 0 && (
                  <div className="flex flex-wrap p-4 mt-2 border-2 border-gray-300 rounded-md">
                    {formik.values.attachments.map(
                      (file: any, index: number) => {
                        const isString = typeof file === "string";
                        const imageUrl = isString
                          ? `${IMAGE_URL}${file}`
                          : URL.createObjectURL(file);

                        return (
                          <div key={index} className="relative w-24 h-24 m-2">
                            <img
                              src={imageUrl}
                              alt={`preview-${index}`}
                              className="object-cover w-full h-full rounded-md"
                            />
                            <button
                              type="button"
                              className="absolute top-0 right-0 p-1 bg-red-500 rounded-full"
                              onClick={() => {
                                const newFiles =
                                  formik.values.attachments.filter(
                                    (_: any, i: number) => i !== index
                                  );
                                formik.setFieldValue("attachments", newFiles);
                              }}
                            >
                              <Icon
                                icon="fluent-mdl2:calculator-multiply"
                                width="14"
                                height="14"
                                className="text-white"
                              />
                            </button>
                          </div>
                        );
                      }
                    )}
                  </div>
                )}
              </div>
            </div>
            <div className="flex items-center justify-end mt-4">
              <button
                type="submit"
                disabled={isCreating || isUpdating}
                className="py-2 px-6 text-white rounded-md bg-[#163381]"
              >
                {isCreating || isUpdating ? (
                  <Icon icon="line-md:loading-loop" width="18" height="18" />
                ) : (
                  "Save"
                )}
              </button>
            </div>
          </Form>
        </FormikProvider>
      </div>
    </PopupModal>
  );
};

export default FinancialTransactionForm;
