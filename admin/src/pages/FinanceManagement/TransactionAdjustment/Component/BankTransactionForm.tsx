import React, { useState, useEffect } from "react";
import { Form, FormikProvider, useFormik } from "formik";
import { PopupModal } from "../../../../components";
import { Icon } from "@iconify/react";
import { FormField } from "../../../BookingManagement/components/ReservationCustomForm";
import get from "lodash/get";
import {
  useCreateTransactionModule,
  useUpdateTransactionModule,
} from "../../../../server-action/API/Bank/Transaction/transaction";
import moment from "moment";
import { useGetAllBank } from "../../../../server-action/API/Bank/FinanceCategory/BankCategory";
import { useGetAllUser } from "../../../../server-action/API/user.tsx";

interface TransactionFormProps {
  close: () => void;
  edit?: boolean;
  editData?: TransactionData;
  transactionType?: string;
}

export interface TransactionData {
  _id?: string;
  name: string;
  date: string;
  time: string;
  transactionType?: string;
  description?: string;
  toBank?: string;
  toAccountNumber?: string;
  bank?: string;
  accountNumber?: string;
  amount?: string;
  createdBy?: string;
  serviceCharge?: string;
}

const TransactionForm: React.FC<TransactionFormProps> = ({
  close,
  edit = false,
  editData,
}) => {
  const { mutate: createTicket, isPending } = useCreateTransactionModule();
  const { mutate: updateTicket, isPending: updating } =
    useUpdateTransactionModule();
  const { data = [], isSuccess } = useGetAllBank();
  const { data: users = [], isSuccess: hasUsers } = useGetAllUser();

  // Ensure userOptions contains user IDs
  const userOptions = hasUsers
    ? users.map((user: any) => ({
        label: user?.name || "Unnamed User",
        value: user._id, // Store the user ID as the value
      }))
    : [];

  const bankOptions = isSuccess
    ? data.map((item: any) => ({
        label: item.bankName,
        value: item._id,
      }))
    : [];

  const [_, setIsAccountDisabled] = useState(false);

  const handleBankChange = (e: any) => {
    const selectedBankId = e.target.value;
    formik.setFieldValue("bank", selectedBankId);

    const selectedBank = data.find((item: any) => item._id === selectedBankId);
    const accountNumber = selectedBank?.accountNumber || "";

    formik.setFieldValue("accountNumber", accountNumber);
    setIsAccountDisabled(!!accountNumber);
  };

  const [selectedTransactionType, setSelectedTransactionType] =
    useState<string>(
      edit ? get(editData, "transactionType", "bankDeposit") : "bankDeposit"
    );

  const formik = useFormik<TransactionData>({
    initialValues: {
      name: edit ? get(editData, "name", "") : "",
      date: edit ? moment(get(editData, "date", "")).format("YYYY-MM-DD") : "",
      time: edit ? get(editData, "time", "") : "",
      transactionType: edit ? get(editData, "transactionType", "bankDeposit") : "bankDeposit",
      toBank: edit ? get(editData, "toBank", "") : "",
      toAccountNumber: edit ? get(editData, "toAccountNumber", "") : "",
      bank: edit ? get(editData, "bank", "") : "",
      accountNumber: edit ? get(editData, "accountNumber", "") : "",
      amount: edit ? get(editData, "amount", "") : "",
      createdBy: edit ? get(editData, "createdBy", "") : "", // Ensure this holds the user ID
      serviceCharge: edit ? get(editData, "serviceCharge", "") : "",
    },
    enableReinitialize: true,
    onSubmit: async (values) => {
      if (edit && editData?._id) {
        await updateTicket({ id: editData._id, body: values });
      } else {
        await createTicket(values);
      }
    },
  });

  const handleTransactionTypeChange = (e: any) => {
    // Handle both direct event objects and custom onChange events
    let selectedType;

    if (e && e.target && e.target.value !== undefined) {
      // Standard event object from select element
      selectedType = e.target.value;
    } else {
      // Direct value passed from custom component
      selectedType = e;
    }

    console.log('Transaction type changed to:', selectedType);

    // Update both state and formik values
    setSelectedTransactionType(selectedType);
    formik.setFieldValue("transactionType", selectedType, true);

    // Force formik to validate and update UI
    setTimeout(() => {
      formik.validateForm();
      formik.setSubmitting(false);
    }, 0);
  };

  // Keep formik values and state in sync
  useEffect(() => {
    if (formik.values.transactionType && formik.values.transactionType !== selectedTransactionType) {
      setSelectedTransactionType(formik.values.transactionType);
    }
  }, [formik.values.transactionType]);

  return (
    <PopupModal onClose={close} classname="w-full max-w-screen-sm">
      <div className="relative flex items-center justify-between bg-[#EBFEF4]">
        <h1 className="w-full p-4 text-center font-semibold">
          {edit ? "Edit Transaction" : "Add Transaction"}
        </h1>
        <button
          className="absolute flex items-center justify-center p-1 bg-black rounded-full top-4 right-4"
          onClick={close}
        >
          <Icon
            icon="fluent-mdl2:calculator-multiply"
            width="14"
            height="14"
            className="text-white"
          />
        </button>
      </div>
      <div className="p-4">
        <FormikProvider value={formik}>
          <Form onSubmit={formik.handleSubmit}>
            {/* Form content starts here */}
            <div className="flex items-center justify-between gap-4 border-2 rounded-md p-4 mb-4">
              <FormField name="date" label="Date" type="date" formik={formik} />
              <FormField name="time" label="Time" type="time" formik={formik} />
              <FormField name="name" label="Name" type="text" formik={formik} />
            </div>

            <div className="border-2 rounded-md w-full p-4 mb-4">
              <FormField
                name="transactionType"
                label="Transaction Type"
                type="dropdown"
                value={formik.values.transactionType}
                options={[
                  { value: "transfer", label: "Transfer" },
                  { value: "bankDeposit", label: "Bank Deposit" },
                  { value: "cashWithdrawal", label: "Cash Withdrawal" }
                ]}
                placeholder="Select Transaction Type"
                onChange={handleTransactionTypeChange}
                formik={formik}
              />
              {(formik.values.transactionType === "transfer" ||
                formik.values.transactionType === "cashWithdrawal") && (
                <div className="grid grid-cols-2 gap-4 my-4">
                  <FormField
                    name="bank"
                    label="From Bank"
                    type="dropdown"
                    options={bankOptions}
                    placeholder="Select bank"
                    formik={formik}
                    onChange={handleBankChange}
                  />

                  <FormField
                    name="accountNumber"
                    label="From Account Number"
                    type="text"
                    placeholder="Account Number"
                    formik={formik}
                    // disabled={isAccountDisabled}
                  />
                </div>
              )}
            </div>

            {formik.values.transactionType === "transfer" ? (
              <>
                <div className="grid grid-cols-3 gap-4 border-2 rounded-md p-4 mb-4">
                  <FormField
                    name="toBank"
                    label="To Bank"
                    type="text"
                    placeholder="To XYZ Bank"
                    formik={formik}
                  />
                  <FormField
                    name="toAccountNumber"
                    label="To Account Number"
                    type="text"
                    placeholder="xxxxxxxxx"
                    formik={formik}
                  />
                  <FormField
                    name="amount"
                    label="Total Amount"
                    type="number"
                    placeholder="Amount"
                    formik={formik}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4 border-2 rounded-md p-4 mb-4">
                  <FormField
                    name="createdBy"
                    label="Created By"
                    type="dropdown"
                    options={userOptions}
                    formik={formik}
                  />
                  {/* <div className="form-group">
                    <label htmlFor="createdBy" className="block mb-1 text-sm">
                      Created By
                    </label>
                    <input
                      list="userOptions"
                      name="createdBy"
                      id="createdBy"
                      className="w-full border rounded p-2"
                      placeholder="Search or Select Creator"
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      value={formik.values.createdBy}
                    />
                    <datalist id="userOptions">
                      {userOptions.map((user: any) => (
                        <option key={user.value}>{user.label}</option>
                      ))}
                    </datalist>
                    {formik.touched.createdBy && formik.errors.createdBy && (
                      <p className="text-red-500 text-sm mt-1">
                        {formik.errors.createdBy}
                      </p>
                    )}
                  </div> */}

                  <FormField
                    name="serviceCharge"
                    label="Service Charge"
                    type="text"
                    placeholder="Service Type"
                    formik={formik}
                  />
                </div>
              </>
            ) : formik.values.transactionType === "bankDeposit" ? (
              <>
                <div className="grid grid-cols-3 gap-4 border-2 rounded-md p-4 mb-4">
                  <FormField
                    name="bank"
                    label="Bank"
                    type="text"
                    formik={formik}
                  />
                  <FormField
                    name="accountNumber"
                    label="Account Number"
                    type="text"
                    formik={formik}
                  />
                  <FormField
                    name="amount"
                    label="Total Amount"
                    type="text"
                    formik={formik}
                  />
                </div>
                <FormField
                  name="createdBy"
                  label="Created By"
                  type="dropdown"
                  options={userOptions}
                  formik={formik}
                />
              </>
            ) : formik.values.transactionType === "cashWithdrawal" ? (
              <>
                <div className="grid grid-cols-1 gap-4 border-2 rounded-md p-4 mb-4">
                  <FormField
                    name="amount"
                    label="Total Amount"
                    type="text"
                    formik={formik}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4 border-2 rounded-md p-4 mb-4">
                  <FormField
                    name="createdBy"
                    label="Created By"
                    type="dropdown"
                    options={userOptions}
                    formik={formik}
                  />
                  <FormField
                    name="serviceCharge"
                    label="Service Charge"
                    type="text"
                    formik={formik}
                  />
                </div>
              </>
            ) : (
              <>
                <div className="grid grid-cols-3 gap-4 border-2 rounded-md p-4 mb-4">
                  <FormField
                    name="toBank"
                    label="To Bank"
                    type="text"
                    placeholder="To XYZ Bank"
                    formik={formik}
                  />
                  <FormField
                    name="toAccount"
                    label="To Account Number"
                    type="text"
                    placeholder="xxxxxxxxx"
                    formik={formik}
                  />
                  <FormField
                    name="amount"
                    label="Total Amount"
                    type="number"
                    placeholder="Amount"
                    formik={formik}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4 border-2 rounded-md p-4 mb-4">
                  <FormField
                    name="createdBy"
                    label="Created By"
                    type="dropdown"
                    options={userOptions}
                    placeholder="Creator Name"
                    formik={formik}
                  />
                  <FormField
                    name="serviceCharge"
                    label="Service Charge"
                    type="text"
                    placeholder="Service Type"
                    formik={formik}
                  />
                </div>
              </>
            )}
            <div className="flex items-center justify-end mt-4">
              <button
                type="submit"
                className="py-2 px-6 text-white rounded-md bg-[#163381]"
                disabled={isPending || updating}
              >
                {edit ? "Update" : "Submit"}
              </button>
            </div>
          </Form>
        </FormikProvider>
      </div>
    </PopupModal>
  );
};

export default TransactionForm;
