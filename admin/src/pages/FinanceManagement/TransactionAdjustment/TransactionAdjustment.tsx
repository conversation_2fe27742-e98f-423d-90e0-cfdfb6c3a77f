import { useState, useMemo, useEffect } from "react";
import { CustomTabs } from "../../../components/CustomTab";
import Header from "../../../components/Header";
import CashTransaction from "./Transaction/CashTransaction";
import BankTransaction from "./Transaction/BankTransaction";
import BankTransactionForm from "./Component/BankTransactionForm";
import FinancialTransactionForm from "./Component/FinancialTransactionForm";
import TransactionAdjustmentTable from "./Transaction/transactionAdjustmentTable";

interface Itab {
  [key: string]: React.ReactNode;
}
const tabs = ["Cash Transaction", "Bank Transaction", "Transaction Adjustment"];
type TabType = (typeof tabs)[number];

interface TransactionDetailProps {
  onSearch?: (searchTerm: string) => void;
  canSearch?: boolean;
}

const TransactionAdjustment: React.FC<TransactionDetailProps> = () => {
  const [selectedTab, setSelectedTab] = useState<TabType>("Cash Transaction");
  const [showForm, setShowForm] = useState(false);
  const [showFinancialForm, setShowFinancialForm] = useState(false);

  const tabComponents: Itab = useMemo(
    () => ({
      "Cash Transaction": <CashTransaction />,
      "Bank Transaction": <BankTransaction />,
      "Transaction Adjustment": <TransactionAdjustmentTable />,
    }),
    []
  );

  const handleFormClose = () => {
    setShowForm(false);
    setShowFinancialForm(false);
  };

  useEffect(() => {
    setShowForm(false);
    setShowFinancialForm(false);
  }, [selectedTab]);

  return (
    <>
      <Header
        title={`${
          selectedTab === "Transaction Adjustment" ? "Transaction" : ""
        }`}
        onAddClick={() => {
          if (selectedTab === "Transaction Adjustment") {
            setShowFinancialForm(true);
          } else {
            // No form for other tabs
            setShowFinancialForm(false);
            setShowForm(false);
          }
        }}
      />

      {selectedTab === "Transaction Adjustment" && showFinancialForm && (
        <FinancialTransactionForm close={handleFormClose} />
      )}

      {/* {showForm && <BankTransactionForm close={handleFormClose} />} */}
      {showFinancialForm && (
        <FinancialTransactionForm close={handleFormClose} />
      )}

      <div className="w-full h-full gap-4 flex flex-col">
        <div className="w-full p-4 bg-white rounded-xl shadow-sm">
          <CustomTabs
            tabs={tabs}
            defaultTab={tabs[0]}
            onTabChange={setSelectedTab}
          />
        </div>

        <div className="flex-1">{tabComponents[selectedTab]}</div>
      </div>
    </>
  );
};

export default TransactionAdjustment;
