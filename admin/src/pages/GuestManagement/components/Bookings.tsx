import { useState } from "react";
import Header from "../../../components/Header";
import { PopupModal } from "../../../components";
import MasterTable from "../../../layouts/Table/MasterTable";
import { Status } from "../../../components/Status";
import { TableAction } from "../../../layouts/Table/TableAction";
import { useParams, useNavigate } from "react-router-dom";
import { useGetBookings } from "../../../server-action/API/BookingManagement/BookingManagement";
import moment from "moment";
import { get } from "lodash";
import { FrontendRoutes } from "../../../routes";

const Bookings = () => {
  const [showPopup, setShowPopup] = useState(false);
  const { id } = useParams();
  const navigate = useNavigate();

  // Fetch bookings for the guest
  const {
    data: bookingData,
    isLoading,
    isSuccess,
  } = useGetBookings({ guest: id });

  const tableData = {
    columns: [
      { title: "Booking ID", key: "bookingId" },
      { title: "Room", key: "room" },
      { title: "Check-in", key: "checkin" },
      { title: "Check-out", key: "checkout" },
      { title: "Amount", key: "amount" },
      { title: "Status", key: "status" },
      { title: "Action", key: "action" },
    ],
    rows:
      isSuccess && bookingData?.data
        ? bookingData.data.map((booking: any) => ({
            key: booking._id,
            bookingId: `B-${get(booking, "_id", "").slice(-5)}`,
            room: `${get(booking, "room.roomNo", "")} / ${get(
              booking,
              "room.roomType.name",
              ""
            )}`,
            checkin: moment(get(booking, "checkIn", "")).format("DD/MM/YYYY"),
            checkout: get(booking, "checkOut")
              ? moment(get(booking, "checkOut", "")).format("DD/MM/YYYY")
              : "-",
            amount: `${get(booking, "amount", 0).toFixed(2)} NPR`,
            status: <Status status={get(booking, "status", "")} />,
            action: (
              <TableAction
                onShow={() =>
                  navigate(`${FrontendRoutes.VIEWBOOKING}/${booking._id}`)
                }
                onEdit={() => {}}
                onDelete={() => {}}
              />
            ),
          }))
        : [],
  };

  return (
    <div>
      <Header title="Reservation" />
      {showPopup && (
        <PopupModal onClose={() => setShowPopup(false)}>
          <div>hello</div>
        </PopupModal>
      )}
      <div className="bg-white ">
        <MasterTable
          columns={tableData?.columns}
          rows={tableData.rows}
          loading={isLoading}
          pagination={{
            currentPage: 1,
            totalPage: Math.ceil((bookingData?.data?.length || 0) / 10),
            limit: 10,
            onClick: () => {},
          }}
        />
      </div>
    </div>
  );
};

export default Bookings;
