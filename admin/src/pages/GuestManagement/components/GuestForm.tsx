// import { Form, FormikProvider, useFormik } from "formik";
// import { IGuestCreateData } from "../../../server-action/API/Guest/guest";
// import HeadingPopup from "./HeadingPopup";
// import { GlobalForm } from "../../../components/GlobalForm/GlobalFormComponent";
// import { ActionButton } from "../../../components/ActionButton";
// import { guestFormData } from "./guestFormData";
// import { useCreateGuest } from "./guestAPI";

// interface GuestFormProps {
//   onClose: () => void;
//   editData: IGuestCreateData | null; // You can create IGuestEditData if structure is different
// }

// // const GuestFormValidationSchema = Yup.object().shape({
// //   name: Yup.string().required("Name is required"),
// //   email: Yup.string().email("Invalid email").required("Email is required"),
// //   phoneNumber: Yup.string().required("Phone number is required"),
// //   gender: Yup.string().required(""),
// // });

// const GuestForm = ({ onClose, editData }: GuestFormProps) => {
//   const { mutateAsync: createGuest } = useCreateGuest();

//   const formik = useFormik<IGuestCreateData>({
//     initialValues: {
//       name: editData?.name || "",
//       //   guestName: editData?.guestName || "",
//       email: editData?.email || "",
//       phoneNumber: editData?.phoneNumber || "",
//       address: editData?.address || "",
//       country: editData?.country || "",
//       idType: editData?.idType || undefined,
//       idNumber: editData?.idNumber || "",
//       dob: editData?.dob || "",
//       gender: editData?.gender || undefined,
//     },
//     enableReinitialize: true,
//     // validationSchema: GuestFormValidationSchema,
//     onSubmit: async (values) => {
//       const formData = new FormData();

//       formData.append("name", values.name);
//       formData.append("email", values.email);
//       formData.append("phoneNumber", values.phoneNumber);
//       if (values.address) formData.append("address", values.address);
//       if (values.nationality)
//         formData.append("nationality", values.nationality);
//       if (values.idType) formData.append("idType", values.idType);
//       if (values.idNumber) formData.append("idNumber", values.idNumber);
//       if (values.dateOfBirth)
//         formData.append("dateOfBirth", values.dateOfBirth);
//       if (values.gender) formData.append("gender", values.gender);

//       // 🖼️ Image upload
//       if (values.images) {
//         if (typeof values.images === "string") {
//           formData.append("existingImage", values.images); // if editing
//         } else {
//           formData.append("image", values.images); // new file upload
//         }
//       }

//       try {
//         await createGuest(values as any); // <-- your API call using FormData
//         onClose();
//       } catch (error) {
//         console.error("Guest creation failed:", error);
//       }
//     },
//   });

//   const { handleSubmit, getFieldProps } = formik;

//   return (
//     <HeadingPopup
//       onClose={onClose}
//       className="w-full max-w-screen-lg"
//       heading={`${editData ? "Edit" : "Add"} Guest`}
//     >
//       <FormikProvider value={formik}>
//         <Form onSubmit={handleSubmit}>
//           <div className="grid grid-cols-3 gap-4">
//             <GlobalForm
//               formDatails={guestFormData}
//               getFieldProps={getFieldProps}
//             />
//           </div>
//           <ActionButton onCancel={onClose} onSubmit={handleSubmit} />
//         </Form>
//       </FormikProvider>
//     </HeadingPopup>
//   );
// };

// export default GuestForm;
