// const genderOptions = [
//   { label: "Male", value: "male" },
//   { label: "Female", value: "female" },
//   { label: "Others", value: "others" },
// ];

// export const guestFormData = [
//   {
//     field: "guestName",
//     label: "Guest Name",
//     type: "text",
//     placeholder: "Enter Guest Name",
//   },
//   {
//     field: "name",
//     label: "Guest Name",
//     type: "text",
//     placeholder: "Enter Guest Name",
//   },
//   {
//     field: "gender",
//     label: "Gender",
//     type: "select",
//     placeholder: "Select Gender",
//     options: genderOptions,
//   },
//   {
//     field: "dob",
//     label: "Date of Birth",
//     type: "date",
//     placeholder: "Select Date of Birth",
//   },

//   {
//     field: "documents.IdNo",
//     label: "ID Number",
//     type: "number",
//     placeholder: "Enter ID Number",
//   },

//   {
//     field: "phoneNumber",
//     label: "Phone Number",
//     type: "text",
//     placeholder: "Enter Phone Number",
//   },
//   {
//     field: "email",
//     label: "Email",
//     type: "email",
//     placeholder: "Enter Email",
//   },
//   {
//     field: "country",
//     label: "Country",
//     type: "text",
//     placeholder: "Enter Country",
//   },
//   {
//     field: "address",
//     label: "Address",
//     type: "text",
//     placeholder: "Enter Address",
//   },
//   {
//     field: "documents.IdentityType",
//     label: "Identity Type",
//     type: "text",
//     placeholder: "Enter Identity Type",
//   },

//   {
//     field: "documents.images",
//     label: "Images",
//     type: "files",
//     placeholder: "Upload Images",
//   },
// ];
