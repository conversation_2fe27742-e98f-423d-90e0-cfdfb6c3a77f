import { Form, FormikProvider, useFormik } from "formik";
import HeadingPopup from "./HeadingPopup";
import { FormField } from "../../BookingManagement/components/ReservationCustomForm";

const FilterDetails = ({ onClose }: { onClose: () => void }) => {
  const formik = useFormik({
    initialValues: {
      status: "",
      floorList: "",
      bookDate: "",
      roomName: "",
      roomType: "",
    },
    onSubmit: (values) => {
      console.log(values);
    },
  });
  return (
    <HeadingPopup
      onClose={onClose}
      className="w-full max-w-screen-sm"
      heading="Filter Details"
    >
      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit} className="space-y-3">
          <FormField
            label="Status"
            type="dropdown"
            name="status"
            formik={formik}
            placeholder="Success"
            options={[{ label: "Success", value: "success" }]}
          />
          <div className="grid grid-cols-2 gap-x-4 gap-y-3">
            <FormField
              label="Floor List"
              type="dropdown"
              name="floorList"
              formik={formik}
              placeholder="All"
              options={[{ label: "Success", value: "success" }]}
            />
            <FormField
              label="Book Date"
              type="date"
              name="bookDate"
              formik={formik}
              //   placeholder="Success"
            />
            <FormField
              label="Room Name"
              type="dropdown"
              name="roomName"
              formik={formik}
              placeholder="All"
              options={[{ label: "Success", value: "success" }]}
            />
            <FormField
              label="Room Type"
              type="dropdown"
              name="roomType"
              formik={formik}
              placeholder="All"
              options={[{ label: "Success", value: "success" }]}
            />
          </div>
          <div className="flex items-center justify-end mt-4">
            <button
              type="submit"
              className="px-4 py-2 bg-[#2A3A6D] text-white rounded-md"
            >
              Apply
            </button>
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default FilterDetails;
