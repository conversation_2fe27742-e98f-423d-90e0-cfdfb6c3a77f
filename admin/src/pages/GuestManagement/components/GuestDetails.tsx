import { memo } from "react";
import { Card } from "../../../components/Card";
import Folio from "./Folio";
import { useParams } from "react-router-dom";
import { useGetUserById } from "../../../server-action/API/user.tsx";

const GuestDetails = memo(() => {
  const { id } = useParams();

  const { data: userData } = useGetUserById(id ?? "");
  console.log("guestData", userData);

  return (
    <div>
      <Card className="bg-white">
        <Folio user={userData} />
      </Card>
    </div>
  );
});

export default GuestDetails;
