import React from "react";
import { PopupModal } from "../../../components";
import { Icon } from "@iconify/react/dist/iconify.js";
import moment from "moment";

interface TransactionDetailsProps {
  transaction: any;
  onClose: () => void;
}

const TransactionDetails: React.FC<TransactionDetailsProps> = ({
  transaction,
  onClose,
}) => {
  const isPayment = transaction.debitAmount > 0;

  return (
    <PopupModal onClose={onClose} classname="w-full max-w-screen-sm">
      <div className="relative flex items-center justify-between bg-[#EBFEF4]">
        <h1 className="w-full p-4 text-center text-semibold">
          {isPayment ? "Payment Details" : "Transaction Details"}
        </h1>
        <button
          className="absolute flex items-center justify-center p-1 bg-black rounded-full top-4 right-4"
          onClick={onClose}
        >
          <Icon
            icon="fluent-mdl2:calculator-multiply"
            width="14"
            height="14"
            className="text-white"
          />
        </button>
      </div>
      <div className="p-6">
        <div className="grid grid-cols-2 gap-4">
          <div className="col-span-2 p-4 bg-gray-50 rounded-md">
            <h2 className="mb-2 text-lg font-semibold">
              {isPayment ? "Payment Information" : "Transaction Information"}
            </h2>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <p className="text-sm text-gray-500">Reference Number</p>
                <p className="font-medium">{transaction.referenceNo}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Date</p>
                <p className="font-medium">
                  {moment(transaction.date).format("MMM DD, YYYY")}
                </p>
              </div>
              <div className="col-span-2">
                <p className="text-sm text-gray-500">Description</p>
                <p className="font-medium">{transaction.description}</p>
              </div>
              {isPayment ? (
                <div>
                  <p className="text-sm text-gray-500">Amount Paid</p>
                  <p className="font-medium text-green-600">
                    NPR {transaction.debitAmount.toFixed(2)}
                  </p>
                </div>
              ) : (
                <>
                  <div>
                    <p className="text-sm text-gray-500">Debit Amount</p>
                    <p className="font-medium text-red-600">
                      NPR {transaction.debitAmount.toFixed(2)}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Credit Amount</p>
                    <p className="font-medium text-green-600">
                      NPR {transaction.creditAmount.toFixed(2)}
                    </p>
                  </div>
                </>
              )}
              <div>
                <p className="text-sm text-gray-500">Balance</p>
                <p className="font-medium">
                  NPR {transaction.balance.toFixed(2)}
                </p>
              </div>
            </div>
          </div>

          <div className="col-span-2 mt-4">
            <div className="flex justify-end">
              <button
                onClick={onClose}
                className="px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    </PopupModal>
  );
};

export default TransactionDetails;
