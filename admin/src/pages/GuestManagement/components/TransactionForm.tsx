import { Form, FormikProvider, useFormik } from "formik";
import { PopupModal } from "../../../components";
import { Icon } from "@iconify/react/dist/iconify.js";
import { FormField } from "../../BookingManagement/components/ReservationCustomForm";
import { useCreateTransaction } from "../../../server-action/API/Guest/transaction";
import * as Yup from "yup";
import { useParams } from "react-router-dom";
import { useGetBookings } from "../../../server-action/API/BookingManagement/BookingManagement";
import { get } from "lodash";
import moment from "moment";

interface TransactionFormProps {
  close: () => void;
  onSuccess?: () => void;
  isPayment?: boolean; // Flag to indicate if this is a payment form
}

const TransactionValidationSchema = Yup.object().shape({
  booking: Yup.string().required("Booking is required"),
  date: Yup.string().required("Date is required"),
  description: Yup.string().required("Description is required"),
  referenceNo: Yup.string().required("Reference number is required"),
  transactionType: Yup.string().required("Transaction type is required"),
  amount: Yup.number()
    .required("Amount is required")
    .min(1, "Amount must be greater than 0"),
});

const TransactionForm: React.FC<TransactionFormProps> = ({
  close,
  onSuccess,
  isPayment = false,
}) => {
  const { id } = useParams();
  const { mutate: createTransaction, isPending } = useCreateTransaction();
  const { data: bookings, isSuccess: isBookingsSuccess } = useGetBookings({
    guest: id,
  });

  const formik = useFormik({
    initialValues: {
      booking: "",
      date: moment().format("YYYY-MM-DD"),
      description: isPayment ? "Payment received" : "",
      referenceNo: `TXN-${Date.now().toString().slice(-6)}`,
      transactionType: "debit", // Default to debit
      amount: "",
    },
    validationSchema: TransactionValidationSchema,
    onSubmit: (values) => {
      // Prepare transaction data
      const transactionData = {
        booking: values.booking,
        guest: id || "",
        hotel: localStorage.getItem("hotelId") || "",
        date: values.date,
        description: values.description,
        referenceNo: values.referenceNo,
        debitAmount:
          isPayment || values.transactionType === "debit"
            ? Number(values.amount)
            : 0,
        creditAmount:
          !isPayment && values.transactionType === "credit"
            ? Number(values.amount)
            : 0,
        balance: 0, // This will be calculated on the server
      };

      createTransaction(transactionData, {
        onSuccess: () => {
          if (onSuccess) onSuccess();
          close();
        },
      });
    },
  });

  // Generate booking options for dropdown
  const bookingOptions = isBookingsSuccess && bookings?.data
    ? bookings.data.map((booking: any) => ({
        label: `B-${get(booking, "_id", "").slice(-5)} - ${moment(
          get(booking, "checkIn", "")
        ).format("DD/MM/YYYY")}`,
        value: booking._id,
      }))
    : [];

  return (
    <PopupModal onClose={close} classname="w-full max-w-screen-sm">
      <div className="relative flex items-center jusify-between bg-[#EBFEF4]">
        <h1 className="w-full p-4 text-center text-semibold">
          {isPayment ? "Add Payment" : "Add Transaction"}
        </h1>
        <button
          className="absolute flex items-center justify-center p-1 bg-black rounded-full top-4 right-4"
          onClick={close}
        >
          <Icon
            icon="fluent-mdl2:calculator-multiply"
            width="14"
            height="14"
            className="text-white"
          />
        </button>
      </div>
      <div className="p-4">
        <FormikProvider value={formik}>
          <Form onSubmit={formik.handleSubmit}>
            <div className="grid grid-cols-2 gap-2">
              <FormField
                name="booking"
                label="Booking"
                type="dropdown"
                options={bookingOptions}
                placeholder="Select Booking"
                formik={formik}
              />
              <FormField
                name="date"
                label="Date"
                type="date"
                placeholder=""
                formik={formik}
              />
              <div className="col-span-2">
                <FormField
                  name="description"
                  label="Description"
                  type="text"
                  placeholder="Enter description"
                  formik={formik}
                />
              </div>
              <FormField
                name="referenceNo"
                label="Reference Number"
                type="text"
                placeholder="Enter reference number"
                formik={formik}
              />
              {!isPayment && (
                <FormField
                  name="transactionType"
                  label="Transaction Type"
                  type="dropdown"
                  options={[
                    { label: "Debit", value: "debit" },
                    { label: "Credit", value: "credit" },
                  ]}
                  placeholder="Select transaction type"
                  formik={formik}
                />
              )}
              <FormField
                name="amount"
                label="Amount"
                type="number"
                placeholder="Enter amount"
                formik={formik}
              />
            </div>
            <div className="flex justify-end gap-2 mt-4">
              <button
                type="button"
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md"
                onClick={close}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 text-white rounded-md bg-blue-600"
                disabled={isPending}
              >
                {isPending
                  ? "Saving..."
                  : isPayment
                  ? "Save Payment"
                  : "Save Transaction"}
              </button>
            </div>
          </Form>
        </FormikProvider>
      </div>
    </PopupModal>
  );
};

export default TransactionForm;
