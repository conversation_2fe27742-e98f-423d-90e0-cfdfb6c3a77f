import { memo, useCallback, useEffect, useMemo, useState } from "react";
import { PopupModal } from "../../../components";
import { Card, CardContent } from "../../../components/Card";
import { CustomTabs } from "../../../components/CustomTab";
import { SampleTableData } from "../../../components/SampleData";
import { Status } from "../../../components/Status";
import { TableAction } from "../../../layouts/Table/TableAction";
import FilterDetails from "./FilterDetails";
import { Icon } from "@iconify/react/dist/iconify.js";
import MasterTable from "../../../layouts/Table/MasterTable";
import { useGetBookings } from "../../../server-action/API/BookingManagement/BookingManagement";
import { get } from "lodash";
import moment from "moment";
import { getStatus } from "../../BookingManagement/BookingDetails";
import { useParams } from "react-router-dom";
import no_user from "../../../assets/images/no-user.png";
import { useGetGuestTransactions } from "../../../server-action/API/Guest/transaction";
import TransactionForm from "./TransactionForm";
import TransactionDetails from "./TransactionDetails";
interface propTypes {
  user?: any;
}

const Folio = memo(({ user }: propTypes) => {
  const { id } = useParams();
  const [tab, setTab] = useState<keyof typeof tabData>("Payments");
  const {
    data: bookings,
    isSuccess,
    isLoading,
  } = useGetBookings({ guest: id });

  // Fetch guest transactions
  const {
    data: transactions,
    isLoading: isLoadingTransactions,
    isSuccess: isTransactionsSuccess,
  } = useGetGuestTransactions(id);
  const [showPopup, setShowPopup] = useState({ state: "", details: null });
  const [showTransactionForm, setShowTransactionForm] = useState({
    show: false,
    isPayment: false,
  });
  // Filter transactions to only show payments (debit transactions)
  const paymentTransactions =
    isTransactionsSuccess && transactions
      ? transactions.filter((transaction: any) => transaction.debitAmount > 0)
      : [];

  const PaymentTableData = {
    columns: [
      { title: "Date", key: "date" },
      { title: "Description", key: "description" },
      { title: "Reference No", key: "referenceNo" },
      { title: "Amount Paid (NPR)", key: "amount" },
      { title: "Balance (NPR)", key: "balance" },
      { title: "Action", key: "action" },
    ],
    rows: paymentTransactions.map((transaction: any) => ({
      key: transaction._id,
      date: moment(transaction.date).format("MMM-DD-YYYY"),
      description: transaction.description,
      referenceNo: transaction.referenceNo,
      amount: transaction.debitAmount.toFixed(2),
      balance: transaction.balance.toFixed(2),
      action: (
        <TableAction
          onShow={() =>
            setShowPopup({ state: "details", details: transaction })
          }
          onEdit={() => {}}
          onDelete={() => {}}
        />
      ),
    })),
  };

  const RoomTableData = {
    columns: [
      // { title: "Room Image", key: "img" },
      { title: "Room Floor", key: "floor" },
      { title: "Room Number ", key: "number" },
      // { title: "Room Facility", key: "facility" },
      { title: "Room Type", key: "roomType" },
      { title: "Book Date", key: "bookDate" },
      { title: "Status", key: "status" },
      // { title: "Action", key: "action" },
    ],
    rows:
      isSuccess && bookings?.data
        ? bookings.data.map((item: any) => ({
            floor: get(item, "room.floor", "-"),
            number: get(item, "room.roomNo", "-"),
            // facility: get(item, "room.features", []).join(", "),
            roomType: get(item, "room.roomType.name", "-"),
            bookDate: moment(get(item, "createdAt", "-")).format("MMM-DD-YYYY"),
            status:
              getStatus(item.checkIn, item.checkOut) === "checkout" ? (
                <div className="px-3 py-1 mx-auto text-white rounded-md w-fit bg-green">
                  Checked Out
                </div>
              ) : (
                <div className="px-3 py-1 mx-auto text-white rounded-md w-fit bg-red">
                  Checked In
                </div>
              ),
          }))
        : [],
  };

  const BillingTableData = {
    columns: [
      { title: "Date", key: "date" },
      { title: "Description", key: "description" },
      { title: "Reference No.", key: "referenceNo" },
      { title: "Debit (NPR)", key: "debitAmount" },
      { title: "Credit (NPR)", key: "creditAmount" },
      { title: "Balance (NPR)", key: "balance" },
    ],
    rows:
      isTransactionsSuccess && transactions
        ? transactions.map((transaction: any) => ({
            key: transaction._id,
            date: moment(transaction.date).format("MMM-DD-YYYY"),
            description: transaction.description,
            referenceNo: transaction.referenceNo,
            debitAmount: transaction.debitAmount.toFixed(2),
            creditAmount: transaction.creditAmount.toFixed(2),
            balance: transaction.balance.toFixed(2),
          }))
        : [],
  };

  const tabData = useMemo(
    () => ({
      Payments: (
        <div className="mt-6">
          <div className="flex justify-end mb-4">
            <button
              onClick={() =>
                setShowTransactionForm({ show: true, isPayment: true })
              }
              className="px-4 py-2 text-white rounded-md bg-blue-600 hover:bg-blue-700"
            >
              Add Payment
            </button>
          </div>
          <MasterTable
            columns={PaymentTableData.columns}
            rows={PaymentTableData.rows}
            loading={isLoadingTransactions}
            sortBy="createdAt"
            sortOrder="desc"
          />
        </div>
      ),
      Bookings: (
        <div className="mt-6">
          <MasterTable
            columns={RoomTableData.columns}
            rows={RoomTableData.rows}
            loading={isLoading}
            sortBy="createdAt"
            sortOrder="desc"
          />
        </div>
      ),
      "Billing Ledger": (
        <div className="mt-6">
          <div className="flex justify-end mb-4">
            <button
              onClick={() =>
                setShowTransactionForm({ show: true, isPayment: false })
              }
              className="px-4 py-2 text-white rounded-md bg-blue-600 hover:bg-blue-700"
            >
              Add Transaction
            </button>
          </div>
          <MasterTable
            columns={BillingTableData.columns}
            rows={BillingTableData.rows}
            loading={isLoadingTransactions}
            sortBy="createdAt"
            sortOrder="desc"
          />
        </div>
      ),
    }),
    [
      RoomTableData.rows,
      BillingTableData.rows,
      PaymentTableData.rows,
      isLoading,
      isLoadingTransactions,
    ]
  );

  const obj = {
    DOB: user?.DOB,
    Gender: user?.gender,
    "Total Visited": get(bookings, "data", []).length,
    "Last Visited": (() => {
      const lastBooking = get(bookings, "data", []).find((item: any) =>
        moment().startOf("day").isAfter(item.checkOut)
      );
      return lastBooking?.checkOut
        ? moment(lastBooking.checkOut).format("MMM-DD-YYYY")
        : "-";
    })(),
  };

  const tabOptions = useMemo(
    () => ["Payments", "Bookings", "Billing Ledger"],
    []
  );

  const handleTabChange = useCallback(
    (state: string) => setTab(state as keyof typeof tabData),
    []
  );
  return (
    <div className="space-y-2">
      <div className="mt-2 space-y-2">
        <Card className="bg-white">
          <CardContent>
            <div className="grid grid-cols-12">
              <div className="col-span-2">
                <img
                  // src={}
                  src={
                    user?.photo?.[0]
                      ? `http://62.72.42.129:8085/${user?.photo?.[0]}`
                      : no_user
                  }
                  // alt="Louis Khan"
                  className="object-cover rounded-md size-36"
                />
              </div>
              <div className="col-span-10">
                <div className="flex items-center justify-between py-1 ">
                  <div>
                    <div className="flex gap-4">
                      <h2 className="text-2xl font-semibold">{user?.name}</h2>
                    </div>
                    <div></div>
                  </div>
                  <button className="flex items-center gap-1 px-4 py-2 text-white rounded-md bg-light-secondary">
                    <span>View Document</span>
                  </button>
                </div>

                <div className="flex gap-3 pb-2 border-b borderb-gray-200 place-items-center">
                  <section className="flex gap-1 place-items-center">
                    <Icon icon="material-symbols:mail-outline-rounded" />
                    <span className="text-xs">{user?.email}</span>
                  </section>
                  <section className="flex gap-1 place-items-center">
                    <Icon icon="tabler:user-pin" />
                    <span className="text-xs">{user?.email}</span>
                  </section>

                  <section className="flex gap-1 place-items-center">
                    <Icon icon="fluent:call-32-regular" />
                    <span className="text-xs">{user?.phoneNumber}</span>
                  </section>
                </div>
                <div className="grid grid-cols-1 gap-2 py-1 mt-2 lg:grid-cols-3 sm:grid-cols-2">
                  {Object.entries(obj).map(([key, value]) => (
                    <div className="flex items-center gap-2 text-sm" key={key}>
                      <span className="w-32 text-gray-400 whitesc">{key}</span>
                      <span>{value}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="bg-white">
          <CardContent>
            <CustomTabs
              tabs={tabOptions}
              defaultTab={tab}
              onTabChange={handleTabChange}
            />
            <div>{tabData[tab]}</div>
          </CardContent>
        </Card>
      </div>
      {showPopup.state === "view" && (
        <PopupModal onClose={() => setShowPopup({ state: "", details: null })}>
          <div>hello</div>
        </PopupModal>
      )}

      {showPopup.state === "details" && showPopup.details && (
        <TransactionDetails
          transaction={showPopup.details}
          onClose={() => setShowPopup({ state: "", details: null })}
        />
      )}

      {showTransactionForm.show && (
        <TransactionForm
          close={() =>
            setShowTransactionForm({ show: false, isPayment: false })
          }
          onSuccess={() => {
            // Refresh transactions data
            setShowTransactionForm({ show: false, isPayment: false });
          }}
          isPayment={showTransactionForm.isPayment}
        />
      )}
    </div>
  );
});

export default Folio;
