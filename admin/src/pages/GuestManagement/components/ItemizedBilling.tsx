import { memo, useState } from "react";
import { PopupModal } from "../../../components";
import Header from "../../../components/Header";
import { SampleTableData } from "../../../components/SampleData";
import { Status } from "../../../components/Status";
import MasterTable from "../../../layouts/Table/MasterTable";
import { TableAction } from "../../../layouts/Table/TableAction";

const ItemizedBilling = memo(() => {
  const [showPopup, setShowPopup] = useState(false);

  const tableData = {
    columns: [
      { title: "Id", key: "tokenid" },
      { title: "Patient Name", key: "GuestName" },
      { title: "Date ", key: "date" },
      { title: "Room", key: "Room" },
      { title: "checkin", key: "checkin" },
      { title: "Checkout", key: "checkout" },
      { title: "Status", key: "status" },
      { title: "Action", key: "action" },
    ],
    rows: SampleTableData.map(
      (
        { tokenId, GuestName, date, checkin, status, Room, checkout },
        index
      ) => ({
        key: index,
        tokenid: tokenId,
        patientName: GuestName,
        date,
        checkin,
        checkout,
        status: <Status status={status} />,
        Room,
        action: (
          <TableAction
            onShow={() => setShowPopup(true)}
            onEdit={() => {}}
            onDelete={() => {}}
          />
        ),
      })
    ),
  };

  return (
    <div>
      <Header title="Reservation" />
      {showPopup && (
        <PopupModal onClose={() => setShowPopup(false)}>
          <div>hello</div>
        </PopupModal>
      )}
      <div className="bg-white ">
        <MasterTable
          columns={tableData?.columns}
          rows={tableData.rows}
          loading={false}
          pagination={{
            currentPage: 1,
            totalPage: 200,
            limit: 5,
            onClick: () => {},
          }}
        />
      </div>
    </div>
  );
});

export default ItemizedBilling;
