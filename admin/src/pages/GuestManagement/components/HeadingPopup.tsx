import React, { useEffect } from "react";
import { PopupModal } from "../../../components";
import { Icon } from "@iconify/react/dist/iconify.js";
import { useModal } from "../../../context/ModelContext";

interface HeadingPopupProps {
  heading: string;
  onClose: () => void;
  children?: React.ReactNode;
  className?: string;
}

const HeadingPopup: React.FC<HeadingPopupProps> = ({
  heading,
  onClose,
  className,
  children,
  ...props
}) => {
  const { openModal, closeModal } = useModal(); // Use the modal context

  // Log when the component renders and manage scroll lock
  useEffect(() => {
    console.log("HeadingPopup rendered with heading:", heading);
    openModal(); // Increment modal count
    return () => {
      console.log("HeadingPopup unmounted");
      closeModal(); // Decrement modal count
    };
  }, [heading, openModal, closeModal]);

  return (
    <PopupModal
      onClose={() => {
        console.log("PopupModal close triggered");
        onClose();
      }}
      classname={`${className} overflow-auto`}
      {...props}
    >
      <div className="relative flex items-center justify-between rounded-t-md bg-[#F1F6FD]">
        <h1 className="w-full p-4 font-semibold text-center">{heading}</h1>
        <button
          className="absolute flex items-center justify-center p-1 bg-black rounded-full top-4 right-4"
          onClick={(e) => {
            e.preventDefault();
            console.log("Close button clicked");
            onClose();
          }}
        >
          <Icon
            icon="fluent-mdl2:calculator-multiply"
            width="14"
            height="14"
            className="text-white"
          />
        </button>
      </div>
      <div className="p-4">{children}</div>
    </PopupModal>
  );
};

export default HeadingPopup;
