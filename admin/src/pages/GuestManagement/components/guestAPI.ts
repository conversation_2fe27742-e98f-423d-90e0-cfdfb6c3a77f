import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-toastify";
import { apiClient } from "../../../server-action/utils/ApiGateway";

// No change needed to the response interface
export interface IGuestCreateResponse {
  success: boolean;
  message: string;
  data: {
    _id: string;
    name: string;
    email?: string;
    phoneNumber: string;
    address?: string;
    nationality?: string;
    idType?: string;
    idNumber?: string;
    dateOfBirth?: string;
    gender?: string;
    hotel: string;
    role: string;
    emailSent?: boolean;
    responseTime: string;
  };
}

/**
 * Hook to create a new guest using FormData (for image upload support)
 */
export const useCreateGuest = () => {
  const queryClient = useQueryClient();

  return useMutation<IGuestCreateResponse, Error, FormData>({
    mutationFn: async (formData: FormData) => {
      console.log("Creating guest with FormData:", formData);

      const response = await apiClient.post("guest", formData);

      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["user"] });
      queryClient.invalidateQueries({ queryKey: ["user", "booking"] });

      toast.success(data.message || "Guest created successfully");
      console.log("Guest created successfully:", data);
      return data;
    },
    onError: (error: any) => {
      console.error("Error creating guest:", error);

      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        "Error creating guest";

      toast.error(errorMessage);
    },
  });
};
