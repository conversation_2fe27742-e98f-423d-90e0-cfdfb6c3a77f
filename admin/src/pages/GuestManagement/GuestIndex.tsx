import { useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";
import MasterTable from "../../layouts/Table/MasterTable";
import { TableAction } from "../../layouts/Table/TableAction";
import { useDeleteUser, useGetAllUser } from "../../server-action/API/user.tsx";
import Header from "../../components/Header.tsx";
// import GuestForm from "./components/GuestForm.tsx";

const GuestIndex = () => {
  const { data: userData } = useGetAllUser({ role: "guest" });
  const { mutate: deleteUser } = useDeleteUser();
  const navigate = useNavigate();

  // const [openForm, setOpenForm] = useState(false);
  // const [editData, setEditData] = useState<any | null>(null); // Replace with proper interface if available

  // const handleEdit = (data: any) => {
  //   setEditData(data);
  //   setOpenForm(true);
  // };

  // const handleAdd = () => {
  //   setEditData(null);
  //   setOpenForm(true);
  // };

  const columns = useMemo(
    () => [
      { title: "Id", key: "tokenid" },
      { title: "Name", key: "guestName" },
      { title: "Email", key: "email" },
      { title: "Mobile", key: "mobile" },
      { title: "Action", key: "action" },
    ],
    []
  );

  const rows = userData?.map((item: any) => ({
    key: item?._id,
    tokenid: `U-${item?._id?.slice(-5)}`,
    guestName: item?.name,
    email: item?.email,
    mobile: item?.phoneNumber,
    action: (
      <TableAction
        onShow={() => navigate(`/guest-management/guest-details/${item?._id}`)}
        // onEdit={() => handleEdit(item)}
        onDelete={() => deleteUser(item._id)}
      />
    ),
  }));

  return (
    <div>
      <Header hideHeader={false} title="" />
      <MasterTable loading={false} columns={columns} rows={rows} />

      {/* Popup Form */}
      {/* {openForm && (
        <GuestForm onClose={() => setOpenForm(false)} editData={editData} />
      )} */}
    </div>
  );
};

export default GuestIndex;
