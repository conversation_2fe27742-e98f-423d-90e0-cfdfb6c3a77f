import { Icon } from "@iconify/react/dist/iconify.js";
import { Form, FormikProvider, useFormik } from "formik";
import get from "lodash/get";
import moment from "moment";
import { PopupModal } from "../../../components";
import { useGetAllRoom } from "../../../server-action/API/Room/room";
import {
  useCreateWakeUpModule,
  useUpdateWakeUpModule,
} from "../../../server-action/API/WakeUp/wakeup";
import { FormField } from "../../BookingManagement/components/ReservationCustomForm";
import { useEffect } from "react";
import { WakeUpValidationSchema } from "./WakeUpValidationSchema";
interface WakeupFormProps {
  close: () => void;
  edit?: boolean;
  editData?: any;
}

const WakeupForm: React.FC<WakeupFormProps> = ({
  close,
  edit = false,
  editData,
}) => {
  const { data } = useGetAllRoom();
  const {
    mutate: createWakeup,
    isPending,
    isSuccess,
  } = useCreateWakeUpModule();
  const {
    mutate: updateWakeup,
    isPending: updating,
    isSuccess: updateSuccess,
  } = useUpdateWakeUpModule();
  useEffect(() => {
    if (updateSuccess || isSuccess) {
      close();
    }
  }, [updateSuccess, isSuccess]);
  console.log("data", data);
  const roomOption =
    data?.map((item: any) => ({ label: item.roomNo, value: item._id })) || [];

  const formik = useFormik({
    initialValues: {
      date: edit
        ? moment(get(editData, "date", "")).format("YYYY-MM-DD")
        : moment().format("YYYY-MM-DD"),
      time: edit
        ? moment(get(editData, "time", "")).format("HH:mm")
        : moment().format("HH:mm"),
      room: edit ? get(editData, "room._id", "") : "",
      description: edit ? get(editData, "description", "") : "",
      status: edit ? get(editData, "status", "") : "",
    },
    enableReinitialize: true,
    validationSchema: WakeUpValidationSchema,
    onSubmit: async (values) => {
      if (edit) await updateWakeup({ id: editData._id, body: values });
      else await createWakeup(values);
    },
  });

  return (
    <PopupModal onClose={close} classname="w-full max-w-screen-sm">
      <div className="relative flex items-center jusify-between bg-[#F1F6FD]">
        <h1 className="w-full p-4 text-center text-semibold">Wake Up Add</h1>
        <button
          className="absolute flex items-center justify-center p-1 bg-black rounded-full top-4 right-4"
          onClick={close}
        >
          <Icon
            icon="fluent-mdl2:calculator-multiply"
            width="14"
            height="14"
            className="text-white"
          />
        </button>
      </div>
      <div className="p-4">
        <FormikProvider value={formik}>
          <Form className="space-y-4" onSubmit={formik.handleSubmit}>
            <div className="flex items-center justify-between gap-4">
              <FormField name="date" label="Date" type="date" formik={formik} />
              <FormField
                name="time"
                label="Select Time"
                type="time"
                formik={formik}
              />
            </div>
            <div className="grid grid-cols-2 place-items-start justify-between gap-4">
              <FormField
                name="room"
                label="Room number"
                options={roomOption as any}
                formik={formik}
                type="dropdown"
                placeholder="Select Room number"
              />
              <FormField
                name="status"
                label="Status"
                formik={formik}
                type="dropdown"
                placeholder="Enter Staus"
                options={[
                  { label: "Active", value: "active" },
                  { label: "InActive", value: "inactive" },
                ]}
              />
              <FormField
                name="description"
                label="Description"
                formik={formik}
                type="textarea"
                placeholder="Enter Description"
              />

              {/* <textarea rows={3} {...formik.getFieldProps("description")} /> */}
            </div>

            <div className="flex items-center justify-end mt-4">
              <button
                type="submit"
                disabled={isPending || updating}
                className="py-2 px-6 text-white rounded-md bg-[#163381]"
              >
                {isPending || updating ? (
                  <Icon icon="line-md:loading-loop" width="18" height="18" />
                ) : (
                  "Submit"
                )}
              </button>
            </div>
          </Form>
        </FormikProvider>
      </div>
    </PopupModal>
  );
};

export default WakeupForm;
