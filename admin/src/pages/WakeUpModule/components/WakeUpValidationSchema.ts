import * as Yup from "yup";

export const WakeUpValidationSchema = Yup.object().shape({
  date: Yup.date()
    .required("Date is required")
    .typeError("Invalid date format"),

  time: Yup.string()
    .required("Time is required")
    .matches(
      /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/,
      "Invalid time format (HH:mm)"
    ),

  room: Yup.string().required("Room is required"),

  description: Yup.string()
    .max(500, "Description cannot exceed 500 characters")
    .nullable(), // if optional
});
