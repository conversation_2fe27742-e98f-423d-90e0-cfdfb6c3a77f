import get from "lodash/get";
import moment from "moment";
import { useState } from "react";
import { PopupModal } from "../../components";
import Header from "../../components/Header";
import MasterTable from "../../layouts/Table/MasterTable";
import { TableAction } from "../../layouts/Table/TableAction";
import {
  useDeleteWakeUpModule,
  useGetAllWakeUp,
  useUpdateWakeUpModule,
} from "../../server-action/API/WakeUp/wakeup";
import WakeupForm from "./components/WakeupForm";
import { Status } from "../../components/Status";
import { DateForamter } from "../../components/DateFormater";
import { TimeFormater } from "../../components/TimeFormater";
interface FormState {
  state: boolean;
  edit: boolean;
  editData?: any;
}

const WakeupIndex = () => {
  const { mutate: deleteModule } = useDeleteWakeUpModule();
  const [showPopup, setShowPopup] = useState("");
  const { data, isLoading, isSuccess } = useGetAllWakeUp();
  const { mutateAsync: updateWakeUp } = useUpdateWakeUpModule();

  const [formState, setFormState] = useState<FormState>({
    state: false,
    edit: true,
    editData: null,
  });
  const tableData = {
    columns: [
      { title: "Date", key: "date" },
      { title: "Wake Up Reminder", key: "reminder" },
      { title: "Room Number", key: "room" },
      { title: "Description", key: "description" },
      { title: "Status", key: "status" },
      { title: "Action", key: "action" },
    ],
    rows: isSuccess
      ? data?.map((item) => ({
          id: item._id,
          date: DateForamter(item?.date),
          room: get(item, "room.roomNo", "-"),
          reminder: TimeFormater(item?.time),
          status: <Status status={item?.status ?? ""} />,
          description: get(item, "description", "-"),
          action: (
            <TableAction
              onSwitch={() => {
                updateWakeUp({
                  id: item._id,
                  body: {
                    ...item,
                    status: item.status === "active" ? "inactive" : "active",
                  },
                });
              }}
              switchStatus={item?.status}
              onEdit={() =>
                setFormState({
                  edit: true,
                  state: true,
                  editData: item,
                })
              }
              onDelete={() => {
                deleteModule(item._id);
              }}
            />
          ),
        }))
      : [],
  };

  return (
    <div>
      <Header title="Wakeup" onAddClick={() => setShowPopup("wakeup")} />
      {showPopup === "wakeup" && <WakeupForm close={() => setShowPopup("")} />}

      {showPopup === "view" && (
        <PopupModal onClose={() => setShowPopup("")}>
          <div>hello</div>
        </PopupModal>
      )}
      <div className="bg-white ">
        <MasterTable
          columns={tableData?.columns}
          rows={tableData.rows ?? []}
          loading={isLoading}
        />
        {formState.state && (
          <WakeupForm
            close={() =>
              setFormState({ state: false, edit: false, editData: null })
            }
            edit={formState.edit}
            editData={formState.editData}
          />
        )}
      </div>
    </div>
  );
};

export default WakeupIndex;
