import { IFormData } from "../../../../components/GlobalForm/globalinterface";

export const getRoomFormData = (
  roomTypes: any[],
  bedTypes: any[],
  floorPlans: any[]
): IFormData[] => {
  const roomTypeOptions = roomTypes.map((room: any) => ({
    label: room.name,
    value: room._id,
  }));

  const bedTypeOptions = bedTypes.map((bed: any) => ({
    label: bed.name,
    value: bed._id,
  }));

  const acOptions = [
    { label: "None", value: "none" },
    { label: "Central", value: "central" },
    { label: "Split", value: "split" },
    { label: "Window", value: "window" },
  ];

  const floorPlanOptions = floorPlans.map((floor: any) => ({
    label: floor.name,
    value: floor._id,
  }));

  const roomStatusOptions = [
    { label: "Available", value: "available" },
    { label: "Occupied", value: "occupied" },
    { label: "Maintenance", value: "maintenance" },
    { label: "Cleaning", value: "cleaning" },
    { label: "Reserved", value: "reserved" },
  ];

  return [
    {
      field: "roomType",
      label: "Room Type",
      type: "select",
      placeholder: "Select Room Type",
      options: roomTypeOptions,
    },
    {
      field: "capacity.standard",
      label: "Standard Capacity",
      type: "text",
      placeholder: "Enter standard capacity",
    },
    {
      field: "capacity.maximum",
      label: "Maximum Capacity",
      type: "text",
      placeholder: "Enter max capacity Max(20)",
    },
    {
      field: "roomPrice.base",
      label: "Base Price",
      type: "text",
      placeholder: "Enter base price",
    },
    {
      field: "beds.count",
      label: "No. of Beds",
      type: "text",
      placeholder: "Enter no. of beds max(10)",
    },
    {
      field: "beds.types",
      label: "Bed Type",
      type: "select",
      placeholder: "Select bed type",
      options: bedTypeOptions,
    },
    {
      field: "features.acType",
      label: "A/C Type",
      type: "select",
      placeholder: "Select A/C type",
      options: acOptions,
    },

    {
      field: "floorPlan",
      label: "Floor Plan",
      type: "select",
      placeholder: "Select floor plan",
      options: floorPlanOptions,
    },
    {
      field: "floor",
      label: "Floor No.",
      type: "text",
      placeholder: "Enter floor number",
    },
    {
      field: "roomNo",
      label: "Room Number",
      type: "text",
      placeholder: "Enter room number",
    },
    {
      field: "status",
      label: "Status",
      type: "select",
      placeholder: "Select room status",
      options: roomStatusOptions,
    },
    {
      field: "isVip",
      label: "isVip",
      type: "toggle",
      placeholder: "Enter room number",
    },
    {
      field: "balcony",
      label: "Balcony",
      type: "toggle",
      placeholder: "",
    },
    {
      field: "description",
      label: "Description",
      type: "textarea",
      placeholder: "Enter room Description",
    },
    // {
    //   field: "images",
    //   label: "Image",
    //   type: "files",
    //   placeholder: "Enter Image ",
    // },
  ];
};
