export const getRoomPackageFormData = (
  roomTypes: any[],
  amenities: any[],
  rooms: any[],
  roomService: any[]
) => [
  {
    field: "name",
    label: "Package Name",
    type: "text",
    placeholder: "Enter Package Name",
  },
  {
    field: "price",
    label: "Price",
    type: "number",
    placeholder: "Enter Package Price",
  },
  {
    field: "priceType",
    label: "Price Type",
    type: "select",
    placeholder: "Select Price Type",
    options: [
      { label: "Total", value: "total" },
      { label: "Per Night", value: "perNight" },
    ],
  },
  {
    field: "durationNights",
    label: "Duration (Nights)",
    type: "number",
    placeholder: "Enter Duration in Nights",
  },
  {
    field: "minGuests",
    label: "Minimum Guests",
    type: "number",
    placeholder: "Enter Minimum Guests",
  },
  {
    field: "maxGuests",
    label: "Maximum Guests",
    type: "number",
    placeholder: "Enter Maximum Guests",
  },
  {
    field: "startDate",
    label: "Start Date",
    type: "date",
    placeholder: "Select Start Date",
  },
  {
    field: "endDate",
    label: "End Date",
    type: "date",
    placeholder: "Select End Date",
  },
  {
    field: "isActive",
    label: "Is Active?",
    type: "toggle",
    placeholder: "",
  },
  {
    field: "inclusions.roomTypes",
    label: "Included Room Types",
    type: "select",
    placeholder: "Select Room Types",
    options: roomTypes?.map((item) => ({
      value: item?._id,
      label: item?.name,
    })),
  },
  {
    field: "inclusions.services",
    label: "Included Services",
    type: "select",
    placeholder: "Select Services",
    options: roomService?.map((item) => ({
      value: item?._id,
      label: item?.name,
    })), // To be populated
  },
  {
    field: "inclusions.amenities",
    label: "Included Amenities",
    type: "checkbox",
    placeholder: "Select Amenities",
    options: amenities?.map((item: any) => ({
      value: item?._id,
      label: item?.name?.join(", "),
    })),
  },
  {
    field: "inclusions.rooms",
    label: "Included Rooms",
    type: "select",
    placeholder: "Select Specific Rooms",
    options: rooms?.map((item) => ({
      value: item?._id,
      label: item?.roomNumber || item?.name,
    })),
  },
  {
    field: "images",
    label: "Images",
    type: "file",
    placeholder: "Upload Package Images",
    multiple: true,
  },
  {
    field: "description",
    label: "Description",
    type: "textarea",
    placeholder: "Enter Package Description",
  },
  {
    field: "termsAndConditions",
    label: "Terms and Conditions",
    type: "textarea",
    placeholder: "Enter Terms and Conditions",
  },
];
