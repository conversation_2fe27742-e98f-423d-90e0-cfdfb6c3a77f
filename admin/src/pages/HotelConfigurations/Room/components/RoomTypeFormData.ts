enum AcType {
  Split = "split",
  Window = "window",
  Central = "central",
}

export const getRoomFormData = (
  roomTypes: any[],
  bedTypes: any[],
  floorPlans: any[]
) => [
  {
    field: "roomType",
    label: "Room Type",
    type: "select",
    placeholder: "Select room type",
    options:
      roomTypes?.map((type) => ({
        value: type?._id,
        label: type?.name,
      })) || [],
  },
  {
    field: "capacity.standard",
    label: "Standard Capacity",
    type: "number",
    placeholder: "Enter standard capacity",
    min: 1,
    max: 20,
  },
  {
    field: "capacity.maximum",
    label: "Maximum Capacity",
    type: "number",
    placeholder: "Enter maximum capacity",
    min: 1,
    max: 20,
  },
  {
    field: "roomPrice.base",
    label: "Base Price",
    type: "number",
    placeholder: "Enter base price",
  },
  {
    field: "roomPrice.weekend",
    label: "Weekend Price",
    type: "number",
    placeholder: "Enter weekend price",
  },
  {
    field: "beds.count",
    label: "Bed Count",
    type: "number",
    placeholder: "Enter number of beds",
    min: 1,
    max: 10,
  },
  {
    field: "bedType",
    label: "Bed Type",
    type: "select",
    placeholder: "Select Bed Type",
    options:
      bedTypes?.map((type) => ({
        value: type?._id,
        label: type?.name,
      })) || [],
  },
  {
    field: "acType",
    label: "A/C Type",
    type: "select",
    placeholder: "Select A/C Type",
    options: Object.values(AcType).map((value) => ({
      value,
      label: value.charAt(0).toUpperCase() + value.slice(1),
    })),
  },
  {
    field: "floor",
    label: "Floor",
    type: "number",
    placeholder: "Enter floor number",
  },
  {
    field: "floorPlan",
    label: "Floor Plan",
    type: "select",
    placeholder: "Select Floor List",
    options:
      floorPlans?.map((plan) => ({
        value: plan?._id,
        label: plan?.name,
      })) || [],
  },
  {
    field: "roomNo",
    label: "Room Number",
    type: "text",
    placeholder: "Enter room number",
  },
  // {
  //   field: "features.viewType",
  //   label: "View Type",
  //   type: "text",
  //   placeholder: "Enter view type (e.g., Ocean, City)",
  // },
  // {
  //   field: "features.balcony",
  //   label: "Balcony",
  //   type: "switch",
  // },
  // {
  //   field: "features.smoking",
  //   label: "Smoking Allowed",
  //   type: "switch",
  // },
  // {
  //   field: "images",
  //   label: "Upload Images",
  //   type: "file",
  //   multiple: true,
  // },
  // {
  //   field: "description",
  //   label: "Room Description",
  //   type: "textarea",
  //   placeholder: "Enter room description",
  // },
  // {
  //   field: "vip",
  //   label: "VIP Room",
  //   type: "boolean",
  // },
  // {
  //   field: "isActive",
  //   label: "Active Room",
  //   type: "switch",
  // },
];
