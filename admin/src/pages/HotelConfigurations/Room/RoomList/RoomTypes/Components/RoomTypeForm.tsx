import { Form, FormikProvider, useFormik } from "formik";

import * as Yup from "yup";
import {
  useCreateRoomType,
  useUpdateRoomType,
} from "../../../../../../server-action/API/Room/room-type";
import HeadingPopup from "../../../../../GuestManagement/components/HeadingPopup";
import { FormField } from "../../../../../BookingManagement/components/ReservationCustomForm";

const RoomTypeForm = ({
  onClose,
  editData,
}: {
  onClose: () => void;
  editData?: {
    _id: string;
    name: string;
  };
}) => {
  const { mutateAsync: createRoomType } = useCreateRoomType();
  const { mutateAsync: updateRoomType } = useUpdateRoomType();

  const RoomTypeValidaiton = Yup.object({
    name: Yup.string().required("Name is required"),
  });

  const formik = useFormik({
    validationSchema: RoomTypeValidaiton,
    initialValues: {
      name: editData ? editData?.name : "",
    },
    onSubmit: async (values) => {
      if (editData) {
        await updateRoomType({
          roomTypeData: values,
          _id: editData?._id as string,
        });
      } else {
        await createRoomType(values);
      }

      onClose();
    },
  });
  console.log("formik", formik.values);
  return (
    <HeadingPopup
      onClose={onClose}
      className="w-[350px]"
      heading="Create Room Type"
    >
      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit} className="space-y-3">
          <FormField
            label="Room Type"
            type="text"
            name="name"
            formik={formik}
            placeholder="Room Type"
          />

          <div className="flex items-center justify-end mt-4">
            <button
              type="submit"
              className="px-4 py-2 bg-[#2A3A6D] text-white rounded-md"
            >
              Add
            </button>
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default RoomTypeForm;
