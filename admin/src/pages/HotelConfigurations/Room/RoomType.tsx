import { useState } from "react";
import MasterTable from "../../../layouts/Table/MasterTable";
import { TableAction } from "../../../layouts/Table/TableAction";
import RoomTypeForm from "./components/RoomTypeForm";
import Header from "../../../components/Header";
import {
  useDeleteRoomType,
  useGetAllRoomType,
} from "../../../server-action/API/Room/room-type";
import type { RoomType } from "../../../server-action/API/Room/room-type";

const RoomType = () => {
  const [showPopup, setShowPopup] = useState("");
  const { data: roomType, isSuccess, isLoading } = useGetAllRoomType();
  const { mutateAsync: deleteRoom } = useDeleteRoomType();

  const [selectedData, setSelectedData] = useState<RoomType>();
  const tableData = {
    columns: [
      { title: "Id", key: "tokenid" },
      { title: "Room Type", key: "roomType" },
      { title: "Action", key: "action" },
    ],
    rows: isSuccess
      ? roomType?.map((item) => ({
          key: item?._id,
          tokenid: item?._id?.slice(item?._id?.length - 5, item?._id.length),
          roomType: item?.name,
          action: (
            <TableAction
              onEdit={() => {
                setShowPopup("view");
                setSelectedData(item);
              }}
              onDelete={async () => {
                await deleteRoom(item?._id as string);
              }}
            />
          ),
        }))
      : [],
  };

  return (
    <div>
      {/* <Header
        onAddClick={() => {
          setSelectedData(undefined);
          setShowPopup("add");
        }}
      /> */}

      {showPopup === "add" && <RoomTypeForm onClose={() => setShowPopup("")} />}

      {showPopup === "view" && (
        <RoomTypeForm
          onClose={() => setShowPopup("")}
          editData={selectedData}
        />
      )}
      <div className="bg-white ">
        <MasterTable
          columns={tableData?.columns}
          rows={tableData.rows}
          loading={isLoading}
        />
      </div>
    </div>
  );
};

export default RoomType;
