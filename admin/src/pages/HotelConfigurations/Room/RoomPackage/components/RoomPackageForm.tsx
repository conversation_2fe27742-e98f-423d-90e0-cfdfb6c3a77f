import { Form, FormikProvider, useFormik } from "formik";
import { Icon } from "@iconify/react/dist/iconify.js";
import moment from "moment";

import { IRoomPackage } from "../../../../../Interface/room.interface";
import { useGetAllRoomType } from "../../../../../server-action/API/Room/room-type";
import {
  useCreatePackage,
  useUpdatePackage,
} from "../../../../../server-action/API/HotelConfiguration/roompackage";
import { useGetAmenities } from "../../../../../server-action/API/HotelConfiguration/amenities";
import { useGetAllService } from "../../../../../server-action/API/HotelConfiguration/services";
import { FormField } from "../../../../BookingManagement/components/ReservationCustomForm";
import { PopupModal } from "../../../../../components";
import RoomPackageImages from "./RoomPackageImageUpload";
import { RoomPackageValidationSchema } from "./RoomPackageSchems";
import { useGetAllRoom } from "../../../../../server-action/API/Room/room";

interface PackageFormProps {
  onClose: () => void;
  editData: IRoomPackage | null;
}

// Default initial values
const defaultInitialValues = {
  name: "",
  price: "",
  priceType: "",
  durationNights: "",
  minGuests: "",
  maxGuests: "",
  startDate: "",
  endDate: "",
  isActive: false,
  inclusions: {
    roomTypes: [],
    services: [],
    amenities: [],
    rooms: [],
  },
  images: [],
  description: "",
  termsAndConditions: "",
};

const RoomPackageForm = ({ onClose, editData }: PackageFormProps) => {
  const {
    mutateAsync: createPackage,
    reset: resetCreate,
    isPending: isCreating,
  } = useCreatePackage();
  const {
    mutateAsync: updatePackage,
    reset: resetUpdate,
    isPending: isUpdating,
  } = useUpdatePackage();

  const { data: roomTypes } = useGetAllRoomType();
  const { data: amenities } = useGetAmenities();
  const { data: rooms } = useGetAllRoom();
  const { data: roomService } = useGetAllService();

  const formik = useFormik({
    enableReinitialize: true,
    initialValues: editData
      ? {
          name: editData.name ?? "",
          price: editData.price ?? "",
          priceType: editData.priceType ?? "",
          durationNights: editData.durationNights ?? "",
          minGuests: editData.minGuests ?? "",
          maxGuests: editData.maxGuests ?? "",
          startDate: editData.startDate
            ? moment(editData.startDate).format("YYYY-MM-DD")
            : "",
          endDate: editData.endDate
            ? moment(editData.endDate).format("YYYY-MM-DD")
            : "",
          isActive: editData.isActive ?? false,
          inclusions: {
            roomTypes:
              typeof editData.inclusions === "string"
                ? JSON.parse(editData.inclusions)?.roomTypes ?? []
                : editData.inclusions?.roomTypes?.map((rt) => rt?._id) ?? [],
            services:
              typeof editData.inclusions === "string"
                ? JSON.parse(editData.inclusions)?.services ?? []
                : editData.inclusions?.services?.map((s) => s._id) ?? [],
            amenities:
              typeof editData.inclusions === "string"
                ? JSON.parse(editData.inclusions)?.amenities ?? []
                : editData.inclusions?.amenities?.map((a) => a._id) ?? [],
            rooms:
              typeof editData.inclusions === "string"
                ? JSON.parse(editData.inclusions)?.rooms ?? []
                : editData.inclusions?.rooms?.map((r) => r._id) ?? [],
          },
          images: editData.images ?? [],
          description: editData.description ?? "",
          termsAndConditions: editData.termsAndConditions ?? "",
        }
      : defaultInitialValues,
    validationSchema: RoomPackageValidationSchema,
    onSubmit: async (values) => {
      const formData = new FormData();

      formData.append("name", values.name);
      formData.append("price", values.price.toString());
      formData.append("priceType", values.priceType);
      formData.append("durationNights", values.durationNights.toString());
      formData.append("minGuests", values.minGuests.toString());
      formData.append("maxGuests", values.maxGuests.toString());
      formData.append("startDate", values.startDate);
      formData.append("endDate", values.endDate);
      formData.append("isActive", values.isActive.toString());
      formData.append("description", values.description);
      formData.append("termsAndConditions", values.termsAndConditions);
      formData.append("inclusions", JSON.stringify(values.inclusions));

      if (values.images?.length > 0) {
        values.images.forEach((file: string | File) => {
          formData.append("images", file);
        });
      }

      if (editData) {
        await updatePackage({
          packageData: formData,
          _id: editData._id as string,
        });
        resetUpdate();
      } else {
        await createPackage(formData);
        resetCreate();
      }

      onClose();
    },
  });

  return (
    <PopupModal onClose={onClose} classname="w-full max-w-4xl">
      <div className="relative flex items-center justify-between bg-[#F4F6FB]">
        <h1 className="w-full p-4 text-center text-semibold">
          {editData ? "Edit Room Package" : "Add Room Package"}
        </h1>
        <button
          className="absolute flex items-center justify-center p-1 bg-black rounded-full top-4 right-4"
          onClick={onClose}
        >
          <Icon
            icon="fluent-mdl2:calculator-multiply"
            width="14"
            height="14"
            className="text-white"
          />
        </button>
      </div>
      <div className="p-4 max-h-[80vh] overflow-y-auto">
        <FormikProvider value={formik}>
          <Form onSubmit={formik.handleSubmit}>
            <div className="grid grid-cols-3 gap-4">
              <FormField
                name="name"
                label="Package Name"
                type="text"
                placeholder="Enter Package Name"
                formik={formik}
              />
              <FormField
                name="price"
                label="Price"
                type="number"
                placeholder="Enter Package Price"
                formik={formik}
              />
              <FormField
                name="priceType"
                label="Price Type"
                type="dropdown"
                placeholder="Select Price Type"
                options={[
                  { label: "Total", value: "total" },
                  { label: "Per Night", value: "perNight" },
                ]}
                formik={formik}
              />
              <FormField
                name="durationNights"
                label="Duration (Nights)"
                type="number"
                placeholder="Enter Duration in Nights"
                formik={formik}
              />
              <FormField
                name="minGuests"
                label="Minimum Guests"
                type="number"
                placeholder="Enter Minimum Guests"
                formik={formik}
              />
              <FormField
                name="maxGuests"
                label="Maximum Guests"
                type="number"
                placeholder="Enter Maximum Guests"
                formik={formik}
              />
              <FormField
                name="startDate"
                label="Start Date"
                type="date"
                placeholder="Select Start Date"
                formik={formik}
              />
              <FormField
                name="endDate"
                label="End Date"
                type="date"
                placeholder="Select End Date"
                formik={formik}
              />

              {/* Inclusions */}
              <FormField
                name="inclusions.roomTypes"
                label="Included Room Types"
                type="dropdown"
                placeholder="Select Room Types"
                options={roomTypes?.map((item) => ({
                  label: item?.name,
                  value: item?._id,
                }))}
                formik={formik}
                multiSelect={true}
                required
              />
              <FormField
                name="inclusions.services"
                label="Included Services"
                type="dropdown"
                placeholder="Select Services"
                options={(roomService || []).map((item) => ({
                  label: item?.name,
                  value: item?._id,
                }))}
                formik={formik}
                multiSelect={true}
              />
              <FormField
                name="inclusions.amenities"
                label="Included Amenities"
                type="dropdown"
                placeholder="Select Amenities"
                options={(amenities || []).map((item: any) => ({
                  label: item?.name,
                  value: item?._id,
                }))}
                formik={formik}
                multiSelect={true}
              />
              <FormField
                name="inclusions.rooms"
                label="Included Rooms"
                type="dropdown"
                placeholder="Select Specific Rooms"
                options={(rooms || []).map((item) => ({
                  label: item?.roomNo ?? "",
                  value: item?._id ?? "",
                }))}
                formik={formik}
                multiSelect={true}
              />

              {/* Status */}
              <div className="col-span-2">
                <div className="flex items-center mb-4">
                  <label className="mr-2 text-sm">Is Active</label>
                  <input
                    type="checkbox"
                    name="isActive"
                    checked={formik.values.isActive}
                    onChange={formik.handleChange}
                    className="w-4 h-4"
                  />
                </div>
              </div>

              {/* Description and Terms */}
              <div className="col-span-2">
                <FormField
                  name="description"
                  label="Description"
                  type="textarea"
                  placeholder="Enter Package Description"
                  formik={formik}
                />
              </div>

              <div className="col-span-1">
                <FormField
                  name="termsAndConditions"
                  label="Terms and Conditions"
                  type="textarea"
                  placeholder="Enter Terms and Conditions"
                  formik={formik}
                />
              </div>

              {/* Images Upload room package */}
              <RoomPackageImages formik={formik} />
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-end mt-6 space-x-4">
              <button
                type="button"
                onClick={onClose}
                className="py-2 px-6 text-gray-700 border border-gray-300 rounded-md"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isCreating || isUpdating}
                className="py-2 px-6 text-white rounded-md bg-[#163381]"
              >
                {isCreating || isUpdating ? (
                  <Icon icon="line-md:loading-loop" width="18" height="18" />
                ) : (
                  "Save"
                )}
              </button>
            </div>
          </Form>
        </FormikProvider>
      </div>
    </PopupModal>
  );
};

export default RoomPackageForm;
