import * as Yup from "yup";
export const RoomPackageValidationSchema = Yup.object().shape({
  name: Yup.string()
    .required("Package name is required")
    .min(3, "Package name must be at least 3 characters"),
  price: Yup.number()
    .required("Price is required")
    .min(1, "Price must be greater than 0"),
  priceType: Yup.string().required("Price type is required"),
  durationNights: Yup.number()
    .required("Duration is required")
    .min(1, "Duration must be at least 1 night"),
  minGuests: Yup.number()
    .required("Minimum guests is required")
    .min(1, "Minimum guests must be at least 1"),
  maxGuests: Yup.number()
    .required("Maximum guests is required")
    .min(1, "Maximum guests must be at least 1"),
  startDate: Yup.date().required("Start date is required"),

  endDate: Yup.date()
    .min(Yup.ref("startDate"), "End date can't be before start date")
    .required("End date is required"),
  isActive: Yup.boolean(),
  inclusions: Yup.object().shape({
    roomTypes: Yup.array().min(1, "Select at least one room type"),
    services: Yup.array().min(1, "Select at least one service"),
    amenities: Yup.array().min(1, "Select at least one amenity"),
    rooms: Yup.array().min(1, "Select at least one room"),
  }),
  images: Yup.array().max(10, "You can upload up to 10 images only"),
  description: Yup.string().required("Description is required"),
  termsAndConditions: Yup.string(),
});
