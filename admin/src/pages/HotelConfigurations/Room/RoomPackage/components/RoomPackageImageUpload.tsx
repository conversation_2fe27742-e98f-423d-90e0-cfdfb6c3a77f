import React, { JSX } from "react";
import { Icon } from "@iconify/react";
import { IMAGE_URL } from "../../../../../constant/constant";
import { galleryIcon } from "../../../../../components/svgExports";

const RoomPackageImages = ({ formik }: any) => {
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newFiles = Array.from(e.target.files);
      const existingImages = Array.isArray(formik.values.images)
        ? formik.values.images.filter(
            (img: any) => typeof img === "string" || img instanceof File
          )
        : [];

      // Log existing and new images for debugging
      console.log("Existing images:", existingImages);
      console.log("New files:", newFiles);

      // Check if total images (existing + new) exceed the max limit (10)
      const totalImages = existingImages.length + newFiles.length;
      if (totalImages > 10) {
        formik.setFieldError("images", "You can upload up to 10 images only");
        formik.setFieldTouched("images", true);
        return;
      }

      // Merge existing and new images
      const updatedImages = [...existingImages, ...newFiles];
      formik.setFieldValue("images", updatedImages);
      formik.setFieldTouched("images", true);
      e.target.value = ""; // Reset file input
      console.log("Updated Formik images:", updatedImages);
    }
  };

  const isImage = (file: File | string) =>
    typeof file === "string"
      ? file.match(/\.(jpg|jpeg|png|gif)$/i)
      : file.type.startsWith("image/");

  return (
    <div className="col-span-2">
      <div className="flex flex-col">
        <div>
          <label className="mb-1 text-sm">
            Upload Images <span className="text-red-600">*</span>
          </label>
          <div className="flex items-center w-full space-x-2 border-2 rounded-md">
            <label
              htmlFor="room-package-images"
              className="flex items-center gap-1 p-2 text-white bg-[#163381] cursor-pointer rounded-l-md"
            >
              {galleryIcon({ color: "white", size: "18" })}
              Choose Files
            </label>
            <input
              name="images"
              id="room-package-images"
              multiple={true}
              accept="image/*"
              type="file"
              onChange={handleFileChange}
              onBlur={() => formik.setFieldTouched("images", true)}
              className="hidden"
            />
            <span className="text-sm text-gray-500">
              {Array.isArray(formik.values.images)
                ? formik.values.images.length
                : 0}{" "}
              files selected
            </span>
          </div>
          {formik.touched.images && formik.errors.images && (
            <span className="text-sm text-red-500">
              {String(formik.errors.images)}
            </span>
          )}
        </div>
        {formik.values.images?.length > 0 && (
          <div className="flex flex-wrap p-4 mt-2 border-2 border-gray-300 rounded-md">
            {formik.values.images.map((file: File | string, index: number) => {
              if (!isImage(file)) {
                console.warn(`Invalid image at index ${index}:`, file);
                return null;
              }
              const isString = typeof file === "string";
              const imageUrl = isString
                ? `${IMAGE_URL}${file}`
                : URL.createObjectURL(file);

              return (
                <div key={index} className="relative w-24 h-24 m-2">
                  <img
                    src={imageUrl}
                    alt={`preview-${index}`}
                    className="object-cover w-full h-full rounded-md"
                  />
                  <button
                    type="button"
                    className="absolute top-0 right-0 p-1 bg-red-500 rounded-full"
                    onClick={() => {
                      const newFiles = formik.values.images.filter(
                        (_: any, i: number) => i !== index
                      );
                      formik.setFieldValue("images", newFiles);
                      formik.setFieldTouched("images", true);
                      console.log("After deletion, Formik images:", newFiles);
                    }}
                  >
                    <Icon
                      icon="fluent-mdl2:calculator-multiply"
                      width="16"
                      height="16"
                      className="text-red bg-white rounded-full p-[2px]"
                    />
                  </button>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default RoomPackageImages;
