import { Icon } from "@iconify/react";
import { useState } from "react";
import { IPackage } from "../../../../../Interface/package.interface";
import HeadingPopup from "../../../../GuestManagement/components/HeadingPopup";
import { IMAGE_URL } from "../../../../../constant/constant";
import ImageModal from "../../../../ImageModal";

interface IRoomPackageDetailsProps {
  onClose: () => void;
  data: IPackage | null;
}

const RoomPackageDetails = ({ onClose, data }: IRoomPackageDetailsProps) => {
  if (!data) return null;

  const {
    name,
    description,
    price,
    priceType,
    minGuests,
    maxGuests,
    startDate,
    endDate,
    termsAndConditions,
    images,
    durationNights,
  } = data;

  console.log(images, "ima");

  const formattedStartDate = startDate
    ? new Date(startDate).toLocaleDateString("en-GB")
    : "N/A";

  const formattedEndDate = endDate
    ? new Date(endDate).toLocaleDateString("en-GB")
    : "N/A";

  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  return (
    <HeadingPopup
      heading="Room Package Details"
      onClose={onClose}
      className="w-full max-w-2xl"
    >
      <div className="w-full p-6 relative">
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-500 hover:text-red-500"
        >
          {/* <Icon icon="ic:round-close" width={24} height={24} /> */}
        </button>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <DetailItem label="Package Name" value={name} />
          <DetailItem label="Description" value={description ?? "N/A"} />
          <DetailItem label="Price" value={`Rs. ${price}`} />
          <DetailItem label="Price Type" value={priceType} />
          <DetailItem label="Duration (Nights)" value={durationNights} />
          <DetailItem
            label="Guests (Min - Max)"
            value={`${minGuests} - ${maxGuests}`}
          />
          <DetailItem label="Start Date" value={formattedStartDate} />
          <DetailItem label="End Date" value={formattedEndDate} />
          <DetailItem
            label="Terms & Conditions"
            value={termsAndConditions ?? "N/A"}
          />
          <div>
            <p className="text-lg font-medium text-black-500">Images</p>
            {images && (
              <div className="grid grid-cols-2">
                {images?.map((img) => (
                  <div
                    className="relative w-24 h-24 m-2 border-2 cursor-pointer"
                    onClick={() => setSelectedImage(`${IMAGE_URL}${img}`)}
                  >
                    <img
                      src={`${IMAGE_URL}${img}`}
                      alt="Package"
                      className="object-cover w-full h-full rounded-md"
                    />
                    <span className="absolute  right-0 top-0 p-1 bg-blue-500 text-white rounded-full">
                      <Icon icon="mdi:eye" width={16} height={16} />
                    </span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
      {selectedImage && (
        <ImageModal
          selectedImage={selectedImage}
          onClose={() => setSelectedImage(null)}
          printable
        />
      )}
    </HeadingPopup>
  );
};

const DetailItem = ({
  label,
  value,
}: {
  label: string;
  value: string | number;
}) => (
  <div>
    <div className="text-lg font-medium text-black-500">{label}</div>
    <div className="text-sm">{value || "N/A"}</div>
  </div>
);

export default RoomPackageDetails;
