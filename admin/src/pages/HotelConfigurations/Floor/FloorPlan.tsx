import { useCallback, useState } from "react";
import { PopupModal } from "../../../components";
import Header from "../../../components/Header";
import MasterTable from "../../../layouts/Table/MasterTable";
import { TableAction } from "../../../layouts/Table/TableAction";
import AddFloorForm from "./components/AddFloorForm";
import AssignFloorForm from "./components/AssignFloorForm";
import { Icon } from "@iconify/react/dist/iconify.js";
import {
  useDeleteFloorPlan,
  useGetAllFloorPlans,
  useUpdateFloorPlan,
} from "../../../server-action/API/HotelConfiguration/floorPlan";
import { Status } from "../../../components/Status";

const FloorPlan = () => {
  const [formType, setFormType] = useState<"assign" | "add" | null>(null);
  const [editData, setEditData] = useState(null);

  const { data: allFlorPlan } = useGetAllFloorPlans();
  const { mutateAsync: deleteBed } = useDeleteFloorPlan();
  const { mutateAsync: updateFloorPlan } = useUpdateFloorPlan();
  const closePopup = useCallback(() => setFormType(null), []);

  const tableData = {
    columns: [
      { title: "SN", key: "sn" },
      { title: "Floor Name", key: "floorName" },
      { title: "Status ", key: "status" },

      // { title: "Start Room Number", key: "startingRoomNumber" },
      { title: "Actions", key: "action" },
    ],
    rows: (allFlorPlan || [])?.map((item: any, index: number) => ({
      sn: index + 1,
      floorName: item?.name,
      status: <Status status={item?.isActive} />,
      action: (
        <TableAction
          onSwitch={() => {
            updateFloorPlan({
              _id: item?._id,
              floorPlanData: {
                ...item,
                isActive: !item?.isActive,
              },
            });
          }}
          switchStatus={item?.isActive}
          onEdit={() => {
            setEditData(item as any);
            setFormType("add");
          }}
          onDelete={() => {
            deleteBed(item?._id);
          }}
        />
      ),
    })),
  };

  return (
    <div>
      <div className="flex items-center justify-between">
        <Header showButton={false} />
        <div className="flex items-center gap-5">
          <button
            onClick={() => {
              setEditData(null);
              setFormType("assign");
            }}
            className="hidden items-center gap-2 bg-[#1B62F5] rounded-md px-3 py-1 text-lg text-white "
          >
            <Icon icon="ic:baseline-plus" width={20} height={20} />
            <span>Assgin Floor</span>
          </button>
          <button
            className="flex items-center gap-2 bg-[#2E4476] rounded-md px-3 py-1 text-lg text-white "
            onClick={() => {
              setEditData(null);
              setFormType("add");
            }}
          >
            <Icon icon="ic:baseline-plus" width={20} height={20} />
            <span>Add Floor</span>
          </button>
        </div>
      </div>

      {formType === "assign" && (
        <PopupModal onClose={closePopup}>
          <AssignFloorForm onClose={closePopup} />
        </PopupModal>
      )}

      {formType === "add" && (
        <PopupModal onClose={closePopup}>
          <AddFloorForm onClose={closePopup} editData={editData} />
        </PopupModal>
      )}

      <div className="bg-white ">
        <MasterTable
          columns={tableData?.columns}
          rows={tableData.rows}
          loading={false}
        />
      </div>
    </div>
  );
};

export default FloorPlan;
