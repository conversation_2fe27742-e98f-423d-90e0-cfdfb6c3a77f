import { Form, FormikProvider, useFormik } from "formik";
import HeadingPopup from "../../../GuestManagement/components/HeadingPopup";
import { GlobalForm } from "../../../../components/GlobalForm/GlobalFormComponent";
import { ActionButton } from "../../../../components/ActionButton";
import { floorAssignData } from "./FloorPlanData";

const AssignFloorForm = ({ onClose }: { onClose: () => void }) => {
  const formik = useFormik({
    initialValues: {
      floorSelect: "",
      startRoomNumber: "",
      numberofRoom: "",
    },
    enableReinitialize: true,
    onSubmit: async (values) => {
      console.log(values);
    },
  });

  return (
    <HeadingPopup
      onClose={onClose}
      className="w-full max-w-screen-sm"
      heading="Assign Floor"
    >
      <FormikProvider value={formik}>
        <Form
          onSubmit={(e) => {
            e.preventDefault();
            formik.handleSubmit(e);
          }}
        >
          <div className="grid grid-cols-4 gap-2">
            <GlobalForm
              formDatails={floorAssignData || []}
              getFieldProps={formik.getFieldProps}
            />
          </div>
          <ActionButton onCancel={onClose} onSubmit={formik.handleSubmit} />
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default AssignFloorForm;
