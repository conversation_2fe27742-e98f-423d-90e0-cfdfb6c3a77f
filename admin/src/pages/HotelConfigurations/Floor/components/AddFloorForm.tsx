import { Form, FormikProvider, useFormik } from "formik";
import HeadingPopup from "../../../GuestManagement/components/HeadingPopup";
import { GlobalForm } from "../../../../components/GlobalForm/GlobalFormComponent";
import { ActionButton } from "../../../../components/ActionButton";
import {
  useCreateFloorPlan,
  useUpdateFloorPlan,
} from "../../../../server-action/API/HotelConfiguration/floorPlan";
import { IFloorPlan } from "../../../../Interface/floorplan.interfacce";
import { floorPlanData } from "./FloorPlanData";
import * as Yup from "yup";
interface IFloorFormProps {
  onClose: () => void;
  editData: IFloorPlan | null;
}

const floorPlanSchema = Yup.object({
  name: Yup.string().required("Name is required"),
});

const AddFloorForm = ({ onClose, editData }: IFloorFormProps) => {
  const { mutateAsync: createFloorPlan } = useCreateFloorPlan();
  const { mutateAsync: updateFloorPlan } = useUpdateFloorPlan();
  const formik = useFormik({
    initialValues: {
      name: editData?.name,
    },
    validationSchema: floorPlanSchema,
    enableReinitialize: true,
    onSubmit: async (values) => {
      try {
        if (editData) {
          await updateFloorPlan({
            floorPlanData: values as IFloorPlan,
            _id: editData?._id as string,
          });
        } else {
          createFloorPlan(values as any);
        }
        onClose();
      } catch (error) {}
    },
  });
  const { handleSubmit, getFieldProps } = formik;

  return (
    <HeadingPopup
      onClose={onClose}
      className="w-full max-w-screen-sm"
      heading={`${!editData ? "Add" : "Update"} Floor`}
    >
      <FormikProvider value={formik}>
        <Form onSubmit={handleSubmit}>
          <div className="grid grid-cols-2 gap-2">
            <GlobalForm
              formDatails={floorPlanData}
              getFieldProps={getFieldProps}
            />
          </div>
          <ActionButton onCancel={onClose} onSubmit={handleSubmit} />
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default AddFloorForm;
