import React, { useState, useEffect } from "react";
import Stepper from "../../../components/Stepper";
import { Card, CardContent } from "../../../components/Card";
import { Icon } from "@iconify/react";
import { Link, useNavigate } from "react-router-dom";
import { ActionButton } from "../../../components/ActionButton";
import useSetupStatus from "../../../hooks/useSetupStatus";
import {
  SetupStatus,
  refreshSetupStatus,
  updateSetupStatus,
} from "../../../utils/setupUtils";
import { toast } from "react-toastify";

// Setup components
import BedTypeSetup from "./components/BedTypeSetup";
import FloorPlanSetup from "./components/FloorPlanSetup";
import RoomTypeSetup from "./components/RoomTypeSetup";
import AmenityCategorySetup from "./components/AmenityCategorySetup";
import AmenitySetup from "./components/AmenitySetup";
import RoomSetup from "./components/RoomSetup";
import ServiceSetup from "./components/ServiceSetup";
import PackageSetup from "./components/PackageSetup";
import StaffSetup from "./components/StaffSetup";

// Setup status interface is imported from utils/setupUtils.ts

const HotelSetupPage: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [activeTab, setActiveTab] = useState<"guide" | "setup">("guide");
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Fetch setup status using our custom hook
  const { data: apiSetupStatus, isLoading, refetch } = useSetupStatus();

  // Fallback setup status in case the API isn't working - assume all true since API shows all true
  const fallbackSetupStatus: SetupStatus = {
    hotelConfigured: true,
    roomsConfigured: true,
    usersConfigured: true,
    servicesConfigured: true,
    amenitiesConfigured: true,
    lastUpdated: new Date().toISOString(),
    overallCompletion: 100,
  };
  const navigate = useNavigate();
  // Use API data if available, otherwise use fallback data
  const setupStatus = apiSetupStatus || fallbackSetupStatus;

  // For debugging
  useEffect(() => {
    console.log("API Setup Status:", apiSetupStatus);
    console.log("Using Setup Status:", setupStatus);
  }, [apiSetupStatus, setupStatus]);

  // Handle refresh setup status
  const handleRefreshStatus = async () => {
    try {
      setIsRefreshing(true);
      await refreshSetupStatus();
      await refetch();
      toast.success("Setup status refreshed successfully");
    } catch (error) {
      console.error("Error refreshing setup status:", error);
      toast.error("Failed to refresh setup status");
    } finally {
      setIsRefreshing(false);
    }
  };

  // Define setup steps
  const steps = [
    { id: 1, label: "Bed Types" },
    { id: 2, label: "Floor Plans" },
    { id: 3, label: "Room Types" },
    { id: 4, label: "Amenity Categories" },
    { id: 5, label: "Amenities" },
    { id: 6, label: "Rooms" },
    { id: 7, label: "Services" },
    { id: 8, label: "Packages" },
    { id: 9, label: "Staff" },
  ];

  // Handle step navigation
  const handleStepNavigation = (stepId: number) => {
    setCurrentStep(stepId);
  };

  // Handle step completion
  const handleStepComplete = async (stepId: number) => {
    try {
      // Determine which setup status field to update based on the step
      let updates: Partial<SetupStatus> = {};

      switch (stepId) {
        case 1: // Bed Types
        case 2: // Floor Plans
        case 3: // Room Types
        case 6: // Rooms
          updates = { roomsConfigured: true };
          break;
        case 4: // Amenity Categories
        case 5: // Amenities
          updates = { amenitiesConfigured: true };
          break;
        case 7: // Services
        case 8: // Packages
          updates = { servicesConfigured: true };
          break;
        case 9: // Staff
          updates = { usersConfigured: true };
          break;
        default:
          break;
      }

      // Update the setup status
      if (Object.keys(updates).length > 0) {
        setIsRefreshing(true);
        await updateSetupStatus(updates);
        await refetch();
        toast.success(`Step ${stepId} completed successfully!`);
        setIsRefreshing(false);
      }

      // Move to the next step
      if (stepId < 9) {
        setCurrentStep(stepId + 1);
      }
    } catch (error) {
      console.error("Error updating setup status:", error);
      toast.error("Failed to update setup status");
      // Still move to the next step even if the update fails
      if (stepId < 9) {
        setCurrentStep(stepId + 1);
      }
    }
  };

  // Render the current setup component
  const renderSetupComponent = () => {
    switch (currentStep) {
      case 1:
        return <BedTypeSetup onComplete={() => handleStepComplete(1)} />;
      case 2:
        return <FloorPlanSetup onComplete={() => handleStepComplete(2)} />;
      case 3:
        return <RoomTypeSetup onComplete={() => handleStepComplete(3)} />;
      case 4:
        return (
          <AmenityCategorySetup onComplete={() => handleStepComplete(4)} />
        );
      case 5:
        return <AmenitySetup onComplete={() => handleStepComplete(5)} />;
      case 6:
        return <RoomSetup onComplete={() => handleStepComplete(6)} />;
      case 7:
        return <ServiceSetup onComplete={() => handleStepComplete(7)} />;
      case 8:
        return <PackageSetup onComplete={() => handleStepComplete(8)} />;
      case 9:
        return <StaffSetup onComplete={() => handleSetupComplete()} />;
      default:
        return <BedTypeSetup onComplete={() => handleStepComplete(1)} />;
    }
  };

  // Handle setup completion
  const handleSetupComplete = async () => {
    try {
      setIsRefreshing(true);
      // Update all setup status fields to true
      const updates: Partial<SetupStatus> = {
        hotelConfigured: true,
        roomsConfigured: true,
        usersConfigured: true,
        servicesConfigured: true,
        amenitiesConfigured: true,
      };

      await updateSetupStatus(updates);
      await refetch();
      setIsRefreshing(false);

      toast.success("Hotel setup completed successfully!", {
        position: "top-center",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });
      navigate("/");
    } catch (error) {
      console.error("Error completing setup:", error);
      toast.error("Failed to complete setup");
      setIsRefreshing(false);
    }
  };

  // Refresh setup status when step changes
  useEffect(() => {
    refetch();
  }, [currentStep, refetch]);

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Hotel Setup</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => setActiveTab("guide")}
            className={`px-4 py-2 rounded-md ${
              activeTab === "guide"
                ? "bg-[#2A3A6D] text-white"
                : "bg-gray-200 text-gray-700"
            }`}
          >
            Setup Guide
          </button>
          <button
            onClick={() => setActiveTab("setup")}
            className={`px-4 py-2 rounded-md ${
              activeTab === "setup"
                ? "bg-[#2A3A6D] text-white"
                : "bg-gray-200 text-gray-700"
            }`}
          >
            Setup Wizard
          </button>
          <button
            onClick={handleRefreshStatus}
            disabled={isRefreshing}
            className="px-4 py-2 rounded-md bg-green-600 text-white hover:bg-green-700 disabled:opacity-50 flex items-center"
          >
            {isRefreshing ? (
              <>
                <Icon
                  icon="mdi:loading"
                  className="animate-spin mr-2"
                  width="16"
                  height="16"
                />
                Refreshing...
              </>
            ) : (
              <>
                <Icon
                  icon="mdi:refresh"
                  className="mr-2"
                  width="16"
                  height="16"
                />
                Refresh Status
              </>
            )}
          </button>
        </div>
      </div>

      {activeTab === "guide" ? (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Hotel Setup Guide</h2>
          <p className="text-gray-600 mb-6">
            This guide outlines the sequence of steps needed to set up your
            hotel in the system. Follow these steps in order to ensure all
            components are properly configured.
          </p>

          <div className="mb-6">
            <h3 className="text-lg font-medium mb-2">Setup Progress</h3>
            <div className="w-full bg-gray-200 rounded-full h-4">
              <div
                className="bg-green-500 h-4 rounded-full"
                style={{ width: `${setupStatus?.overallCompletion || 0}%` }}
              ></div>
            </div>
            <div className="flex justify-between items-center mt-1">
              <p className="text-sm text-gray-600">
                Last updated:{" "}
                {setupStatus?.lastUpdated
                  ? new Date(setupStatus.lastUpdated).toLocaleString()
                  : "Never"}
              </p>
              <p className="text-sm text-gray-600">
                {setupStatus?.overallCompletion || 0}% Complete
              </p>
            </div>
          </div>

          <div className="space-y-6">
            {steps.map((step, index) => {
              // Map step labels to setup status properties
              const getStatusProperty = (
                stepLabel: string
              ): keyof SetupStatus => {
                switch (stepLabel.toLowerCase()) {
                  case "bed types":
                  case "floor plans":
                  case "room types":
                  case "rooms":
                    return "roomsConfigured";
                  case "amenity categories":
                  case "amenities":
                    return "amenitiesConfigured";
                  case "services":
                    return "servicesConfigured";
                  case "staff":
                    return "usersConfigured";
                  case "packages":
                    return "servicesConfigured";
                  default:
                    return "hotelConfigured";
                }
              };

              const statusProperty = getStatusProperty(step.label);
              const isComplete = setupStatus?.[statusProperty] || false;

              return (
                <div key={step.id} className="border-b pb-4 last:border-b-0">
                  <div className="flex items-start">
                    <div
                      className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
                        isComplete
                          ? "bg-green-100 text-green-600"
                          : "bg-gray-100 text-gray-500"
                      }`}
                    >
                      {isComplete ? (
                        <Icon icon="mdi:check" width="20" height="20" />
                      ) : (
                        <span>{index + 1}</span>
                      )}
                    </div>
                    <div className="flex-grow">
                      <h4 className="text-md font-medium flex items-center">
                        {step.label}
                        {isComplete && (
                          <span className="ml-2 text-green-600 text-sm">
                            Completed
                          </span>
                        )}
                      </h4>
                      <p className="text-sm text-gray-600 mt-1">
                        {isComplete
                          ? "Configuration complete"
                          : "Not configured yet"}
                      </p>
                      <div className="mt-2">
                        <button
                          onClick={() => {
                            setCurrentStep(step.id);
                            setActiveTab("setup");
                          }}
                          className="text-[#2A3A6D] text-sm font-medium hover:underline"
                        >
                          {isComplete ? "View/Edit" : "Configure Now"}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          <div className="mt-8 p-4 bg-blue-50 rounded-lg border border-blue-100">
            <h3 className="text-lg font-medium text-blue-800 mb-2">
              Important Notes
            </h3>
            <ul className="list-disc pl-5 space-y-1 text-blue-700">
              <li>
                The system automatically creates default stores (Main Store and
                Laundry) when a hotel is created.
              </li>
              <li>
                Create components in the order listed above as they have
                dependencies (e.g., rooms depend on room types, bed types, and
                floor plans).
              </li>
              <li>
                For file uploads (like room images), use the file upload option
                in the respective forms.
              </li>
            </ul>
          </div>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="mb-8">
            <Stepper
              steps={steps}
              currentStep={currentStep}
              onStepClick={handleStepNavigation}
            />
          </div>

          <div className="mt-6">{renderSetupComponent()}</div>

          <div className="mt-8 flex justify-between">
            <button
              onClick={() => setCurrentStep(Math.max(1, currentStep - 1))}
              disabled={currentStep === 1}
              className={`px-4 py-2 rounded ${
                currentStep === 1
                  ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                  : "bg-gray-200 text-gray-700 hover:bg-gray-300"
              }`}
            >
              Previous Step
            </button>
            <div className="flex space-x-2">
              {currentStep === 9 ? (
                <button
                  onClick={() => handleSetupComplete()}
                  className="px-4 py-2 rounded bg-green-600 text-black hover:bg-green-700"
                  disabled={isRefreshing}
                >
                  {isRefreshing ? "Completing..." : "Complete Setup"}
                </button>
              ) : (
                <>
                  <button
                    onClick={() => setCurrentStep(Math.min(9, currentStep + 1))}
                    className="px-4 py-2 rounded bg-gray-200 text-gray-700 hover:bg-gray-300"
                  >
                    Skip Step
                  </button>
                  <button
                    onClick={() => handleStepComplete(currentStep)}
                    className="px-4 py-2 rounded bg-[#2A3A6D] text-white hover:bg-[#1E2A4D]"
                    disabled={isRefreshing}
                  >
                    {isRefreshing ? "Saving..." : "Complete & Continue"}
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default HotelSetupPage;
