import React, { useState } from 'react';
import { Form, FormikProvider, useFormik } from 'formik';
import * as Yup from 'yup';
import { useGetAllRoomTypes, useCreateRoomType } from '../../../../server-action/API/HotelConfiguration/room';
import { ActionButton } from '../../../../components/ActionButton';
import { FormField } from '../../../BookingManagement/components/ReservationCustomForm';
import { Card, CardContent } from '../../../../components/Card';
import { TableAction } from '../../../../layouts/Table/TableAction';
import MasterTable from '../../../../layouts/Table/MasterTable';
import { PopupModal } from '../../../../components';

interface RoomTypeSetupProps {
  onComplete: () => void;
}

const RoomTypeSetup: React.FC<RoomTypeSetupProps> = ({ onComplete }) => {
  const [showForm, setShowForm] = useState(false);
  const { data: roomTypes, isLoading } = useGetAllRoomTypes();
  const { mutate: createRoomType, isPending } = useCreateRoomType();

  const validationSchema = Yup.object({
    name: Yup.string().required('Room type name is required'),
    description: Yup.string().required('Description is required'),
    basePrice: Yup.number().required('Base price is required').min(0, 'Price cannot be negative'),
    standardCapacity: Yup.number().required('Standard capacity is required').min(1, 'Capacity must be at least 1'),
    maximumCapacity: Yup.number().required('Maximum capacity is required').min(1, 'Capacity must be at least 1'),
  });

  const formik = useFormik({
    initialValues: {
      name: '',
      description: '',
      basePrice: 100,
      standardCapacity: 2,
      maximumCapacity: 3,
    },
    validationSchema,
    onSubmit: async (values, { resetForm }) => {
      const roomTypeData = {
        name: values.name,
        description: values.description,
        basePrice: values.basePrice,
        capacity: {
          standard: values.standardCapacity,
          maximum: values.maximumCapacity
        }
      };
      
      await createRoomType(roomTypeData);
      resetForm();
      setShowForm(false);
    },
  });

  const tableData = {
    columns: [
      { title: 'S.N.', key: 'serialNo' },
      { title: 'Room Type', key: 'name' },
      { title: 'Base Price', key: 'basePrice' },
      { title: 'Capacity', key: 'capacity' },
      { title: 'Actions', key: 'action' },
    ],
    rows: roomTypes?.map((roomType, index) => ({
      key: roomType._id,
      serialNo: index + 1,
      name: roomType.name,
      basePrice: `$${roomType.basePrice}`,
      capacity: `${roomType.capacity?.standard || 0} (max: ${roomType.capacity?.maximum || 0})`,
      action: <TableAction onEdit={() => {}} onDelete={() => {}} />,
    })) || [],
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Room Types Configuration</h2>
        <button
          onClick={() => setShowForm(true)}
          className="bg-[#2A3A6D] text-white px-4 py-2 rounded-md flex items-center"
        >
          <span className="mr-2">+</span> Add Room Type
        </button>
      </div>

      <Card>
        <CardContent>
          <p className="text-gray-600 mb-4">
            Define the types of rooms available in your hotel. Each room type should have a name, description,
            base price, and capacity information.
          </p>

          <div className="mb-4">
            <MasterTable
              columns={tableData.columns}
              rows={tableData.rows}
              loading={isLoading}
              sortBy="name"
              sortOrder="asc"
            />
          </div>

          {roomTypes && roomTypes.length > 0 && (
            <div className="flex justify-end mt-4">
              <button
                onClick={onComplete}
                className="bg-green-600 text-white px-4 py-2 rounded-md"
              >
                Continue to Next Step
              </button>
            </div>
          )}
        </CardContent>
      </Card>

      {showForm && (
        <PopupModal onClose={() => setShowForm(false)} classname="w-full max-w-md">
          <div className="p-4">
            <h3 className="text-lg font-semibold mb-4">Add Room Type</h3>
            <FormikProvider value={formik}>
              <Form onSubmit={formik.handleSubmit}>
                <div className="space-y-4">
                  <FormField
                    name="name"
                    label="Room Type Name"
                    type="text"
                    placeholder="e.g., Deluxe Room, Suite"
                    formik={formik}
                  />
                  <FormField
                    name="description"
                    label="Description"
                    type="textarea"
                    placeholder="Describe the room type"
                    formik={formik}
                  />
                  <FormField
                    name="basePrice"
                    label="Base Price"
                    type="number"
                    placeholder="Base price per night"
                    formik={formik}
                  />
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      name="standardCapacity"
                      label="Standard Capacity"
                      type="number"
                      placeholder="Standard occupancy"
                      formik={formik}
                    />
                    <FormField
                      name="maximumCapacity"
                      label="Maximum Capacity"
                      type="number"
                      placeholder="Maximum occupancy"
                      formik={formik}
                    />
                  </div>
                </div>
                <div className="mt-6">
                  <ActionButton
                    onCancel={() => setShowForm(false)}
                    onSubmit={formik.handleSubmit}
                    loading={isPending}
                    submitText="Add Room Type"
                  />
                </div>
              </Form>
            </FormikProvider>
          </div>
        </PopupModal>
      )}
    </div>
  );
};

export default RoomTypeSetup;
