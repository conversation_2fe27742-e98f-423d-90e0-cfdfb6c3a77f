import React, { useState } from 'react';
import { Form, FormikProvider, useFormik } from 'formik';
import * as Yup from 'yup';
import { useGetAmenities, useCreateAmenities } from '../../../../server-action/API/HotelConfiguration/amenities';
import { ActionButton } from '../../../../components/ActionButton';
import { FormField } from '../../../BookingManagement/components/ReservationCustomForm';
import { Card, CardContent } from '../../../../components/Card';
import { TableAction } from '../../../../layouts/Table/TableAction';
import MasterTable from '../../../../layouts/Table/MasterTable';
import { PopupModal } from '../../../../components';

interface AmenitySetupProps {
  onComplete: () => void;
}

const AmenitySetup: React.FC<AmenitySetupProps> = ({ onComplete }) => {
  const [showForm, setShowForm] = useState(false);
  const { data: amenitiesData, isLoading } = useGetAmenities();
  const { mutate: createAmenity, isPending } = useCreateAmenities();

  // Sample amenity categories for the dropdown
  const amenityCategories = [
    { label: 'Bathroom', value: '1' },
    { label: 'Entertainment', value: '2' },
    { label: 'Kitchen', value: '3' },
    { label: 'Comfort', value: '4' },
  ];

  const validationSchema = Yup.object({
    name: Yup.string().required('Amenity name is required'),
    category: Yup.string().required('Category is required'),
  });

  const formik = useFormik({
    initialValues: {
      name: '',
      category: '',
    },
    validationSchema,
    onSubmit: async (values, { resetForm }) => {
      await createAmenity(values);
      resetForm();
      setShowForm(false);
    },
  });

  // Sample amenities data for demonstration
  const sampleAmenities = [
    { _id: '1', name: 'Hair Dryer', category: { name: 'Bathroom' } },
    { _id: '2', name: 'Smart TV', category: { name: 'Entertainment' } },
    { _id: '3', name: 'Mini Fridge', category: { name: 'Kitchen' } },
    { _id: '4', name: 'Air Conditioning', category: { name: 'Comfort' } },
    { _id: '5', name: 'Shower', category: { name: 'Bathroom' } },
    { _id: '6', name: 'WiFi', category: { name: 'Entertainment' } },
  ];

  const tableData = {
    columns: [
      { title: 'S.N.', key: 'serialNo' },
      { title: 'Amenity Name', key: 'name' },
      { title: 'Category', key: 'category' },
      { title: 'Actions', key: 'action' },
    ],
    rows: sampleAmenities.map((amenity, index) => ({
      key: amenity._id,
      serialNo: index + 1,
      name: amenity.name,
      category: amenity.category.name,
      action: <TableAction onEdit={() => {}} onDelete={() => {}} />,
    })),
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Amenities Configuration</h2>
        <button
          onClick={() => setShowForm(true)}
          className="bg-[#2A3A6D] text-white px-4 py-2 rounded-md flex items-center"
        >
          <span className="mr-2">+</span> Add Amenity
        </button>
      </div>

      <Card>
        <CardContent>
          <p className="text-gray-600 mb-4">
            Define the amenities available in your hotel rooms. Each amenity should belong to a category
            that you've already defined in the previous step.
          </p>

          <div className="mb-4">
            <MasterTable
              columns={tableData.columns}
              rows={tableData.rows}
              loading={isLoading}
              sortBy="name"
              sortOrder="asc"
            />
          </div>

          <div className="flex justify-end mt-4">
            <button
              onClick={onComplete}
              className="bg-green-600 text-white px-4 py-2 rounded-md"
            >
              Continue to Next Step
            </button>
          </div>
        </CardContent>
      </Card>

      {showForm && (
        <PopupModal onClose={() => setShowForm(false)} classname="w-full max-w-md">
          <div className="p-4">
            <h3 className="text-lg font-semibold mb-4">Add Amenity</h3>
            <FormikProvider value={formik}>
              <Form onSubmit={formik.handleSubmit}>
                <div className="space-y-4">
                  <FormField
                    name="name"
                    label="Amenity Name"
                    type="text"
                    placeholder="e.g., Hair Dryer, Smart TV"
                    formik={formik}
                  />
                  <FormField
                    name="category"
                    label="Category"
                    type="dropdown"
                    placeholder="Select a category"
                    options={amenityCategories}
                    formik={formik}
                  />
                </div>
                <div className="mt-6">
                  <ActionButton
                    onCancel={() => setShowForm(false)}
                    onSubmit={formik.handleSubmit}
                    loading={isPending}
                    submitText="Add Amenity"
                  />
                </div>
              </Form>
            </FormikProvider>
          </div>
        </PopupModal>
      )}
    </div>
  );
};

export default AmenitySetup;
