import React, { useState } from 'react';
import { Form, FormikProvider, useFormik } from 'formik';
import * as Yup from 'yup';
import { useGetAllPackages, useCreatePackage } from '../../../../server-action/API/HotelConfiguration/roompackage';
import { useGetAllRoomTypes } from '../../../../server-action/API/HotelConfiguration/room';
import { useGetAllService } from '../../../../server-action/API/HotelConfiguration/services';
import { useGetAmenities } from '../../../../server-action/API/HotelConfiguration/amenities';
import { ActionButton } from '../../../../components/ActionButton';
import { FormField } from '../../../BookingManagement/components/ReservationCustomForm';
import { Card, CardContent } from '../../../../components/Card';
import { TableAction } from '../../../../layouts/Table/TableAction';
import MasterTable from '../../../../layouts/Table/MasterTable';
import { PopupModal } from '../../../../components';
/*eslint-disable*/
interface PackageSetupProps {
  onComplete: () => void;
}

const PackageSetup: React.FC<PackageSetupProps> = ({ onComplete }) => {
  const [showForm, setShowForm] = useState(false);
  const { isLoading } = useGetAllPackages();
  const { data: roomTypes } = useGetAllRoomTypes();
  const { data: services } = useGetAllService();
  const { data: amenities } = useGetAmenities();
  const { mutate: createPackage, isPending } = useCreatePackage();

  const validationSchema = Yup.object({
    name: Yup.string().required('Package name is required'),
    description: Yup.string(),
    price: Yup.number().required('Price is required').min(0, 'Price cannot be negative'),
    priceType: Yup.string().required('Price type is required'),
    durationNights: Yup.number().required('Duration is required').min(1, 'Duration must be at least 1 night'),
    roomTypes: Yup.array().min(1, 'At least one room type is required'),
    services: Yup.array(),
    amenities: Yup.array(),
    maxGuests: Yup.number().min(1, 'Maximum guests must be at least 1'),
    minGuests: Yup.number().min(1, 'Minimum guests must be at least 1'),
  });

  const formik = useFormik({
    initialValues: {
      name: '',
      description: '',
      price: 0,
      priceType: 'total',
      durationNights: 1,
      roomTypes: [],
      services: [],
      amenities: [],
      maxGuests: 2,
      minGuests: 1,
      isActive: true,
    },
    validationSchema,
    onSubmit: async (values, { resetForm }) => {
      const packageData = {
        name: values.name,
        description: values.description,
        price: values.price,
        priceType: values.priceType,
        durationNights: values.durationNights,
        inclusions: {
          roomTypes: values.roomTypes,
          services: values.services,
          amenities: values.amenities,
        },
        maxGuests: values.maxGuests,
        minGuests: values.minGuests,
        isActive: values.isActive,
      };

      await createPackage(packageData);
      resetForm();
      setShowForm(false);
    },
  });

  // Sample packages data for demonstration
  const samplePackages = [
    {
      _id: '1',
      name: 'Weekend Getaway',
      description: 'Perfect weekend package',
      price: 250,
      priceType: 'total',
      durationNights: 2,
      isActive: true
    },
    {
      _id: '2',
      name: 'Honeymoon Package',
      description: 'Romantic package for couples',
      price: 500,
      priceType: 'total',
      durationNights: 3,
      isActive: true
    },
    {
      _id: '3',
      name: 'Business Trip',
      description: 'Package for business travelers',
      price: 150,
      priceType: 'perNight',
      durationNights: 1,
      isActive: true
    },
  ];

  const tableData = {
    columns: [
      { title: 'S.N.', key: 'serialNo' },
      { title: 'Package Name', key: 'name' },
      { title: 'Price', key: 'price' },
      { title: 'Duration', key: 'duration' },
      { title: 'Status', key: 'status' },
      { title: 'Actions', key: 'action' },
    ],
    rows: samplePackages.map((pkg, index) => ({
      key: pkg._id,
      serialNo: index + 1,
      name: pkg.name,
      price: `$${pkg.price} (${pkg.priceType === 'total' ? 'Total' : 'Per Night'})`,
      duration: `${pkg.durationNights} night${pkg.durationNights > 1 ? 's' : ''}`,
      status: pkg.isActive ? 'Active' : 'Inactive',
      action: <TableAction onEdit={() => {}} onDelete={() => {}} />,
    })),
  };

  // Sample options for dropdowns
  const roomTypeOptions = roomTypes?.map(type => ({ label: type.name, value: type._id })) || [
    { label: 'Deluxe Room', value: '1' },
    { label: 'Suite', value: '2' },
  ];

  const serviceOptions = services?.map(service => ({ label: service.name, value: service._id })) || [
    { label: 'Room Cleaning', value: '1' },
    { label: 'Laundry', value: '2' },
    { label: 'Airport Pickup', value: '3' },
  ];

  const amenityOptions = amenities?.data?.map((amenity: { name: string; _id: string }) => ({ label: amenity.name, value: amenity._id })) || [
    { label: 'Hair Dryer', value: '1' },
    { label: 'Smart TV', value: '2' },
    { label: 'Mini Fridge', value: '3' },
  ];

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Packages Configuration</h2>
        <button
          onClick={() => setShowForm(true)}
          className="bg-[#2A3A6D] text-white px-4 py-2 rounded-md flex items-center"
        >
          <span className="mr-2">+</span> Add Package
        </button>
      </div>

      <Card>
        <CardContent>
          <p className="text-gray-600 mb-4">
            Create packages that combine rooms, services, and amenities for special offers. Packages can be
            priced per night or as a total for the entire stay.
          </p>

          <div className="mb-4">
            <MasterTable
              columns={tableData.columns}
              rows={tableData.rows}
              loading={isLoading}
              sortBy="name"
              sortOrder="asc"
            />
          </div>

          <div className="flex justify-end mt-4">
            <button
              onClick={onComplete}
              className="bg-green-600 text-white px-4 py-2 rounded-md"
            >
              Continue to Next Step
            </button>
          </div>
        </CardContent>
      </Card>

      {showForm && (
        <PopupModal onClose={() => setShowForm(false)} classname="w-full max-w-4xl">
          <div className="p-4">
            <h3 className="text-lg font-semibold mb-4">Add Package</h3>
            <FormikProvider value={formik}>
              <Form onSubmit={formik.handleSubmit}>
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    name="name"
                    label="Package Name"
                    type="text"
                    placeholder="e.g., Weekend Getaway, Honeymoon Package"
                    formik={formik}
                  />
                  <div className="grid grid-cols-2 gap-2">
                    <FormField
                      name="price"
                      label="Price"
                      type="number"
                      placeholder="Package price"
                      formik={formik}
                    />
                    <FormField
                      name="priceType"
                      label="Price Type"
                      type="dropdown"
                      placeholder="Select price type"
                      options={[
                        { label: 'Total', value: 'total' },
                        { label: 'Per Night', value: 'perNight' },
                      ]}
                      formik={formik}
                    />
                  </div>
                  <FormField
                    name="description"
                    label="Description"
                    type="textarea"
                    placeholder="Describe the package"
                    formik={formik}
                  />
                  <FormField
                    name="durationNights"
                    label="Duration (Nights)"
                    type="number"
                    placeholder="Number of nights"
                    formik={formik}
                  />
                  <div className="grid grid-cols-2 gap-2">
                    <FormField
                      name="minGuests"
                      label="Minimum Guests"
                      type="number"
                      placeholder="Min guests"
                      formik={formik}
                    />
                    <FormField
                      name="maxGuests"
                      label="Maximum Guests"
                      type="number"
                      placeholder="Max guests"
                      formik={formik}
                    />
                  </div>
                  <FormField
                    name="roomTypes"
                    label="Room Types"
                    type="multiselect"
                    placeholder="Select room types"
                    options={roomTypeOptions as any}
                    formik={formik}
                  />
                  <FormField
                    name="services"
                    label="Services"
                    type="multiselect"
                    placeholder="Select services"
                    options={serviceOptions}
                    formik={formik}
                  />
                  <FormField
                    name="amenities"
                    label="Amenities"
                    type="multiselect"
                    placeholder="Select amenities"
                    options={amenityOptions}
                    formik={formik}
                  />
                  <FormField
                    name="isActive"
                    label="Active"
                    type="checkbox"
                    formik={formik}
                  />
                </div>
                <div className="mt-6">
                  <ActionButton
                    onCancel={() => setShowForm(false)}
                    onSubmit={formik.handleSubmit}
                    loading={isPending}
                    submitText="Add Package"
                  />
                </div>
              </Form>
            </FormikProvider>
          </div>
        </PopupModal>
      )}
    </div>
  );
};

export default PackageSetup;
