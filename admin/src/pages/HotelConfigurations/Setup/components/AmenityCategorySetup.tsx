import React, { useState } from 'react';
import { Form, FormikProvider, useFormik } from 'formik';
import * as Yup from 'yup';
import { useGetAmenityCategories, useCreateAmenityCategory } from '../../../../server-action/API/HotelConfiguration/amenityCategory';
import { ActionButton } from '../../../../components/ActionButton';
import { FormField } from '../../../BookingManagement/components/ReservationCustomForm';
import { Card, CardContent } from '../../../../components/Card';
import { TableAction } from '../../../../layouts/Table/TableAction';
import MasterTable from '../../../../layouts/Table/MasterTable';
import { PopupModal } from '../../../../components';

// Mock implementations removed as we now have the actual implementations

interface AmenityCategorySetupProps {
  onComplete: () => void;
}

const AmenityCategorySetup: React.FC<AmenityCategorySetupProps> = ({ onComplete }) => {
  const [showForm, setShowForm] = useState(false);
  const { data: amenityCategories, isLoading } = useGetAmenityCategories();
  const { mutate: createAmenityCategory, isPending } = useCreateAmenityCategory();

  const validationSchema = Yup.object({
    name: Yup.string().required('Category name is required'),
  });

  const formik = useFormik({
    initialValues: {
      name: '',
    },
    validationSchema,
    onSubmit: async (values, { resetForm }) => {
      await createAmenityCategory(values);
      resetForm();
      setShowForm(false);
    },
  });

  const tableData = {
    columns: [
      { title: 'S.N.', key: 'serialNo' },
      { title: 'Category Name', key: 'name' },
      { title: 'Actions', key: 'action' },
    ],
    rows: amenityCategories?.map((category: { _id: string; name: string }, index) => ({
      key: category._id,
      serialNo: index + 1,
      name: category.name,
      action: <TableAction onEdit={() => {}} onDelete={() => {}} />,
    })) || [],
  };

  // For demo purposes, let's add some sample data
  const sampleCategories = [
    { _id: '1', name: 'Bathroom' },
    { _id: '2', name: 'Entertainment' },
    { _id: '3', name: 'Kitchen' },
    { _id: '4', name: 'Comfort' },
  ];

  const sampleTableData = {
    columns: tableData.columns,
    rows: sampleCategories.map((category, index) => ({
      key: category._id,
      serialNo: index + 1,
      name: category.name,
      action: <TableAction onEdit={() => {}} onDelete={() => {}} />,
    })),
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Amenity Categories Configuration</h2>
        <button
          onClick={() => setShowForm(true)}
          className="bg-[#2A3A6D] text-white px-4 py-2 rounded-md flex items-center"
        >
          <span className="mr-2">+</span> Add Amenity Category
        </button>
      </div>

      <Card>
        <CardContent>
          <p className="text-gray-600 mb-4">
            Define categories for your hotel amenities. Categories help organize amenities by their type or location
            (e.g., Bathroom, Entertainment, Kitchen).
          </p>

          <div className="mb-4">
            <MasterTable
              columns={sampleTableData.columns}
              rows={sampleTableData.rows}
              loading={isLoading}
              sortBy="name"
              sortOrder="asc"
            />
          </div>

          <div className="flex justify-end mt-4">
            <button
              onClick={onComplete}
              className="bg-green-600 text-white px-4 py-2 rounded-md"
            >
              Continue to Next Step
            </button>
          </div>
        </CardContent>
      </Card>

      {showForm && (
        <PopupModal onClose={() => setShowForm(false)} classname="w-full max-w-md">
          <div className="p-4">
            <h3 className="text-lg font-semibold mb-4">Add Amenity Category</h3>
            <FormikProvider value={formik}>
              <Form onSubmit={formik.handleSubmit}>
                <div className="space-y-4">
                  <FormField
                    name="name"
                    label="Category Name"
                    type="text"
                    placeholder="e.g., Bathroom, Entertainment"
                    formik={formik}
                  />
                </div>
                <div className="mt-6">
                  <ActionButton
                    onCancel={() => setShowForm(false)}
                    onSubmit={formik.handleSubmit}
                    loading={isPending}
                    submitText="Add Category"
                  />
                </div>
              </Form>
            </FormikProvider>
          </div>
        </PopupModal>
      )}
    </div>
  );
};

export default AmenityCategorySetup;
