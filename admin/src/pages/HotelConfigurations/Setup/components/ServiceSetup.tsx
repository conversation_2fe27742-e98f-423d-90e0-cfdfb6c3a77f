import React, { useState } from 'react';
import { Form, FormikProvider, useFormik } from 'formik';
import * as Yup from 'yup';
import { useGetAllService, useCreateService } from '../../../../server-action/API/HotelConfiguration/services';
import { ActionButton } from '../../../../components/ActionButton';
import { FormField } from '../../../BookingManagement/components/ReservationCustomForm';
import { Card, CardContent } from '../../../../components/Card';
import { TableAction } from '../../../../layouts/Table/TableAction';
import MasterTable from '../../../../layouts/Table/MasterTable';
import { PopupModal } from '../../../../components';

interface ServiceSetupProps {
  onComplete: () => void;
}

const ServiceSetup: React.FC<ServiceSetupProps> = ({ onComplete }) => {
  const [showForm, setShowForm] = useState(false);
  const { data: services, isLoading } = useGetAllService();
  const { mutate: createService, isPending } = useCreateService();

  const validationSchema = Yup.object({
    name: Yup.string().required('Service name is required'),
    price: Yup.number().required('Price is required').min(0, 'Price cannot be negative'),
    description: Yup.string(),
    taxRate: Yup.number().min(0, 'Tax rate cannot be negative').max(100, 'Tax rate cannot exceed 100%'),
  });

  const formik = useFormik({
    initialValues: {
      name: '',
      price: 0,
      description: '',
      taxRate: 0,
      isActive: true,
    },
    validationSchema,
    onSubmit: async (values, { resetForm }) => {
      await createService(values);
      resetForm();
      setShowForm(false);
    },
  });

  // Sample services data for demonstration
  const sampleServices = [
    { _id: '1', name: 'Room Cleaning', price: 10, description: 'Daily room cleaning service', isActive: true },
    { _id: '2', name: 'Laundry', price: 15, description: 'Laundry service per load', isActive: true },
    { _id: '3', name: 'Airport Pickup', price: 50, description: 'One-way airport transfer', isActive: true },
    { _id: '4', name: 'Breakfast', price: 20, description: 'Breakfast buffet', isActive: true },
  ];

  const tableData = {
    columns: [
      { title: 'S.N.', key: 'serialNo' },
      { title: 'Service Name', key: 'name' },
      { title: 'Price', key: 'price' },
      { title: 'Description', key: 'description' },
      { title: 'Status', key: 'status' },
      { title: 'Actions', key: 'action' },
    ],
    rows: sampleServices.map((service, index) => ({
      key: service._id,
      serialNo: index + 1,
      name: service.name,
      price: `$${service.price}`,
      description: service.description,
      status: service.isActive ? 'Active' : 'Inactive',
      action: <TableAction onEdit={() => {}} onDelete={() => {}} />,
    })),
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Services Configuration</h2>
        <button
          onClick={() => setShowForm(true)}
          className="bg-[#2A3A6D] text-white px-4 py-2 rounded-md flex items-center"
        >
          <span className="mr-2">+</span> Add Service
        </button>
      </div>

      <Card>
        <CardContent>
          <p className="text-gray-600 mb-4">
            Define the services offered by your hotel. Services can be booked by guests during their stay
            and will appear on their bill.
          </p>

          <div className="mb-4">
            <MasterTable
              columns={tableData.columns}
              rows={tableData.rows}
              loading={isLoading}
              sortBy="name"
              sortOrder="asc"
            />
          </div>

          <div className="flex justify-end mt-4">
            <button
              onClick={onComplete}
              className="bg-green-600 text-white px-4 py-2 rounded-md"
            >
              Continue to Next Step
            </button>
          </div>
        </CardContent>
      </Card>

      {showForm && (
        <PopupModal onClose={() => setShowForm(false)} classname="w-full max-w-md">
          <div className="p-4">
            <h3 className="text-lg font-semibold mb-4">Add Service</h3>
            <FormikProvider value={formik}>
              <Form onSubmit={formik.handleSubmit}>
                <div className="space-y-4">
                  <FormField
                    name="name"
                    label="Service Name"
                    type="text"
                    placeholder="e.g., Room Cleaning, Laundry"
                    formik={formik}
                  />
                  <FormField
                    name="price"
                    label="Price"
                    type="number"
                    placeholder="Service price"
                    formik={formik}
                  />
                  <FormField
                    name="description"
                    label="Description"
                    type="textarea"
                    placeholder="Describe the service"
                    formik={formik}
                  />
                  <FormField
                    name="taxRate"
                    label="Tax Rate (%)"
                    type="number"
                    placeholder="Tax rate percentage"
                    formik={formik}
                  />
                  <FormField
                    name="isActive"
                    label="Active"
                    type="checkbox"
                    formik={formik}
                  />
                </div>
                <div className="mt-6">
                  <ActionButton
                    onCancel={() => setShowForm(false)}
                    onSubmit={formik.handleSubmit}
                    loading={isPending}
                    submitText="Add Service"
                  />
                </div>
              </Form>
            </FormikProvider>
          </div>
        </PopupModal>
      )}
    </div>
  );
};

export default ServiceSetup;
