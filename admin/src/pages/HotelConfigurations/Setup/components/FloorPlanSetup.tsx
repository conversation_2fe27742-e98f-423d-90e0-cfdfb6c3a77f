import React, { useState } from 'react';
import { Form, FormikProvider, useFormik } from 'formik';
import * as Yup from 'yup';
import { useGetAllFloorPlans, useCreateFloorPlan } from '../../../../server-action/API/HotelConfiguration/floorPlan';
import { ActionButton } from '../../../../components/ActionButton';
import { FormField } from '../../../BookingManagement/components/ReservationCustomForm';
import { Card, CardContent } from '../../../../components/Card';
import { TableAction } from '../../../../layouts/Table/TableAction';
import MasterTable from '../../../../layouts/Table/MasterTable';
import { PopupModal } from '../../../../components';

interface FloorPlanSetupProps {
  onComplete: () => void;
}

const FloorPlanSetup: React.FC<FloorPlanSetupProps> = ({ onComplete }) => {
  const [showForm, setShowForm] = useState(false);
  const { data: floorPlans, isLoading } = useGetAllFloorPlans();
  const { mutate: createFloorPlan, isPending } = useCreateFloorPlan();

  const validationSchema = Yup.object({
    name: Yup.string().required('Floor name is required'),
    floorNumber: Yup.number().required('Floor number is required'),
  });

  const formik = useFormik({
    initialValues: {
      name: '',
      floorNumber: 0,
    },
    validationSchema,
    onSubmit: async (values, { resetForm }) => {
      await createFloorPlan(values);
      resetForm();
      setShowForm(false);
    },
  });

  const tableData = {
    columns: [
      { title: 'S.N.', key: 'serialNo' },
      { title: 'Floor Name', key: 'name' },
      { title: 'Floor Number', key: 'floorNumber' },
      { title: 'Actions', key: 'action' },
    ],
    rows: floorPlans?.map((floor, index) => ({
      key: floor._id,
      serialNo: index + 1,
      name: floor.name,
      floorNumber: floor.floorNumber,
      action: <TableAction onEdit={() => {}} onDelete={() => {}} />,
    })) || [],
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Floor Plans Configuration</h2>
        <button
          onClick={() => setShowForm(true)}
          className="bg-[#2A3A6D] text-white px-4 py-2 rounded-md flex items-center"
        >
          <span className="mr-2">+</span> Add Floor Plan
        </button>
      </div>

      <Card>
        <CardContent>
          <p className="text-gray-600 mb-4">
            Define the floors in your hotel. Each floor should have a name and a floor number.
            Floor numbers can be negative for basement levels (e.g., -1, -2).
          </p>

          <div className="mb-4">
            <MasterTable
              columns={tableData.columns}
              rows={tableData.rows}
              loading={isLoading}
              sortBy="floorNumber"
              sortOrder="asc"
            />
          </div>

          {floorPlans && floorPlans.length > 0 && (
            <div className="flex justify-end mt-4">
              <button
                onClick={onComplete}
                className="bg-green-600 text-white px-4 py-2 rounded-md"
              >
                Continue to Next Step
              </button>
            </div>
          )}
        </CardContent>
      </Card>

      {showForm && (
        <PopupModal onClose={() => setShowForm(false)} classname="w-full max-w-md">
          <div className="p-4">
            <h3 className="text-lg font-semibold mb-4">Add Floor Plan</h3>
            <FormikProvider value={formik}>
              <Form onSubmit={formik.handleSubmit}>
                <div className="space-y-4">
                  <FormField
                    name="name"
                    label="Floor Name"
                    type="text"
                    placeholder="e.g., Ground Floor, First Floor"
                    formik={formik}
                  />
                  <FormField
                    name="floorNumber"
                    label="Floor Number"
                    type="number"
                    placeholder="Floor number (0 for ground floor)"
                    formik={formik}
                  />
                </div>
                <div className="mt-6">
                  <ActionButton
                    onCancel={() => setShowForm(false)}
                    onSubmit={formik.handleSubmit}
                    loading={isPending}
                    submitText="Add Floor Plan"
                  />
                </div>
              </Form>
            </FormikProvider>
          </div>
        </PopupModal>
      )}
    </div>
  );
};

export default FloorPlanSetup;
