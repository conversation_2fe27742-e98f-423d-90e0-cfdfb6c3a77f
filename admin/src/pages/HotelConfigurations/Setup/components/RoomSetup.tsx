import React, { useState } from "react";
import { Form, FormikProvider, useFormik } from "formik";
import * as Yup from "yup";
import {
  useGetAllRooms,
  useCreateRoom,
} from "../../../../server-action/API/HotelConfiguration/room";
import { useGetAllRoomTypes } from "../../../../server-action/API/HotelConfiguration/room";
import { useGetBeds } from "../../../../server-action/API/HotelConfiguration/bed";
import { useGetAllFloorPlans } from "../../../../server-action/API/HotelConfiguration/floorPlan";
import { useGetAmenities } from "../../../../server-action/API/HotelConfiguration/amenities";
import { IRoom } from "../../../../Interface/room.interface";
import { ActionButton } from "../../../../components/ActionButton";
import { FormField } from "../../../BookingManagement/components/ReservationCustomForm";
import { Card, CardContent } from "../../../../components/Card";
import { TableAction } from "../../../../layouts/Table/TableAction";
import MasterTable from "../../../../layouts/Table/MasterTable";
import { PopupModal } from "../../../../components";
/*eslint-disable*/
interface RoomSetupProps {
  onComplete: () => void;
}

const RoomSetup: React.FC<RoomSetupProps> = ({ onComplete }) => {
  const [showForm, setShowForm] = useState(false);
  const { isLoading } = useGetAllRooms();
  const { data: roomTypes } = useGetAllRoomTypes();
  const { data: bedTypes } = useGetBeds();
  const { data: floorPlans } = useGetAllFloorPlans();
  const { data: amenities } = useGetAmenities();
  const { mutate: createRoom, isPending } = useCreateRoom();

  const validationSchema = Yup.object({
    roomNo: Yup.string().required("Room number is required"),
    roomType: Yup.string().required("Room type is required"),
    floorPlan: Yup.string().required("Floor plan is required"),
    basePrice: Yup.number()
      .required("Base price is required")
      .min(0, "Price cannot be negative"),
    weekendPrice: Yup.number()
      .required("Weekend price is required")
      .min(0, "Price cannot be negative"),
    bedType: Yup.string().required("Bed type is required"),
    bedCount: Yup.number()
      .required("Bed count is required")
      .min(1, "At least one bed is required"),
  });

  const formik = useFormik({
    initialValues: {
      roomNo: "",
      roomType: "",
      floorPlan: "",
      basePrice: 100,
      weekendPrice: 120,
      bedType: "",
      bedCount: 1,
      amenities: [],
      isVip: false,
      hasSmoking: false,
      acType: "split",
      viewType: "city",
      balcony: false,
    },
    validationSchema,
    onSubmit: async (values, { resetForm }) => {
      const roomData: IRoom = {
        roomNo: values.roomNo,
        roomType: { _id: values.roomType, name: "" },
        floorPlan: { _id: values.floorPlan },
        roomPrice: {
          base: values.basePrice,
          weekend: values.weekendPrice,
        },
        beds: {
          count: values.bedCount,
          types: [{ _id: values.bedType }],
        },
        amenities: values.amenities,
        features: {
          acType: values.acType as "none" | "central" | "split" | "window",
          viewType: values.viewType,
          balcony: values.balcony,
          smoking: values.hasSmoking,
        },
        isVip: values.isVip,
      };

      await createRoom(roomData);
      resetForm();
      setShowForm(false);
    },
  });

  // Sample room data for demonstration
  const sampleRooms = [
    {
      _id: "1",
      roomNo: "101",
      roomType: { name: "Deluxe Room" },
      status: "available",
      floorPlan: { name: "Ground Floor" },
    },
    {
      _id: "2",
      roomNo: "102",
      roomType: { name: "Suite" },
      status: "available",
      floorPlan: { name: "Ground Floor" },
    },
    {
      _id: "3",
      roomNo: "201",
      roomType: { name: "Deluxe Room" },
      status: "available",
      floorPlan: { name: "First Floor" },
    },
    {
      _id: "4",
      roomNo: "202",
      roomType: { name: "Suite" },
      status: "available",
      floorPlan: { name: "First Floor" },
    },
  ];

  const tableData = {
    columns: [
      { title: "Room No.", key: "roomNo" },
      { title: "Room Type", key: "roomType" },
      { title: "Floor", key: "floor" },
      { title: "Status", key: "status" },
      { title: "Actions", key: "action" },
    ],
    rows: sampleRooms.map((room) => ({
      key: room._id,
      roomNo: room.roomNo,
      roomType: room.roomType.name,
      floor: room.floorPlan.name,
      status: room.status,
      action: <TableAction onEdit={() => {}} onDelete={() => {}} />,
    })),
  };

  // Sample options for dropdowns
  const roomTypeOptions = roomTypes?.map((type) => ({
    label: type.name,
    value: type._id,
  })) || [
    { label: "Deluxe Room", value: "1" },
    { label: "Suite", value: "2" },
  ];

  const floorPlanOptions = floorPlans?.map((floor) => ({
    label: floor.name,
    value: floor._id,
  })) || [
    { label: "Ground Floor", value: "1" },
    { label: "First Floor", value: "2" },
  ];

  const bedTypeOptions = bedTypes?.map((bed) => ({
    label: bed.name,
    value: bed._id,
  })) || [
    { label: "King Size", value: "1" },
    { label: "Queen Size", value: "2" },
    { label: "Twin", value: "3" },
  ];

  const amenityOptions = amenities?.data?.map(
    (amenity: { name: string; _id: string }) => ({
      label: amenity.name,
      value: amenity._id,
    })
  ) || [
    { label: "Hair Dryer", value: "1" },
    { label: "Smart TV", value: "2" },
    { label: "Mini Fridge", value: "3" },
    { label: "Air Conditioning", value: "4" },
  ];

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Rooms Configuration</h2>
        <button
          onClick={() => setShowForm(true)}
          className="bg-[#2A3A6D] text-white px-4 py-2 rounded-md flex items-center"
        >
          <span className="mr-2">+</span> Add Room
        </button>
      </div>

      <Card>
        <CardContent>
          <p className="text-gray-600 mb-4">
            Configure individual rooms in your hotel. Each room should have a
            room number, room type, floor plan, pricing, and other details.
          </p>

          <div className="mb-4">
            <MasterTable
              columns={tableData.columns}
              rows={tableData.rows}
              loading={isLoading}
              sortBy="roomNo"
              sortOrder="asc"
            />
          </div>

          <div className="flex justify-end mt-4">
            <button
              onClick={onComplete}
              className="bg-green-600 text-white px-4 py-2 rounded-md"
            >
              Continue to Next Step
            </button>
          </div>
        </CardContent>
      </Card>

      {showForm && (
        <PopupModal
          onClose={() => setShowForm(false)}
          classname="w-full max-w-4xl"
        >
          <div className="p-4">
            <h3 className="text-lg font-semibold mb-4">Add Room</h3>
            <FormikProvider value={formik}>
              <Form onSubmit={formik.handleSubmit}>
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    name="roomNo"
                    label="Room Number"
                    type="text"
                    placeholder="e.g., 101, 102"
                    formik={formik}
                  />
                  <FormField
                    name="roomType"
                    label="Room Type"
                    type="dropdown"
                    placeholder="Select room type"
                    options={roomTypeOptions as any}
                    formik={formik}
                  />
                  <FormField
                    name="floorPlan"
                    label="Floor"
                    type="dropdown"
                    placeholder="Select floor"
                    options={floorPlanOptions as any}
                    formik={formik}
                  />
                  <div className="grid grid-cols-2 gap-2">
                    <FormField
                      name="basePrice"
                      label="Base Price"
                      type="number"
                      placeholder="Regular price"
                      formik={formik}
                    />
                    <FormField
                      name="weekendPrice"
                      label="Weekend Price"
                      type="number"
                      placeholder="Weekend price"
                      formik={formik}
                    />
                  </div>
                  <FormField
                    name="bedType"
                    label="Bed Type"
                    type="dropdown"
                    placeholder="Select bed type"
                    options={bedTypeOptions as any}
                    formik={formik}
                  />
                  <FormField
                    name="bedCount"
                    label="Number of Beds"
                    type="number"
                    placeholder="Number of beds"
                    formik={formik}
                  />
                  <FormField
                    name="amenities"
                    label="Amenities"
                    type="multiselect"
                    placeholder="Select amenities"
                    options={amenityOptions}
                    formik={formik}
                  />
                  <div className="grid grid-cols-2 gap-2">
                    <FormField
                      name="acType"
                      label="AC Type"
                      type="dropdown"
                      placeholder="Select AC type"
                      options={[
                        { label: "Split", value: "split" },
                        { label: "Central", value: "central" },
                        { label: "None", value: "none" },
                      ]}
                      formik={formik}
                    />
                    <FormField
                      name="viewType"
                      label="View Type"
                      type="dropdown"
                      placeholder="Select view type"
                      options={[
                        { label: "City View", value: "city" },
                        { label: "Ocean View", value: "ocean" },
                        { label: "Garden View", value: "garden" },
                        { label: "Mountain View", value: "mountain" },
                      ]}
                      formik={formik}
                    />
                  </div>
                  <div className="flex space-x-4">
                    <FormField
                      name="isVip"
                      label="VIP Room"
                      type="checkbox"
                      formik={formik}
                    />
                    <FormField
                      name="balcony"
                      label="Has Balcony"
                      type="checkbox"
                      formik={formik}
                    />
                    <FormField
                      name="hasSmoking"
                      label="Smoking Allowed"
                      type="checkbox"
                      formik={formik}
                    />
                  </div>
                </div>
                <div className="mt-6">
                  <ActionButton
                    onCancel={() => setShowForm(false)}
                    onSubmit={formik.handleSubmit}
                    loading={isPending}
                    submitText="Add Room"
                  />
                </div>
              </Form>
            </FormikProvider>
          </div>
        </PopupModal>
      )}
    </div>
  );
};

export default RoomSetup;
