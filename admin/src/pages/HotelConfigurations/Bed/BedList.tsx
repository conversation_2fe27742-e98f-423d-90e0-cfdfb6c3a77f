import { useState } from "react";
import Header from "../../../components/Header";
import MasterTable from "../../../layouts/Table/MasterTable";
import { TableAction } from "../../../layouts/Table/TableAction";
import { Icon } from "@iconify/react/dist/iconify.js";
import { PopupModal } from "../../../components";
import BedForm from "./components/BedForm";
import {
  useDeleteBed,
  useGetBeds,
} from "../../../server-action/API/HotelConfiguration/bed";

const BedList = () => {
  const [showPopup, setShowPopup] = useState(false);
  const [editData, setEditData] = useState(null);

  const { data: allBed } = useGetBeds();

  const { mutateAsync: deleteBed } = useDeleteBed();
  const togglePopup = () => {
    setShowPopup((prev) => !prev);
  };
  const tableData = {
    columns: [
      { title: "SN", key: "sn" },
      { title: "Bed Name", key: "bedName" },
      { title: "Action", key: "action" },
    ],
    rows: (allBed || [])?.map((item: any, index: number) => ({
      sn: index + 1,
      bedName: item?.name,
      action: (
        <TableAction
          onEdit={() => {
            setEditData(item as any);
            setShowPopup(true);
          }}
          onDelete={() => {
            deleteBed(item?._id);
          }}
        />
      ),
    })),
  };

  return (
    <div>
        <Header onAddClick={() => {
              setEditData(null);
              togglePopup();
        }}/>

      {showPopup && (
        <PopupModal onClose={togglePopup}>
          <BedForm onClose={togglePopup} editData={editData} />
        </PopupModal>
      )}

      <div className="bg-white ">
        <MasterTable
          columns={tableData?.columns}
          rows={(tableData.rows as any) || []}
          loading={false}
          pagination={{
            currentPage: 1,
            totalPage: 200,
            limit: 5,
            onClick: () => {},
          }}
        />
      </div>
    </div>
  );
};

export default BedList;
