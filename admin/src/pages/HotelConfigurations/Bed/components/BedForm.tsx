import { Form, FormikProvider, useFormik, validateYupSchema } from "formik";
import React from "react";
import HeadingPopup from "../../../GuestManagement/components/HeadingPopup";
import { GlobalForm } from "../../../../components/GlobalForm/GlobalFormComponent";
import { ActionButton } from "../../../../components/ActionButton";
import {
  useCreateBed,
  useUpdateBed,
} from "../../../../server-action/API/HotelConfiguration/bed";
import { IBed } from "../../../../Interface/bed.interface";
import { BedFormData } from "./BedFormData";
import * as yup from "yup";
interface BEDFORMPROPS {
  onClose: () => void;
  editData: IBed | null;
}
const BedValidationSchema = yup.object().shape({
  name: yup.string().required("Bed Name is required").min(3),
});
const BedForm: React.FC<BEDFORMPROPS> = ({
  onClose,
  editData,
}: BEDFORMPROPS) => {
  const { mutateAsync: createBed } = useCreateBed();
  const { mutateAsync: updateBed } = useUpdateBed();
  const formik = useFormik({
    validationSchema: BedValidationSchema,
    initialValues: {
      name: editData?.name,
    },
    enableReinitialize: true,
    onSubmit: async (values) => {
      try {
        if (editData) {
          await updateBed({
            bedData: values as IBed,
            _id: editData?._id as string,
          });
        } else {
          createBed(values as any);
        }
        onClose();
      } catch (error) {
        console.error("Sorry Error Occured", error);
      }
    },
  });
  const { handleSubmit, getFieldProps } = formik;
  return (
    <HeadingPopup
      onClose={onClose}
      className="w-full max-w-screen-sm"
      heading={`${!editData ? "Add" : "Update"} Bed`}
    >
      <FormikProvider value={formik}>
        <Form onSubmit={handleSubmit}>
          <div className="grid grid-cols-2 gap-2">
            <GlobalForm
              formDatails={BedFormData}
              getFieldProps={getFieldProps}
            />
          </div>
          <ActionButton onCancel={onClose} onSubmit={handleSubmit} />
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default BedForm;
