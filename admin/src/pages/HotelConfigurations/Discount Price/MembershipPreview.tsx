import React, { useState } from "react";
import { discountTypes } from "../../../server-action/API/Discount";

interface MembershipPreviewProps {
  memberships: discountTypes[];
}

const MembershipPreview: React.FC<MembershipPreviewProps> = ({
  memberships,
}) => {
  const [roomPrice, setRoomPrice] = useState<number>(100);
  const [totalSpent, setTotalSpent] = useState<number>(0);

  // Sort memberships by minSpend (ascending), falling back to range for backward compatibility
  const sortedMemberships = [...memberships].sort((a, b) => {
    const aValue = a.minSpend !== undefined ? a.minSpend : a.range;
    const bValue = b.minSpend !== undefined ? b.minSpend : b.range;
    return aValue - bValue;
  });

  // Find applicable membership tier based on total spent
  const applicableTier = sortedMemberships
    .filter((tier) => {
      const threshold =
        tier.minSpend !== undefined ? tier.minSpend : tier.range;
      return totalSpent >= threshold;
    })
    .reduce(
      (highest, current) => {
        const highestValue =
          highest.minSpend !== undefined ? highest.minSpend : highest.range;
        const currentValue =
          current.minSpend !== undefined ? current.minSpend : current.range;
        return currentValue > highestValue ? current : highest;
      },
      { name: "None", discount: 0, range: 0, minSpend: 0 }
    );

  // Calculate discounted price
  const discountAmount = (roomPrice * applicableTier.discount) / 100;
  const finalPrice = roomPrice - discountAmount;

  return (
    <div className="bg-white rounded-lg shadow-sm p-4">
      <h2 className="text-lg font-semibold mb-4">
        Membership Discount Preview
      </h2>

      <div className="grid md:grid-cols-2 gap-6">
        <div>
          <h3 className="font-medium mb-3">Simulate a Booking</h3>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Room Base Price (NPR)
              </label>
              <input
                type="number"
                min="1"
                value={roomPrice}
                onChange={(e) => setRoomPrice(Number(e.target.value))}
                className="w-full p-2 border rounded-md"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Guest's Total Spent (NPR)
              </label>
              <input
                type="number"
                min="0"
                value={totalSpent}
                onChange={(e) => setTotalSpent(Number(e.target.value))}
                className="w-full p-2 border rounded-md"
              />
            </div>
          </div>

          <div className="mt-6 p-4 bg-gray-50 rounded-md">
            <h4 className="font-medium mb-2">Calculation Result</h4>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Applicable Tier:</span>
                <span className="font-medium">{applicableTier.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Discount Rate:</span>
                <span className="font-medium">{applicableTier.discount}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Room Base Price:</span>
                <span className="font-medium">Rs. {roomPrice.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Discount Amount:</span>
                <span className="font-medium text-green-600">
                  Rs. {discountAmount.toFixed(2)} NPR
                </span>
              </div>
              <div className="flex justify-between pt-2 border-t">
                <span className="font-medium">Final Price:</span>
                <span className="font-bold text-indigo-600">
                  Rs. {finalPrice.toFixed(2)} NPR
                </span>
              </div>
            </div>
          </div>
        </div>

        <div>
          <h3 className="font-medium mb-3">Membership Tiers Overview</h3>

          <div className="overflow-hidden rounded-md border">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tier
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Min. Spend
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Total Spend
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Discount
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {sortedMemberships.length > 0 ? (
                  sortedMemberships.map((tier, index) => (
                    <tr
                      key={index}
                      className={
                        tier.name === applicableTier.name ? "bg-green-50" : ""
                      }
                    >
                      <td className="px-4 py-3 whitespace-nowrap">
                        <div className="flex items-center">
                          {tier.name === applicableTier.name && (
                            <span className="mr-2 flex-shrink-0 h-2 w-2 rounded-full bg-green-500"></span>
                          )}
                          <span className="font-medium">{tier.name}</span>
                        </div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        Rs. {tier.minSpend !== undefined ? tier.minSpend : 0}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        Rs. {tier.range}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        {tier.discount}%
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td
                      colSpan={4}
                      className="px-4 py-3 text-center text-gray-500"
                    >
                      No membership tiers defined yet
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <h4 className="text-sm font-medium text-yellow-800 mb-1">
              How It Works
            </h4>
            <p className="text-sm text-yellow-700">
              1. Guests must meet the minimum spend requirement to qualify for a
              tier
              <br />
              2. When total spending reaches the total spend threshold, they're
              automatically upgraded
              <br />
              3. Their membership discount is automatically applied to all
              future bookings
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MembershipPreview;
