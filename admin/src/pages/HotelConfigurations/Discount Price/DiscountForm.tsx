import { Icon } from '@iconify/react/dist/iconify.js';
import { Form, FormikProvider, useFormik } from 'formik';
import { GlobalForm } from '../../../components/GlobalForm/GlobalFormComponent';
import {
  discountTypes,
  useCreateDiscount,
  useUpdateDiscount,
  useGetAllDiscount,
} from '../../../server-action/API/Discount';
import * as Yup from 'yup';
import { useEffect, useState } from 'react';

// Membership tier enum
enum MembershipTier {
  Silver = "Silver",
  Gold = "Gold",
  Platinum = "Platinum",
  Diamond = "Diamond",
  Ace = "Ace"
}

const DiscountForm = ({
  onClose,
  editData,
}: {
  onClose: () => void;
  editData?: discountTypes;
}) => {
  const { mutateAsync: createDiscount } = useCreateDiscount();
  const { mutateAsync: updateDiscount } = useUpdateDiscount();
  const { data: existingMemberships } = useGetAllDiscount();

  // State to track available membership tiers
  const [availableTiers, setAvailableTiers] = useState<{label: string; value: string}[]>([]);

  // Validation schema
  const validationSchema = Yup.object({
    name: Yup.string()
      .required('Tier name is required')
      .matches(/^[a-zA-Z\s]+$/, 'Only letters and spaces are allowed')
      .min(3, 'Name must be at least 3 characters'),
    discount: Yup.number()
      .required('Discount percentage is required')
      .min(0, 'Discount must be at least 0%')
      .max(100, 'Discount cannot exceed 100%'),
    minSpend: Yup.number()
      .required('Minimum spending threshold is required')
      .min(0, 'Threshold must be at least 0'),
    range: Yup.number()
      .required('Total spending threshold is required')
      .min(0, 'Threshold must be at least 0')
  });

  const form = useFormik({
    initialValues: {
      name: editData?.name ?? '',
      discount: editData?.discount ?? 0,
      minSpend: editData?.minSpend ?? 0,
      range: editData?.range ?? 0,
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        if (editData) {
          await updateDiscount({
            id: editData?._id,
            body: values,
          });
          onClose();
        } else {
          await createDiscount(values);
          onClose();
        }
      } catch (error) {
        console.error('Error saving membership tier:', error);
      }
    },
  });

  const { handleSubmit, getFieldProps } = form;

  // Filter out already existing membership tiers
  useEffect(() => {
    if (existingMemberships) {
      const existingNames = existingMemberships.map((tier: discountTypes) => tier.name);

      // If in edit mode, include the current tier in available options
      if (editData?.name) {
        existingNames.splice(existingNames.indexOf(editData.name), 1);
      }

      // Create options array from enum, filtering out existing tiers
      const tierOptions = Object.values(MembershipTier)
        .filter(tier => !existingNames.includes(tier))
        .map(tier => ({
          label: tier,
          value: tier
        }));

      setAvailableTiers(tierOptions);
    }
  }, [existingMemberships, editData]);

  const formField = [
    {
      field: 'name',
      label: 'Tier Name',
      type: 'select',
      placeholder: 'Select a membership tier',
      description: 'Name of the membership tier',
      options: availableTiers,
    },
    {
      field: 'discount',
      label: 'Discount Percentage',
      type: 'number',
      placeholder: 'e.g. 5, 10, 15',
      description: 'Percentage discount applied to room bookings',
    },
    {
      field: 'minSpend',
      label: 'Minimum Spend',
      type: 'number',
      placeholder: 'e.g. 500, 1000, 2000',
      description: 'Minimum spending required to qualify for this tier ($)',
    },
    {
      field: 'range',
      label: 'Total Spend Threshold',
      type: 'number',
      placeholder: 'e.g. 1000, 5000, 10000',
      description: 'Total user spending for automatic tier upgrade ($)',
    },
  ];

  return (
    <div>
      <FormikProvider value={form}>
        <div className="relative flex bg-[#F1F6FD] p-2 items-center justify-center rounded-t-md">
          <h1 className="text-center font-semibold">Membership Tier</h1>
          <button
            className="absolute flex right-2 justify-self-end"
            onClick={onClose}
          >
            <Icon icon="mdi:cross-circle" height={20} />
          </button>
        </div>
        <Form
          className="p-5 flex flex-col gap-3"
          onSubmit={(e) => {
            e.preventDefault();
            handleSubmit();
          }}
        >
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <p className="text-sm text-blue-700">
              Membership tiers automatically apply discounts to room bookings based on guest spending.
              Higher tiers offer better discounts to reward loyal customers.
            </p>
          </div>

          <section className="grid grid-cols-1 gap-4">
            <GlobalForm formDatails={formField} getFieldProps={getFieldProps} />
          </section>

          <button
            type="submit"
            disabled={!form.isValid || form.isSubmitting}
            className="bg-[#163381] self-end px-4 py-2 mt-5 text-white rounded-md font-medium w-fit disabled:opacity-50"
          >
            {editData ? 'Update' : 'Create'} Tier
          </button>
        </Form>
      </FormikProvider>
    </div>
  );
};

export default DiscountForm;
