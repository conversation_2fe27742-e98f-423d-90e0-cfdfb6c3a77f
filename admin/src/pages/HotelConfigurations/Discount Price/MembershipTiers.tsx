import { useState } from "react";
import Header from "../../../components/Header";
import { TableAction } from "../../../layouts/Table/TableAction";
import { PopupModal } from "../../../components";
import MasterTable from "../../../layouts/Table/MasterTable";
import DiscountForm from "./DiscountForm";
import {
  discountTypes,
  useGetAllDiscount,
  useDeleteDiscount,
} from "../../../server-action/API/Discount";
import { toast } from "react-toastify";
import MembershipPreview from "./MembershipPreview";

const MembershipTiers = () => {
  const [showPopup, setShowPopup] = useState("");
  const { data: discountData } = useGetAllDiscount();
  const { mutateAsync: deleteDiscount } = useDeleteDiscount();
  const [showPreview, setShowPreview] = useState(false);
  const [selectedDiscount, setSelectedDiscount] = useState<
    discountTypes | undefined
  >(undefined);

  // Handle membership tier deletion
  const handleDelete = async (id: string) => {
    if (
      window.confirm("Are you sure you want to delete this membership tier?")
    ) {
      try {
        await deleteDiscount(id);
        toast.success("Membership tier deleted successfully");
      } catch (error) {
        toast.error("Error deleting membership tier");
      }
    }
  };

  const tableData = {
    columns: [
      { title: "S.N.", key: "serialNo" },
      { title: "Membership Tier", key: "membershipTier" },
      { title: "Min. Spend", key: "minSpend" },
      { title: "Total Spend Threshold", key: "spendingThreshold" },
      { title: "Discount %", key: "discount" },
      { title: "Actions", key: "action" },
    ],
    rows: discountData?.map((item: any, index: number) => ({
      key: index,
      serialNo: index + 1,
      membershipTier: item?.name,
      minSpend: `Rs. ${item?.minSpend || 0}`,
      spendingThreshold: `Rs. ${item?.range}`,
      discount: `${item?.discount}%`,
      action: (
        <TableAction
          onEdit={() => {
            setShowPopup("edit");
            setSelectedDiscount(item);
          }}
          onDelete={() => handleDelete(item._id)}
        />
      ),
    })),
  };

  return (
    <div>
      <Header
        onAddClick={() => {
          setShowPopup("add");
          setSelectedDiscount(undefined);
        }}
        title="Membership Tiers"
      />

      <div className="mb-6 p-4 bg-white rounded-lg shadow-sm">
        <h2 className="text-lg font-semibold mb-2">About Membership Tiers</h2>
        <p className="text-gray-700 mb-4">
          Membership tiers provide discounts to loyal guests based on their
          total spending. As guests spend more, they automatically qualify for
          higher tiers with better discounts.
        </p>
        <div className="flex justify-between items-center">
          <div>
            <h3 className="font-medium">Key Benefits:</h3>
            <ul className="list-disc pl-5 mt-1 text-gray-600">
              <li>Automatic discounts on room bookings</li>
              <li>Guests are automatically upgraded as they spend more</li>
              <li>Encourages customer loyalty and repeat bookings</li>
            </ul>
          </div>
          <button
            onClick={() => setShowPreview(!showPreview)}
            className="px-4 py-2 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 transition-colors"
          >
            {showPreview ? "Hide Preview" : "Show How It Works"}
          </button>
        </div>
      </div>

      {showPreview && (
        <div className="mb-6">
          <MembershipPreview memberships={discountData || []} />
        </div>
      )}

      {showPopup === "add" && (
        <PopupModal
          onClose={() => setShowPopup("")}
          classname="w-full max-w-md"
        >
          <DiscountForm onClose={() => setShowPopup("")} />
        </PopupModal>
      )}

      {showPopup === "edit" && (
        <PopupModal
          onClose={() => setShowPopup("")}
          classname="w-full max-w-md"
        >
          <DiscountForm
            onClose={() => setShowPopup("")}
            editData={selectedDiscount}
          />
        </PopupModal>
      )}

      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <MasterTable
          columns={tableData?.columns}
          rows={tableData.rows}
          loading={false}
          pagination={{
            currentPage: 1,
            totalPage: 200,
            limit: 5,
            onClick: () => {},
          }}
        />
      </div>
    </div>
  );
};

export default MembershipTiers;
