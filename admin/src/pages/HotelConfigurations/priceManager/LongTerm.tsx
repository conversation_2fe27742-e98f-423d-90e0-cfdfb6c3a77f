import { useState } from "react"
import { PriceManagerFilter } from "./components/Filter"
import { ShowFilterDataTable } from "./components/ShowFilterData"
import { Icon } from "@iconify/react/dist/iconify.js"
import { PriceManagerForm } from "./components/PriceManagerForm"
import { ResultTable } from "./components/ResultTable"
import { RoomFilterParams } from "../../../server-action/API/Room/room"

export const LongTerm = () => {
    const [showHideRateManager, setShowHideRateManager] = useState(true)
    const [filters, setFilters] = useState<RoomFilterParams>({})
    const [formRoomType, setFormRoomType] = useState<string>('all')
    const [priceStatus, setPriceStatus] = useState<string>('all')

    const handleFilterChange = (newFilters: RoomFilterParams) => {
        setFilters(newFilters);
    }

    return <div className="flex flex-col gap-3">
        <section className="flex shadow-md  bg-white p-3 gap-5 rounded-lg ">
            <PriceManagerFilter purpose="longTerm" onFilterChange={handleFilterChange}/>
            <div className="w-full h-[400px] overflow-scroll ">
            <ShowFilterDataTable filters={filters}/>
            </div>
        </section>

            <section className="shadow-md flex flex-col gap-2" >
                <div className="flex justify-between p-4 place-items-center shadow-sm rounded-lg bg-white">
                        <p className="text-base">Rate Manager</p>
                    <Icon icon="lsicon:down-filled" fontSize={20} onClick={() => {
                        setShowHideRateManager(!showHideRateManager)
                        }} />
                </div>
                {
                    showHideRateManager &&
                    <PriceManagerForm
                        showDate={false}
                        onRoomTypeChange={(roomType) => setFormRoomType(roomType)}
                    />
                }
            </section>

            <section className="shadow-md bg-white p-3 rounded-lg">
                <h3 className="text-lg font-medium mb-4">Date-Based Room Pricing</h3>
                <div className="flex justify-between items-center mb-4">
                    <p className="text-sm text-gray-600">
                        {formRoomType === 'all'
                            ? 'Showing date-based pricing for all rooms'
                            : `Showing date-based pricing for selected room type`}
                    </p>
                    <div className="flex items-center space-x-4">
                        <span className="text-sm font-medium">Filter by price type:</span>
                        <div className="flex space-x-2">
                            <label className="inline-flex items-center">
                                <input
                                    type="radio"
                                    name="priceStatus"
                                    value="all"
                                    checked={priceStatus === 'all'}
                                    onChange={() => setPriceStatus('all')}
                                    className="form-radio h-4 w-4 text-indigo-600"
                                />
                                <span className="ml-2 text-sm text-gray-700">All</span>
                            </label>
                            <label className="inline-flex items-center">
                                <input
                                    type="radio"
                                    name="priceStatus"
                                    value="elevated"
                                    checked={priceStatus === 'elevated'}
                                    onChange={() => setPriceStatus('elevated')}
                                    className="form-radio h-4 w-4 text-indigo-600"
                                />
                                <span className="ml-2 text-sm text-gray-700">Elevated</span>
                            </label>
                            <label className="inline-flex items-center">
                                <input
                                    type="radio"
                                    name="priceStatus"
                                    value="offered"
                                    checked={priceStatus === 'offered'}
                                    onChange={() => setPriceStatus('offered')}
                                    className="form-radio h-4 w-4 text-indigo-600"
                                />
                                <span className="ml-2 text-sm text-gray-700">Discount</span>
                            </label>
                        </div>
                    </div>
                </div>
                <ResultTable roomType={formRoomType} priceStatus={priceStatus} />
            </section>
            </div>
}