import { Form, FormikProvider, useFormik } from "formik";
import { GlobalForm } from "../../../../components/GlobalForm/GlobalFormComponent";
import { useGetAllRoomType } from "../../../../server-action/API/Room/room-type";
import {
  useUpdateRoomPrices,
  IRoomPriceUpdate,
} from "../../../../server-action/API/HotelConfiguration/priceManager";
import * as Yup from "yup";
import { useState, useEffect } from "react";
/*eslint-disable @typescript-eslint/no-explicit-any */
interface propTypes {
  showDate?: boolean;
  onRoomTypeChange?: (roomType: string) => void;
}

export const PriceManagerForm = ({
  showDate = true,
  onRoomTypeChange,
}: propTypes) => {
  const { data } = useGetAllRoomType();
  const [isLoading, setIsLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Prepare room type options including multi-select option
  const roomTypeOptions = [
    { value: "all", label: "All Room Types" },
    ...(data?.map((item) => ({
      value: item?._id,
      label: item?.name,
    })) || []),
  ];

  const statusOptions = [
    {
      value: "elevated",
      label: "Elevated (Add to base price)",
    },
    {
      value: "offered",
      label: "Offered (Discount from base price)",
    },
  ];

  const acNonOptions = [
    { value: "all", label: "All" },
    { value: "ac", label: "AC" },
    { value: "non-ac", label: "Non-AC" },
  ];

  const adjustmentTypeOptions = [
    { value: "percentage", label: "Percentage (%)" },
    { value: "fixed", label: "Fixed Amount" },
  ];

  const roomField = [
    {
      field: "roomType",
      label: "Room Type",
      type: "select",
      options: roomTypeOptions,
    },
    {
      field: "acNon",
      label: "A/C-Non",
      type: "select",
      options: acNonOptions,
    },
  ];

  const inputValues = [
    {
      field: "status",
      label: "Price Type",
      type: "select",
      options: statusOptions,
    },
    {
      field: "adjustmentType",
      label: "Adjustment Type",
      type: "select",
      options: adjustmentTypeOptions,
    },
    {
      field: "rate",
      label: "Rate",
      type: "number",
    },
    {
      field: "fromDate",
      label: "From Date",
      type: "date",
      display: showDate,
    },
    {
      field: "toDate",
      label: "To Date",
      type: "date",
      display: showDate,
    },
    {
      field: "fromTime",
      label: "From Time",
      type: "time",
      display: showDate,
    },
    {
      field: "toTime",
      label: "To Time",
      type: "time",
      display: showDate,
    },
  ];

  const { mutateAsync: updateRoomPrices } = useUpdateRoomPrices();

  const validationSchema = Yup.object({
    roomType: Yup.string().required("Room Type is required"),
    acNon: Yup.string().required("AC/Non-AC is required"),
    status: Yup.string().required("Price type is required"),
    adjustmentType: Yup.string().required("Adjustment type is required"),
    rate: Yup.number()
      .required("Rate is required")
      .test(
        "is-valid-percentage",
        "Percentage must be between 0 and 100",
        function (value) {
          const { adjustmentType } = this.parent;
          if (adjustmentType === "percentage" && (value < 0 || value > 100)) {
            return false;
          }
          return true;
        }
      ),
    fromDate: Yup.date().required("From date is required"),
    toDate: Yup.date()
      .required("To date is required")
      .min(Yup.ref("fromDate"), "End date must be later than start date"),
  });

  const form = useFormik({
    initialValues: {
      roomType: "",
      acNon: "all",
      status: "elevated",
      adjustmentType: "percentage",
      rate: "",
      fromDate: new Date().toISOString().split("T")[0],
      toDate: new Date().toISOString().split("T")[0],
      fromTime: "",
      toTime: "",
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        setIsLoading(true);
        setSuccessMessage(null);

        // Prepare price update data
        const priceData: IRoomPriceUpdate = {
          // Now using roomTypeId instead of roomId
          roomTypeId: values.roomType !== "all" ? values.roomType : undefined,
          // If 'all' is selected, we need to fetch all roomTypeIds
          roomTypeIds:
            values.roomType === "all"
              ? data?.map((roomType) => roomType._id)
              : undefined,
          // Date range
          fromDate: values.fromDate,
          toDate: values.toDate,
          fromTime: values.fromTime || undefined,
          toTime: values.toTime || undefined,
          // Price settings
          price: values.rate ? Number(values.rate) : 0,
          isPercentage: values.adjustmentType === "percentage",
          status: values.status as "elevated" | "offered",
        };

        const result: any = await updateRoomPrices(priceData);

        if (result?.success) {
          setSuccessMessage(result.message || "Price updated successfully!");
          form.resetForm({
            values: {
              ...form.initialValues,
              fromDate: values.fromDate,
              toDate: values.toDate,
            },
          });
        }
      } catch (error) {
        console.error("Error submitting form:", error);
      } finally {
        setIsLoading(false);
      }
    },
  });

  const { handleSubmit, getFieldProps, values } = form;

  // Call onRoomTypeChange when roomType value changes
  useEffect(() => {
    if (onRoomTypeChange && values.roomType) {
      onRoomTypeChange(values.roomType);
    }
  }, [values.roomType, onRoomTypeChange]);

  return (
    <div className="flex flex-col shadow-md bg-white p-3 gap-5 rounded-lg">
      <div className="mb-4">
        <h3 className="text-lg font-medium">Room Price Management</h3>
        <p className="text-sm text-gray-600 mt-1">
          Apply price adjustments by room type. Select a room type, specify date
          range, and choose whether to elevate (increase) or offer (discount)
          the price by a percentage or fixed amount.
        </p>
      </div>

      {successMessage && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4">
          {successMessage}
        </div>
      )}

      <FormikProvider value={form}>
        <Form
          onSubmit={(e) => {
            e.preventDefault();
            handleSubmit();
          }}
          className="flex flex-col gap-3 p-3"
        >
          <section className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 p-3">
            <GlobalForm formDatails={roomField} getFieldProps={getFieldProps} />

            <GlobalForm
              formDatails={inputValues}
              getFieldProps={getFieldProps}
            />
          </section>

          <div className="flex justify-end mt-4">
            {values.adjustmentType === "percentage" && (
              <div className="text-sm text-gray-600 mr-4 self-center">
                {values.status === "elevated" ? "Adding" : "Discounting"}{" "}
                {values.rate || 0}%
                {values.fromDate === values.toDate
                  ? ` on ${new Date(values.fromDate).toLocaleDateString()}`
                  : ` from ${new Date(
                      values.fromDate
                    ).toLocaleDateString()} to ${new Date(
                      values.toDate
                    ).toLocaleDateString()}`}
              </div>
            )}

            <button
              className={`py-3 px-8 rounded-md text-white font-normal text-lg shadow-lg ${
                isLoading
                  ? "bg-gray-400 cursor-not-allowed"
                  : "bg-[#132852] hover:bg-green-700 border-2 border-green-500"
              }`}
              type="submit"
              disabled={isLoading}
            >
              {isLoading ? "Updating..." : "Apply Changes"}
            </button>
          </div>
        </Form>
      </FormikProvider>
    </div>
  );
};
