import { Form, FormikProvider, useFormik } from "formik";
import { useGetAllRoom } from "../../../../server-action/API/Room/room";
import {
  useUpdateRoomPrices,
  IRoomPriceUpdate,
} from "../../../../server-action/API/HotelConfiguration/priceManager";
import * as Yup from "yup";
import { useState } from "react";

interface DateBasedPriceFormProps {
  onSuccess?: () => void;
}

export const DateBasedPriceForm = ({ onSuccess }: DateBasedPriceFormProps) => {
  const { data: rooms } = useGetAllRoom();
  const { mutateAsync: updateRoomPrices } = useUpdateRoomPrices();
  const [isBulkOperation, setIsBulkOperation] = useState(false);

  // Create room options for dropdown
  const roomOptions =
    rooms?.map((room) => ({
      value: room._id || "",
      label: `${room.roomNo} - ${room.roomType?.name || "Unknown Type"}`,
    })) || [];

  // Status options
  const statusOptions = [
    { value: "elevated", label: "Elevated (Add to base price)" },
    { value: "offered", label: "Offered (Discount from base price)" },
  ];

  // Validation schema
  const validationSchema = Yup.object({
    fromDate: Yup.string().required("From date is required"),
    toDate: Yup.string().required("To date is required"),
    price: Yup.number()
      .required("Price is required")
      .min(0, "Price must be positive"),
    status: Yup.string().required("Status is required"),
    ...(isBulkOperation
      ? {
          roomIds: Yup.array().min(1, "Select at least one room"),
        }
      : {
          roomId: Yup.string().required("Room is required"),
        }),
  });

  // Form initialization
  const form = useFormik({
    initialValues: {
      roomId: "",
      roomIds: [],
      fromDate: "",
      fromTime: "00:00",
      toDate: "",
      toTime: "23:59",
      price: 0,
      isPercentage: false,
      status: "elevated",
      isBulkOperation: false,
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        const priceData: IRoomPriceUpdate = {
          fromDate: values.fromDate,
          fromTime: values.fromTime,
          toDate: values.toDate,
          toTime: values.toTime,
          price: Number(values.price),
          isPercentage: values.isPercentage,
          status: values.status as "elevated" | "offered",
        };

        // Add either roomId or roomIds based on operation type
        if (values.isBulkOperation) {
          priceData.roomTypeIds = values.roomIds as string[];
        } else {
          priceData.roomTypeId = values.roomId;
        }

        await updateRoomPrices(priceData);
        form.resetForm();

        if (onSuccess) {
          onSuccess();
        }
      } catch (error) {
        console.error("Error updating room prices:", error);
      }
    },
  });

  // Update bulk operation state when form value changes
  const handleBulkOperationChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setIsBulkOperation(e.target.checked);
    form.setFieldValue("isBulkOperation", e.target.checked);
    // Reset room selection when switching modes
    if (e.target.checked) {
      form.setFieldValue("roomId", "");
    } else {
      form.setFieldValue("roomIds", []);
    }
  };

  const { handleSubmit, getFieldProps } = form;

  return (
    <div className="flex flex-col shadow-md bg-white p-3 gap-5 rounded-lg">
      <h3 className="text-lg font-medium">Date-Based Room Pricing</h3>
      <p className="text-sm text-gray-600">
        Set special pricing for specific date ranges. You can choose between two
        pricing types:
        <br />• <strong>Elevated</strong>: Adds to the base price (e.g., during
        peak seasons)
        <br />• <strong>Offered</strong>: Discounts from the base price (e.g.,
        for promotions)
        <br />
        You can set either a fixed amount or a percentage of the base price.
      </p>

      <FormikProvider value={form}>
        <Form
          onSubmit={(e) => {
            e.preventDefault();
            handleSubmit();
          }}
          className="flex flex-col gap-3 p-3"
        >
          <div className="mb-4">
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                checked={isBulkOperation}
                onChange={handleBulkOperationChange}
                className="form-checkbox h-4 w-4 text-blue-600"
              />
              <span className="text-sm font-medium text-gray-700">
                Apply to multiple rooms
              </span>
            </label>
          </div>

          <section className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            {isBulkOperation ? (
              <div className="col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Select Rooms
                </label>
                <select
                  multiple
                  {...getFieldProps("roomIds")}
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                >
                  {roomOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                {form.touched.roomIds && form.errors.roomIds && (
                  <p className="mt-1 text-sm text-red-600">
                    {form.errors.roomIds as string}
                  </p>
                )}
              </div>
            ) : (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Select Room
                </label>
                <select
                  {...getFieldProps("roomId")}
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                >
                  <option value="">Select a room</option>
                  {roomOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                {form.touched.roomId && form.errors.roomId && (
                  <p className="mt-1 text-sm text-red-600">
                    {form.errors.roomId}
                  </p>
                )}
              </div>
            )}
          </section>

          <section className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                From Date
              </label>
              <input
                type="date"
                {...getFieldProps("fromDate")}
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
              />
              {form.touched.fromDate && form.errors.fromDate && (
                <p className="mt-1 text-sm text-red-600">
                  {form.errors.fromDate}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                From Time
              </label>
              <input
                type="time"
                {...getFieldProps("fromTime")}
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                To Date
              </label>
              <input
                type="date"
                {...getFieldProps("toDate")}
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
              />
              {form.touched.toDate && form.errors.toDate && (
                <p className="mt-1 text-sm text-red-600">
                  {form.errors.toDate}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                To Time
              </label>
              <input
                type="time"
                {...getFieldProps("toTime")}
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
              />
            </div>
          </section>

          <section className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Price
              </label>
              <input
                type="number"
                {...getFieldProps("price")}
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
              />
              {form.touched.price && form.errors.price && (
                <p className="mt-1 text-sm text-red-600">{form.errors.price}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Price Type
              </label>
              <select
                {...getFieldProps("status")}
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
              >
                {statusOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              {form.touched.status && form.errors.status && (
                <p className="mt-1 text-sm text-red-600">
                  {form.errors.status}
                </p>
              )}
            </div>

            <div className="flex items-center mt-8">
              <label className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  {...getFieldProps("isPercentage")}
                  className="form-checkbox h-4 w-4 text-blue-600"
                />
                <span className="text-sm font-medium text-gray-700">
                  {form.values.isPercentage ? "Percentage (%)" : "Fixed Amount"}
                </span>
              </label>
            </div>
          </section>

          <div className="flex justify-end mt-4">
            <button
              type="submit"
              className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-[#132852] hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Apply Price Rule
            </button>
          </div>
        </Form>
      </FormikProvider>
    </div>
  );
};
