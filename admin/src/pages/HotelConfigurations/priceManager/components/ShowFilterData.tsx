/*eslint-disable @typescript-eslint/no-explicit-any */
import MasterTable from "../../../../layouts/Table/MasterTable";
import { useGetAllRoomType } from "../../../../server-action/API/Room/room-type";
import {
  useGetAllRoom,
  RoomFilterParams,
} from "../../../../server-action/API/Room/room";
import { IDateBasedPrice } from "../../../../Interface/room.interface";
interface ShowFilterDataTableProps {
  filters?: RoomFilterParams;
}

export const ShowFilterDataTable = ({ filters }: ShowFilterDataTableProps) => {
  const { data: roomTypes } = useGetAllRoomType();
  // Ensure we're showing all room types when filters is empty or roomType is 'all'
  const effectiveFilters = filters || {};
  if (effectiveFilters.roomType === "all") {
    delete effectiveFilters.roomType;
  }
  const { data: rooms, isLoading: roomsLoading } =
    useGetAllRoom(effectiveFilters);
  // Function to get room type name by ID
  const getRoomTypeName = (roomTypeId: string) => {
    if (!roomTypeId) return "Unknown";
    const roomType = roomTypes?.find((rt) => rt._id === roomTypeId);
    return roomType?.name || "Unknown";
  };

  // Initialize data structure to hold our grouped data
  const finalGroupedPrices: any = {};

  // Process rooms data to extract dateBasedPrice information
  if (rooms && rooms.length > 0) {
    // Group rooms by room type
    const roomsByType: any = {};

    rooms.forEach((room) => {
      if (room.roomType) {
        // Handle both string and object roomType
        let roomTypeId;
        let roomTypeName;

        if (typeof room.roomType === "string") {
          roomTypeId = room.roomType;
          roomTypeName = getRoomTypeName(roomTypeId);
        } else if (typeof room.roomType === "object") {
          roomTypeId = room.roomType._id;
          roomTypeName =
            room.roomType.name || getRoomTypeName(roomTypeId as any);
        }

        if (roomTypeName) {
          if (!roomsByType[roomTypeName]) {
            roomsByType[roomTypeName] = [];
          }
          roomsByType[roomTypeName].push(room);

          // Initialize or update the entry in finalGroupedPrices
          if (!finalGroupedPrices[roomTypeName]) {
            finalGroupedPrices[roomTypeName] = {
              elevated: 0,
              offered: 0,
              rooms: 0,
              activeDates: 0,
            };
          }

          // Count the room
          finalGroupedPrices[roomTypeName].rooms += 1;

          // Process dateBasedPrices for this room
          if (
            room.roomPrice?.dateBasedPrices &&
            room.roomPrice.dateBasedPrices.length > 0
          ) {
            // Count active date ranges
            finalGroupedPrices[roomTypeName].activeDates +=
              room.roomPrice.dateBasedPrices.length;

            // Count by status
            room.roomPrice.dateBasedPrices.forEach(
              (priceEntry: IDateBasedPrice) => {
                if (priceEntry.status === "elevated") {
                  finalGroupedPrices[roomTypeName].elevated += 1;
                } else if (priceEntry.status === "offered") {
                  finalGroupedPrices[roomTypeName].offered += 1;
                }
              }
            );
          }
        }
      }
    });
  }

  // Convert grouped data to rows
  const rows = Object.entries(finalGroupedPrices).map(
    ([roomType, data]: [string, any]) => ({
      roomType,
      totalRooms: data.rooms,
      activeDateRanges: data.activeDates,
      elevated: data.elevated,
      offered: data.offered,
      status: data.elevated > 0 || data.offered > 0 ? "Active" : "Normal",
    })
  );

  // Prepare table data
  const tableData = {
    columns: [
      {
        key: "roomType",
        title: "Room Type",
      },
      {
        key: "totalRooms",
        title: "Total Rooms",
      },
      {
        key: "activeDateRanges",
        title: "Active Date Ranges",
      },
      {
        key: "elevated",
        title: "Elevated Prices",
      },
      {
        key: "offered",
        title: "Offered Discounts",
      },
      {
        key: "status",
        title: "Status",
      },
    ],
    rows:
      rows && rows.length > 0
        ? rows
        : [
            {
              roomType: "No data available",
              totalRooms: "-",
              activeDateRanges: "-",
              elevated: "-",
              offered: "-",
              status: "-",
            },
          ],
  };

  return (
    <div className="flex flex-col gap-5 p-3 bg-white shadow-md rounded-lg">
      <MasterTable
        showTopPageSelector={false}
        rows={tableData.rows}
        columns={tableData.columns}
        loading={roomsLoading}
      />
    </div>
  );
};
