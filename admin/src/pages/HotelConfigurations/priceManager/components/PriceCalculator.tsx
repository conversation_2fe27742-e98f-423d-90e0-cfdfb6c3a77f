import { useState } from "react";
import { Form, FormikProvider, useFormik } from "formik";
import { useGetAllRoom } from "../../../../server-action/API/Room/room";
import {
  useCalculateRoomPrice,
  IPriceCalculationResponse,
} from "../../../../server-action/API/HotelConfiguration/priceManager";
import { useGetAllUser } from "../../../../server-action/API/user";
import * as Yup from "yup";
import { format } from "date-fns";

export const PriceCalculator = () => {
  const { data: rooms } = useGetAllRoom();
  const { data: users } = useGetAllUser({ role: "guest" });
  const { mutateAsync: calculatePrice, isPending: isLoading } =
    useCalculateRoomPrice();
  const [calculationResult, setCalculationResult] =
    useState<IPriceCalculationResponse | null>(null);

  // Create room options for dropdown
  const roomOptions =
    rooms?.map((room) => ({
      value: room._id || "",
      label: `${room.roomNo} - ${room.roomType?.name || "Unknown Type"}`,
    })) || [];

  // Create user options for dropdown
  const userOptions =
    users?.map((user: any) => ({
      value: user._id || "",
      label: `${user.name} ${user.phoneNumber ? `(${user.phoneNumber})` : ""}`,
    })) || [];

  // Validation schema
  const validationSchema = Yup.object({
    roomId: Yup.string().required("Room is required"),
    checkInDate: Yup.string().required("Check-in date is required"),
    checkOutDate: Yup.string().required("Check-out date is required"),
    userId: Yup.string(), // Optional user ID for membership discount
  });

  // Form initialization
  const form = useFormik({
    initialValues: {
      roomId: "",
      checkInDate: "",
      checkOutDate: "",
      userId: "",
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        const result = await calculatePrice({
          roomId: values.roomId,
          checkInDate: values.checkInDate,
          checkOutDate: values.checkOutDate,
          userId: values.userId || undefined, // Only include if a user is selected
        });
        setCalculationResult(result);
      } catch (error) {
        console.error("Error calculating price:", error);
      }
    },
  });

  const { handleSubmit, getFieldProps } = form;

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "MMM dd, yyyy");
    } catch (error: any) {
      console.error("Error formatting date:", error);
      return dateString;
    }
  };

  return (
    <div className="flex flex-col shadow-md bg-white p-6 gap-5 rounded-lg">
      <h3 className="text-lg font-medium">Room Price Calculator</h3>
      <p className="text-sm text-gray-600">
        Calculate the total price for a room based on the selected date range,
        including any special pricing rules.
        <br />
        The calculator considers:
        <br />• Base room price
        <br />• Elevated prices (additions to base price)
        <br />• Offered prices (discounts from base price)
        <br />
        The breakdown shows which pricing rule applies to each day of the stay.
      </p>

      <FormikProvider value={form}>
        <Form
          onSubmit={(e) => {
            e.preventDefault();
            handleSubmit();
          }}
          className="flex flex-col gap-4"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Select Room
              </label>
              <select
                {...getFieldProps("roomId")}
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
              >
                <option value="">Select a room</option>
                {roomOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              {form.touched.roomId && form.errors.roomId && (
                <p className="mt-1 text-sm text-red-600">
                  {form.errors.roomId}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Guest (for Membership Discount)
              </label>
              <select
                {...getFieldProps("userId")}
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
              >
                <option value="">Select a guest (optional)</option>
                {userOptions.map((option: any) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              {form.touched.userId && form.errors.userId && (
                <p className="mt-1 text-sm text-red-600">
                  {form.errors.userId}
                </p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Check-in Date
              </label>
              <input
                type="date"
                {...getFieldProps("checkInDate")}
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
              />
              {form.touched.checkInDate && form.errors.checkInDate && (
                <p className="mt-1 text-sm text-red-600">
                  {form.errors.checkInDate}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Check-out Date
              </label>
              <input
                type="date"
                {...getFieldProps("checkOutDate")}
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
              />
              {form.touched.checkOutDate && form.errors.checkOutDate && (
                <p className="mt-1 text-sm text-red-600">
                  {form.errors.checkOutDate}
                </p>
              )}
            </div>
          </div>

          <div className="flex justify-end mt-2">
            <button
              type="submit"
              disabled={isLoading}
              className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-[#132852] hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            >
              {isLoading ? "Calculating..." : "Calculate Price"}
            </button>
          </div>
        </Form>
      </FormikProvider>

      {calculationResult && (
        <div className="mt-6 border-t pt-4">
          <h4 className="text-md font-medium mb-2">Price Calculation Result</h4>

          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              <div>
                <p className="text-xs text-gray-500">Room</p>
                <p className="text-sm font-medium">
                  {calculationResult.roomNo}
                </p>
              </div>
              <div>
                <p className="text-xs text-gray-500">Base Price</p>
                <p className="text-sm font-medium">
                  {calculationResult.basePrice}
                </p>
              </div>
              <div>
                <p className="text-xs text-gray-500">Nights</p>
                <p className="text-sm font-medium">
                  {calculationResult.nights}
                </p>
              </div>
              <div>
                <p className="text-xs text-gray-500">Total Price</p>
                <p className="text-lg font-bold text-indigo-600">
                  {calculationResult.totalPrice}
                </p>
              </div>
            </div>

            {calculationResult.membershipDiscount && (
              <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md">
                <h5 className="text-sm font-medium text-green-800 mb-1">
                  Membership Discount Applied
                </h5>
                <div className="grid grid-cols-3 gap-2">
                  <div>
                    <p className="text-xs text-green-600">Membership</p>
                    <p className="text-sm font-medium">
                      {calculationResult.membershipDiscount.name}
                    </p>
                  </div>
                  <div>
                    <p className="text-xs text-green-600">Discount %</p>
                    <p className="text-sm font-medium">
                      {calculationResult.membershipDiscount.discountPercentage}%
                    </p>
                  </div>
                  <div>
                    <p className="text-xs text-green-600">Amount Saved</p>
                    <p className="text-sm font-medium text-green-700">
                      {calculationResult.membershipDiscount.discountAmount}
                    </p>
                  </div>
                </div>
              </div>
            )}

            <div className="mb-4">
              <p className="text-xs text-gray-500 mb-1">Stay Period</p>
              <p className="text-sm">
                {formatDate(calculationResult.checkIn)} -{" "}
                {formatDate(calculationResult.checkOut)}
              </p>
            </div>

            <div>
              <p className="text-xs text-gray-500 mb-2">Price Breakdown</p>
              <div className="max-h-60 overflow-y-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Price
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Rule Applied
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {calculationResult.priceBreakdown.map((day, index) => (
                      <tr
                        key={index}
                        className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}
                      >
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-900">
                          {formatDate(day.date)}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-900">
                          {day.price}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                          {day.rule}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
