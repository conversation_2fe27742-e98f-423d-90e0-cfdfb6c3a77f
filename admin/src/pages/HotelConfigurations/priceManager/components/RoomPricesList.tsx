import { useState } from "react";
import {
  useGetRoomDateBasedPrices,
  useDeleteDateBasedPrice,
  useUpdateDateBasedPriceStatus,
} from "../../../../server-action/API/HotelConfiguration/priceManager";
import { IDateBasedPrice } from "../../../../Interface/room.interface";
import { format } from "date-fns";
import { Icon } from "@iconify/react";

interface RoomPricesListProps {
  roomId: string;
}

export const RoomPricesList = ({ roomId }: RoomPricesListProps) => {
  const { data, isLoading, refetch } = useGetRoomDateBasedPrices(roomId);
  const { mutateAsync: deletePriceEntry } = useDeleteDateBasedPrice();
  const { mutateAsync: updatePriceStatus } = useUpdateDateBasedPriceStatus();

  const [expandedPriceId, setExpandedPriceId] = useState<string | null>(null);

  if (isLoading) {
    return <div className="flex justify-center p-8">Loading price data...</div>;
  }

  if (!data || !data.dateBasedPrices || data.dateBasedPrices.length === 0) {
    return (
      <div className="bg-white rounded-lg p-6 text-center">
        <p className="text-gray-500">
          No date-based prices found for this room.
        </p>
      </div>
    );
  }

  const handleDelete = async (priceId: string) => {
    if (window.confirm("Are you sure you want to delete this price entry?")) {
      try {
        await deletePriceEntry({ roomId, priceId });
        refetch();
      } catch (error) {
        console.error("Error deleting price entry:", error);
      }
    }
  };

  const handleStatusChange = async (
    priceId: string,
    newStatus: "elevated" | "offered"
  ) => {
    try {
      await updatePriceStatus({ roomId, priceId, status: newStatus });
      refetch();
    } catch (error) {
      console.error("Error updating price status:", error);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "MMM dd, yyyy HH:mm");
    } catch (error) {
      return dateString;
    }
  };

  const toggleExpand = (priceId: string) => {
    if (expandedPriceId === priceId) {
      setExpandedPriceId(null);
    } else {
      setExpandedPriceId(priceId);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow overflow-hidden">
      <div className="px-4 py-5 sm:px-6">
        <h3 className="text-lg leading-6 font-medium text-gray-900">
          Date-Based Prices for Room {data.roomNo}
        </h3>
        <p className="mt-1 max-w-2xl text-sm text-gray-500">
          Special pricing rules applied to this room
        </p>
      </div>

      <div className="border-t border-gray-200">
        <ul className="divide-y divide-gray-200">
          {data.dateBasedPrices.map(
            (price: IDateBasedPrice & { _id?: string }) => (
              <li key={price._id} className="px-4 py-4 sm:px-6">
                <div className="flex items-center justify-between">
                  <div className="flex flex-col">
                    <span className="text-sm font-medium text-indigo-600">
                      {formatDate(price.fromDate)} - {formatDate(price.toDate)}
                    </span>
                    <span className="text-sm text-gray-500">
                      {price.isPercentage
                        ? `${price.price}% ${
                            price.status === "elevated"
                              ? "increase"
                              : "discount"
                          }`
                        : `${price.price} ${
                            price.status === "elevated"
                              ? "additional"
                              : "discount"
                          }`}
                    </span>
                  </div>

                  <div className="flex items-center space-x-2">
                    <span
                      className={`px-2 py-1 text-xs rounded-full ${
                        price.status === "elevated"
                          ? "bg-green-100 text-green-800"
                          : "bg-blue-100 text-blue-800"
                      }`}
                    >
                      {price.status === "elevated" ? "Elevated" : "Offered"}
                    </span>

                    <button
                      onClick={() => toggleExpand(price._id || "")}
                      className="text-gray-400 hover:text-gray-500"
                    >
                      <Icon
                        icon={
                          expandedPriceId === price._id
                            ? "mdi:chevron-up"
                            : "mdi:chevron-down"
                        }
                        width={20}
                      />
                    </button>
                  </div>
                </div>

                {expandedPriceId === price._id && (
                  <div className="mt-4 pt-4 border-t border-gray-100">
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div>
                        <p className="text-xs text-gray-500">From</p>
                        <p className="text-sm">{formatDate(price.fromDate)}</p>
                      </div>
                      <div>
                        <p className="text-xs text-gray-500">To</p>
                        <p className="text-sm">{formatDate(price.toDate)}</p>
                      </div>
                      <div>
                        <p className="text-xs text-gray-500">Price</p>
                        <p className="text-sm">
                          {price.isPercentage ? `${price.price}%` : price.price}
                        </p>
                      </div>
                      <div>
                        <p className="text-xs text-gray-500">Type</p>
                        <p className="text-sm">
                          {price.isPercentage ? "Percentage" : "Fixed"}
                        </p>
                      </div>
                    </div>

                    <div className="flex justify-between mt-2">
                      <div className="flex space-x-2">
                        <button
                          onClick={() =>
                            handleStatusChange(price._id || "", "elevated")
                          }
                          className={`px-3 py-1 text-xs rounded ${
                            price.status === "elevated"
                              ? "bg-[#132852] text-white"
                              : "bg-gray-200 text-gray-700 hover:bg-green-100"
                          }`}
                        >
                          Elevated
                        </button>
                        <button
                          onClick={() =>
                            handleStatusChange(price._id || "", "offered")
                          }
                          className={`px-3 py-1 text-xs rounded ${
                            price.status === "offered"
                              ? "bg-[#132852] text-white"
                              : "bg-gray-200 text-gray-700 hover:bg-blue-100"
                          }`}
                        >
                          Offered
                        </button>
                      </div>

                      <button
                        onClick={() => handleDelete(price._id || "")}
                        className="px-3 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200"
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                )}
              </li>
            )
          )}
        </ul>
      </div>
    </div>
  );
};
