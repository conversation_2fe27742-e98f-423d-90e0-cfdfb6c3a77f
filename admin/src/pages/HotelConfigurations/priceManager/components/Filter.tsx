import { Form, FormikProvider, useFormik } from "formik";
import { useGetAllRoomType } from "../../../../server-action/API/Room/room-type";
import { GlobalForm } from "../../../../components/GlobalForm/GlobalFormComponent";
import * as Yup from "yup";
import { RoomFilterParams } from "../../../../server-action/API/Room/room";
import { useState } from "react";
/*eslint-disable @typescript-eslint/no-unused-vars */
interface propTypes {
  purpose?: string;
  onFilterChange?: (filters: RoomFilterParams) => void;
}

export const PriceManagerFilter = ({ purpose, onFilterChange }: propTypes) => {
  const { data } = useGetAllRoomType();
  const [filters, setFilters] = useState<RoomFilterParams>({});

  const roomTypeOptions = [
    { value: "all", label: "All" },
    ...(data?.map((item) => ({
      value: item?._id,
      label: item?.name,
    })) || []),
  ];

  const acNonOptions = [
    { value: "none", label: "All" },
    { value: "central", label: "Central" },
    { value: "split", label: "Split" },
    { value: "window", label: "Window" },
  ];

  const validationSchema = Yup.object({
    roomType: Yup.string(),
    acNon: Yup.string(),
    dateFrom: Yup.string(),
    dateTo: Yup.string(),
  });

  const form = useFormik({
    initialValues: {
      roomType: "all",
      acNon: "all",
      dateFrom: "",
      dateTo: "",
    },
    validationSchema,
    onSubmit: (values) => {
      // Create filter params from form values
      const filterParams: RoomFilterParams = {
        roomType: values.roomType,
        acType: values.acNon,
        dateFrom: values.dateFrom,
        dateTo: values.dateTo,
      };

      // Update local state
      setFilters(filterParams);

      // Notify parent component if callback provided
      if (onFilterChange) {
        onFilterChange(filterParams);
      }

      console.log("Filter values:", filterParams);
    },
  });

  const inputValues = [
    {
      field: "roomType",
      label: "Room Type",
      type: "select",
      options: roomTypeOptions,
    },
    {
      field: "acNon",
      label: "A/C-Non",
      type: "select",
      options: acNonOptions,
    },
  ];

  const dateValues = [
    {
      field: "dateFrom",
      label: "Date From",
      type: "date",
    },
    {
      field: "dateTo",
      label: "Date to",
      type: "date",
    },
  ];

  const { getFieldProps } = form;

  return (
    <div className="flex flex-col gap-5 p-3 bg-white shadow-md rounded-lg h-fit">
      <FormikProvider value={form}>
        <Form
          onSubmit={(e) => {
            e.preventDefault();
            form.handleSubmit();
          }}
          className={`flex flex-col w-full gap-3 p-3 ${
            purpose !== "longTerm" && "min-w-[280px]"
          }`}
        >
          <GlobalForm formDatails={inputValues} getFieldProps={getFieldProps} />

          {purpose === "longTerm" && (
            <section className="flex place-items-center gap-3">
              <GlobalForm
                formDatails={dateValues}
                getFieldProps={getFieldProps}
              />
            </section>
          )}

          <section className="flex place-items-center gap-3 justify-between mt-5">
            <button
              type="submit"
              className="border border-blue-600 bg-[#132852] py-1 px-3 text-white text-xs rounded w-full"
            >
              Apply
            </button>
            <button
              className="border border-fade-black py-1 px-3 text-fade-black text-xs rounded w-full"
              type="button"
              onClick={() => {
                form.resetForm();
                // Call onFilterChange with default values when reset
                if (onFilterChange) {
                  onFilterChange({
                    roomType: "all",
                    acType: "all",
                    dateFrom: "",
                    dateTo: "",
                  });
                }
              }}
            >
              Reset Filters
            </button>
          </section>
        </Form>
      </FormikProvider>
    </div>
  );
};
