import { useCallback, useMemo, useState } from "react";
import Breadcrumbs from "../../../components/Breadcrumb";
import { CustomTabs } from "../../../components/CustomTab";
import { Today } from "./Today";
import { LongTerm } from "./LongTerm";
import { DateBasedPriceForm } from "./components/DateBasedPriceForm";
import { PriceCalculator } from "./components/PriceCalculator";
import { RoomPricesList } from "./components/RoomPricesList";
import { useGetAllRoom } from "../../../server-action/API/Room/room";

const PriceManager = () => {
  const [tab, setTab] = useState("Today");
  const [selectedRoomId, setSelectedRoomId] = useState<string>("");
  const { data: rooms } = useGetAllRoom();

  const tabOptions = useMemo(
    () => ["Today", "Long-Term", "Date-Based Pricing", "Price Calculator"],
    []
  );

  const onTabChange = useCallback((status: string) => {
    setTab(status);
    // Reset selected room when changing tabs
    if (status !== "Date-Based Pricing") {
      setSelectedRoomId("");
    }
  }, []);

  // Handle room selection for date-based pricing
  const handleRoomSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedRoomId(e.target.value);
  };

  const renderTabContent = () => {
    switch (tab) {
      case "Today":
        return <Today />;
      case "Long-Term":
        return <LongTerm />;
      case "Date-Based Pricing":
        return (
          <div className="flex flex-col gap-6">
            <DateBasedPriceForm onSuccess={() => {
              // If a room is selected, this will refresh its price list
              if (selectedRoomId) {
                // Force a re-render of the RoomPricesList component
                setSelectedRoomId("");
                setTimeout(() => setSelectedRoomId(selectedRoomId), 100);
              }
            }} />

            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-medium mb-4">View Room Price Rules</h3>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">Select Room</label>
                <select
                  value={selectedRoomId}
                  onChange={handleRoomSelect}
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                >
                  <option value="">Select a room to view its price rules</option>
                  {rooms?.map((room) => (
                    <option key={room._id} value={room._id}>
                      {room.roomNo} - {room.roomType?.name || "Unknown Type"}
                    </option>
                  ))}
                </select>
              </div>

              {selectedRoomId && <RoomPricesList roomId={selectedRoomId} />}
            </div>
          </div>
        );
      case "Price Calculator":
        return <PriceCalculator />;
      default:
        return <Today />;
    }
  };

  return (
    <div className="flex flex-col gap-6">
      <Breadcrumbs />

      <section className="bg-white p-3 rounded-lg">
        <CustomTabs
          tabs={tabOptions}
          defaultTab={tab}
          onTabChange={onTabChange}
        />
      </section>

      <section>
        {renderTabContent()}
      </section>
    </div>
  );
};

export default PriceManager;
