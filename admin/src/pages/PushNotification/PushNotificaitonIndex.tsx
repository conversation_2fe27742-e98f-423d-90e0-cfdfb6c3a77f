"use client";

import { useState, useMemo, Component, type ReactNode } from "react";
import { Icon } from "@iconify/react";
import { useGetNotifications } from "../../server-action/API/push-notifications";
import Header from "../../components/Header";

interface Notification {
  _id: string;
  title: string;
  body: {
    message: string;
    ticketId?: string;
    ticket?: string;
    priority?: string;
  };
  type: string;
  isRead: boolean;
  createdAt: string;
  ticketCategory?: string;
  createdBy?: {
    _id: string;
    name: string;
    email: string;
    hotel: {
      name: string;
      logo: string;
    };
  };
}

class NotificationErrorBoundary extends Component<
  { children: ReactNode },
  { hasError: boolean }
> {
  state = { hasError: false };

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
          <div className="text-center p-6 bg-white rounded-xl shadow-md max-w-sm">
            <Icon
              icon="solar:danger-triangle-bold"
              className="w-12 h-12 text-red-500 mx-auto mb-3"
            />
            <h2 className="text-lg font-bold text-gray-900 mb-2">
              Something went wrong
            </h2>
            <p className="text-gray-600 text-sm">
              Failed to load notifications. Please try again later.
            </p>
          </div>
        </div>
      );
    }
    return this.props.children;
  }
}

const PushNotificationIndex = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [activeTab, setActiveTab] = useState<string>("All");
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>(
    []
  );

  const {
    data: getAllNotifications,
    isLoading,
    error,
    isFetching,
  } = useGetNotifications(currentPage, pageSize);

  const notifications: Notification[] =
    getAllNotifications?.data?.notification || [];
  const pagination = getAllNotifications?.data?.pagination;

  const tabs = [
    { key: "All", label: "All", icon: "solar:inbox-bold" },
    { key: "Laundry", label: "Laundry", icon: "solar:washing-machine-bold" },
    {
      key: "Inspection",
      label: "Inspection",
      icon: "solar:clipboard-check-bold",
    },
    { key: "Cleaning", label: "Cleaning", icon: "solar:broom-bold" },
    { key: "Room Service", label: "Room Service", icon: "solar:cup-hot-bold" },
  ];

  const extractTicketCategory = (ticket: string): string | null => {
    try {
      const ticketData = JSON.parse(ticket);
      return ticketData.ticketCategory || null;
    } catch (error) {
      console.warn(`Failed to parse ticket JSON: ${ticket}`, error);
      const match = ticket.match(/ticketCategory:\s*'([^']+)'/);
      return match ? match[1] : null;
    }
  };

  const filteredNotifications = useMemo(() => {
    if (activeTab === "All") return notifications;
    return notifications.filter((notification) => {
      let category: string | null = null;

      if (notification.body.ticket) {
        category = extractTicketCategory(notification.body.ticket);
      } else {
        category = notification.body.message
          .toLowerCase()
          .includes(activeTab.toLowerCase())
          ? activeTab.toLowerCase()
          : null;
      }

      return category === activeTab.toLowerCase();
    });
  }, [activeTab, notifications]);

  const unreadCount = notifications.filter((n) => !n.isRead).length;

  const getTabCount = (tab: string) => {
    if (tab === "All") return notifications.length;
    return notifications.filter((notification) => {
      let category: string | null = null;
      if (notification.body.ticket) {
        category = extractTicketCategory(notification.body.ticket);
      } else {
        category = notification.body.message
          .toLowerCase()
          .includes(tab.toLowerCase())
          ? tab.toLowerCase()
          : null;
      }
      return category === tab.toLowerCase();
    }).length;
  };

  const getPriorityColor = (priority?: string) => {
    switch (priority?.toLowerCase()) {
      case "urgent":
        return "bg-red-50 text-red-700 border-red-200 shadow-red-100";
      case "high":
        return "bg-orange-50 text-orange-700 border-orange-200 shadow-orange-100";
      case "medium":
        return "bg-amber-50 text-amber-700 border-amber-200 shadow-amber-100";
      case "low":
        return "bg-emerald-50 text-emerald-700 border-emerald-200 shadow-emerald-100";
      default:
        return "bg-gray-50 text-gray-700 border-gray-200 shadow-gray-100";
    }
  };

  const getPriorityIcon = (priority?: string) => {
    switch (priority?.toLowerCase()) {
      case "urgent":
        return "solar:danger-triangle-bold";
      case "high":
        return "solar:shield-warning-bold";
      case "medium":
        return "solar:info-circle-bold";
      case "low":
        return "solar:check-circle-bold";
      default:
        return "solar:record-circle-bold";
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1) return "Just now";
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    return date.toLocaleDateString();
  };

  const getTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case "ticket_status_update":
        return "solar:ticket-bold";
      case "inspection_scheduled":
        return "solar:clipboard-check-bold";
      case "cleaning_request":
        return "solar:broom-bold";
      case "room_service":
        return "solar:cup-hot-bold";
      default:
        return "solar:bell-bold";
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    setActiveTab("All");
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1);
  };

  const handlePreviousPage = () => {
    if (pagination && currentPage > 1) {
      handlePageChange(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (pagination && currentPage < pagination.pages) {
      handlePageChange(currentPage + 1);
    }
  };

  const getPageNumbers = () => {
    if (!pagination) return [];

    const totalPages = pagination.pages;
    const current = currentPage;
    const pages: (number | string)[] = [];

    if (totalPages <= 7) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      pages.push(1);

      if (current > 3) {
        pages.push("...");
      }

      const start = Math.max(2, current - 1);
      const end = Math.min(totalPages - 1, current + 1);

      for (let i = start; i <= end; i++) {
        pages.push(i);
      }

      if (current < totalPages - 2) {
        pages.push("...");
      }

      if (totalPages > 1) {
        pages.push(totalPages);
      }
    }

    return pages;
  };

  if (isLoading && currentPage === 1) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
        <Header hideHeader={true} title="" />
        <div className="flex items-center justify-center py-16">
          <div className="text-center bg-white p-6 rounded-xl shadow-md">
            <Icon
              icon="solar:refresh-bold"
              className="w-6 h-6 text-[#070C44] animate-spin mx-auto mb-3"
            />
            <p className="text-gray-600 text-sm font-medium">
              Loading notifications...
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
        <Header hideHeader={true} title="" />
        <div className="flex items-center justify-center py-16">
          <div className="text-center bg-white p-6 rounded-xl shadow-md max-w-sm">
            <Icon
              icon="solar:danger-triangle-bold"
              className="w-10 h-10 text-red-500 mx-auto mb-3"
            />
            <h2 className="text-lg font-bold text-gray-900 mb-2">
              Failed to load notifications
            </h2>
            <p className="text-gray-600 text-sm">Please try again later.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <NotificationErrorBoundary>
      <Header hideHeader={true} title="" />
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="bg-gradient-to-r from-[#070C44]  to-[#0A1055] border-b rounded-md border-gray-200 sticky top-0 z-10 shadow-md">
          <div className="mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="relative">
                  <div className="w-10 h-10 bg-white/10 backdrop-blur-sm rounded-lg flex items-center justify-center">
                    <Icon
                      icon="solar:bell-bold"
                      className="w-5 h-5 text-white"
                    />
                  </div>
                  {unreadCount > 0 && (
                    <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center shadow-md">
                      {unreadCount > 99 ? "99+" : unreadCount}
                    </span>
                  )}
                </div>
                <div>
                  <h1 className="text-xl font-bold text-white">
                    Notifications
                  </h1>
                  <p className="text-white/70 text-xs">
                    {unreadCount > 0
                      ? `${unreadCount} unread notifications`
                      : "All caught up!"}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <select
                  value={pageSize}
                  onChange={(e) => handlePageSizeChange(Number(e.target.value))}
                  className="px-3 py-1.5 text-sm bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/30"
                >
                  <option value={5} className="text-gray-900">
                    5 per page
                  </option>
                  <option value={10} className="text-gray-900">
                    10 per page
                  </option>
                  <option value={20} className="text-gray-900">
                    20 per page
                  </option>
                  <option value={50} className="text-gray-900">
                    50 per page
                  </option>
                </select>

                {unreadCount > 0 && (
                  <button className="flex items-center gap-1.5 px-3 py-1.5 text-sm bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg text-white hover:bg-white/20 transition-all duration-200">
                    <Icon icon="solar:check-read-bold" className="w-4 h-4" />
                    Mark all read
                  </button>
                )}

                <button className="p-1.5 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg text-white hover:bg-white/20 transition-all duration-200">
                  <Icon icon="solar:filter-bold" className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="mx-auto px-4 py-6 border">
          <div className="bg-white rounded-md border border-gray-200  mb-2 overflow-hidden">
            <div className="border-b border-gray-100">
              <nav className="flex" aria-label="Tabs">
                {tabs.map((tab) => {
                  const count = getTabCount(tab.key);
                  const isActive = activeTab === tab.key;
                  return (
                    <button
                      key={tab.key}
                      onClick={() => setActiveTab(tab.key)}
                      className={`flex-1 py-3 px-4 border-b-2 font-medium text-sm  ${
                        isActive
                          ? "border-[#070C44] text-[#070C44] bg-[#2A3A6D]/5"
                          : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 hover:bg-gray-50"
                      }`}
                    >
                      <div className="flex items-center justify-center gap-1.5">
                        <Icon icon={tab.icon} className="w-4 h-4" />
                        {tab.label}
                        {count > 0 && (
                          <span
                            className={`px-1.5 py-0.5 rounded-full text-xs font-medium ${
                              isActive
                                ? "bg-[#2A3A6D] text-white"
                                : "bg-gray-100 text-gray-600"
                            }`}
                          >
                            {count}
                          </span>
                        )}
                      </div>
                    </button>
                  );
                })}
              </nav>
            </div>
          </div>

          <div className="relative">
            {isFetching && (
              <div className="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-10 rounded-md">
                <div className="flex items-center gap-2 text-[#070C44] bg-white px-4 py-2 rounded-lg shadow-md">
                  <Icon
                    icon="solar:refresh-bold"
                    className="w-4 h-4 animate-spin"
                  />
                  <span className="text-sm font-medium">Loading...</span>
                </div>
              </div>
            )}

            <div className="space-y-2  gap-2 items-start">
              {filteredNotifications.length > 0 ? (
                filteredNotifications.map((notification) => (
                  <div
                    key={notification._id}
                    className={`bg-white rounded-xl shadow-sm border transition-all duration-200 hover:shadow-md ${
                      notification.isRead
                        ? "border-gray-200"
                        : "border-l-4 border-l-[#070C44] bg-gradient-to-r from-[#070C44]/5 to-white"
                    }`}
                  >
                    <div className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-3 flex-1">
                          <div className="flex-shrink-0">
                            <div
                              className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                                notification.isRead
                                  ? "bg-gray-100"
                                  : "bg-[#2A3A6D]/10"
                              }`}
                            >
                              <Icon
                                icon={getTypeIcon(notification.type)}
                                className={`w-5 h-5 ${
                                  notification.isRead
                                    ? "text-gray-600"
                                    : "text-[#070C44]"
                                }`}
                              />
                            </div>
                          </div>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <h3 className="font-bold text-gray-900 text-sm">
                                {notification.title}
                              </h3>
                              {!notification.isRead && (
                                <div className="h-2 w-2 bg-[#2A3A6D] rounded-full flex-shrink-0 animate-pulse" />
                              )}
                            </div>

                            <div className="mb-1">
                              <p className="text-gray-700 text-xs leading-relaxed">
                                {notification.createdBy && (
                                  <span className="font-semibold text-xs text-[#070C44]">
                                    {notification.createdBy.name}:{" "}
                                  </span>
                                )}
                                {notification.body.message}
                              </p>
                            </div>

                            <div className="flex items-center gap-4 text-xs text-gray-500">
                              <div className="flex items-center gap-1.5">
                                <Icon
                                  icon="solar:clock-circle-bold"
                                  className="w-3 h-3"
                                />
                                <span className="font-medium">
                                  {formatTimeAgo(notification.createdAt)}
                                </span>
                              </div>

                              {notification.body.ticketId && (
                                <div className="flex items-center gap-1.5">
                                  <Icon
                                    icon="solar:ticket-bold"
                                    className="w-3 h-3"
                                  />
                                  <span className="font-medium">
                                    {notification.body.ticketId}
                                  </span>
                                </div>
                              )}

                              {notification.body.priority && (
                                <div
                                  className={`inline-flex items-center gap-1.5 px-2 py-1 rounded-lg text-xs font-medium border shadow-sm ${getPriorityColor(
                                    notification.body.priority
                                  )}`}
                                >
                                  <Icon
                                    icon={getPriorityIcon(
                                      notification.body.priority
                                    )}
                                    className="w-3 h-3"
                                  />
                                  {notification.body.priority}
                                </div>
                              )}
                            </div>

                            {notification.createdBy?.hotel && (
                              <div className="mt-2 flex items-center gap-1.5 text-xs text-gray-500">
                                <Icon
                                  icon="solar:buildings-2-bold"
                                  className="w-3 h-3"
                                />
                                <span>{notification.createdBy.hotel.name}</span>
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="flex items-center gap-1.5 ml-3">
                          {!notification.isRead && (
                            <button className="p-1.5 text-gray-400 hover:text-[#070C44] hover:bg-[#2A3A6D]/10 rounded-lg transition-all duration-200">
                              <Icon
                                icon="solar:check-circle-bold"
                                className="w-4 h-4"
                              />
                            </button>
                          )}

                          <button className="p-1.5 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200">
                            <Icon
                              icon="solar:menu-dots-bold"
                              className="w-4 h-4"
                            />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-12 bg-white rounded-xl shadow-sm">
                  <Icon
                    icon="solar:bell-off-bold"
                    className="w-12 h-12 text-gray-300 mx-auto mb-4"
                  />
                  <h3 className="text-lg font-bold text-gray-900 mb-2">
                    No notifications
                  </h3>
                  <p className="text-gray-500 text-sm">
                    {activeTab === "All"
                      ? "You're all caught up! No notifications to show."
                      : `No ${activeTab} notifications found.`}
                  </p>
                </div>
              )}
            </div>
          </div>

          {pagination && pagination.pages > 1 && (
            <div className="mt-6 bg-white rounded-xl shadow-md p-4">
              <div className="flex items-center justify-between">
                <div className="text-xs text-gray-600 font-medium">
                  Showing{" "}
                  <span className="font-bold text-[#070C44]">
                    {(currentPage - 1) * pageSize + 1}
                  </span>{" "}
                  to{" "}
                  <span className="font-bold text-[#070C44]">
                    {Math.min(currentPage * pageSize, pagination.total)}
                  </span>{" "}
                  of{" "}
                  <span className="font-bold text-[#070C44]">
                    {pagination.total}
                  </span>{" "}
                  notifications
                </div>

                <div className="flex items-center gap-2">
                  <button
                    onClick={handlePreviousPage}
                    disabled={currentPage === 1 || isFetching}
                    className="flex items-center gap-1.5 w-8 h-8 text-sm font-medium border border-gray-300 rounded-full hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  >
                    <Icon
                      icon="solar:arrow-left-bold"
                      className="w-4 h-4 mx-auto"
                    />
                  </button>

                  <div className="flex items-center gap-1">
                    {getPageNumbers().map((page, index) => (
                      <button
                        key={index}
                        onClick={() =>
                          typeof page === "number" && handlePageChange(page)
                        }
                        disabled={page === "..." || isFetching}
                        className={`w-8 h-8 text-sm font-medium rounded-full transition-all duration-200 ${
                          page === currentPage
                            ? "bg-[#2A3A6D] text-white shadow-md"
                            : page === "..."
                            ? "text-gray-400 cursor-default"
                            : "border border-gray-300 hover:bg-gray-50 text-gray-700"
                        }`}
                      >
                        {page}
                      </button>
                    ))}
                  </div>

                  <button
                    onClick={handleNextPage}
                    disabled={currentPage === pagination.pages || isFetching}
                    className="flex items-center gap-1.5 w-8 h-8 text-sm font-medium border border-gray-300 rounded-full hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  >
                    <Icon
                      icon="solar:arrow-right-bold"
                      className="w-4 h-4 mx-auto"
                    />
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </NotificationErrorBoundary>
  );
};

export default PushNotificationIndex;
