import { Icon } from "@iconify/react";
import { ChangeEvent } from "react";

interface Option {
  label: string;
  value: string;
}

interface NotificationFilterProps {
  onSearch: (value: string) => void;
  onStatusChange: (value: string) => void;
  onPriorityChange: (value: string) => void;
  onCategoryChange: (value: string) => void;
  statusOptions: Option[];
  priorityOptions: Option[];
  categoryOptions: Option[];
}

const NotificationFilter = ({
  onSearch,
  onStatusChange,
  onPriorityChange,
  onCategoryChange,
  statusOptions,
  priorityOptions,
  categoryOptions,
}: NotificationFilterProps) => {
  const handleSearchChange = (e: ChangeEvent<HTMLInputElement>) => {
    onSearch(e.target.value);
  };

  return (
    <div className="flex flex-col sm:flex-row gap-4 items-center">
      <div className="relative w-full sm:w-1/3">
        <input
          type="text"
          placeholder="Search notifications..."
          onChange={handleSearchChange}
          className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-[#070C44] focus:border-[#070C44] text-sm"
        />
        <Icon
          icon="mdi:magnify"
          className="w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2"
        />
      </div>
      <div className="flex gap-4 w-full sm:w-2/3">
        <select
          onChange={(e) => onStatusChange(e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-lg focus:ring-[#070C44] focus:border-[#070C44] text-sm"
        >
          {statusOptions.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        <select
          onChange={(e) => onPriorityChange(e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-lg focus:ring-[#070C44] focus:border-[#070C44] text-sm"
        >
          {priorityOptions.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        <select
          onChange={(e) => onCategoryChange(e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-lg focus:ring-[#070C44] focus:border-[#070C44] text-sm"
        >
          {categoryOptions.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
};

export default NotificationFilter;
