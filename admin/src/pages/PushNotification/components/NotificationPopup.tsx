"use client";

import { useState, useEffect, useRef } from "react";
import {
  useGetAllNotifications,
  useUpdatePushNotification,
} from "../../../server-action/API/push-notifications";
import { Icon } from "@iconify/react";
import { useNavigate } from "react-router-dom";

const NotificationPopup = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { data: notificationData, isLoading } = useGetAllNotifications();
  const { mutate: updateNotification } = useUpdatePushNotification();
  const popupRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  const notifications = (notificationData?.data?.notification || []).filter(
    (notification: any) => !notification.isRead
  );

  // Close popup when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        popupRef.current &&
        !popupRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [isOpen]);

  // Handle marking notification as read
  const handleMarkAsRead = (notificationId: any) => {
    updateNotification({
      id: notificationId,
      notificationData: { isRead: true } as any,
    });
  };

  // Handle marking all as read
  const handleMarkAllAsRead = () => {
    notifications.forEach((notification: any) => {
      updateNotification({
        id: notification._id,
        notificationData: { isRead: true } as any,
      });
    });
  };

  // Get notification type styling and icon
  const getNotificationStyle = (type: string) => {
    switch (type) {
      case "ticket_status_update":
        return {
          icon: "mdi:ticket-confirmation",
          iconColor: "text-blue-600",
          iconBg: "bg-blue-100",
        };
      case "new_order":
        return {
          icon: "mdi:shopping",
          iconColor: "text-green-600",
          iconBg: "bg-green-100",
        };
      case "payment_confirmed":
        return {
          bg: "bg-emerald-50",
          border: "border-l-emerald-400",
          icon: "mdi:credit-card-check",
          iconColor: "text-emerald-600",
          iconBg: "bg-emerald-100",
        };
      default:
        return {
          bg: "bg-gray-50",
          border: "border-l-gray-400",
          icon: "mdi:bell",
          iconColor: "text-gray-600",
          iconBg: "bg-gray-100",
        };
    }
  };

  // Format time ago
  const formatTimeAgo = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return "Just now";
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400)
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    return `${Math.floor(diffInSeconds / 86400)}d ago`;
  };

  return (
    <div className="relative">
      {/* Notification Bell Button */}
      <button
        className="relative rounded-full  transition-all duration-200 focus:outline-none  group"
        onClick={() => setIsOpen(!isOpen)}
      >
        <Icon icon="mdi:bell-outline" className="h-8 w-8" />
        {notifications.length > 0 && (
          <span className="absolute top-0 -right-1 h-5 w-5 bg-red-500 text-white border-2 border-[#2A3A6D] bg-red rounded-full flex items-center justify-center text-xs font-semibold  shadow-lg">
            {notifications.length > 9 ? "9+" : notifications.length}
          </span>
        )}
      </button>

      {/* Notification Popup */}
      {isOpen && (
        <div
          ref={popupRef}
          className="absolute right-0 mt-3 w-[400px] bg-white rounded-xl shadow-2xl border border-gray-200 z-50 transform transition-all duration-200 ease-out scale-100 opacity-100"
          style={{
            animation: "slideIn 0.2s ease-out",
          }}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-xl">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-full">
                <Icon icon="mdi:bell" className="h-5 w-5 text-[#2A3A6D]" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  Notifications
                </h3>
                <p className="text-sm text-gray-600">
                  {notifications.length} unread messages
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {notifications.length > 0 && (
                <button
                  onClick={handleMarkAllAsRead}
                  className="px-3 py-1.5 text-xs text-[#2A3A6D] hover:text-blue-800 hover:bg-blue-100 rounded-lg transition-all duration-150 font-medium flex items-center gap-1"
                >
                  <Icon icon="mdi:check-all" className="h-3 w-3" />
                  Mark all read
                </button>
              )}
              <button
                onClick={() => setIsOpen(false)}
                className="h-8 w-8 rounded-full hover:bg-gray-200 flex items-center justify-center transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-gray-300"
              >
                <Icon icon="mdi:close" className="h-4 w-4 text-gray-500" />
              </button>
            </div>
          </div>

          {/* Notifications List */}
          <div className="max-h-96 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
            {isLoading ? (
              <div className="p-8 text-center">
                <Icon
                  icon="mdi:loading"
                  className="h-8 w-8 text-[#2A3A6D] mx-auto mb-4 animate-spin"
                />
                <p className="text-gray-500 font-medium">
                  Loading notifications...
                </p>
              </div>
            ) : notifications.length > 0 ? (
              <div className="divide-y divide-gray-100">
                {notifications.map((notification: any) => {
                  const style = getNotificationStyle(notification.type);
                  return (
                    <div
                      key={notification._id}
                      className={`p-4 hover:bg-gray-50 transition-all duration-150 border-l-4 ${style.border} ${style.bg} group`}
                    >
                      <div className="flex items-start gap-3">
                        <div
                          className={`mt-1 p-2 rounded-full ${style.iconBg} group-hover:scale-110 transition-transform duration-200`}
                        >
                          <Icon
                            icon={style.icon}
                            className={`h-4 w-4 ${style.iconColor}`}
                          />
                        </div>

                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between gap-2 mb-2">
                            <h4 className="text-sm font-semibold text-gray-900 truncate">
                              {notification.title}
                            </h4>
                            <div className="flex items-center gap-1 text-xs text-gray-500 shrink-0">
                              <Icon
                                icon="mdi:clock-outline"
                                className="h-3 w-3"
                              />
                              {formatTimeAgo(notification.createdAt)}
                            </div>
                          </div>

                          <p className="text-sm text-gray-700 leading-relaxed mb-3 line-clamp-2">
                            {notification.body.message}
                          </p>

                          <div className="flex items-center justify-between">
                            <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700 border">
                              <Icon
                                icon="mdi:tag-outline"
                                className="h-3 w-3 mr-1"
                              />
                              {notification.type
                                .replace("_", " ")
                                .replace(/\b\w/g, (l: string) =>
                                  l.toUpperCase()
                                )}
                            </span>

                            <button
                              onClick={() => handleMarkAsRead(notification._id)}
                              className="inline-flex items-center px-3 py-1.5 text-xs text-[#2A3A6D] hover:text-blue-800 hover:bg-blue-100 rounded-lg transition-all duration-150 font-medium border border-blue-200 hover:border-blue-300"
                            >
                              <Icon
                                icon="mdi:check-circle-outline"
                                className="h-3 w-3 mr-1"
                              />
                              Mark read
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="p-8 text-center">
                <div className="mx-auto w-16 h-16 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-full flex items-center justify-center mb-4">
                  <Icon
                    icon="mdi:bell-check"
                    className="h-8 w-8 text-blue-500"
                  />
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">
                  All caught up!
                </h4>
                <p className="text-sm text-gray-500">
                  No unread notifications at the moment
                </p>
              </div>
            )}
          </div>

          {/* Footer */}
          {notifications.length > 0 && (
            <div className="p-4 border-t border-gray-100 bg-gray-50 rounded-b-xl">
              <button
                className="w-full py-2.5 text-sm text-gray-700 hover:text-gray-900 hover:bg-white rounded-lg transition-all duration-150 flex items-center justify-center gap-2 font-medium border border-gray-200 hover:border-gray-300"
                onClick={() => setIsOpen(false)}
                onMouseDown={() => {
                  notifications.forEach((notification: any) =>
                    handleMarkAsRead(notification._id)
                  );
                  navigate("/push-notifications");
                }}
              >
                <Icon icon="mdi:eye" className="h-4 w-4" />
                View all notifications
              </button>
            </div>
          )}
        </div>
      )}

      <style>{`
        @keyframes slideIn {
          from {
            opacity: 0;
            transform: translateY(-10px) scale(0.95);
          }
          to {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }

        .scrollbar-thin::-webkit-scrollbar {
          width: 6px;
        }

        .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
          background-color: #d1d5db;
          border-radius: 3px;
        }

        .scrollbar-track-gray-100::-webkit-scrollbar-track {
          background-color: #f3f4f6;
        }

        .line-clamp-2 {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      `}</style>
    </div>
  );
};

export default NotificationPopup;
