import { useCallback, useMemo, useState } from "react";
import { PopupModal } from "../../../components";
import Header from "../../../components/Header";
import MasterTable from "../../../layouts/Table/MasterTable";
import { TableAction } from "../../../layouts/Table/TableAction";
import ChannelCategoryForm from "./Components/ChannelCategoryForm";

const ChannelCategoryIndex = () => {
  const [showPopup, setShowPopup] = useState(false);
  const [editData, setEditData] = useState(null);

  const openModal = () => setShowPopup(true);
  const closeModal = () => setShowPopup(false);
  const togglePopup = useCallback(() => setShowPopup((prev) => !prev), []);

  const columns = useMemo(
    () => [
      { title: "#", key: "index" },
      { title: "Channle Name", key: "name" },
      { title: "Status", key: "status" },
      { title: "Actions", key: "action" },
    ],
    []
  );

  const rows = [
    {
      key: "1",
      index: 1,
      name: "Booking.com",
      status: <p>Bg red</p>,
      action: (
        <TableAction
          onEdit={() => {
            setEditData(rows as any);
            setShowPopup(true);
          }}
          onDelete={() => {}}
        />
      ),
    },
    {
      key: "2",
      index: 2,
      name: "Airbnb",
      status: true,
      action: (
        <TableAction
          onEdit={() => {
            setEditData(rows as any);
            setShowPopup(true);
          }}
          onDelete={() => {}}
        />
      ),
    },
  ];

  return (
    <div>
      {showPopup && (
        <PopupModal onClose={closeModal}>
          <ChannelCategoryForm onClose={togglePopup} editData={editData} />
        </PopupModal>
      )}
      <Header title="Channel Category" onAddClick={openModal} />
      <MasterTable loading={false} columns={columns} rows={rows as any} />
    </div>
  );
};

export default ChannelCategoryIndex;
