import { Form, FormikProvider, useFormik } from "formik";
import { GlobalForm } from "../../../../components/GlobalForm/GlobalFormComponent";
import HeadingPopup from "../../../GuestManagement/components/HeadingPopup";
import { ActionButton } from "../../../../components/ActionButton";
import { ChannelCategoryFormData } from "./ChannelCategoryFormData";

interface IChannelFormProps {
  onClose: () => void;
  editData: null;
}
const ChannelCategoryForm = ({ onClose, editData }: IChannelFormProps) => {
  const formik = useFormik({
    initialValues: {},
    enableReinitialize: true,
    onSubmit: async (values: any) => {
      console.log(values, "values");
    },
  });
  const { handleSubmit, getFieldProps } = formik;

  return (
    <HeadingPopup
      onClose={onClose}
      className="w-full max-w-screen-sm"
      heading={`${!editData ? "Add" : "Update"}  Channel Category`}
    >
      <FormikProvider value={formik}>
        <Form onSubmit={handleSubmit}>
          <div className="grid grid-cols-2 gap-2">
            <GlobalForm
              formDatails={ChannelCategoryFormData}
              getFieldProps={getFieldProps}
            />
          </div>
          <ActionButton onCancel={onClose} onSubmit={handleSubmit} />
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default ChannelCategoryForm;
