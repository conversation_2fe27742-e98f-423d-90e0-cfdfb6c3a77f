import { useState } from "react";
import { Card, CardContent } from "../../../components/Card";
import Header from "../../../components/Header";
import { TabData } from "../../../components/TabData";
import InventoryStatus from "./components/InventoryStatus";
import CleanItems from "./components/CleanItems";
import LaundryProcessing from "./components/LaundryProcessing";
import AddInventoryItemForm from "./components/AddInventoryItemForm";

const HousekeepingInventory = () => {
  const [selectedTab, setSelectedTab] = useState("status");
  const [showAddForm, setShowAddForm] = useState(false);

  const tabData = [
    {
      title: "Inventory Status",
      value: "status",
    },
    {
      title: "Clean Items",
      value: "clean",
    },
    {
      title: "Laundry Processing",
      value: "laundry",
    },
  ];

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between items-center">
        <Header title="Housekeeping Inventory" showButton={false} />
        <button
          onClick={() => {
            setShowAddForm(true);
          }}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Add Inventory Item
        </button>
      </div>

      <TabData
        selectedTabData={selectedTab}
        setSelectedTabData={setSelectedTab}
        tabData={tabData}
      />

      <Card className="bg-white">
        <CardContent>
          {selectedTab === "status" && <InventoryStatus />}
          {selectedTab === "clean" && <CleanItems />}
          {selectedTab === "laundry" && <LaundryProcessing />}
        </CardContent>
      </Card>

      {showAddForm && (
        <>
          <AddInventoryItemForm
            onClose={() => {
              console.log("AddInventoryItemForm onClose called");
              setShowAddForm(false);
            }}
          />
        </>
      )}
    </div>
  );
};

export default HousekeepingInventory;
