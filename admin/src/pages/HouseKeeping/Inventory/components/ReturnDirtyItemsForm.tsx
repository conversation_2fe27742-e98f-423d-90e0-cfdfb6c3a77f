import { Form, FormikProvider, useFormik } from "formik";
import * as Yup from "yup";
import HeadingPopup from "../../../GuestManagement/components/HeadingPopup";
import { FormField } from "../../../BookingManagement/components/ReservationCustomForm";
import { useReturnDirtyItems } from "../../../../server-action/API/inventoryApi";
import { IHousekeepingInventoryItem } from "../../../../Interface/housekeepingInventory.interface";
import { toast } from "react-toastify";

interface ReturnDirtyItemsFormProps {
  item: IHousekeepingInventoryItem;
  onClose: () => void;
}

const ReturnDirtyItemsForm: React.FC<ReturnDirtyItemsFormProps> = ({
  item,
  onClose,
}) => {
  const { mutateAsync: returnDirtyItems, isPending } = useReturnDirtyItems();

  // For dirty items, we use the quantity directly since status is 'dirty'
  const quantity = item.quantity || 0;

  const validationSchema = Yup.object({
    quantity: Yup.number()
      .required("Quantity is required")
      .positive("Quantity must be positive")
      .max(
        quantity,
        `Maximum dirty items available is ${quantity}`
      ),
  });

  const formik = useFormik({
    initialValues: {
      itemId: item.item?._id || item.id || '',
      itemName: item.item?.name || 'Unknown Item',
      availableQuantity: quantity,
      quantity: 1,
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        // Get the correct item ID
        const itemId = item.item?._id || item.id || values.itemId;

        console.log('Submitting return dirty data:', {
          itemId: itemId,
          quantity: values.quantity,
        });

        await returnDirtyItems({
          itemId: itemId,
          quantity: values.quantity,
        });
        toast.success("Dirty items returned successfully");
      } catch (error) {
        toast.error("Failed to return dirty items: " + error);
      }
      onClose();
    },
  });

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <HeadingPopup
          heading="Return Dirty Items to Laundry"
          onClose={onClose}
        />
        <FormikProvider value={formik}>
          <Form>
            <div className="space-y-4 mt-4">
              <FormField
                label="Item Name"
                name="itemName"
                type="text"
                disabled={true}
                formik={formik}
              />
              <FormField
                label="Available Dirty Quantity"
                name="availableQuantity"
                type="number"
                disabled={true}
                formik={formik}
              />
              <FormField
                label="Quantity to Return"
                name="quantity"
                type="number"
                min="1"
                max={quantity.toString()}
                formik={formik}
              />

              <div className="flex justify-end space-x-2 mt-6">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isPending}
                  className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
                >
                  {isPending ? "Returning..." : "Return Dirty Items"}
                </button>
              </div>
            </div>
          </Form>
        </FormikProvider>
      </div>
    </div>
  );
};

export default ReturnDirtyItemsForm;
