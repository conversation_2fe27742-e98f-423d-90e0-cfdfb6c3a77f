/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/ban-ts-comment */
import { Form, FormikProvider, useFormik } from "formik";
import * as Yup from "yup";
import HeadingPopup from "../../../GuestManagement/components/HeadingPopup";
import { FormField } from "../../../BookingManagement/components/ReservationCustomForm";
import { toast } from "react-toastify";
import { useState, useEffect } from "react";
import {
  useAddInventoryItem,
  useGetInventoryCategories,
  useGetInventoryStores,
} from "../../../../server-action/API/inventoryApi";

interface AddInventoryItemFormProps {
  onClose: () => void;
}

const AddInventoryItemForm: React.FC<AddInventoryItemFormProps> = ({
  onClose,
}) => {
  const [stores, setStores] = useState<
    { id: string; name?: string; type: string }[]
  >([]);
  const [categories, setCategories] = useState<{ _id: string; name: string }[]>(
    []
  );
  const { mutateAsync: addInventoryItem, isPending: isAddingItem } =
    useAddInventoryItem();
  const {
    data: storesData,
    isLoading: isLoadingStores,
    error: storesError,
  } = useGetInventoryStores();
  const {
    data: categoriesData,
    isLoading: isLoadingCategories,
    error: categoriesError,
  } = useGetInventoryCategories();

  const isLoading = isAddingItem || isLoadingStores || isLoadingCategories;

  // Fetch stores and categories
  useEffect(() => {
    if (storesError) {
      console.error("Error fetching stores:", storesError);
      toast.error("Failed to load stores data");
      // Fallback to mock data
      setStores([
        { id: "1", name: "Main Store", type: "main" },
        { id: "2", name: "Laundry", type: "laundry" },
        { id: "3", name: "Floor Store", type: "floor" },
      ]);
    } else if (storesData) {
      setStores(storesData);
    }

    if (categoriesError) {
      console.error("Error fetching categories:", categoriesError);
      toast.error("Failed to load categories data");
      // Fallback to mock data
      setCategories([
        { _id: "1", name: "Toiletries" },
        { _id: "2", name: "Linens" },
        { _id: "3", name: "Cleaning Supplies" },
      ]);
    } else if (categoriesData) {
      setCategories(categoriesData);
    }
  }, [storesData, categoriesData, storesError, categoriesError]);

  const validationSchema = Yup.object({
    name: Yup.string().required("Item name is required"),
    description: Yup.string(),
    categoryId: Yup.string().required("Category is required"),
    storeId: Yup.string().required("Store is required"),
    quantity: Yup.number()
      .required("Quantity is required")
      .positive("Quantity must be positive"),
    unit: Yup.string(),
    status: Yup.string().required("Status is required"),
  });

  const formik = useFormik({
    initialValues: {
      name: "",
      description: "",
      categoryId: "",
      storeId: "",
      quantity: 1,
      unit: "Pcs",
      status: "clean",
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        console.log("Submitting inventory item:", values);

        await addInventoryItem({
          name: values.name,
          description: values.description,
          categoryId: values.categoryId,
          storeId: values.storeId,
          quantity: values.quantity,
          unit: values.unit,
          // @ts-ignore - status is used in the API but not in the interface
          status: values.status,
        });

        toast.success("Inventory item added successfully");
        onClose();
      } catch (error) {
        console.error("Error adding inventory item:", error);
        toast.error("Failed to add inventory item: " + error);
      }
    },
  });

  // Add console logs for debugging
  console.log("AddInventoryItemForm rendering");
  console.log("Stores:", stores);
  console.log("Categories:", categories);
  console.log("Loading states:", {
    isAddingItem,
    isLoadingStores,
    isLoadingCategories,
  });

  return (
    <HeadingPopup
      heading="Add Inventory Item"
      onClose={onClose}
      className="w-full max-w-md"
    >
      <FormikProvider value={formik}>
        <Form>
          <div className="space-y-4 mt-4">
            <FormField
              label="Item Name"
              name="name"
              type="text"
              formik={formik}
            />
            <FormField
              label="Description"
              name="description"
              type="textarea"
              formik={formik}
            />

            <div className="form-control w-full">
              <label className="label">
                <span className="label-text">Category</span>
              </label>
              <select
                name="categoryId"
                value={formik.values.categoryId}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                className="select select-bordered w-full"
              >
                <option value="">Select Category</option>
                {categories.map((category) => (
                  <option key={category._id} value={category._id}>
                    {category.name}
                  </option>
                ))}
              </select>
              {formik.touched.categoryId && formik.errors.categoryId && (
                <div className="text-red-500 text-sm mt-1">
                  {formik.errors.categoryId}
                </div>
              )}
            </div>

            <div className="form-control w-full">
              <label className="label">
                <span className="label-text">Store</span>
              </label>
              <select
                name="storeId"
                value={formik.values.storeId}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                className="select select-bordered w-full"
              >
                <option value="">Select Store</option>
                {stores.map((store) => (
                  <option key={store.id} value={store.id}>
                    {store.name || store.type}
                  </option>
                ))}
              </select>
              {formik.touched.storeId && formik.errors.storeId && (
                <div className="text-red-500 text-sm mt-1">
                  {formik.errors.storeId}
                </div>
              )}
            </div>

            <FormField
              label="Quantity"
              name="quantity"
              type="number"
              min="1"
              formik={formik}
            />

            <FormField label="Unit" name="unit" type="text" formik={formik} />

            <div className="form-control w-full">
              <label className="label">
                <span className="label-text">Status</span>
              </label>
              <select
                name="status"
                value={formik.values.status}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                className="select select-bordered w-full"
              >
                <option value="clean">Clean</option>
                <option value="dirty">Dirty</option>
              </select>
              {formik.touched.status && formik.errors.status && (
                <div className="text-red-500 text-sm mt-1">
                  {formik.errors.status}
                </div>
              )}
            </div>

            <div className="flex justify-end space-x-2 mt-6">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
              >
                {isAddingItem
                  ? "Adding..."
                  : isLoading
                  ? "Loading..."
                  : "Add Item"}
              </button>
            </div>
          </div>

          <div className="form-control w-full">
            <label className="label">
              <span className="label-text">Store</span>
            </label>
            <select
              name="storeId"
              value={formik.values.storeId}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              className="select select-bordered w-full"
            >
              <option value="">Select Store</option>
              {stores.map((store) => (
                <option key={store.id} value={store.id}>
                  {store.name || store.type}
                </option>
              ))}
            </select>
            {formik.touched.storeId && formik.errors.storeId && (
              <div className="text-red-500 text-sm mt-1">
                {formik.errors.storeId}
              </div>
            )}
          </div>

          <FormField
            label="Quantity"
            name="quantity"
            type="number"
            min="1"
            formik={formik}
          />

          <FormField label="Unit" name="unit" type="text" formik={formik} />

          <div className="form-control w-full">
            <label className="label">
              <span className="label-text">Status</span>
            </label>
            <select
              name="status"
              value={formik.values.status}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              className="select select-bordered w-full"
            >
              <option value="clean">Clean</option>
              <option value="dirty">Dirty</option>
            </select>
            {formik.touched.status && formik.errors.status && (
              <div className="text-red-500 text-sm mt-1">
                {formik.errors.status}
              </div>
            )}
          </div>

          <div className="flex justify-end space-x-2 mt-6">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
            >
              {isAddingItem
                ? "Adding..."
                : isLoading
                ? "Loading..."
                : "Add Item"}
            </button>
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default AddInventoryItemForm;
