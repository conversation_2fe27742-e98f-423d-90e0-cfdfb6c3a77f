import { Form, FormikProvider, useFormik } from "formik";
import { PopupModal } from "../../../../components";
import { Icon } from "@iconify/react";
import { FormField } from "../../../BookingManagement/components/ReservationCustomForm";
import {
  useCreateLostFoundModule,
  useUpdateLostFoundModule,
} from "../../../../server-action/API/Lost & found/Lost&Found";
import { useGetAllUser } from "../../../../server-action/API/user.tsx";
import { useGetAllRoom } from "../../../../server-action/API/Room/room";
import get from "lodash/get";
import moment from "moment";
import { useMemo } from "react";
import FileUpload from "../../../../components/UploadFile";

// ---------- Types ----------
type User = {
  _id: string;
  name: string;
};
import * as yup from "yup";

const validationSchema = yup.object().shape({
  date: yup.string().required("Date is required"),
  time: yup.string().required("Time is required"),
  room: yup.string().required("Room is required"),
  item: yup.string().required("Item is required"),
  reportedBy: yup.string().required("Reported By is required"),
  description: yup.string().required("Description is required"),
  status: yup.string().required("Status is required"),
});

type DropdownOption = {
  label: string | number;
  value: string;
};

type LostFoundFormValues = {
  name: string;
  date: string;
  time: string;
  room: string;
  item: string;
  reportedBy: string;
  description: string;
  images: (File | string)[];
  formData?: any;
  existingImages: string[];
  status: string;
};

type LostFoundFormProps = {
  close: () => void;
  edit?: boolean;
  editData?: Partial<LostFoundFormValues> & { _id?: string; images?: string[] };
};

const LostFoundForm: React.FC<LostFoundFormProps> = ({
  close,
  edit = false,
  editData,
}) => {
  const { data, isSuccess } = useGetAllRoom();
  const { mutate: createLostFound, isPending } = useCreateLostFoundModule();
  const { mutate: updateLostFound, isPending: updatePending } =
    useUpdateLostFoundModule();
  const { data: users, isSuccess: isUserSuccess } = useGetAllUser();

  const roomOptions = isSuccess
    ? data.map((item: any) => ({ label: item.roomNo, value: item._id }))
    : [];

  const userOptions: DropdownOption[] = useMemo(
    () =>
      isUserSuccess
        ? users.map((user: User) => ({
            label: user.name,
            value: user._id,
          }))
        : [],
    [isUserSuccess, users]
  );

  // Prepare image paths for display (with full URL) but keep original paths for backend
  const displayImagePaths = useMemo(() => {
    if (edit && editData?.images && editData.images.length > 0) {
      return editData.images.map(
        (img) => `https://hotel-api.webstudiomatrix.com/${img}`
      );
    }
    return [];
  }, [edit, editData?.images]);

  const formik = useFormik<LostFoundFormValues>({
    initialValues: {
      name: edit ? get(editData, "name", "") : "",
      date: edit ? moment(get(editData, "date", "")).format("YYYY-MM-DD") : "",
      time: edit ? get(editData, "time", "") : "",
      room: edit ? get(editData, "room._id", "") : "",
      item: edit
        ? Array.isArray(editData?.item)
          ? editData.item[0]
          : editData?.item || ""
        : "",
      status: edit ? get(editData, "status", "") : "",

      reportedBy: edit ? get(editData, "reportedBy._id", "") : "",
      description: edit ? get(editData, "description", "") : "",
      images: edit ? get(editData, "images", []) : [],
      formData: edit ? get(editData, "formData", "") : "",
      existingImages: [],
    },
    validationSchema: validationSchema,
    enableReinitialize: true,
    onSubmit: async (values) => {
      console.log(values, "values");
      const formData = new FormData();

      formData.append("name", values.name);
      formData.append("date", values.date);
      formData.append("time", values.time);
      formData.append("room", values.room);
      formData.append("item", values.item);
      formData.append("status", values.status);
      formData.append("reportedBy", values.reportedBy);
      formData.append("description", values.description);

      // Handle existing images from DB and newly uploaded files
      if (edit && values.images) {
        const existingImages = values.images.filter(
          (img) => typeof img === "string"
        );
        const newImages = values.images.filter((img) => img instanceof File);

        // Process existing images
        existingImages.forEach((img) => {
          // Extract just the path part if it's a full URL
          let imagePath = img;
          if (
            typeof img === "string" &&
            img.startsWith("https://hotel-api.webstudiomatrix.com/")
          ) {
            imagePath = img.replace(
              "https://hotel-api.webstudiomatrix.com/",
              ""
            );
          }
          formData.append(`existingImages`, imagePath);
        });

        // Process new file uploads
        newImages.forEach((img, _) => {
          formData.append(`images`, img);
        });
      } else if (values.images && values.images.length > 0) {
        // For new entries, just append all files
        values.images.forEach((img, _) => {
          if (img instanceof File) {
            formData.append(`images`, img);
          }
        });
      }

      if (edit && editData?._id) {
        await updateLostFound({ id: editData._id, body: formData });
      } else {
        await createLostFound(formData);
      }

      close();
    },
  });

  // Handle file uploads
  const handleImageChange = (files: (File | string)[]) => {
    console.log(files, "selected images");

    formik.setFieldValue("images", files);
    console.log(formik.values, "handle file after");
  };

  return (
    <PopupModal
      onClose={close}
      classname="w-full max-w-screen-sm h-[580px] overflow-scroll"
    >
      <div className="relative flex items-center justify-between bg-[#F1F6FD] p-4">
        <h1 className="w-full text-center font-semibold">Lost & Found</h1>
        <button
          className="absolute top-4 right-4 p-1 bg-black rounded-full"
          onClick={close}
        >
          <Icon
            icon="fluent-mdl2:calculator-multiply"
            width="14"
            height="14"
            className="text-white"
          />
        </button>
      </div>

      <div className="p-4">
        <FormikProvider value={formik}>
          <Form onSubmit={formik.handleSubmit}>
            <div className="grid grid-cols-2 gap-4">
              <FormField name="date" label="Date" type="date" formik={formik} />
              <FormField name="time" label="Time" type="time" formik={formik} />
              <FormField
                name="room"
                label="Room Number"
                type="dropdown"
                options={roomOptions as any}
                formik={formik}
              />
              <FormField
                name="item"
                label="Item Name"
                type="text"
                formik={formik}
              />
              <FormField
                name="reportedBy"
                label="Reported By"
                type="dropdown"
                options={userOptions as any}
                formik={formik}
              />
              <FormField
                name="description"
                label="Description"
                type="text"
                formik={formik}
              />
              <FormField
                name="status"
                label="Status"
                type="dropdown"
                formik={formik}
                options={[
                  { label: "Reported", value: "reported" },
                  { label: "Claimed", value: "claimed" },
                ]}
              />
            </div>

            <div className="mt-4">
              <h3 className="font-medium mb-2">Images</h3>
              <FileUpload
                multiple={true}
                onChange={handleImageChange}
                defaultImage={displayImagePaths}
              />
            </div>

            <div className="flex justify-end mt-4">
              <button
                type="submit"
                disabled={isPending || updatePending}
                className="py-2 px-6 bg-[#163381] text-white rounded-md"
              >
                {edit
                  ? updatePending
                    ? "Updating..."
                    : "Update"
                  : isPending
                  ? "Submitting..."
                  : "Submit"}
              </button>
            </div>
          </Form>
        </FormikProvider>
      </div>
    </PopupModal>
  );
};

export default LostFoundForm;
