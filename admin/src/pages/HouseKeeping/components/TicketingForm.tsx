import { Form, FormikProvider, useFormik } from "formik";
import React from "react";
import { FormField } from "../../BookingManagement/components/ReservationCustomForm";
import HeadingPopup from "../../GuestManagement/components/HeadingPopup";
import { ticketingFormObj } from "./houseObj";

interface GuestLaundryFormProps {
  close: () => void;
}
const TicketingForm: React.FC<GuestLaundryFormProps> = ({ close }) => {
  const formik = useFormik({
    initialValues: {
      category: "",
      room: "",
      priority: "",
    },
    onSubmit: (values) => {
      console.log(values);
    },
  });
  return (
    <HeadingPopup
      heading="Service Ticketing"
      className="w-full max-w-screen-sm"
      onClose={close}
    >
      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit}>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-2">
            {ticketingFormObj.map(
              ({ name, label, type, placeholder, options }) => (
                <FormField
                  key={name}
                  name={name}
                  label={label}
                  type={type}
                  placeholder={placeholder}
                  options={options}
                  formik={formik}
                />
              )
            )}
          </div>
          <div className="flex justify-end mt-4">
            <button className="px-4 py-2 text-white bg-[#163381] rounded-md">
              Create
            </button>
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default TicketingForm;
