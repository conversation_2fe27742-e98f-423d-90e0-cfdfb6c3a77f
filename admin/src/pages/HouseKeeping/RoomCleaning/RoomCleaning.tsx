import MasterTable from "../../../layouts/Table/MasterTable";
import { TableAction } from "../../../layouts/Table/TableAction";
import Header from "../../../components/Header";
import { useState } from "react";
import { PopupModal } from "../../../components";
import RoomCleaningForm from "./Component/RoomCleaningForm";

import {
  useGetAllTickets,
  useDeleteTicket,
} from "../../../server-action/API/Ticket/Ticket";
import get from "lodash/get";
import { Status } from "../../../components/Status";
import { DateForamter } from "../../../components/DateFormater";
import { TimeFormater } from "../../../components/TimeFormater";

interface FormState {
  state: boolean;
  edit: boolean;
  editData?: any;
}

interface Ticket {
  _id: string;
  room?: {
    roomNo?: string;
  };
  user?: {
    name?: string;
  };
  date: string;
  time: string;
}

const RoomCleaning = () => {
  const [showPopup, setShowPopup] = useState("");
  const { data, isLoading, isSuccess } = useGetAllTickets({
    ticketCategory: "cleaning",
  });
  const { mutate: deleteModule } = useDeleteTicket();
  const [formState, setFormState] = useState<FormState>({
    state: false,
    edit: true,
    editData: null,
  });

  console.log(data, "Cleaning");

  const tableData = {
    columns: [
      {
        title: "Room Number",
        key: "room",
      },
      {
        title: "Assigned staff",
        key: "user",
      },
      {
        title: "Cleaning Date & Time",
        key: "cleaningDate",
      },
      {
        title: "Additional Notes",
        key: "notes",
      },
      {
        title: "Status",
        key: "ticketStatus",
      },
      {
        title: "Actions",
        key: "action",
      },
    ],
    rows: isSuccess
      ? (data as Ticket[])?.map((item) => ({
          id: item._id ?? "",
          room: get(item, "room.roomNo", "-"),
          user: get(item, "user.name", "-"),
          cleaningDate: `${DateForamter(item?.date)} / ${TimeFormater(
            item?.date
          )} `,
          // cleaningDate: (
          //   <p>
          //     {item?.date}
          //     <br />
          //     {item?.time}
          //   </p>
          // ),
          notes: get(item, "notes", "-"),
          ticketStatus: <Status status={get(item, "ticketStatus", "-")} />,
          action: (
            <TableAction
              onShow={() => {
                setFormState({
                  edit: false,
                  state: false,
                  editData: item,
                });
                setShowPopup("view");
              }}
              onEdit={() =>
                setFormState({
                  edit: true,
                  state: true,
                  editData: item,
                })
              }
              onDelete={() => {
                deleteModule(item._id as string);
              }}
            />
          ),
        }))
      : [],
  };

  return (
    <div>
      {/* {showPopup === "Room Cleaning" && (
        <RoomCleaningForm close={() => setShowPopup("")} />
      )} */}

      {showPopup === "view" && formState.editData && (
        <PopupModal onClose={() => setShowPopup("")}>
          <div className="w-full max-w-3xl mx-auto">
            {/* Header */}
            <div className="relative flex items-center justify-between bg-[#F1F6FD] p-4 rounded-t-lg">
              <h1 className="w-full text-center text-xl font-bold text-gray-800">
                Room Cleaning Details
              </h1>
              <button
                className="absolute top-4 right-4 p-2 bg-gray-800 rounded-full hover:bg-gray-700 transition-colors"
                onClick={() => setShowPopup("")}
                aria-label="Close modal"
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="text-white"
                >
                  <path
                    d="M2 2L14 14M2 14L14 2"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                  />
                </svg>
              </button>
            </div>

            <div className="p-6 bg-white rounded-b-lg shadow-lg">
              <div className="space-y-6">
                {/* Cleaning Date & Time */}
                <div className="border-b pb-4">
                  <div className="flex items-center space-x-4">
                    <p className="text-sm text-gray-600 font-medium">
                      Cleaning Date & Time:
                    </p>
                    <p className="text-base font-semibold text-gray-900">
                      {`${get(formState.editData, "date", "-")} / ${get(
                        formState.editData,
                        "time",
                        "-"
                      )}`}
                    </p>
                  </div>
                </div>

                {/* Staff, Room, and Status */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-8 gap-y-4 border-b pb-4">
                  <div className="flex items-center space-x-4">
                    <p className="text-sm text-gray-600 font-medium">
                      Assigned Staff:
                    </p>
                    <p className="text-base font-semibold text-gray-900">
                      {get(formState.editData, "user.name", "-")}
                    </p>
                  </div>
                  <div className="flex items-center space-x-4">
                    <p className="text-sm text-gray-600 font-medium">
                      Room Number:
                    </p>
                    <p className="text-base font-semibold text-gray-900">
                      {get(formState.editData, "room.roomNo", "-")}
                    </p>
                  </div>
                  <div className="flex items-center space-x-4">
                    <p className="text-sm text-gray-600 font-medium">Status:</p>
                    <span
                      className={`inline-flex items-center px-3 py-1 border text-sm font-semibold rounded-full 
                  ${
                    get(formState.editData, "ticketStatus", "-") === "Done"
                      ? "bg-green-100 text-green-800"
                      : get(formState.editData, "ticketStatus", "-") ===
                        "In Progress"
                      ? "bg-yellow-100 text-yellow-800"
                      : "bg-gray-100 text-gray-800"
                  }`}
                    >
                      {get(formState.editData, "ticketStatus", "-")}
                    </span>
                  </div>
                </div>

                {/* Additional Notes */}
                <div className="border-b pb-4">
                  <div className="flex items-start space-x-4">
                    <p className="text-sm text-gray-600 font-medium">
                      Additional Notes:
                    </p>
                    <p className="text-base font-semibold text-gray-900 flex-1">
                      {get(formState.editData, "notes", "-")}
                    </p>
                  </div>
                </div>

                {/* Areas to Clean */}
                <div className="space-y-4">
                  <h2 className="text-sm text-gray-600 font-medium">
                    Areas to Clean:
                  </h2>
                  {formState.editData.areasToClean &&
                  formState.editData.areasToClean.length > 0 ? (
                    <ul className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                      {formState.editData.areasToClean.map(
                        (
                          area: { title: string; status: string },
                          index: number
                        ) => (
                          <li
                            key={index}
                            className="flex items-center justify-between border p-3 bg-gray-50 rounded-md hover:bg-gray-100 transition-colors"
                          >
                            <span className="text-base text-gray-700">
                              {area.title}
                            </span>
                            <span
                              className={`text-sm font-semibold px-3 py-1 rounded-full 
                      ${
                        area.status === "Done" ||
                        area.status === "Cleaned" ||
                        area.status === "Yes"
                          ? "bg-green-100 text-green-800"
                          : area.status === "Removed"
                          ? "bg-blue-100 text-blue-800"
                          : "bg-red-100 text-red-800"
                      }`}
                            >
                              {area.status}
                            </span>
                          </li>
                        )
                      )}
                    </ul>
                  ) : (
                    <p className="text-base text-gray-500">
                      No areas to clean specified.
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>
        </PopupModal>
      )}

      <div className="">
        <Header title="Room Cleaning" showButton={false} />
        <MasterTable
          columns={tableData?.columns}
          rows={tableData.rows}
          loading={isLoading}
        />
        {formState.state && (
          <RoomCleaningForm
            close={() =>
              setFormState({ state: false, edit: false, editData: null })
            }
            edit={formState.edit}
            editData={formState.editData}
          />
        )}
      </div>
    </div>
  );
};

export default RoomCleaning;
