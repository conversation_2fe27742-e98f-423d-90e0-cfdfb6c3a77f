import { Icon } from "@iconify/react/dist/iconify.js";
import MasterTable from "../../../layouts/Table/MasterTable";
const handlePrint = () => {
  const printableContent =
    document?.querySelector(".printable-section")?.outerHTML;
  const originalContent = document.body.innerHTML;
  document.body.innerHTML = `
      <style>
        @media print {
          @page {
            margin: 0.5;
          }
          body {
            margin: 0.5;
          }
        }
      </style>
      ${printableContent}
    `;
  window.print();
  document.body.innerHTML = originalContent;
  window.location.reload();
};

export const BillDetails = ({
  roomData,
  setShowBillModal,
  setShowPaymentModal,
}: {
  roomData: any;
  setShowBillModal: (val: boolean) => void;
  setShowPaymentModal: (val: boolean) => void;
}) => {
  // const [showPaymentModal, setShowPaymentModal] = useState(false);
  // const { data: userData } = useGetAllUser({ role: "guest" });
  // const { data: bookingData } = useGetBookings();

  console.log(roomData, "roomdata");

  const tableData = {
    column: [
      { title: "Details", key: "details" },
      { title: "Price", key: "price" },
      { title: "qty", key: "qty" },
      { title: "Total", key: "total" },
    ],
    rows: [],
  };

  return (
    <div className="w-[60vw] h-[90vh] overflow-scroll mx-auto p-6 bg-white">
      {/* Header */}
      <section className="printable-section">
        <div className="flex justify-between items-center mb-8">
          <img src="/logo.png" alt="Logo" className="h-[50px]" />
          <div className="bg-[#EBFEF4] px-4 py-3 rounded-md">
            <span className="font-medium">INVOICE</span>
          </div>
        </div>

        {/* Invoice Info */}
        <section className="flex justify-between items-center mb-6">
          <div>
            <div className="mb-2 font-medium">
              Invoice Number: <span className="font-thin">81</span>
            </div>
            <div className="mb-2 font-medium">
              Date: <span className="font-thin">{roomData?.updatedAt}</span>
            </div>
            <div className="mb-2 font-medium">
              Payment Method: <span className="font-thin">Cash</span>
            </div>
          </div>
          <button
            onClick={handlePrint}
            className="flex items-center gap-2 bg-[#EBFEF4] px-4 py-2 rounded-md hover:bg-green-100"
          >
            <Icon icon="mdi-light:printer" fontSize={18} fontWeight={20} />
            Print
          </button>
        </section>

        {/* Payment Info */}
        <div className="p-4 rounded-md mb-6">
          <h2 className="text-center bg-[#EBFEF4] p-2 font-medium mb-4">
            Invoice Details
          </h2>
          <div className="grid grid-cols-2 gap-6">
            {/* Payment To */}
            <div>
              <h3 className="font-medium mb-2">Payment To</h3>
              <div className="mb-1">
                <span className="font-medium">Hotel Name:</span>{" "}
                {roomData?.hotel?.name}
              </div>
              <div className="mb-1">
                <span className="font-medium">Address:</span>{" "}
                {roomData?.hotel?.address}
              </div>
              <div className="mb-1">
                <span className="font-medium">Description:</span>{" "}
                {roomData?.hotel?.description}
              </div>
              <div className="mb-1">
                <span className="font-medium">Email:</span>{" "}
                {roomData?.hotel?.email || "<EMAIL>"}
              </div>
              <div className="mb-1">
                <span className="font-medium">Phone Number:</span>{" "}
                {roomData?.hotel?.phone}
              </div>
            </div>

            {/* Bill To */}
            <div>
              <h3 className="font-medium mb-2">Bill To</h3>
              <div className="mb-1">
                <span className="font-medium">Guest Name:</span> John Doe
              </div>
              <div className="mb-1">
                <span className="font-medium">Address:</span> 1234 Maple Street
                Apt 5B
              </div>
              <div className="mb-1">Springfield, IL 62704 United States</div>
              <div className="mb-1">
                <span className="font-medium">Email:</span> <EMAIL>
              </div>
              <div className="mb-1">
                <span className="font-medium">Phone Number:</span>{" "}
                +377-9812345678
              </div>
            </div>
          </div>
        </div>

        <MasterTable
          rows={tableData.rows ?? []}
          columns={tableData.column ?? []}
          canSearch={false}
          apiPagination={false}
        />

        {/* Summary */}
        <div className="flex flex-col mt-8 items-end gap-2 mb-8">
          <div className="flex justify-between w-48">
            <span className="">Subtotal:</span> <span>Rs.2200</span>
          </div>
          <div className="flex justify-between w-48">
            <span className="">VAT(10%):</span> <span>Rs.200</span>
          </div>
          <div className="flex justify-between w-48 ">
            <span>Total Amount:</span> <span>Rs.2000</span>
          </div>
          <div className="flex justify-between w-48">
            <span className="">Amount Paid:</span> <span>Rs.500</span>
          </div>
          <div className="flex justify-between w-48">
            <span>Balance Due:</span> <span>Rs.1500</span>
          </div>
        </div>
      </section>
      {/* Payment Modal */}
      <div className="flex justify-end border-t py-3">
        <button
          onClick={() => {
            setShowBillModal(false);
            setShowPaymentModal(true);
          }}
          className="bg-[#163381] justify-end text-white py-3 px-6 rounded-md hover:bg-blue-900 transition-colors"
        >
          Proceed To Pay
        </button>
      </div>
    </div>
  );
};
