import { Form, FormikProvider, useFormik } from "formik";
import * as Yup from "yup";
import { FormField } from "../../BookingManagement/components/ReservationCustomForm";

interface IPaymentFormProps {
  onClose: () => void;
  editData?: {
    date: string;
    time: string;
    method: string;
    amount: number;
  } | null;
}

const PaymentSchema = Yup.object().shape({
  date: Yup.string().required("Date is required"),
  time: Yup.string().required("Time is required"),
  method: Yup.string().required("Payment Method is required"),
  amount: Yup.number()
    .required("Amount is required")
    .positive("Amount must be positive"),
});

const PaymentForm = ({ onClose, editData }: IPaymentFormProps) => {
  const formik = useFormik({
    initialValues: {
      date: editData?.date ?? "",
      time: editData?.time ?? "",
      method: editData?.method ?? "",
      amount: editData?.amount ?? 0,
    },
    enableReinitialize: true,
    validationSchema: PaymentSchema,
    onSubmit: async (values) => {
      try {
        console.log("Submitted values:", values);
        onClose();
      } catch (error) {
        console.error("Payment submission error:", error);
      }
    },
  });

  return (
    <div className="rounded-sm flex flex-col gap-4 pb-4">
      <p className="bg-[#F1F6FD] py-3 rounded-md text-center border-b font-medium text-base">
        Payment Form
      </p>
      <div className="px-4 ">
        <FormikProvider value={formik}>
          <Form onSubmit={formik.handleSubmit} className="space-y-4">
            <div className="grid grid-cols-2 gap-x-4 gap-y-4">
              <FormField
                label="Date"
                type="date"
                name="date"
                formik={formik}
                placeholder="Select date"
              />
              <FormField
                label="Time"
                type="time"
                name="time"
                formik={formik}
                placeholder="Enter time"
              />
              <FormField
                label="Payment Method"
                type="dropdown"
                name="method"
                formik={formik}
                placeholder="Select Payment Method"
                options={[
                  { label: "Cash", value: "cash" },
                  { label: "Bank Transfer", value: "bank" },
                  { label: "Credit Card", value: "card" },
                ]}
              />
              <FormField
                label="Amount"
                type="number"
                name="amount"
                formik={formik}
                placeholder="Enter Amount"
              />
            </div>
            <div className="flex justify-end mt-4">
              <button
                type="submit"
                className="px-6 py-2 bg-[#2A3A6D] text-white rounded-md"
              >
                Save
              </button>
            </div>
          </Form>
        </FormikProvider>
      </div>
    </div>
  );
};

export default PaymentForm;
