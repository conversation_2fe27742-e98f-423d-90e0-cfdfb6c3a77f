import { useState } from "react";

interface RoomStatusFilterProps {
  onFilterChange: (filters: { status: string | null; search: string }) => void;
  statusCounts?: Record<string, number>;
}

const RoomStatusFilter: React.FC<RoomStatusFilterProps> = ({
  onFilterChange,
  statusCounts = {},
}) => {
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState<string>("");

  const statusOptions = [
    { label: "All Rooms", value: "all", color: "bg-gray-500" },
    { label: "Available", value: "available", color: "bg-green-500" },
    { label: "Occupied", value: "occupied", color: "bg-red-500" },
    { label: "Reserved", value: "reserved", color: "bg-yellow-500" },
    { label: "Maintenance", value: "maintenance", color: "bg-orange-500" },
    { label: "Cleaning", value: "cleaning", color: "bg-blue-500" },
  ];

  const handleStatusChange = (status: string): void => {
    setSelectedStatus(status);
    onFilterChange({
      status: status === "all" ? null : status,
      search: searchTerm,
    });
  };

  const handleSearchChange = (search: string): void => {
    setSearchTerm(search);
    onFilterChange({
      status: selectedStatus === "all" ? null : selectedStatus,
      search,
    });
  };

  const clearFilters = (): void => {
    setSelectedStatus("all");
    setSearchTerm("");
    onFilterChange({ status: null, search: "" });
  };

  const totalRooms = Object.values(statusCounts).reduce(
    (sum, count) => sum + count,
    0
  );

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border mb-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold text-gray-900">
          Filter & Search Rooms
        </h2>
        <div className="text-sm text-gray-500">
          Total Rooms:{" "}
          <span className="font-medium text-gray-900">{totalRooms}</span>
        </div>
      </div>

      <div className="space-y-4">
        {/* Search Input */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Search Rooms
          </label>
          <div className="relative">
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => handleSearchChange(e.target.value)}
              placeholder="Search by room number, type, price, or bed capacity..."
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
            />
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg
                className="h-5 w-5 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
          </div>
        </div>

        {/* Status Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Filter by Room Status
          </label>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
            {statusOptions.map((option) => {
              const count =
                option.value === "all"
                  ? totalRooms
                  : statusCounts[option.value] || 0;

              const isSelected = selectedStatus === option.value;

              return (
                <button
                  key={option.value}
                  onClick={() => handleStatusChange(option.value)}
                  className={`relative p-3 rounded-lg border-2 transition-all duration-200 text-left ${
                    isSelected
                      ? "border-blue-500 bg-blue-50 shadow-md"
                      : "border-gray-200 bg-white hover:border-gray-300 hover:bg-gray-50"
                  }`}
                >
                  <div className="flex items-center justify-between mb-1">
                    <div className="flex items-center gap-2">
                      <div
                        className={`w-3 h-3 rounded-full ${option.color}`}
                      ></div>
                      <span
                        className={`text-sm font-medium ${
                          isSelected ? "text-blue-900" : "text-gray-700"
                        }`}
                      >
                        {option.label}
                      </span>
                    </div>
                  </div>
                  <div
                    className={`text-lg font-bold ${
                      isSelected ? "text-blue-600" : "text-gray-900"
                    }`}
                  >
                    {count}
                  </div>
                  {count === 0 && option.value !== "all" && (
                    <div className="absolute inset-0 bg-gray-100 bg-opacity-50 rounded-lg flex items-center justify-center"></div>
                  )}
                </button>
              );
            })}
          </div>
        </div>

        {/* Active Filters & Clear Button */}
        {(selectedStatus !== "all" || searchTerm.trim()) && (
          <div className="flex flex-wrap items-center justify-between gap-4 pt-4 border-t border-gray-200">
            <div className="flex flex-wrap gap-2 items-center">
              <span className="text-sm font-medium text-gray-600">
                Active filters:
              </span>
              {selectedStatus !== "all" && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  Status:{" "}
                  {
                    statusOptions.find((opt) => opt.value === selectedStatus)
                      ?.label
                  }
                  <button
                    onClick={() => handleStatusChange("all")}
                    className="ml-2 inline-flex items-center justify-center w-4 h-4 text-blue-400 hover:text-blue-600"
                  >
                    ×
                  </button>
                </span>
              )}
              {searchTerm.trim() && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Search: "{searchTerm.trim()}"
                  <button
                    onClick={() => handleSearchChange("")}
                    className="ml-2 inline-flex items-center justify-center w-4 h-4 text-green-400 hover:text-green-600"
                  >
                    ×
                  </button>
                </span>
              )}
            </div>

            <button
              onClick={clearFilters}
              className="text-sm text-gray-500 hover:text-gray-700 underline font-medium"
            >
              Clear All Filters
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default RoomStatusFilter;
