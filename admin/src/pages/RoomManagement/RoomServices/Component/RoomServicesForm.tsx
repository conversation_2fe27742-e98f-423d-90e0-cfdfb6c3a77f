import { Form, FormikProvider, useFormik } from "formik";
import { Icon } from "@iconify/react";
import {
  useCreateService,
  useUpdateService,
} from "../../../../server-action/API/HotelConfiguration/services";
import { PopupModal } from "../../../../components";
import { FormField } from "../../../BookingManagement/components/ReservationCustomForm";
import { galleryIcon } from "../../../../components/svgExports";
import { IMAGE_URL } from "../../../../constant/constant";
import { RoomServiceValidationSchema } from "./RoomServiceValidationSchema";

interface RoomServicesFormProps {
  onClose: () => void;
  editData?: any;
}

const RoomServicesForm: React.FC<RoomServicesFormProps> = ({
  onClose,
  editData,
}) => {
  const { mutateAsync: createService, isPending: isCreating } =
    useCreateService();
  const { mutateAsync: updateService, isPending: isUpdating } =
    useUpdateService();

  const formik = useFormik({
    initialValues: {
      name: editData?.name ?? "",
      description: editData?.description ?? "",
      price: editData?.price ?? "",
      taxRate: editData?.taxRate ?? "",
      isActive: editData?.isActive ? "Yes" : "No",
      images: editData?.images ?? [],
    },
    validationSchema: RoomServiceValidationSchema,
    onSubmit: async (values) => {
      const formData = new FormData();
      formData.append("name", values.name);
      formData.append("description", values.description);
      formData.append("price", values.price.toString());
      formData.append("taxRate", values.taxRate.toString());
      formData.append("isActive", values.isActive === "Yes" ? "true" : "false");

      // Append images
      if (values.images && values.images.length > 0) {
        if (Array.isArray(values.images)) {
          values.images.forEach((file: File) => {
            if (typeof file !== "string") {
              formData.append("images", file);
            }
          });
        }
      }

      try {
        if (editData?._id) {
          await updateService({ id: editData._id, body: formData });
        } else {
          await createService(formData as any);
        }
        onClose();
      } catch (error) {
        console.error("Submission failed:", error);
      }
    },
  });

  return (
    <PopupModal onClose={onClose} classname="w-full max-w-screen-sm">
      <div className="relative flex items-center justify-between bg-[#F1F6FD]">
        <h1 className="w-full p-4 text-center font-semibold">
          {editData ? "Edit Room Service" : "Add Room Service"}
        </h1>
        <button
          className="absolute top-4 right-4 p-1 bg-black rounded-full"
          onClick={onClose}
        >
          <Icon
            icon="fluent-mdl2:calculator-multiply"
            className="text-white"
            width="14"
            height="14"
          />
        </button>
      </div>

      <div className="p-4">
        <FormikProvider value={formik}>
          <Form onSubmit={formik.handleSubmit}>
            <div className="grid grid-cols-2 gap-4">
              <FormField
                name="name"
                label="Service Name"
                type="text"
                placeholder="Enter service name"
                formik={formik}
              />
              <FormField
                name="description"
                label="Description"
                type="text"
                placeholder="Enter description"
                formik={formik}
              />
              <FormField
                name="price"
                label="Price"
                type="number"
                placeholder="Enter price"
                formik={formik}
              />
              <FormField
                name="taxRate"
                label="Tax Rate (%)"
                type="number"
                placeholder="Enter tax rate"
                formik={formik}
              />
              <FormField
                name="isActive"
                label="Is Active"
                type="dropdown"
                options={[
                  { label: "Yes", value: "Yes" },
                  { label: "No", value: "No" },
                ]}
                formik={formik}
              />

              <div className="flex flex-col">
                <div>
                  <label className="mb-1 text-sm">Upload Photo</label>
                  <div className="flex items-center w-full space-x-2 border-2 rounded-md">
                    <label
                      htmlFor="expense-list-form"
                      className="flex items-center gap-1 p-2 text-white bg-[#163381] cursor-pointer rounded-l-md"
                    >
                      {galleryIcon({ color: "white", size: "18" })}
                      Choose File
                    </label>
                    <input
                      name="file"
                      id="expense-list-form"
                      multiple={true}
                      type="file"
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                        const files = e.target.files
                          ? Array.from(e.target.files)
                          : [];
                        formik.setFieldValue("images", files);
                      }}
                      className="hidden"
                    />
                    <span className="text-sm text-gray-500">
                      {formik.values.images?.length} files selected
                    </span>
                  </div>
                </div>
                {formik.values.images?.length > 0 && (
                  <div className="flex flex-wrap p-4 mt-2 border-2 border-gray-300 rounded-md">
                    {Array.isArray(formik.values.images) &&
                      formik.values.images.map((file: any, index: number) => {
                        const isString = typeof file === "string";
                        const imageUrl = isString
                          ? `${IMAGE_URL}${file}`
                          : URL.createObjectURL(file);

                        return (
                          <div key={index} className="relative w-24 h-24 m-2">
                            <img
                              src={imageUrl}
                              alt={`preview-${index}`}
                              className="object-cover w-full h-full rounded-md"
                            />
                            <button
                              type="button"
                              className="absolute top-0 right-0 p-1 bg-red-500 rounded-full"
                              onClick={() => {
                                const newFiles = Array.isArray(
                                  formik.values.images
                                )
                                  ? formik.values.images.filter(
                                      (_: any, i: number) => i !== index
                                    )
                                  : [];
                                formik.setFieldValue("images", newFiles);
                              }}
                            >
                              <Icon
                                icon="fluent-mdl2:calculator-multiply"
                                width="14"
                                height="14"
                                className="text-white"
                              />
                            </button>
                          </div>
                        );
                      })}
                  </div>
                )}
              </div>
            </div>

            <div className="flex items-center justify-end mt-6">
              <button
                type="submit"
                disabled={isCreating || isUpdating}
                className="py-2 px-6 text-white rounded-md bg-[#163381]"
              >
                {isCreating || isUpdating ? (
                  <Icon icon="line-md:loading-loop" width="18" height="18" />
                ) : (
                  "Save"
                )}
              </button>
            </div>
          </Form>
        </FormikProvider>
      </div>
    </PopupModal>
  );
};

export default RoomServicesForm;
