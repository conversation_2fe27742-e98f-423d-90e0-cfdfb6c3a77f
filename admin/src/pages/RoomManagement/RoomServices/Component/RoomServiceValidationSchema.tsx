import * as Yup from "yup";
export const RoomServiceValidationSchema = Yup.object().shape({
  name: Yup.string().required("Service name is required"),
  description: Yup.string().required("Description is required"),
  price: Yup.number()
    .required("Price is required")
    .min(0, "Price must be positive"),
  taxRate: Yup.number()
    .required("Tax rate is required")
    .min(0, "Tax rate must be positive"),
  isActive: Yup.string().required("Active status is required"),
});
