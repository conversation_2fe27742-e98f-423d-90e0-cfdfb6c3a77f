import { useState } from "react";

interface RoomStatusFilterProps {
  onFilterChange: (status: string) => void;
}

const RoomStatusFilter: React.FC<RoomStatusFilterProps> = ({
  onFilterChange,
}) => {
  const [selectedStatus, setSelectedStatus] = useState("all");

  const statusOptions = [
    { label: "All", value: "all" },
    { label: "Available", value: "available" },
    { label: "Occupied", value: "occupied" },
    { label: "Reserved", value: "reserved" },
    { label: "Maintenance", value: "maintenance" },
    { label: "Cleaning", value: "cleaning" },
  ];

  const handleStatusChange = (status: string): void => {
    setSelectedStatus(status);
    onFilterChange(status);
  };

  return (
    <div className="flex flex-col md:flex-row items-center mb-4 gap-2">
      <label className="font-medium text-gray-700">Filter by Status:</label>
      <div className="flex flex-wrap gap-2">
        {statusOptions.map((option) => (
          <button
            key={option.value}
            onClick={() => handleStatusChange(option.value)}
            className={`px-3 py-1 rounded-md text-sm border ${
              selectedStatus === option.value
                ? "bg-[#163381] text-white border-[#163381]"
                : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
            }`}
          >
            {option.label}
          </button>
        ))}
      </div>
    </div>
  );
};

export default RoomStatusFilter;
