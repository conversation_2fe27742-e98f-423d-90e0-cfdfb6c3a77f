import { useState, useMemo } from "react";
import Header from "../../components/Header";
import MasterTable from "../../layouts/Table/MasterTable";
import { useGetAllRooms } from "../../server-action/API/HotelConfiguration/room";
import { PopupModal } from "../../components";
import { get } from "lodash";
import { Status } from "../../components/Status";
import { Icon } from "@iconify/react/dist/iconify.js";
import RoomStatusFilter from "./RoomStatusFilter";
import CheckoutForm from "../BookingManagement/components/CheckoutForm";
import RoomStatusEditForm from "./components/RoomStatusEditForm";

const RoomManagementIndex = () => {
  const { data: roomData, isLoading, refetch } = useGetAllRooms();
  const [showCheckoutModal, setShowCheckoutModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedRoom, setSelectedRoom] = useState<any>(null);

  // Filter state management
  const [filters, setFilters] = useState({
    search: "",
    status: null as string | null,
  });

  // Calculate status counts for the filter component
  const statusCounts = useMemo(() => {
    if (!roomData) return {};

    return roomData.reduce((acc: any, room: any) => {
      const status = get(room, "status", "unknown");
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {});
  }, [roomData]);

  // Filter room data based on current filters
  const filteredRoomData = useMemo(() => {
    if (!roomData) return [];

    return roomData.filter((item: any) => {
      // Status filter
      if (filters.status && filters.status !== "all") {
        const itemStatus = get(item, "status", "").toLowerCase();
        if (itemStatus !== filters.status.toLowerCase()) {
          return false;
        }
      }

      // Search filter - search across multiple fields
      if (filters.search && filters.search.trim()) {
        const searchTerm = filters.search.toLowerCase().trim();
        const roomNo = get(item, "roomNo", "").toString().toLowerCase();
        const roomType = get(item, "roomType.name", "").toLowerCase();
        const price = get(item, "currentPrice", 0).toString();
        const bedCount = get(item, "beds.count", 0).toString();

        const searchFields = [roomNo, roomType, price, bedCount].join(" ");

        if (!searchFields.includes(searchTerm)) {
          return false;
        }
      }

      return true;
    });
  }, [roomData, filters]);

  // Handle filter changes from the custom filter component
  const handleFilterChange = (newFilters: {
    status: string | null;
    search: string;
  }) => {
    setFilters({
      status: newFilters.status,
      search: newFilters.search,
    });
  };

  // Table configuration
  const tableData = {
    column: [
      { title: "Room Category", key: "roomType" },
      { title: "Bed Capacity", key: "bed" },
      { title: "Room No.", key: "Room" },
      { title: "Price (NRS)", key: "price" },
      { title: "Facilities", key: "facility" },
      { title: "Room Status", key: "roomStatus" },
      { title: "Action", key: "action" },
    ],
    rows:
      filteredRoomData?.map((item: any) => ({
        id: get(item, "_id", ""),
        key: get(item, "_id", ""),
        roomType: get(item, "roomType.name", "N/A"),
        bed: get(item, "beds.count", 0),
        Room: get(item, "roomNo", "N/A"),
        price: `${get(item, "currentPrice", 0).toLocaleString()}`,
        facility: get(item, "amenities"),
        status: get(item, "status", ""),
        roomStatus: <Status status={item?.status} />,
        action: (
          <div className="flex justify-center gap-2">
            {get(item, "status", "") === "occupied" ? (
              <button
                className="px-3 py-1.5 text-white bg-[#2A3A6D] rounded hover:bg-[#1E2A5A] transition-colors duration-200 text-sm font-medium cursor-pointer flex items-center gap-1"
                onClick={() => {
                  setSelectedRoom(item);
                  setShowCheckoutModal(true);
                }}
              >
                <Icon icon="mdi:logout" width="14" height="14" />
                Checkout
              </button>
            ) : get(item, "status", "") === "available" ? (
              <button className="px-3 py-1.5 bg-green-600 text-white rounded hover:bg-green-700 transition-colors duration-200 text-sm font-medium cursor-pointer flex items-center gap-1">
                <Icon icon="mdi:login" width="14" height="14" />
                Check-in
              </button>
            ) : (
              <span className="text-gray-400 text-sm">-</span>
            )}

            {/* Edit Status Button - show for all rooms but only cleaning can be changed */}
            <button
              className={`px-3 py-1.5 rounded transition-colors duration-200 text-sm font-medium cursor-pointer flex items-center gap-1 ${
                get(item, "status", "") === "cleaning"
                  ? "bg-blue-600 text-white hover:bg-blue-700"
                  : "bg-gray-300 text-gray-600 hover:bg-gray-400"
              }`}
              onClick={() => {
                setSelectedRoom(item);
                setShowEditModal(true);
              }}
            >
              <Icon icon="mdi:pencil" width="14" height="14" />
              Edit
            </button>
          </div>
        ),
      })) ?? [],
  };

  // Calculate filtered results summary
  const filteredSummary = useMemo(() => {
    const total = roomData?.length || 0;
    const filtered = filteredRoomData.length;
    const isFiltered = filters.status || filters.search.trim();

    return {
      total,
      filtered,
      isFiltered,
      message: isFiltered
        ? `Showing ${filtered} of ${total} rooms`
        : `Showing all ${total} rooms`,
    };
  }, [roomData, filteredRoomData, filters]);

  return (
    <div className="min-h-screen bg-gray-50">
      <Header showButton={false} />

      <div className="container mx-auto px-4 py-6">
        {/* Custom Filter Component */}
        <RoomStatusFilter
          onFilterChange={handleFilterChange}
          statusCounts={statusCounts}
        />

        {/* Results Summary */}
        <div className="mb-4 flex justify-between items-center">
          <div className="text-sm text-gray-600">{filteredSummary.message}</div>

          {/* Quick Stats */}
          <div className="flex gap-4 text-sm">
            <div className="text-green-600 font-medium">
              Available: {statusCounts.available || 0}
            </div>
            <div className="text-red-600 font-medium">
              Occupied: {statusCounts.occupied || 0}
            </div>
            <div className="text-yellow-600 font-medium">
              Reserved: {statusCounts.reserved || 0}
            </div>
          </div>
        </div>

        {/* Table */}
        <div className="bg-white rounded-lg shadow-sm">
          <MasterTable
            columns={tableData.column}
            rows={tableData.rows ?? []}
            loading={isLoading}
            canSearch={false} // Disabled because we have custom search
            showFilter={false} // Disabled because we have custom filter
            sortBy="roomNo"
            sortOrder="asc"
          />
        </div>

        {/* No Results Message */}
        {!isLoading &&
          filteredRoomData.length === 0 &&
          roomData &&
          roomData.length > 0 && (
            <div className="text-center py-8 bg-white rounded-lg shadow-sm mt-4">
              <div className="text-gray-500">
                <svg
                  className="mx-auto h-12 w-12 text-gray-400 mb-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.469-.786-6.172-2.109M12 21l3.09-6.26L12 9l-3.09 5.74L12 21z"
                  />
                </svg>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No rooms found
                </h3>
                <p className="text-gray-600">
                  Try adjusting your search criteria or filters to find what
                  you're looking for.
                </p>
              </div>
            </div>
          )}
      </div>

      {/* Room Checkout Modal - Using Same Form as Booking */}
      {showCheckoutModal && selectedRoom && (
        <PopupModal
          onClose={() => {
            setShowCheckoutModal(false);
            setSelectedRoom(null);
          }}
        >
          <CheckoutForm
            booking={selectedRoom} // Pass room as booking for compatibility
            onClose={() => {
              setShowCheckoutModal(false);
              setSelectedRoom(null);
            }}
            onSuccess={() => {
              refetch();
              setShowCheckoutModal(false);
              setSelectedRoom(null);
            }}
            isRoomCheckout={true} // Flag to indicate this is room checkout
          />
        </PopupModal>
      )}

      {/* Room Status Edit Modal */}
      {showEditModal && selectedRoom && (
        <PopupModal
          onClose={() => {
            setShowEditModal(false);
            setSelectedRoom(null);
          }}
        >
          <RoomStatusEditForm
            room={selectedRoom}
            onClose={() => {
              setShowEditModal(false);
              setSelectedRoom(null);
            }}
            onSuccess={() => {
              refetch();
              setShowEditModal(false);
              setSelectedRoom(null);
            }}
          />
        </PopupModal>
      )}
    </div>
  );
};

export default RoomManagementIndex;
