import { Form, FormikProvider, useFormik } from "formik";
import { ActionButton } from "../../components/ActionButton";
import { GlobalForm } from "../../components/GlobalForm/GlobalFormComponent";
import { IFormData } from "../../components/GlobalForm/globalinterface";
import Header from "../../components/Header";
import FileUpload from "../../components/UploadFile";

// const GENDER_LABEL = [
//     {
//         value: "MALE",
//         label: "Male",
//     },
//     {
//         value: "FEMALE",
//         label: "Female",
//     }
// ]
export function BankManagementIndex() {
  // const [activeStep, setActiveStep] = useState(1);

  const formik = useFormik({
    initialValues: {
      referForEmergency1: "No",
      referForEmergency2: "No",
      referForSurgery: "No",
      symptoms: [{ symptom: "", details: "" }],
      appointments: [
        {
          department: "",
          testType: "",
          requestedDate: "",
          priority: "",
        },
      ],
      prescription: [
        {
          medicineName: "",
          dose: "",
          frequence: "",
          duration: "",
          condition: "",
          prescriptionNote: "",
        },
      ],
    },
    enableReinitialize: true,
    onSubmit: (values) => {
      // toast.success('Form submitted successfully!');
      // history.back();
      console.log(values, "values");
    },
  });
  const { handleSubmit, getFieldProps } = formik;
  const StudentBasicData: IFormData[] = [
    {
      field: "name",
      label: "Bank Name",
      type: "text",
      placeholder: "Enter Bank Name  ",
    },
    {
      field: "branchName",
      label: " Enter Bank Name ",
      type: "text",
      placeholder: "bank name",
    },
    {
      field: "accountHolderName",
      label: "Account Holder Name",
      type: "text",
      placeholder: "enter Acount Holder Name ",
    },
    {
      field: "accountNumber",
      label: "Account Number",
      type: "number",
      placeholder: "Select DOB",
    },
    {
      field: "phoneNumber",
      label: "Phone No.",
      type: "text",
      placeholder: "Enter Phone Number",
    },

    {
      field: "currentBalance",
      label: "Current Balance",
      type: "text",
      placeholder: "Enter current Balance",
    },
  ];
  return (
    <div>
      <Header title="bank" hideHeader={true} />
      <div className="relative flex w-full gap-6">
        <div className="xl:ml-[15rem] lg:ml-[14rem] md:ml-[13rem] ml-[12rem] w-full">
          <FormikProvider value={formik}>
            <Form onSubmit={handleSubmit}>
              <div className="w-full grid-cols-4 gap-8 pb-4">
                <GlobalForm
                  formDatails={StudentBasicData}
                  getFieldProps={getFieldProps}
                />
              </div>
              <FileUpload
                multiple={true}
                onChange={(files) => {
                  formik.setFieldValue("images", files);
                }}
              />
              <ActionButton
                onCancel={() => history.back()}
                onSubmit={handleSubmit}
              />
            </Form>
          </FormikProvider>
        </div>
      </div>
    </div>
  );
}
