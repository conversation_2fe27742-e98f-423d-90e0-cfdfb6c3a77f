import { Icon } from "@iconify/react/dist/iconify.js";
import { format } from "date-fns";
import { ISalary } from "../../../../Interface/salary.interface";
import HeadingPopup from "../../../GuestManagement/components/HeadingPopup";
import { useRef } from "react";
import { useReactToPrint } from "react-to-print";

interface SalaryInvoiceDetailProps {
  salary: ISalary;
  onClose: () => void;
}

const SalaryInvoiceDetail = ({ salary, onClose }: SalaryInvoiceDetailProps) => {
  const componentRef = useRef<HTMLDivElement>(null);

  const formatCurrency = (amount: number) => {
    return `Rs ${amount?.toLocaleString() || 0}`;
  };

  const getMonthName = (monthNum: string) => {
    const monthNames = [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December",
    ];
    const num = parseInt(monthNum);
    return monthNames[num - 1] || monthNum;
  };

  const handlePrint = useReactToPrint({
    contentRef: componentRef,
    pageStyle: `
      @page {
        size: A4;
        margin: 5mm;
      }
      @media print {
        body {
          -webkit-print-color-adjust: exact;
        }
        .no-print {
          display: none !important;
        }
      }
    `,
  });

  return (
    <HeadingPopup
      onClose={onClose}
      className="w-full max-w-screen-md h-[90vh]"
      heading="Salary Invoice Details"
    >
      <div ref={componentRef} className="">
        {/* display while print only not in preivew */}
        {/* <h1 className="p-5 text-lg font-semibold text-ccls
        enter bg-[#F1F6FD] mb-3">
          Salary Invoice Details
        </h1> */}
        <div className="flex justify-between items-center mb-4 print:flex-col print:items-start">
          <div>
            <h2 className="text-xl font-bold">
              {typeof salary.user === "object" ? salary.user.name : "Employee"}
            </h2>
            <p className="text-gray-600">
              {typeof salary.user === "object" ? salary.user.role : ""}
            </p>
          </div>
          <div className="print:hidden">
            <p className="text-gray-600 text-sm">Generated on</p>
            <p className="font-medium">{format(new Date(), "MMM dd, yyyy")}</p>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 mb-2 border-b pb-4 print:grid-cols-1 print:gap-2">
          <div>
            <p className="text-gray-600 text-sm">Hotel</p>
            <p className="font-medium capitalize">
              {salary.hotel?.name || "N/A"}
            </p>
          </div>
          <div>
            <p className="text-gray-600 text-sm">Month/Year</p>
            <p className="font-medium">{`${getMonthName(salary.targetMonth)} ${
              salary.targetYear
            }`}</p>
          </div>
          <div>
            <p className="text-gray-600 text-sm">Payment Type</p>
            <p className="font-medium capitalize">
              {salary.paymentType || "N/A"}
            </p>
          </div>
          <div>
            <p className="text-gray-600 text-sm">Invoice ID</p>
            <p className="font-medium">{salary._id || "N/A"}</p>
          </div>
        </div>

        <div className="mb-4">
          <h3 className="font-bold mb-2">Salary Breakdown</h3>
          <div className="border rounded-md overflow-hidden">
            <table className="w-full print:table-fixed">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-600 print:px-2 print:text-xs">
                    Description
                  </th>
                  <th className="px-4 py-2 text-right text-sm font-medium text-gray-600 print:px-2 print:text-xs">
                    Amount
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y">
                <tr>
                  <td className="px-4 py-2 print:px-2 print:text-sm">
                    Calculated Salary
                  </td>
                  <td className="px-4 py-2 text-right print:px-2 print:text-sm">
                    {formatCurrency(salary.calculatedSalary || 0)}
                  </td>
                </tr>

                {salary.allowance && salary.allowance.length > 0 && (
                  <>
                    <tr>
                      <td
                        colSpan={2}
                        className="px-4 py-2 font-semibold text-green-700 print:px-2 print:text-sm"
                      >
                        Allowances
                      </td>
                    </tr>
                    {salary.allowance.map((allowance: any, index: number) => (
                      <tr key={`allowance-${index}`}>
                        <td className="px-4 py-2 pl-8 text-green-600 print:px-2 print:pl-4 print:text-sm">
                          <Icon
                            icon="mdi:plus-circle"
                            className="inline mr-2"
                            width="16"
                          />
                          {allowance.allowance?.name || "N/A"}
                        </td>
                        <td className="px-4 py-2 text-right text-green-600 print:px-2 print:text-sm">
                          {formatCurrency(allowance.allowanceAmount || 0)}
                        </td>
                      </tr>
                    ))}
                    <tr className="bg-gray-50">
                      <td className="px-4 py-2 font-medium print:px-2 print:text-sm">
                        Total Allowances
                      </td>
                      <td className="px-4 py-2 text-right font-medium text-green-600 print:px-2 print:text-sm">
                        {formatCurrency(salary.totalAllowance || 0)}
                      </td>
                    </tr>
                  </>
                )}

                {salary.deductance && salary.deductance.length > 0 && (
                  <>
                    <tr>
                      <td
                        colSpan={2}
                        className="px-4 py-2 font-semibold text-red-700 print:px-2 print:text-sm"
                      >
                        Deductions
                      </td>
                    </tr>
                    {salary.deductance.map((deduction: any, index: number) => (
                      <tr key={`deduction-${index}`}>
                        <td className="px-4 py-2 pl-8 text-red-600 print:px-2 print:pl-4 print:text-sm">
                          <Icon
                            icon="mdi:minus-circle"
                            className="inline mr-2"
                            width="16"
                          />
                          {deduction.deductive?.name || "N/A"}
                        </td>
                        <td className="px-4 py-2 text-right text-red-600 print:px-2 print:text-sm">
                          {formatCurrency(deduction.deductedAmount || 0)}
                        </td>
                      </tr>
                    ))}
                    <tr className="bg-gray-50">
                      <td className="px-4 py-2 font-medium print:px-2 print:text-sm">
                        Total Deductions
                      </td>
                      <td className="px-4 py-2 text-right font-medium text-red-600 print:px-2 print:text-sm">
                        {formatCurrency(salary.totalDeduction || 0)}
                      </td>
                    </tr>
                  </>
                )}

                <tr className="bg-blue-50">
                  <td className="px-4 py-2 font-bold print:px-2 print:text-sm">
                    Dispatch Salary
                  </td>
                  <td className="px-4 py-2 text-right font-bold print:px-2 print:text-sm">
                    {formatCurrency(salary?.dispatchedSalary || 0)}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {salary.remarks && (
          <div className="mb-6">
            <h3 className="font-bold mb-2">Remarks</h3>
            <p className="text-gray-700 bg-gray-50 p-3 rounded-md print:text-sm">
              {salary.remarks}
            </p>
          </div>
        )}
      </div>
      <div className="flex justify-end space-x-2 mt-6 no-print">
        <button
          onClick={onClose}
          className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
        >
          Close
        </button>
        <button
          onClick={handlePrint}
          className="px-4 py-2 bg-[#2A3A6D] text-white rounded-md  flex items-center"
          // onClick={() => handlePrint()}
          // className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
        >
          <Icon icon="mdi:printer" className="mr-2" />
          Print Invoice
        </button>
      </div>
    </HeadingPopup>
  );
};

export default SalaryInvoiceDetail;
