import React, { useState } from "react";
import CustomSelect from "../../../../components/GlobalForm/CustomSelect";

interface FilterOption {
  label: string;
  value: string;
}

interface EmployeePayrollFilterProps {
  onSearch: (value: string) => void;
  onApplyFilters: (filters: {
    targetMonth: string;
    targetYear: string;
    role: string;
  }) => void;
  roleOptions: FilterOption[];
}

const EmployeePayrollFilter = ({
  onSearch,
  onApplyFilters,
  roleOptions,
}: EmployeePayrollFilterProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedMonth, setSelectedMonth] = useState("");
  const [targetYear, setTargetYear] = useState("");
  const [selectedRole, setSelectedRole] = useState("");

  const monthOptions: FilterOption[] = [
    { label: "All Months", value: "" },
    { label: "January", value: "1" },
    { label: "February", value: "2" },
    { label: "March", value: "3" },
    { label: "April", value: "4" },
    { label: "May", value: "5" },
    { label: "June", value: "6" },
    { label: "July", value: "7" },
    { label: "August", value: "8" },
    { label: "September", value: "9" },
    { label: "October", value: "10" },
    { label: "November", value: "11" },
    { label: "December", value: "12" },
  ];

  const commonSelectProps = {
    className: "w-full md:w-48",
    isForm: false,
  };

  const renderCustomSelect = (
    label: string,
    value: string,
    onChange: (val: string) => void,
    options: FilterOption[],
    placeholder: string
  ) => (
    <div className="w-full md:w-48">
      <CustomSelect
        label={label}
        value={value}
        onChange={onChange}
        options={options}
        placeholder={placeholder}
        {...commonSelectProps}
      />
    </div>
  );

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    onSearch(value);
  };

  const handleResetFilters = () => {
    setSearchTerm("");
    setSelectedMonth("");
    setTargetYear("");
    setSelectedRole("");
    onSearch("");
    onApplyFilters({ targetMonth: "", targetYear: "", role: "" });
  };

  const handleApplyFilters = () => {
    onApplyFilters({
      targetMonth: selectedMonth,
      targetYear,
      role: selectedRole,
    });
  };

  return (
    <div className="flex flex-col md:flex-row md:items-end gap-4">
      {renderCustomSelect(
        "Target Month",
        selectedMonth,
        setSelectedMonth,
        monthOptions,
        "Select Month"
      )}

      <div className="w-full md:w-48">
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Target Year
        </label>
        <input
          type="number"
          value={targetYear}
          onChange={(e) => setTargetYear(e.target.value)}
          placeholder="e.g. 2025"
          className="w-full p-2 border border-gray-300 focus:outline-none focus:border-gray-400 rounded-md"
        />
      </div>

      {renderCustomSelect(
        "Role",
        selectedRole,
        setSelectedRole,
        [{ label: "All", value: "" }, ...roleOptions],
        "Select Role"
      )}

      <div className="flex gap-2">
        <button
          onClick={handleResetFilters}
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100"
        >
          Reset
        </button>
        <button
          onClick={handleApplyFilters}
          className="px-4 py-2 bg-[#2A3A6D] text-white rounded-md"
        >
          Apply Filters
        </button>
      </div>

      <div className="flex-1 flex justify-end mt-6 md:mt-0">
        <div className="relative w-full md:w-64">
          <input
            type="text"
            placeholder="Search by employee name..."
            value={searchTerm}
            onChange={handleSearchChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 pl-10"
          />
          <svg
            className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
      </div>
    </div>
  );
};

export default EmployeePayrollFilter;
