import { useState } from "react";

interface IProps {
  onShow?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
}

export function PayrollTableAction({ onShow, onEdit, onDelete }: IProps) {
  return (
    <div className="flex items-center justify-center w-full gap-4">
      {onShow && (
        <div className="cursor-pointer " onClick={onShow}>
          <div className="flex justify-center items-center h-[1.6rem] p-1 w-[1.6rem] border-[#3498DB] border-1 bg-[#3498DB] rounded-md">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width={17}
              height={17}
              viewBox="0 0 24 24"
            >
              <g
                fill="none"
                stroke="white"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                color="white"
              >
                <path d="M21.544 11.045c.304.426.456.64.456.955c0 .316-.152.529-.456.955C20.178 14.871 16.689 19 12 19c-4.69 0-8.178-4.13-9.544-6.045C2.152 12.529 2 12.315 2 12c0-.316.152-.529.456-.955C3.822 9.129 7.311 5 12 5c4.69 0 8.178 4.13 9.544 6.045"></path>
                <path d="M15 12a3 3 0 1 0-6 0a3 3 0 0 0 6 0"></path>
              </g>
            </svg>
          </div>
        </div>
      )}

      {onEdit && (
        <div className="cursor-pointer " onClick={onEdit}>
          <div className="flex justify-center items-center h-[1.6rem] p-1 w-[1.6rem] border-[#6C757D] border-1 bg-[#6C757D] rounded-md">
            <svg
              width={15}
              height={15}
              viewBox="0 0 18 18"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M8.16699 2.3332H2.33366C1.89163 2.3332 1.46771 2.50879 1.15515 2.82135C0.842587 3.13391 0.666992 3.55784 0.666992 3.99986V15.6665C0.666992 16.1086 0.842587 16.5325 1.15515 16.845C1.46771 17.1576 1.89163 17.3332 2.33366 17.3332H14.0003C14.4424 17.3332 14.8663 17.1576 15.1788 16.845C15.4914 16.5325 15.667 16.1086 15.667 15.6665V9.8332M14.417 1.0832C14.7485 0.751676 15.1982 0.56543 15.667 0.56543C16.1358 0.56543 16.5855 0.751676 16.917 1.0832C17.2485 1.41472 17.4348 1.86436 17.4348 2.3332C17.4348 2.80204 17.2485 3.25168 16.917 3.5832L9.00033 11.4999L5.66699 12.3332L6.50033 8.99986L14.417 1.0832Z"
                stroke="white"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
        </div>
      )}

      {onDelete && (
        <div className="cursor-pointer " onClick={onDelete}>
          <div className="flex justify-center items-center h-[1.6rem] p-1 w-[1.6rem] border-[#FF474C] border-1 bg-[#FF474C] rounded-md">
            <svg
              width={15}
              height={15}
              viewBox="0 0 16 18"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0.5 4.00033H2.16667M2.16667 4.00033H15.5M2.16667 4.00033V15.667C2.16667 16.109 2.34226 16.5329 2.65482 16.8455C2.96738 17.1581 3.39131 17.3337 3.83333 17.3337H12.1667C12.6087 17.3337 13.0326 17.1581 13.3452 16.8455C13.6577 16.5329 13.8333 16.109 13.8333 15.667V4.00033M4.66667 4.00033V2.33366C4.66667 1.89163 4.84226 1.46771 5.15482 1.15515C5.46738 0.842587 5.89131 0.666992 6.33333 0.666992H9.66667C10.1087 0.666992 10.5326 0.842587 10.8452 1.15515C11.1577 1.46771 11.3333 1.89163 11.3333 2.33366V4.00033M6.33333 8.16699V13.167M9.66667 8.16699V13.167"
                stroke="white"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
        </div>
      )}
    </div>
  );
}
