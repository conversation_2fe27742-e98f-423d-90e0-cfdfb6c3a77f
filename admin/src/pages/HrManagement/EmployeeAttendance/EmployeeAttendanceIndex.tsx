"use client";

import { useCallback, useMemo, useState } from "react";
import Header from "../../../components/Header";
import { Status } from "../../../components/Status";
import MasterTable from "../../../layouts/Table/MasterTable";
import { TableAction } from "../../../layouts/Table/TableAction";
import {
  useDeleteAttendance,
  useGetAttendance,
} from "../../../server-action/API/attendance";
import AttendanceForm from "./Components/AttendanceForm";
import AttendanceFilter from "./Components/AttendanceFilter";
import { DateForamter } from "../../../components/DateFormater";
import { TimeFormater } from "../../../components/TimeFormater";

const EmployeeAttendanceIndex = () => {
  const [popup, setPopup] = useState(false);
  const [editData, setEditData] = useState(null);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(5);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [date, setDate] = useState("");
  const { mutateAsync: deleteAttendance } = useDeleteAttendance();

  const togglePopup = useCallback(() => setPopup((prev) => !prev), []);

  // Combine searchTerm and statusFilter for the backend search
  const effectiveSearch =
    statusFilter === "all"
      ? searchTerm
      : `${searchTerm} ${statusFilter}`.trim();

  const { data: getAttendance, isLoading } = useGetAttendance(
    page,
    limit,
    effectiveSearch,
    date
  );

  const filterConfig = [
    {
      label: "Status",
      key: "attendanceStatus",
      type: "select",
      options: [
        { label: "Present", value: "Present" },
        { label: "Absent", value: "Absent" },
      ],
    },
  ];

  const tableData = useMemo(
    () => ({
      columns: [
        { title: "Employee Name", key: "user" },
        { title: "Email", key: "email" },
        { title: "Date", key: "date" },
        { title: "Check In", key: "checkInTime" },
        { title: "Check Out", key: "checkOutTime" },
        { title: "Remarks", key: "remarks" },
        { title: "Status", key: "attendanceStatus" },
        { title: "Actions", key: "action" },
      ],
      rows:
        getAttendance?.attendance?.map((att: any) => ({
          id: att._id,
          user:
            typeof att.user === "object" && att.user !== null
              ? att.user.name ||
                `${att.user.firstName || ""} ${att.user.lastName || ""}`.trim()
              : att.user,
          email:
            typeof att.user === "object" && att.user !== null
              ? att.user.email
              : "",
          date: DateForamter(att?.date),
          checkInTime: TimeFormater(att?.checkInTime),
          remarks: att?.remarks,
          checkOutTime: TimeFormater(att.checkOutTime),
          attendanceStatus: (
            <Status status={att.attendanceStatus} key={att._id} />
          ),
          action: (
            <TableAction
              key={att._id}
              onDelete={() => {
                att._id && deleteAttendance(att._id);
              }}
              onEdit={() => {
                setEditData(att);
                setPopup(true);
              }}
            />
          ),
        })) || [],
    }),
    [getAttendance, deleteAttendance]
  );

  const handlePageChange = useCallback((newPage: number, pageSize: number) => {
    setPage(newPage + 1);
    setLimit(pageSize);
  }, []);

  const handleSearch = useCallback((value: string) => {
    setSearchTerm(value);
    setPage(1);
  }, []);

  const handleStatusChange = useCallback((value: string) => {
    setStatusFilter(value);
    setPage(1);
  }, []);

  const handleDateChange = useCallback((value: string) => {
    setDate(value);
    setPage(1);
  }, []);

  return (
    <div className="rounded-md shadow">
      <Header
        title="Attendance"
        onAddClick={() => {
          setEditData(null);
          togglePopup();
        }}
      />

      <div className="p-4 border bg-white rounded-md">
        <AttendanceFilter
          onSearch={handleSearch}
          onStatusChange={handleStatusChange}
          onDateChange={handleDateChange}
          statusOptions={filterConfig[0].options}
        />
      </div>

      <div className="my-2">
        <MasterTable
          loading={isLoading}
          columns={tableData.columns}
          rows={tableData.rows}
          apiPagination={true}
          totalItems={getAttendance?.pagination.total || 0}
          onPageChange={handlePageChange}
          entriesPerPage={limit}
          showLimit={true}
          canSearch={false}
        />
        {popup && <AttendanceForm onClose={togglePopup} editData={editData} />}
      </div>
    </div>
  );
};

export default EmployeeAttendanceIndex;
