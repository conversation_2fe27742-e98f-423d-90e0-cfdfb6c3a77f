export const getAttendanceFormData = (allUser: any[]) => [
  {
    field: "user",
    label: "Employee",
    type: "select",
    placeholder: "Select Employee",
    options: allUser
      .filter((user) => user.role === "staff" || user.role === "housekeeper")
      .map((user) => ({
        value: user._id,
        label: `${user.name} (${user.phoneNumber})`,
      })),
  },
  {
    field: "date",
    label: "Date",
    type: "date",
    placeholder: "Select Date",
  },
  {
    field: "checkInTime",
    label: "Check-In Time",
    type: "time",
    placeholder: "Enter Check-In Time",
  },
  {
    field: "checkOutTime",
    label: "Check-Out Time",
    type: "time",
    placeholder: "Enter Check-Out Time",
  },
  {
    field: "attendanceStatus",
    label: "Attendance Status",
    type: "select",
    placeholder: "Select Attendance Status",
    options: [
      { label: "Present", value: "Present" },
      { label: "Absent", value: "Absent" },
    ],
  },
  {
    field: "remarks",
    label: "Remarks",
    type: "textarea",
    placeholder: "Enter Remarks",
  },
];
