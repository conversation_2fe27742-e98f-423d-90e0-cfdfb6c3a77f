import { Form, FormikProvider, useFormik } from "formik";
import * as Yup from "yup";
import {
  IAttendance,
  useCreateAttendance,
  useUpdateAttendance,
} from "../../../../server-action/API/attendance";
import { GetAllUser } from "../../../../server-action/API/auth";
import { GlobalForm } from "../../../../components/GlobalForm/GlobalFormComponent";
import HeadingPopup from "../../../GuestManagement/components/HeadingPopup";
import { ActionButton } from "../../../../components/ActionButton";
import { getAttendanceFormData } from "./AttendanceData";

interface LeavesFormProps {
  onClose: () => void;
  editData: IAttendance | null;
}

const AttendanceValidationSchema = Yup.object().shape({
  user: Yup.string().required("Employee is required"),
  date: Yup.string().required("Date is required"),
  checkInTime: Yup.string().required("Check-in time is required"),
  checkOutTime: Yup.string().required("Check-out time is required"),
  attendanceStatus: Yup.string().required("Attendance status is required"),
  remarks: Yup.string().required("Remarks are required"),
});

const AttendanceForm = ({ onClose, editData }: LeavesFormProps) => {
  const { mutateAsync: createAttendance } = useCreateAttendance();
  const { mutateAsync: updateAttendance } = useUpdateAttendance();
  const { data: allUser } = GetAllUser();

  const AttendanceFormData = getAttendanceFormData(allUser || []);

  const formik = useFormik({
    initialValues: {
      user: editData?.user?._id || "",
      date: editData?.date || "",
      checkInTime: editData?.checkInTime || "",
      checkOutTime: editData?.checkOutTime || "",
      attendanceStatus: editData?.attendanceStatus || "",
      remarks: editData?.remarks || "",
    },
    enableReinitialize: true,
    validationSchema: AttendanceValidationSchema,
    onSubmit: async (values) => {
      if (editData) {
        await updateAttendance({
          attendanceData: values as any,
          _id: editData._id as string,
        });
      } else {
        await createAttendance(values as any);
      }
      onClose();
    },
  });

  const { handleSubmit, getFieldProps } = formik;

  return (
    <HeadingPopup
      onClose={onClose}
      className="w-full max-w-screen-md"
      heading={editData ? "Edit Attendance" : "Add Attendance"}
    >
      <FormikProvider value={formik}>
        <Form onSubmit={handleSubmit}>
          <div className="grid grid-cols-2 gap-2">
            <GlobalForm
              formDatails={AttendanceFormData}
              getFieldProps={getFieldProps}
            />
          </div>
          <ActionButton onCancel={onClose} onSubmit={handleSubmit} />
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default AttendanceForm;
