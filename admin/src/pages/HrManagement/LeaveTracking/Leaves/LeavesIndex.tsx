// LeavesIndex.tsx
import {
  useDeleteLeave,
  useGetLeaves,
  useUpdateLeave,
} from "../../../../server-action/API/LeaveTracking/leavetracking";
import { TableAction } from "../../../../layouts/Table/TableAction";
import MasterTable from "../../../../layouts/Table/MasterTable";
import { Status } from "../../../../components/Status";
import { DateForamter } from "../../../../components/DateFormater";

interface LeavesIndexProps {
  togglePopup: (data: any) => void;
  editData: any;
  setEditData: (data: any) => void;
}

const LeavesIndex = ({ togglePopup }: LeavesIndexProps) => {
  const { data: allLeavesData, refetch } = useGetLeaves();
  const { mutateAsync: deleteLeave } = useDeleteLeave();
  const { mutateAsync: updateLeave } = useUpdateLeave();

  // Handle Accept or Reject actions
  const handleStatusUpdate = async (
    leaveId: string,
    status: "accepted" | "rejected" | "pending"
  ) => {
    try {
      await updateLeave({ _id: leaveId, leaveData: { status } });
      refetch(); // Refresh table data
    } catch (error) {
      console.error("Error updating leave status:", error);
    }
  };

  const tableData = {
    column: [
      { title: "SN", key: "sn" },
      { title: "Employee", key: "employee" },
      { title: "Leave Type", key: "leaveType" },
      { title: "From", key: "from" },
      { title: "To", key: "to" },
      { title: "Status", key: "status" },
      { title: "Reason", key: "reason" },
      { title: "Action", key: "action" },
    ],
    rows: allLeavesData?.map((leaves: any, index: any) => ({
      sn: index + 1,
      employee: leaves?.employee?.name || "",
      leaveType: leaves?.leaveType?.name ?? "",
      from: DateForamter(leaves?.from) || "",
      to: DateForamter(leaves?.to) || "",
      status: <Status status={leaves?.status} />,
      reason: leaves?.reason || "",
      action: (
        <TableAction
          switchStatus={leaves?.status}
          onAccept={() => handleStatusUpdate(leaves._id, "accepted")}
          onReject={() => handleStatusUpdate(leaves._id, "rejected")}
          onEdit={() => togglePopup(leaves)}
          onDelete={() => leaves?._id && deleteLeave(leaves._id.toString())}
        />
      ),
    })),
  };

  return (
    <div className="my-2">
      <MasterTable
        loading={false}
        columns={tableData?.column}
        rows={tableData?.rows || []}
      />
    </div>
  );
};

export default LeavesIndex;
