import { Form, FormikProvider, useFormik } from "formik";
import HeadingPopup from "../../../../GuestManagement/components/HeadingPopup";
import { GlobalForm } from "../../../../../components/GlobalForm/GlobalFormComponent";
import { ActionButton } from "../../../../../components/ActionButton";
import { ILeave } from "../../../../../Interface/leave.interface";
import {
  useCreateLeave,
  useGetAllLeaveTypes,
  useUpdateLeave,
} from "../../../../../server-action/API/LeaveTracking/leavetracking";
import { GetAllUser } from "../../../../../server-action/API/auth";
import { getLeaveFormData } from "./LeavesFormData";
import * as Yup from "yup";

interface LeavesFormProps {
  onClose: () => void;
  editData: ILeave | null;
}

const LeaveValidationSchema = Yup.object().shape({
  employee: Yup.string().required("Please Select Employee "),
  from: Yup.date().required("From Date is required").typeError("Invalid date"),
  to: Yup.date()
    .required("To Date is required")
    .typeError("Invalid date")
    .min(Yup.ref("from"), "To Date must be the same or after From Date"),

  leaveType: Yup.string().required("Leave Type is required"),
  reason: Yup.string().required("Reason is required"),
  status: Yup.string().required("Status is required"),
});

const LeavesForm = ({ onClose, editData }: LeavesFormProps) => {
  const { mutateAsync: createLeave } = useCreateLeave();
  const { mutateAsync: updateLeave } = useUpdateLeave();
  const { data: allLeaveTypeData } = useGetAllLeaveTypes();
  const { data: allUser } = GetAllUser();
  const LeaveFormData = getLeaveFormData(allUser || [], allLeaveTypeData || []);

  const formik = useFormik({
    initialValues: {
      employee: editData?.employee?._id || "",
      from: editData?.from || "",
      to: editData?.to || "",
      reason: editData?.reason || "",
      leaveType: editData?.leaveType?._id || "",
      status: editData?.status || "",
    },
    enableReinitialize: true,
    validationSchema: LeaveValidationSchema,
    onSubmit: async (values) => {
      console.log(values, "leave data");
      if (editData) {
        await updateLeave({
          leaveData: values as any,
          _id: editData._id as string,
        });
      } else {
        await createLeave(values as any);
      }
      onClose();
    },
  });

  const { handleSubmit, getFieldProps } = formik;

  return (
    <HeadingPopup
      onClose={onClose}
      className="w-full max-w-screen-sm"
      heading="Leave Application"
    >
      <FormikProvider value={formik}>
        <Form onSubmit={handleSubmit}>
          <div className="grid grid-cols-2 gap-2">
            <GlobalForm
              formDatails={LeaveFormData}
              getFieldProps={getFieldProps}
            />
          </div>
          <ActionButton onCancel={onClose} onSubmit={handleSubmit} />
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default LeavesForm;
