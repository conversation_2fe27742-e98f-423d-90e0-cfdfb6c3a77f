export const getLeaveFormData = (allUser: any[], allLeaveTypeData: any[]) => [
  {
    field: "employee",
    label: "Employee",
    type: "select",
    placeholder: "Select Employee",
    options: allUser
      ? allUser
          .filter(
            (user) =>
              !["admin", "superAdmin", "guest"].includes(
                user?.role?.toLowerCase()
              )
          )
          .map((user) => ({
            value: user?._id,
            label: user?.name,
          }))
      : [],
  },
  {
    field: "leaveType",
    label: "Leave Type",
    type: "select",
    placeholder: "Select Leave Type",
    options: allLeaveTypeData
      ? allLeaveTypeData.map((item) => ({
          value: item?._id,
          label: item?.name,
        }))
      : [],
  },
  {
    field: "from",
    label: "From Date",
    type: "date",
    placeholder: "Select Start Date",
  },
  {
    field: "to",
    label: "To Date",
    type: "date",
    placeholder: "Select End Date",
  },
  {
    field: "reason",
    label: "Reason",
    type: "textarea",
    placeholder: "Enter Reason for Leave",
  },
  {
    field: "status",
    label: "Status",
    type: "select",
    placeholder: "Enter Reason for Leave",
    options: [
      { label: "Pending", value: "pending" },
      { label: "Accepted", value: "accepted" },
      { label: "Rejected", value: "rejected" },
    ],
  },
];
