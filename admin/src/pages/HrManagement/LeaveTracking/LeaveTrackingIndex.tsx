// LeaveTrackingIndex.tsx
import { useCallback, useMemo, useState } from "react";
import { CustomTabs } from "../../../components/CustomTab";
import LeaveTypeIndex from "./LeaveType/LeaveTypeIndex";
import LeavesIndex from "./Leaves/LeavesIndex";
import Header from "../../../components/Header";
import { CardContent } from "../../../components/Card";
import LeavesForm from "./Leaves/Components/LeavesForm";
import LeaveTypeForm from "./LeaveType/Components/LeaveTypeForm";

const LeaveTrackingIndex = () => {
  const [popup, setPopup] = useState(false);
  const [editData, setEditData] = useState(null);
  const [tab, setTab] = useState("Leaves");

  const togglePopup = useCallback((data: any = null) => {
    setEditData(data);
    setPopup((prev) => !prev);
  }, []);

  const tabOptions = useMemo(() => ["Leaves", "Leave Type"], []);
  const onTabChange = useCallback((status: string) => setTab(status), []);

  const handleAddClick = useCallback(() => {
    setEditData(null);
    setPopup(true);
  }, []);

  return (
    <div>
      <Header
        title={tab === "Leaves" ? "Leave" : "Leave Type"}
        onAddClick={handleAddClick}
      />
      <CardContent className="flex items-center border bg-white py-3 rounded-md">
        <CustomTabs
          tabs={tabOptions}
          defaultTab={tab}
          onTabChange={onTabChange}
        />
      </CardContent>

      <div>
        {tab === "Leaves" ? (
          <LeavesIndex
            togglePopup={togglePopup}
            editData={editData}
            setEditData={setEditData}
          />
        ) : (
          <LeaveTypeIndex
            togglePopup={togglePopup}
            editData={editData}
            setEditData={setEditData}
          />
        )}
      </div>
      {popup && tab === "Leaves" && (
        <LeavesForm onClose={() => togglePopup(null)} editData={editData} />
      )}
      {popup && tab === "Leave Type" && (
        <LeaveTypeForm onClose={() => togglePopup(null)} editData={editData} />
      )}
    </div>
  );
};

export default LeaveTrackingIndex;
