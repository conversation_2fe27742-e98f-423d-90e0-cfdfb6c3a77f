import Header from "../../../components/Header";
import MasterTable from "../../../layouts/Table/MasterTable";
import { useGetAllUserLogs } from "../../../server-action/API/userlog";

const UserLogsIndex = () => {
  const { data: allUserLog, isLoading } = useGetAllUserLogs();

  const tableData = {
    columns: [
      { title: "Guest Name", key: "guestName" },
      { title: "IP Address", key: "ipAddress" },
      { title: "Login", key: "login" },
      { title: "Device", key: "device" },
      // { title: "Access Duration", key: "accessDuration" },
      { title: "Role", key: "role" },
    ],

    rows:
      allUserLog?.map((log: any) => ({
        guestName: log?.user?.name || "-",
        ipAddress: log?.ip || "-",
        login: log?.timestamp || "-",
        device: (() => {
          const words = log?.device?.split(" ") || ["-"];
          return (
            <>
              <div>{words.slice(0, 5).join(" ")}</div>
              <div>{words.slice(5, 10).join(" ")}</div>
              <div>{words.slice(10, 15).join(" ")}</div>
            </>
          );
        })(),
        // accessDuration: log.accessDuration || "-",
        role: log?.user?.role || "-",
      })) || [],
  };

  return (
    <div className=" rounded-md shadow">
      <div>
        <Header showButton={false} />
      </div>

      <MasterTable
        columns={tableData.columns}
        rows={tableData.rows}
        loading={isLoading}
      />
    </div>
  );
};

export default UserLogsIndex;
