import { Icon } from "@iconify/react/dist/iconify.js";
import { useState } from "react";
import { PopupModal } from "../../components";
import { Card, CardContent } from "../../components/Card";
import { SampleTableData } from "../../components/SampleData";
import { Status } from "../../components/Status";
import MasterTable from "../../layouts/Table/MasterTable";
import { TableAction } from "../../layouts/Table/TableAction";
import DesignationForm from "./components/DesignationForm";

const DesignationIndex = () => {
  const [showPopup, setShowPopup] = useState("");
  const tableData = {
    columns: [
      { title: "Id", key: "tokenid" },
      { title: "Patient Name", key: "GuestName" },
      { title: "Date ", key: "date" },
      { title: "Room", key: "Room" },
      { title: "checkin", key: "checkin" },
      { title: "Checkout", key: "checkout" },
      { title: "Status", key: "status" },
      { title: "Action", key: "action" },
    ],
    rows: SampleTableData.map(
      (
        { tokenId, GuestName, date, checkin, status, Room, checkout },
        index
      ) => ({
        key: index,
        tokenid: tokenId,
        patientName: GuestName,
        date,
        checkin,
        checkout,
        status: <Status status={status} />,
        Room,
        action: (
          <TableAction
            onShow={() => setShowPopup("view")}
            onEdit={() => {}}
            onDelete={() => {}}
          />
        ),
      })
    ),
  };

  return (
    <div>
      <Card className="bg-white">
        <CardContent className="flex items-center justify-between">
          <button
            className=" bg-[#2E4476] text-white flex items-center gap-1 rounded-md p-2"
            type="button"
            onClick={() => setShowPopup("addDesignation")}
          >
            <Icon icon="fluent:add-20-regular" width="18" height="18" />
            Add Designation
          </button>
          {showPopup === "addDesignation" && (
            <DesignationForm onClose={() => setShowPopup("")} />
          )}
        </CardContent>
      </Card>

      {showPopup === "view" && (
        <PopupModal onClose={() => setShowPopup("")}>
          <div>hello</div>
        </PopupModal>
      )}
      <div className="bg-white ">
        <MasterTable
          columns={tableData?.columns}
          rows={tableData.rows}
          loading={false}
          pagination={{
            currentPage: 1,
            totalPage: 200,
            limit: 5,
            onClick: () => {},
          }}
        />
      </div>
    </div>
  );
};

export default DesignationIndex;
