import { Icon } from "@iconify/react/dist/iconify.js";
import { get } from "lodash";
import { useCallback, useMemo, useState } from "react";
import { CustomTabs } from "../../../../components/CustomTab";
import MasterTable from "../../../../layouts/Table/MasterTable";
import EmployeeForm from "./EmployeeForm";
import Performance from "./Performance";
import { useGetSalaryById } from "../../../../server-action/API/employeepayroll";
import { format } from "date-fns";
import { IMAGE_URL } from "../../../../constant/constant";
import { useDeleteUser } from "../../../../server-action/API/auth";
import { DeleteDialog } from "../../../../components";

type Employee = {
  _id: string;
  name: string;
  avatar?: string;
  role: string;
  department?: { name: string };
  photo?: string[];
  phoneNumber?: string;
  email?: string;
  joinDate?: string;
  basicSalary?: number;
  designation?: { name: string };
  staffStatus?: string;
  permanentAddress?: {
    district: string;
    municipality: string;
    tole: string;
  };
};

const EmployeeDetails = ({
  selectedEmployee,
}: {
  selectedEmployee: Employee | null;
}) => {
  console.log(selectedEmployee, "selectedEmployee");
  const [activeTab, setActiveTab] = useState("Salary History");
  const [openForm, setOpen] = useState({
    state: false,
    edit: false,
    editData: false,
  });
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const tabOptions = useMemo(() => ["Salary History", "Performance"], []);

  const { mutateAsync: deleteEmployee } = useDeleteUser();

  const getMonthName = (monthNum: string) => {
    const monthNames = [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December",
    ];
    const num = parseInt(monthNum);
    return monthNames[num - 1] || monthNum;
  };

  // Fetch salary data for the selected employee
  const { data: salaryData, isSuccess: isSalarySuccess } = useGetSalaryById(
    selectedEmployee?._id || ""
  );

  // Format salary data for display
  const salaryHistory = useMemo(() => {
    if (!isSalarySuccess || !salaryData || !selectedEmployee) return [];

    return salaryData.map((salary) => {
      const basicSalary = selectedEmployee.basicSalary || 0;
      const allowances = salary.totalAllowance || 0;
      const deductions = salary.totalDeduction || 0;
      // const netSalary = basicSalary + allowances - deductions;
      console.log(salary, "aa");

      return {
        _id: salary._id,
        month: `${getMonthName(salary.targetMonth)} ${salary.targetYear}`,
        amount: basicSalary,
        bonus: 0,
        deductions: deductions,
        allowances: allowances,
        // netSalary: netSalary,
        dispatchedSalary:
          salary.dispatchedSalary && salary.dispatchedSalary > 0
            ? salary.dispatchedSalary.toFixed(1)
            : "0",

        paymentDate: salary.updatedAt
          ? format(new Date(salary.updatedAt), "yyyy-MM-dd")
          : "Pending",
        paymentType: salary.paymentType || "",
      };
    });
  }, [isSalarySuccess, salaryData, selectedEmployee]);

  const columns = useMemo(
    () => [
      { title: "Month", key: "month" },
      { title: "Base Salary", key: "amount" },
      { title: "Allowances", key: "allowances" },
      { title: "Deductions", key: "deductions" },
      // { title: "Net Salary", key: "netSalary" },
      { title: "Dispatch Salary", key: "dispatchedSalary" },
      { title: "Payment Date", key: "paymentDate" },
      { title: "Payment Type", key: "paymentType" },
    ],
    []
  );

  const rows = useMemo(() => salaryHistory, [salaryHistory]);
  const closeForm = useCallback(() => {
    setOpen({
      state: false,
      edit: false,
      editData: false,
    });
  }, []);
  const openEmployeeForm = useCallback(
    ({
      state,
      edit = false,
      editData = null,
    }: {
      state: boolean;
      edit?: boolean;
      editData?: any;
    }) => setOpen({ state, edit, editData }),
    []
  );

  // Get employee status
  const getEmployeeStatus = useCallback(() => {
    const status = get(selectedEmployee, "staffStatus", "").toLowerCase();

    if (status === "permanent") {
      return {
        text: "Permanent",
        className: "text-green-600 bg-green-100 border-green-600",
      };
    } else if (status === "temporary") {
      return {
        text: "Temporary",
        className: "text-orange-600 bg-orange-100 border-orange-600",
      };
    } else {
      return {
        text: "Active",
        className: "text-blue-600 bg-blue-100 border-blue-600",
      };
    }
  }, [selectedEmployee]);

  const status = getEmployeeStatus();

  const handleDeleteEmployee = () => {
    if (selectedEmployee?._id) {
      setShowDeleteDialog(true);
    }
  };

  const confirmDelete = async () => {
    if (selectedEmployee?._id) {
      try {
        await deleteEmployee(selectedEmployee._id);
        setShowDeleteDialog(false);
        // Optionally, reset selectedEmployee or trigger a refresh
      } catch (error) {
        console.error("Failed to delete employee:", error);
      }
    }
  };

  return (
    <div className="w-full h-full">
      {!selectedEmployee ? (
        <div className="flex-1 p-8 rounded shadow text-center text-[#585858]">
          No employee selected
        </div>
      ) : (
        <div className="flex-1 bg-white rounded-lg shadow">
          <div className="bg-[#f0f0f0] rounded-t-lg shadow-sm border p-4 mx-auto">
            <div className="flex items-start">
              <div className="relative w-1/4 mr-6">
                {selectedEmployee?.photo &&
                selectedEmployee?.photo?.length > 0 ? (
                  <img
                    src={`${IMAGE_URL}/${selectedEmployee?.photo[0]}`}
                    alt={selectedEmployee.name}
                    className="w-full aspect-[4/4] object-cover rounded-md"
                  />
                ) : (
                  <Icon
                    icon={"clarity:employee-line"}
                    height={220}
                    width={500}
                    className="object-cover rounded-md"
                  />
                )}
              </div>

              <div className="flex-1">
                <div className="flex justify-between items-start">
                  <div>
                    <div className="flex items-center gap-2">
                      <h2 className="text-xl font-semibold text-gray-800">
                        {selectedEmployee.name || "N/A"}
                      </h2>
                      <span className="px-2 py-0.5 text-xs bg-[#D4EDDA] text-black border border-[#34c759] rounded-sm">
                        {status.text}
                      </span>
                    </div>

                    <div className="flex mt-1">
                      {[...Array(5)].map((_, i) => (
                        <span
                          key={i}
                          className={`text-yellow-400 ${
                            i < 4 ? "text-yellow-400" : "text-gray-300"
                          }`}
                        >
                          ★
                        </span>
                      ))}
                    </div>
                  </div>

                  <div className="flex flex-col gap-2">
                    <button
                      onClick={() => openEmployeeForm({ state: true })}
                      className="flex items-center gap-2 px-2 py-[6px] text-sm border-2 rounded-md border-[#163381] hover:bg-gray-50"
                    >
                      <Icon
                        icon="bx:edit"
                        width="18"
                        height="18"
                        className="text-black"
                      />
                      <span className="text-black text-sm font-medium">
                        Edit
                      </span>
                    </button>
                    <button
                      onClick={handleDeleteEmployee}
                      className="flex items-center gap-2 px-2 py-[6px] text-sm border-2 rounded-md border-red hover:bg-gray-50"
                    >
                      <Icon
                        icon="fluent-mdl2:delete"
                        width="18"
                        height="18"
                        className="text-red"
                      />
                      <span className="text-red text-sm font-medium">
                        Delete
                      </span>
                    </button>
                  </div>
                </div>

                <div className="mt-4 space-y-4">
                  <div className="flex items-center gap-6">
                    <div className="flex items-center gap-2">
                      <span className="text-blue-500">
                        <Icon
                          icon="subway:call"
                          width="16"
                          height="16"
                          color="#4960A8"
                        />
                      </span>
                      <span className="text-sm text-gray-700">
                        {selectedEmployee.phoneNumber || "N/A"}
                      </span>
                    </div>

                    <div className="flex items-center gap-2">
                      <span className="text-blue-500">
                        <Icon
                          icon="mdi:email"
                          width="16"
                          height="16"
                          color="#4960A8"
                        />
                      </span>
                      <span className="text-sm text-gray-700">
                        {selectedEmployee.email || "N/A"}
                      </span>
                    </div>
                  </div>

                  <div className="pt-2 border-t">
                    <h3 className="text-sm font-bold text-[#818181] mb-3">
                      Employee Detail
                    </h3>

                    <div className="grid grid-cols-2 gap-y-3">
                      <div className="flex items-center gap-2">
                        <span className="w-5 h-5 flex items-center justify-center text-[#585858]">
                          <Icon icon="mdi:company" width="16" height="16" />
                        </span>
                        <span className="text-sm">
                          {selectedEmployee.department?.name ||
                            selectedEmployee.role ||
                            "No designation"}
                        </span>
                      </div>

                      <div className="flex items-center gap-2">
                        <span className="text-[#585858]">
                          <Icon
                            icon="basil:location-solid"
                            width="16"
                            height="16"
                          />
                        </span>
                        <span className="text-sm">
                          {selectedEmployee?.permanentAddress
                            ? `${
                                selectedEmployee.permanentAddress.district || ""
                              }, ${
                                selectedEmployee.permanentAddress
                                  .municipality || ""
                              }, ${
                                selectedEmployee.permanentAddress.tole || ""
                              }`
                            : "No Address"}
                        </span>
                      </div>

                      <div className="flex items-center gap-2">
                        <span className="text-[#585858]">
                          <Icon
                            icon="dashicons:money-alt"
                            width="16"
                            height="16"
                          />
                        </span>
                        <span className="text-sm">
                          Salary: Rs.{" "}
                          {selectedEmployee.basicSalary?.toLocaleString() ||
                            "N/A"}
                        </span>
                      </div>

                      <div className="flex items-center gap-2">
                        <span className="text-[#585858]">
                          <Icon icon="mdi:calendar" width="16" height="16" />
                        </span>
                        <span className="text-sm">
                          Join Date: {selectedEmployee.joinDate || "N/A"}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="mb-4 border-b mt-3">
            <CustomTabs
              tabs={tabOptions}
              defaultTab={activeTab}
              onTabChange={setActiveTab}
            />
          </div>

          {activeTab === "Salary History" &&
            (rows.length > 0 ? (
              <div>
                <MasterTable
                  loading={false}
                  columns={columns}
                  rows={rows.map((row) => ({
                    ...row,
                    amount: `Rs ${row.amount.toLocaleString()}`,
                    allowances: `Rs ${row.allowances.toLocaleString()}`,
                    deductions: `Rs ${row.deductions.toLocaleString()}`,
                    // netSalary: `Rs ${row.netSalary.toLocaleString()}`,
                  }))}
                  sortBy="createdAt"
                  sortOrder="desc"
                />
              </div>
            ) : (
              <div className="py-8 text-center text-[#585858]">
                No salary history available
              </div>
            ))}

          {activeTab === "Performance" && (
            <div className="py-8 text-center text-[#585858]">
              <Performance selectedEmployee={selectedEmployee} />
            </div>
          )}
          {openForm.state && (
            <EmployeeForm onClose={closeForm} editData={selectedEmployee} />
          )}
          {showDeleteDialog && (
            <DeleteDialog
              confirmAction={true}
              title="Delete Employee"
              des={`Are you sure you want to delete ${selectedEmployee.name}? This action cannot be undone.`}
              onClose={() => setShowDeleteDialog(false)}
              onConfirm={confirmDelete}
            />
          )}
        </div>
      )}
    </div>
  );
};

export default EmployeeDetails;
