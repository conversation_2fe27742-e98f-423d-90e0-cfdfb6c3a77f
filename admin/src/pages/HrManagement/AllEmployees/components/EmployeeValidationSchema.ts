import * as Yup from "yup";

export const EmployeeValidationSchema = Yup.object().shape({
  // Basic Details - Step 1
  role: Yup.string().required("Role is required"),
  name: Yup.string()
    .required("Employee name is required")
    .min(3, "Name must be at least 3 characters"),
  phoneNumber: Yup.string()
    .required("Phone number is required")
    .matches(/^[0-9]+$/, "Phone number must contain only digits")
    .min(10, "Phone number must be at least 10 digits"),
  email: Yup.string()
    .email("Invalid email address")
    .required("Email is required"),
  // password: Yup.string()
  //   .required("Password is required")
  //   .min(6, "Password must be at least 6 characters"),
  DOB: Yup.date().required("Date of birth is required"),
  gender: Yup.string().required("Gender is required"),

  // Address validation
  tempAddress: Yup.object().shape({
    district: Yup.string().required("District is required"),
    municipality: Yup.string().required("Municipality is required"),
    tole: Yup.string().required("Tole is required"),
  }),
  permanentAddress: Yup.object().shape({
    district: Yup.string().required("District is required"),
    municipality: Yup.string().required("Municipality is required"),
    tole: Yup.string().required("Tole is required"),
  }),

  religion: Yup.string().required("Religion is required"),
  maritalStatus: Yup.string().required("Marital status is required"),
  staffType: Yup.string().required("Staff type is required"),

  // Job Details - Step 2
  joinDate: Yup.date().required("Joining date is required"),
  department: Yup.string().required("Department is required"),
  designation: Yup.string().required("Designation is required"),
  shiftType: Yup.string().required("Shift type is required"),

  // Salary Details - Step 3
  applyTDS: Yup.boolean(),
  basicSalary: Yup.number()
    .required("Basic salary is required")
    .min(0, "Basic salary cannot be negative"),
  annualSalary: Yup.number()
    .required("Annual salary is required")
    .min(0, "Annual salary cannot be negative"),
  // festivalAllowanceMonth: Yup.string().required(
  //   "Festival allowance month is required"
  // ),
  overTimePerHr: Yup.number().min(0, "Overtime rate cannot be negative"),
  paidLeave: Yup.number().min(0, "Paid leave cannot be negative"),

  // Allowance and Deduction validation
  // allowance: Yup.array().of(
  //   Yup.object().shape({
  //     name: Yup.string().required("Allowance is required"),
  //   })
  // ),
  // deduction: Yup.array().of(
  //   Yup.object().shape({
  //     name: Yup.string().required("Deduction is required"),
  //   })
  // ),

  // Document Details - Step 4
  // photo: Yup.array()
  //   .min(1, "At least one photo is required")
  //   .required("Photo is required"),
  // citizenship: Yup.array()
  //   .min(1, "At least one citizenship document is required")
  //   .required("Citizenship document is required"),
});
