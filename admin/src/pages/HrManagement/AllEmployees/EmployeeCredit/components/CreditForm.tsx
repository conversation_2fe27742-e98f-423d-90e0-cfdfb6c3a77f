import React, { use } from "react";
import { Form, FormikProvider, useFormik } from "formik";
import {
  useGetRepaymentFrequency,
  useGetRepaymentMethod,
} from "../../../../../server-action/API/EmployeeConfiguration/creditconfig";
import { useGetAllUser } from "../../../../../server-action/API/user";
import {
  ICredit,
  useCreateCredit,
  useUpdateCredit,
} from "../../../../../server-action/API/employeecredit";
import { getCreditFormData } from "./CreditFormData";
import { CreditFormSchema } from "./CreditValidatonSchema";
import HeadingPopup from "../../../../GuestManagement/components/HeadingPopup";
import { GlobalForm } from "../../../../../components/GlobalForm/GlobalFormComponent";
import { ActionButton } from "../../../../../components/ActionButton";

interface ICreditFormProps {
  editData?: any;
  edit?: boolean;
  close: () => void;
}

const CreditForm: React.FC<ICreditFormProps> = ({
  close,
  edit = false,
  editData,
}) => {
  const { data: repaymentMethods = [] } = useGetRepaymentMethod();
  const { data: repaymentFrequencies = [] } = useGetRepaymentFrequency();
  const { data: employees = [] } = useGetAllUser({ role_ne: "guest" });
  const { mutateAsync: createCredit } = useCreateCredit();
  const { mutateAsync: updateCredit } = useUpdateCredit();

  const formData = getCreditFormData(
    repaymentMethods,
    repaymentFrequencies,
    employees
  );

  const formik = useFormik<ICredit>({
    initialValues: {
      user: editData?.user?._id || "",
      date: editData?.date?.slice(0, 10) || "",
      creditAmount: editData?.creditAmount || 0,
      repaymentStartDate: editData?.repaymentStartDate?.slice(0, 10) || "",
      installmentAmt: editData?.installmentAmt || 0,
      description: editData?.description || "",
      reason: editData?.reason || "",
      creditRepaymentMethod: editData?.creditRepaymentMethod?._id || "",
      creditRepaymentFrequency: editData?.creditRepaymentFrequency?._id || "",
      paidAmount: editData?.paidAmount || 0,
      status: editData?.status || "pending",
    },
    validationSchema: CreditFormSchema,
    enableReinitialize: true,
    onSubmit: async (values) => {
      try {
        // Auto-update status based on amounts
        if ((values.paidAmount ?? 0) >= values.creditAmount) {
          values.status = "paid";
        } else {
          values.status = "unpaid"; // or "pending" if that's your default
        }

        if (editData) {
          await updateCredit({ creditData: values, _id: editData._id });
        } else {
          await createCredit(values);
        }
        close();
      } catch (error) {
        console.error("Failed to submit credit form", error);
      }
    },
  });

  const { handleSubmit, getFieldProps } = formik;

  return (
    <HeadingPopup
      heading={edit ? "Edit Employee Credit" : "Add Employee Credit"}
      onClose={close}
      className="w-[60vw]"
    >
      <FormikProvider value={formik}>
        <div className=" gap-4">
          <Form onSubmit={handleSubmit} className="grid grid-cols-3 gap-4">
            <GlobalForm formDatails={formData} getFieldProps={getFieldProps} />
          </Form>
          <ActionButton onCancel={close} onSubmit={handleSubmit} />
        </div>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default CreditForm;
