import { useCallback, useMemo, useState } from "react";
import { CustomTabs } from "../../../components/CustomTab";
import { CardContent } from "../../../components/Card";
import { Icon } from "@iconify/react/dist/iconify.js";
import JobConfigIndex from "./Component/JobConfig/JobConfigIndex";
import SalaryConfigIndex from "./Component/SalaryConfig/SalaryConfigIndex";
import CreditConfigIndex from "./Component/CreditConfig/CreditConfigIndex";
import Header from "../../../components/Header";

const EmployeeConfigurationIndex = () => {
  const [tab, setTab] = useState("Job Config");
  const tabOptions = useMemo(
    () => ["Job Config", "Salary Config", "Credit Config"],
    []
  );
  const onTabChange = useCallback((status: string) => setTab(status), []);

  return (
    <div>
      <Header showButton={false} />
      <CardContent className="flex items-center border py-3 mb-2 bg-white rounded-md">
        <CustomTabs
          tabs={tabOptions}
          defaultTab={tab}
          onTabChange={onTabChange}
        />
        {/* 
        <Icon
          icon="fluent:add-20-regular"
          className="text-white"
          width="30"
          height="30"
        /> */}
      </CardContent>
      <div>
        {tab === "Job Config" ? (
          <JobConfigIndex />
        ) : tab === "Salary Config" ? (
          <SalaryConfigIndex />
        ) : (
          <CreditConfigIndex />
        )}
      </div>
    </div>
  );
};

export default EmployeeConfigurationIndex;
