import { Form, FormikProvider, useFormik } from "formik";
import HeadingPopup from "../../../../../../GuestManagement/components/HeadingPopup";
import { FormField } from "../../../../../../BookingManagement/components/ReservationCustomForm";
import { IRepaymentFrequency } from "../../../../../../../Interface/employeeconfig.interface";
import {
  useCreateRepaymentFrequency,
  useUpdateRepaymentFrequency,
} from "../../../../../../../server-action/API/EmployeeConfiguration/creditconfig";
import * as Yup from "yup";
interface IRepaymentFrequencyForm {
  onClose: () => void;
  editData: IRepaymentFrequency | null;
}

const RepaymentFrequencySchema = Yup.object().shape({
  name: Yup.string().required("Repayment Frequency is required"),
});

const RepaymentFrequencyForm = ({
  onClose,
  editData,
}: IRepaymentFrequencyForm) => {
  const { mutateAsync: createRepaymentFrequency } =
    useCreateRepaymentFrequency();
  const { mutateAsync: updateRepaymentFrequency } =
    useUpdateRepaymentFrequency();
  const formik = useFormik<IRepaymentFrequency>({
    initialValues: {
      name: editData?.name ?? "",
    },
    enableReinitialize: true,
    validationSchema: RepaymentFrequencySchema,
    onSubmit: async (values, { resetForm }) => {
      onClose();
      try {
        if (editData?._id) {
          await updateRepaymentFrequency({
            _id: String(editData._id),
            repaymentFrequencyData: values,
          });
        } else {
          await createRepaymentFrequency(values);
        }
        resetForm();
      } catch (error) {
        console.error("Error submitting repayment method:", error);
      }
    },
  });
  return (
    <HeadingPopup
      onClose={onClose}
      className="w-full max-w-screen-sm"
      heading="Add Repayment Frequency"
    >
      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit} className="space-y-3">
          <div className="grid grid-cols-2 gap-x-4 gap-y-3">
            <FormField
              label="Repayment Frequency"
              type="text"
              name="name"
              formik={formik}
              placeholder="Enter Repayment Frequency"
            />
          </div>
          <div className="flex items-center mt-4">
            <button
              type="submit"
              className="px-4 py-2 bg-[#2A3A6D] text-white rounded-md"
            >
              {editData ? "Update" : "Add"}
            </button>
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default RepaymentFrequencyForm;
