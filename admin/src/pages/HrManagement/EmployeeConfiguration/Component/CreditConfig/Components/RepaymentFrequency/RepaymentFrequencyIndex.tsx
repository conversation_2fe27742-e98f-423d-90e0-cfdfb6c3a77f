import { useCallback, useState } from "react";
import { Card, CardContent } from "../../../../../../../components/Card";
import MasterTable from "../../../../../../../layouts/Table/MasterTable";
import RepaymentFrequencyForm from "./RepaymentFrequencyForm";
import {
  useDeleteRepaymentFrequency,
  useGetRepaymentFrequency,
} from "../../../../../../../server-action/API/EmployeeConfiguration/creditconfig";
import { TableAction } from "../../../../../../../layouts/Table/TableAction";

const RepaymentFrequencyIndex = () => {
  const [popup, setPopup] = useState(false); // Use boolean for toggle simplicity
  const [editData, setEditData] = useState(null);

  const { data: repaymentFrequencyData } = useGetRepaymentFrequency();
  const { mutateAsync: deleteRepayFrequency } = useDeleteRepaymentFrequency();

  const tableData = {
    columns: [
      { title: "SN", key: "sn" },
      { title: "Repayment Frequency", key: "name" },
      { title: "Action", key: "action" },
    ],
    rows: repaymentFrequencyData?.map((repaymentFrequency, index) => ({
      sn: index + 1,
      name: repaymentFrequency?.name,
      action: (
        <TableAction
          onEdit={() => {
            setEditData(repaymentFrequency as any);
            setPopup(true);
          }}
          onDelete={() =>
            repaymentFrequency?._id &&
            deleteRepayFrequency(repaymentFrequency._id.toString())
          }
        />
      ),
    })),
  };

  const togglePopup = useCallback(() => setPopup((prev) => !prev), []);

  return (
    <div className="w-full">
      <Card className="bg-white">
        <CardContent className="flex items-center justify-between">
          <p>Repayment Frequency</p>
          <button
            onClick={() => {
              togglePopup();
              setEditData(null);
            }}
            className="flex whitespace-nowrap items-center justify-center gap-1 px-4 bg-[#2E4476] py-1 text-white rounded-md"
          >
            Add Repayment Frequency
          </button>
        </CardContent>
      </Card>
      <div>
        <MasterTable
          columns={tableData.columns}
          rows={tableData.rows || []}
          loading={false}
        />
      </div>
      {popup && (
        <RepaymentFrequencyForm onClose={togglePopup} editData={editData} />
      )}
      {/* Render when popup is true */}
    </div>
  );
};

export default RepaymentFrequencyIndex;
