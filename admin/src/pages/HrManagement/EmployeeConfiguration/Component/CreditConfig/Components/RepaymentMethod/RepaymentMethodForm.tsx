import { FormikProvider, useFormik, Form } from "formik";
import { IRepaymentMethod } from "../../../../../../../Interface/employeeconfig.interface";
import {
  useCreateRepaymentMethod,
  useUpdateRepaymentMethod,
} from "../../../../../../../server-action/API/EmployeeConfiguration/creditconfig";
import HeadingPopup from "../../../../../../GuestManagement/components/HeadingPopup";
import { FormField } from "../../../../../../BookingManagement/components/ReservationCustomForm";
import * as Yup from "yup";
interface IRepaymentMethodForm {
  onClose: () => void;
  editData: IRepaymentMethod | null;
}

const RepaymentMethodSchema = Yup.object().shape({
  name: Yup.string().required("Repayment Method Name is required"),
});

const RepaymentMethodForm = ({ onClose, editData }: IRepaymentMethodForm) => {
  const { mutateAsync: createRepaymentMethod } = useCreateRepaymentMethod();
  const { mutateAsync: updateRepaymentMethod } = useUpdateRepaymentMethod();

  const formik = useFormik<IRepaymentMethod>({
    initialValues: {
      name: editData?.name || "",
    },
    enableReinitialize: true,
    validationSchema: RepaymentMethodSchema,
    onSubmit: async (values, { resetForm }) => {
      onClose();
      try {
        if (editData?._id) {
          await updateRepaymentMethod({
            _id: String(editData._id),
            repaymentMethodData: values,
          });
        } else {
          await createRepaymentMethod(values);
        }
        resetForm();
      } catch (error) {
        console.error("Error submitting repayment method:", error);
      }
    },
  });

  return (
    <HeadingPopup
      onClose={onClose}
      className="w-full max-w-screen-sm"
      heading={editData ? "Edit Repayment Method" : "Add Repayment Method"}
    >
      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit} className="space-y-3">
          <div className="grid grid-cols-2 gap-x-4 gap-y-3">
            <FormField
              label="Repayment Method"
              type="text"
              name="name"
              formik={formik}
              placeholder="Enter Repayment Method"
            />
          </div>
          <div className="flex items-center mt-4">
            <button
              type="submit"
              className="px-4 py-2 bg-[#2A3A6D] text-white rounded-md"
            >
              {editData ? "Update" : "Add"}
            </button>
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default RepaymentMethodForm;
