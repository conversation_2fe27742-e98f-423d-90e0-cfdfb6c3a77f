import { useCallback, useState } from "react";
import { Card, CardContent } from "../../../../../../../components/Card";
import MasterTable from "../../../../../../../layouts/Table/MasterTable";
import RepaymentMethodForm from "./RepaymentMethodForm";
import {
  useDeleteRepaymentMethod,
  useGetRepaymentMethod,
} from "../../../../../../../server-action/API/EmployeeConfiguration/creditconfig";
import { TableAction } from "../../../../../../../layouts/Table/TableAction";

const RepaymentMethodIndex = () => {
  const [popup, setPopup] = useState(false); // Use boolean for toggle simplicity
  const [editData, setEditData] = useState(null);
  const { data: repaymentMethodData } = useGetRepaymentMethod();
  const { mutateAsync: deleteRepaymentMethod } = useDeleteRepaymentMethod();

  const tableData = {
    columns: [
      { title: "SN", key: "sn" },
      { title: "Repayment Method", key: "name" },
      { title: "Action", key: "action" },
    ],
    rows: repaymentMethodData?.map((repaymentMethod, index) => ({
      sn: index + 1,
      name: repaymentMethod?.name,
      action: (
        <TableAction
          onEdit={() => {
            setEditData(repaymentMethod as any);
            setPopup(true);
          }}
          onDelete={() =>
            repaymentMethod?._id &&
            deleteRepaymentMethod(repaymentMethod._id.toString())
          }
        />
      ),
    })),
  };
  const togglePopup = useCallback(() => setPopup((prev) => !prev), []);

  return (
    <div className="w-full">
      <Card className="bg-white">
        <CardContent className="flex items-center justify-between">
          <p>Repayment Method</p>
          <button
            onClick={() => {
              togglePopup();
            }}
            className="flex whitespace-nowrap items-center justify-center gap-1 px-4 bg-[#2E4476] py-1 text-white rounded-md"
          >
            Add Repayment Method
          </button>
        </CardContent>
      </Card>
      <div>
        <MasterTable
          columns={tableData.columns}
          rows={tableData.rows || []}
          loading={false}
        />
      </div>
      {popup && (
        <RepaymentMethodForm onClose={togglePopup} editData={editData} />
      )}
      {/* Render when popup is true */}
    </div>
  );
};

export default RepaymentMethodIndex;
