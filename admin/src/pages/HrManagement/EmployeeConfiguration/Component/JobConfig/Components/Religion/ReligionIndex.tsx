import { useCallback, useState } from "react";
import { Card, CardContent } from "../../../../../../../components/Card";
import MasterTable from "../../../../../../../layouts/Table/MasterTable";
import ReligionForm from "./ReligionForm";
import {
  useDeleteReligion,
  useGetReligion,
} from "../../../../../../../server-action/API/EmployeeConfiguration/jobconfig";
import { TableAction } from "../../../../../../../layouts/Table/TableAction";

const ReligionIndex = () => {
  const [popup, setPopup] = useState(false);
  const { data: religionData } = useGetReligion();
  const { mutateAsync: deleteReligion } = useDeleteReligion();
  const [editData, setEditData] = useState(null);

  const tableData = {
    columns: [
      { title: "SN", key: "sn" },
      { title: "Religion", key: "name" },
      { title: "Action", key: "action" },
    ],
    rows: religionData?.map((religion, index) => ({
      sn: index + 1,
      name: religion?.name,
      action: (
        <TableAction
          onEdit={() => {
            setEditData(religion as any);
            setPopup(true);
          }}
          onDelete={() =>
            religion._id && deleteReligion(religion._id.toString())
          }
        />
      ),
    })),
  };

  const togglePopup = useCallback(() => setPopup((prev) => !prev), []);

  return (
    <div className="w-full">
      <Card className="bg-white">
        <CardContent className="flex items-center justify-between">
          <p>Religon</p>
          <button
            onClick={() => {
              togglePopup();
              setEditData(null);
            }} // Use toggle function
            className="flex whitespace-nowrap items-center justify-center gap-1 px-4 bg-[#2E4476] py-1 text-white rounded-md"
          >
            Add Religion
          </button>
        </CardContent>
      </Card>
      <div>
        <MasterTable
          columns={tableData.columns}
          rows={tableData.rows || []}
          loading={false}
        />
      </div>
      {popup && <ReligionForm onClose={togglePopup} editData={editData} />}
      {/* Render when popup is true */}
    </div>
  );
};

export default ReligionIndex;
