import { Form, FormikProvider, useFormik } from "formik";
import HeadingPopup from "../../../../../../GuestManagement/components/HeadingPopup";
import { FormField } from "../../../../../../BookingManagement/components/ReservationCustomForm";
import { IReligion } from "../../../../../../../Interface/employeeconfig.interface";
import {
  useCreateReligion,
  useUpdateReligion,
} from "../../../../../../../server-action/API/EmployeeConfiguration/jobconfig";
import * as Yup from "yup";
interface IReligionForm {
  onClose: () => void;
  editData?: IReligion | null;
}

const ReligionValidationSchema = Yup.object().shape({
  name: Yup.string()
    .required("Religion name is required")
    .min(2, "Religion name must be at least 2 characters"),
});
const ReligionForm = ({ onClose, editData }: IReligionForm) => {
  const { mutateAsync: updateReligion } = useUpdateReligion();
  const { mutateAsync: createReligion } = useCreateReligion();
  const formik = useFormik({
    initialValues: {
      name: editData?.name || "",
    },
    enableReinitialize: true,
    validationSchema: ReligionValidationSchema,
    onSubmit: async (values, { resetForm }) => {
      onClose();
      try {
        if (editData && editData._id) {
          await updateReligion({
            _id: String(editData._id),
            religionData: values,
          });
        } else {
          await createReligion(values);
        }
        resetForm();
      } catch (error) {
        console.error("Error submitting department:", error);
      }
    },
  });
  return (
    <HeadingPopup
      onClose={onClose}
      className="w-full max-w-screen-sm"
      heading={` ${editData ? "Edit" : "Add"} Religion`}
    >
      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit} className="space-y-3">
          <FormField
            label="Religion"
            type="text"
            name="name"
            formik={formik}
            placeholder="Enter Religion"
          />

          <div className="flex items-center mt-4">
            <button
              type="submit"
              className="px-4 py-2 bg-[#2A3A6D] text-white rounded-md"
            >
              {editData ? "Update" : "Add"}
            </button>
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default ReligionForm;
