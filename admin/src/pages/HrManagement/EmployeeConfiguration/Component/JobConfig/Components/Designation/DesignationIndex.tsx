import { useCallback, useState } from "react";
import MasterTable from "../../../../../../../layouts/Table/MasterTable";
import DesignationForm from "./DesignationForm";
import {
  useDeleteDesignation,
  useGetDesignation,
} from "../../../../../../../server-action/API/EmployeeConfiguration/jobconfig";
import { TableAction } from "../../../../../../../layouts/Table/TableAction";
import { Card, CardContent } from "../../../../../../../components/Card";

const DesignationIndex = () => {
  const [popup, setPopup] = useState(false);
  const [editData, setEditData] = useState(null);
  const { data: designationData = [] } = useGetDesignation();
  const { mutateAsync: deleteDesignation } = useDeleteDesignation();

  const togglePopup = useCallback(() => setPopup((prev) => !prev), []);

  const tableData = {
    columns: [
      { title: "SN", key: "sn" },
      { title: "Designation", key: "name" },
      { title: "Action", key: "action" },
    ],
    rows: designationData.map((designation, index) => ({
      sn: index + 1,
      name: `${designation?.name} (${designation?.department?.name}) `,
      //(${designation?.department || ""}) if department is needed
      action: (
        <TableAction
          onEdit={() => {
            setEditData(designation as any);
            setPopup(true);
          }}
          onDelete={async () => {
            if (!designation?._id) return;
            try {
              await deleteDesignation(designation._id.toString());
            } catch (error) {
              console.error("Failed to delete designation:", error);
            }
          }}
        />
      ),
    })),
  };

  return (
    <div className="w-full">
      <Card className="bg-white">
        <CardContent className="flex items-center justify-between">
          <p>Designation</p>
          <button
            onClick={() => {
              setEditData(null);
              togglePopup();
            }}
            className="flex whitespace-nowrap items-center justify-center gap-1 px-4 bg-[#2E4476] py-1 text-white rounded-md"
          >
            Add Designation
          </button>
        </CardContent>
      </Card>
      <div>
        <MasterTable
          columns={tableData.columns}
          rows={tableData.rows}
          loading={false}
        />
      </div>
      {popup && <DesignationForm onClose={togglePopup} editData={editData} />}
    </div>
  );
};

export default DesignationIndex;
