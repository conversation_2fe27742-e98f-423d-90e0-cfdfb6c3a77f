import { useCallback, useState } from "react";
import {
  useDeleteDepartment,
  useGetDepartment,
} from "../../../../../../../server-action/API/EmployeeConfiguration/jobconfig";
import { TableAction } from "../../../../../../../layouts/Table/TableAction";
import { Card, CardContent } from "../../../../../../../components/Card";
import MasterTable from "../../../../../../../layouts/Table/MasterTable";
import DepartmentForm from "./DepartmentForm";

const DepartmentIndex = () => {
  const [popup, setPopup] = useState(false);
  const { data: departmentData, isPending } = useGetDepartment();
  const { mutateAsync: deleteDepartment } = useDeleteDepartment();
  const [editData, setEditData] = useState(null);

  const togglePopup = useCallback(() => setPopup((prev) => !prev), []);

  const tableData = {
    columns: [
      { title: "SN", key: "sn" },
      { title: "Department", key: "name" },
      { title: "Action", key: "action" },
    ],
    rows: departmentData?.map((dept, index) => ({
      sn: index + 1,
      name: dept.name,
      action: (
        <TableAction
          onEdit={() => {
            setEditData(dept as any);
            setPopup(true);
          }}
          onDelete={() => dept?._id && deleteDepartment(dept._id.toString())}
        />
      ),
    })),
  };

  return (
    <div className="w-full">
      <Card className="bg-white">
        <CardContent className="flex items-center justify-between">
          <p>Department</p>
          <button
            onClick={() => {
              setEditData(null);
              togglePopup();
            }}
            className="flex whitespace-nowrap items-center justify-center gap-1 px-4 bg-[#2E4476] py-1 text-white rounded-md"
          >
            Add Department
          </button>
        </CardContent>
      </Card>
      <div>
        {isPending ? (
          "Loading"
        ) : (
          <MasterTable
            columns={tableData.columns}
            rows={tableData.rows || []}
            loading={false}
          />
        )}
      </div>
      {popup && <DepartmentForm onClose={togglePopup} editData={editData} />}
      {/* Pass editData to DepartmentForm */}
    </div>
  );
};

export default DepartmentIndex;
