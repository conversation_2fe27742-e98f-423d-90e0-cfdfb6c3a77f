import { Form, FormikProvider, useFormik } from "formik";
import HeadingPopup from "../../../../../../GuestManagement/components/HeadingPopup";
import { FormField } from "../../../../../../BookingManagement/components/ReservationCustomForm";
import * as Yup from "yup";
import { IDesignation } from "../../../../../../../Interface/employeeconfig.interface";
import {
  useCreateDesignation,
  useGetDepartment,
  useUpdateDesignation,
} from "../../../../../../../server-action/API/EmployeeConfiguration/jobconfig";
interface IDesignationForm {
  onClose: () => void;
  editData?: IDesignation | any;
}

const DesignationValidationSchema = Yup.object().shape({
  name: Yup.string()
    .required("Designation name is required")
    .min(2, "Designation name must be at least 2 characters"),
  isActive: Yup.boolean(),
  department: Yup.string().required("Department is required"),
});

const DesignationForm = ({ onClose, editData }: IDesignationForm) => {
  console.log(editData, "desing");
  const { mutateAsync: createDesignation } = useCreateDesignation();
  const { mutateAsync: updateDesignation } = useUpdateDesignation();
  const { data: departmentData } = useGetDepartment();
  const formik = useFormik<IDesignation>({
    initialValues: {
      name: editData?.name || "",
      isActive: editData?.isActive ?? true,
      department: editData?.department?._id || "",
    },
    validationSchema: DesignationValidationSchema,
    enableReinitialize: true,
    onSubmit: async (values, { resetForm }) => {
      onClose();
      try {
        if (editData) {
          await updateDesignation({
            _id: editData._id || "",
            designationData: values,
          });
        } else {
          await createDesignation(values);
        }
        resetForm();
      } catch (error) {
        console.error("Error submitting designation:", error);
      }
    },
  });

  return (
    <HeadingPopup
      onClose={onClose}
      className="w-full max-w-screen-sm"
      heading={`${editData ? "Edit" : "Add"} Designation`}
    >
      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit} className="space-y-3">
          <div className="flex gap-4">
            <FormField
              label={`Designation`}
              type="text"
              name="name"
              formik={formik}
              placeholder="Enter Designation Name"
            />
            <FormField
              label={"Department"}
              type="dropdown"
              name="department" // Name should match formik's initialValues
              formik={formik}
              placeholder="Select Department"
              options={
                departmentData?.map((dept) => ({
                  label: dept.name,
                  value: dept._id || "",
                })) || []
              }
            />
          </div>
          <div className="flex items-center mt-4">
            <button
              type="submit"
              className="px-4 py-2 bg-[#2A3A6D] text-white rounded-md"
            >
              {editData ? "Update" : "Add"}
            </button>
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default DesignationForm;
