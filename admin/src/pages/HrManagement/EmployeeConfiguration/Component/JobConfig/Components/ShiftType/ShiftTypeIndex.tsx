import { useCallback, useState } from "react";

import { Card, CardContent } from "../../../../../../../components/Card";
import MasterTable from "../../../../../../../layouts/Table/MasterTable";
import ShiftTypeForm from "./ShiftTypeForm";
import {
  useDeleteShiftType,
  useGetShiftType,
} from "../../../../../../../server-action/API/EmployeeConfiguration/jobconfig";
import { TableAction } from "../../../../../../../layouts/Table/TableAction";

const ShiftTypeIndex = () => {
  const [popup, setPopup] = useState(false); // Use boolean for toggle simplicity
  const { data: ShiftTypeData } = useGetShiftType();
  const { mutateAsync: deleteShiftType } = useDeleteShiftType();
  const [editData, setEditData] = useState(null);
  console.log(ShiftTypeData);

  const tableData = {
    columns: [
      { title: "SN", key: "sn" },
      { title: "Shift Type", key: "name" },
      { title: "Time", key: "time" },
      { title: "Action", key: "action" },
    ],
    rows: ShiftTypeData?.map((shiftType, index) => ({
      sn: index + 1,
      name: shiftType?.name,
      time: `${shiftType?.from ? shiftType?.from : "00:00"} - ${
        shiftType?.to ? shiftType?.to : "00:00"
      }`,
      action: (
        <TableAction
          onEdit={() => {
            setEditData(shiftType as any);
            setPopup(true);
          }}
          onDelete={() =>
            shiftType._id && deleteShiftType(shiftType._id.toString())
          }
        />
      ),
    })),
  };

  const togglePopup = useCallback(() => setPopup((prev) => !prev), []);

  return (
    <div className=" w-full">
      <Card className="bg-white">
        <CardContent className="flex items-center justify-between">
          <p>Shift Type</p>
          <button
            onClick={() => {
              setEditData(null);
              togglePopup();
            }}
            className="flex whitespace-nowrap items-center justify-center gap-1 px-4 bg-[#2E4476] py-1 text-white rounded-md"
          >
            Add Shift Type
          </button>
        </CardContent>
      </Card>
      <div>
        <MasterTable
          columns={tableData.columns}
          rows={tableData.rows || []}
          loading={false}
        />
      </div>
      {popup && <ShiftTypeForm onClose={togglePopup} editData={editData} />}
      {/* Render when popup is true */}
    </div>
  );
};

export default ShiftTypeIndex;
