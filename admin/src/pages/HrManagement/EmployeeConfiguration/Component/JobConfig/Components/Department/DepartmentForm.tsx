import { Form, FormikProvider, useFormik } from "formik";
import { IDepartment } from "../../../../../../../Interface/employeeconfig.interface";
import {
  useCreateDepartment,
  useUpdateDepartMent,
} from "../../../../../../../server-action/API/EmployeeConfiguration/jobconfig";
import HeadingPopup from "../../../../../../GuestManagement/components/HeadingPopup";
import { FormField } from "../../../../../../BookingManagement/components/ReservationCustomForm";
import * as Yup from "yup";
interface IDepartmentForm {
  onClose: () => void;
  editData?: IDepartment | null;
}

const DepartmentValidationSchema = Yup.object().shape({
  name: Yup.string()
    .required("Department name is required")
    .min(2, "Department name must be at least 2 characters"),
  isActive: Yup.boolean(),
});

const DepartmentForm = ({ onClose, editData }: IDepartmentForm) => {
  const { mutateAsync: createDepartment } = useCreateDepartment();
  const { mutateAsync: updateDepartment } = useUpdateDepartMent();
  const formik = useFormik<IDepartment>({
    initialValues: {
      name: editData?.name || "",
      isActive: editData?.isActive ?? true,
    },
    enableReinitialize: true,
    validationSchema: DepartmentValidationSchema,
    onSubmit: async (values, { resetForm }) => {
      onClose();
      try {
        if (editData && editData._id) {
          await updateDepartment({
            _id: String(editData._id),
            departmentData: values,
          });
        } else {
          await createDepartment(values);
        }
        resetForm();
      } catch (error) {
        console.error("Error submitting department:", error);
      }
    },
  });

  return (
    <HeadingPopup
      onClose={onClose}
      className="w-full max-w-screen-sm"
      heading={editData ? "Edit Department" : "Add Department"}
    >
      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit} className="space-y-3">
          <div className="grid grid-cols-2 gap-x-4 gap-y-3">
            <FormField
              label="Department"
              type="text"
              name="name"
              formik={formik}
              placeholder="Enter Department Name"
            />
          </div>
          <div className="flex items-center mt-4">
            <button
              type="submit"
              className="px-4 py-2 bg-[#2A3A6D] text-white rounded-md"
              disabled={formik.isSubmitting}
            >
              {formik.isSubmitting ? "Saving..." : editData ? "Update" : "Add"}
              {/* Change button text dynamically */}
            </button>
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default DepartmentForm;
