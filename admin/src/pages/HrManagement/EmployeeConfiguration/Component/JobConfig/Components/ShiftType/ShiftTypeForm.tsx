import { Form, FormikProvider, useFormik } from "formik";
import HeadingPopup from "../../../../../../GuestManagement/components/HeadingPopup";
import { FormField } from "../../../../../../BookingManagement/components/ReservationCustomForm";
import { IShiftType } from "../../../../../../../Interface/employeeconfig.interface";
import * as Yup from "yup";
import {
  useCreateShiftType,
  useUpdateShiftType,
} from "../../../../../../../server-action/API/EmployeeConfiguration/jobconfig";

export const shiftTypeValidationSchema = Yup.object().shape({
  name: Yup.string()
    .required("Shift type name is required")
    .min(2, "Shift type name must be at least 2 characters"),
  from: Yup.string().required("From time is required"),
  to: Yup.string()
    .required("To time is required")
    .test("is-after", "To time must be after From time", function (toValue) {
      const { from } = this.parent;
      if (!from || !toValue) return true;
      return toValue > from;
    }),
});

interface IShiftTypeForm {
  onClose: () => void;
  editData?: IShiftType | null; // Made editData optional
}

const ShiftTypeForm = ({ onClose, editData }: IShiftTypeForm) => {
  const { mutateAsync: createShiftType } = useCreateShiftType();
  const { mutateAsync: updateShiftType } = useUpdateShiftType();
  const formik = useFormik<IShiftType>({
    initialValues: {
      name: editData?.name || "",
      from: editData?.from || "",
      to: editData?.to || "",
    },
    enableReinitialize: true,
    validationSchema: shiftTypeValidationSchema,
    onSubmit: async (values, { resetForm }) => {
      onClose();
      try {
        if (editData && editData._id) {
          await updateShiftType({
            _id: String(editData._id),
            shiftTypeData: values,
          });
        } else {
          await createShiftType(values);
        }
        resetForm();
      } catch (error) {
        console.error("Error submitting department:", error);
      }
    },
  });
  return (
    <HeadingPopup
      onClose={onClose}
      className="w-full max-w-screen-sm"
      heading="Add Shift Type"
    >
      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit} className="space-y-3">
          <FormField
            label="Shift Type"
            type="text"
            name="name"
            formik={formik}
            placeholder="Enter Shift Type"
          />
          <div>
            {/* <p>Time</p> */}
            <div className="flex gap-5">
              <FormField
                label="From"
                type="time"
                name="from"
                formik={formik}
                placeholder="From"
              />
              <FormField
                label="To"
                type="time"
                name="to"
                formik={formik}
                placeholder="To"
              />
            </div>
          </div>

          <div className="flex items-center mt-4">
            <button
              type="submit"
              className="px-4 py-2 bg-[#2A3A6D] text-white rounded-md"
            >
              Submit
            </button>
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default ShiftTypeForm;
