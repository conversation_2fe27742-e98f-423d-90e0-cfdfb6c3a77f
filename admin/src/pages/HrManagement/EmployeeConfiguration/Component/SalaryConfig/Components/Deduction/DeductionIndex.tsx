import { useCallback, useState } from "react";
import { Card, CardContent } from "../../../../../../../components/Card";
import MasterTable from "../../../../../../../layouts/Table/MasterTable";
import DeductionForm from "./DeductionForm";
import {
  useDeleteDeduction,
  useGetDeduction,
} from "../../../../../../../server-action/API/EmployeeConfiguration/salaryconfig";
import { TableAction } from "../../../../../../../layouts/Table/TableAction";

const DeductionIndex = () => {
  const [popup, setPopup] = useState(false);
  const [editData, setEditData] = useState(null);
  const { data: deductionData } = useGetDeduction();
  const { mutateAsync: deleteDeduction } = useDeleteDeduction();

  const tableData = {
    columns: [
      { title: "SN", key: "sn" },
      { title: "Deduction", key: "name" },
      { title: "Category", key: "category" },
      { title: "Value", key: "value" },
      { title: "Action", key: "action" },
    ],
    rows: deductionData?.map((deduction, index) => ({
      sn: index + 1,
      name: deduction?.name,
      category: deduction?.category,
      value: deduction?.value,

      action: (
        <TableAction
          onEdit={() => {
            setEditData(deduction as any);
            setPopup(true);
          }}
          onDelete={() =>
            deduction?._id && deleteDeduction(deduction._id.toString())
          }
        />
      ),
    })),
  };

  const togglePopup = useCallback(() => setPopup((prev) => !prev), []);

  return (
    <div className="w-full">
      <Card className="bg-white">
        <CardContent className="flex items-center justify-between">
          <p>Deduction</p>
          <button
            onClick={() => {
              togglePopup();
              setEditData(null);
            }}
            className="flex whitespace-nowrap items-center justify-center gap-1 px-4 bg-[#2E4476] py-1 text-white rounded-md"
          >
            Add Deduction
          </button>
        </CardContent>
      </Card>
      <div>
        <MasterTable
          columns={tableData.columns}
          rows={tableData?.rows || []}
          loading={false}
        />
      </div>
      {popup && <DeductionForm onClose={togglePopup} editData={editData} />}
      {/* Render when popup is true */}
    </div>
  );
};

export default DeductionIndex;
