import { Form, FormikProvider, useFormik } from "formik";
import HeadingPopup from "../../../../../../GuestManagement/components/HeadingPopup";
import { FormField } from "../../../../../../BookingManagement/components/ReservationCustomForm";
import { IDeduction } from "../../../../../../../Interface/employeeconfig.interface";
import {
  useCreateDeduction,
  useUpdateDeduction,
} from "../../../../../../../server-action/API/EmployeeConfiguration/salaryconfig";
import * as Yup from "yup";
interface IDeductionForm {
  onClose: () => void;
  editData: IDeduction | null;
}

const DeductionSchema = Yup.object().shape({
  name: Yup.string().required("Deduction Name is required"),
  category: Yup.string().required("Deduction Category is required"),
  value: Yup.number()
    .required("Deduction Value is required")
    .positive("Deduction Value must be positive")
    .integer("Deduction Value must be an integer"),
});

const DeductionForm = ({ onClose, editData }: IDeductionForm) => {
  const { mutateAsync: createDeduction } = useCreateDeduction();
  const { mutateAsync: updateDeduction } = useUpdateDeduction();

  const formik = useFormik<IDeduction>({
    initialValues: {
      name: editData?.name ?? "",
      category: editData?.category ?? "",
      value: editData?.value ?? "",
    },
    enableReinitialize: true,
    validationSchema: DeductionSchema,
    onSubmit: async (values, { resetForm }) => {
      onClose();
      try {
        if (editData && editData._id) {
          await updateDeduction({
            _id: String(editData._id),
            deductionData: values,
          });
        } else {
          await createDeduction(values);
        }
        resetForm();
      } catch (error) {
        console.error("Error submitting Deduction:", error);
      }
    },
  });
  return (
    <HeadingPopup
      onClose={onClose}
      className="w-full max-w-screen-sm"
      heading="Add Deduction"
    >
      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit} className="space-y-3">
          <div className="grid grid-cols-2 gap-x-4 gap-y-3">
            <FormField
              label="Deduction"
              type="text"
              name="name"
              formik={formik}
              placeholder="Enter Deduction Name"
            />
            <FormField
              label="Category"
              type="dropdown"
              name="category"
              formik={formik}
              placeholder="Select Deduction Category"
              options={[
                { label: "Percentage", value: "PERCENTAGE" },
                { label: "Flat", value: "FLAT" },
              ]}
            />
            <FormField
              label="Value"
              type="number"
              name="value"
              formik={formik}
              placeholder="Enter Value"
            />
          </div>
          <div className="flex items-center mt-4">
            <button
              type="submit"
              className="px-4 py-2 bg-[#2A3A6D] text-white rounded-md"
            >
              Add
            </button>
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default DeductionForm;
