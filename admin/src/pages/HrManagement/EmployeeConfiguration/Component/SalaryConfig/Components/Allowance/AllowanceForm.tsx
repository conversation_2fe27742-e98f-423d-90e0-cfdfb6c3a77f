import { Form, FormikProvider, useFormik } from "formik";
import HeadingPopup from "../../../../../../GuestManagement/components/HeadingPopup";
import { FormField } from "../../../../../../BookingManagement/components/ReservationCustomForm";
import { IAllowance } from "../../../../../../../Interface/employeeconfig.interface";
import {
  useCreateAllowance,
  useUpdateAllowance,
} from "../../../../../../../server-action/API/EmployeeConfiguration/salaryconfig";
import * as Yup from "yup";
interface IAllowanceForm {
  onClose: () => void;
  editData: IAllowance | null;
}

const AllowanceSchema = Yup.object().shape({
  name: Yup.string().required("Allowance Name is required"),
  category: Yup.string().required("Allowance Category is required"),
  value: Yup.number()
    .required("Allowance Value is required")
    .positive("Allowance Value must be positive")
    .integer("Allowance Value must be an integer"),
});

const AllowanceForm = ({ onClose, editData }: IAllowanceForm) => {
  const { mutateAsync: updateAllowance } = useUpdateAllowance();
  const { mutateAsync: createAllowance } = useCreateAllowance();
  const formik = useFormik<IAllowance>({
    initialValues: {
      name: editData?.name ?? "",
      category: editData?.category ?? "",
      value: editData?.value ?? "",
    },
    enableReinitialize: true,
    validationSchema: AllowanceSchema,
    onSubmit: async (values, { resetForm }) => {
      onClose();
      try {
        if (editData && editData._id) {
          await updateAllowance({
            _id: String(editData._id),
            allowanceData: values,
          });
        } else {
          await createAllowance(values);
        }
        resetForm();
      } catch (error) {
        console.error("Error submitting Deduction:", error);
      }
    },
  });
  return (
    <HeadingPopup
      onClose={onClose}
      className="w-full max-w-screen-sm"
      heading="Add Allowance"
    >
      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit} className="space-y-3">
          <div className="grid grid-cols-2 gap-x-4 gap-y-3">
            <FormField
              label="Allowance"
              type="text"
              name="name"
              formik={formik}
              placeholder="Enter Allowance Name"
            />
            <FormField
              label="Category"
              type="dropdown"
              name="category"
              formik={formik}
              placeholder="Select Deduction Category"
              options={[
                { label: "Percentage", value: "PERCENTAGE" },
                { label: "Flat", value: "FLAT" },
              ]}
            />
            <FormField
              label="Value"
              type="number"
              name="value"
              formik={formik}
              placeholder="Enter Value"
            />
          </div>
          <div className="flex items-center mt-4">
            <button
              type="submit"
              className="px-4 py-2 bg-[#2A3A6D] text-white rounded-md"
            >
              Add
            </button>
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default AllowanceForm;
