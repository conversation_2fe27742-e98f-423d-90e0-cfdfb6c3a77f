"use client";

import { useState, useRef, useEffect } from "react";
import { Icon } from "@iconify/react";

const EmployeeScheduleIndex = () => {
  const [currentDate, setCurrentDate] = useState(new Date(2024, 9, 20)); // October 20, 2024
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [collapsedDepartments, setCollapsedDepartments] = useState<number[]>(
    []
  );
  const [showInfoTooltip, setShowInfoTooltip] = useState<number | null>(null);
  const [currentWeek, setCurrentWeek] = useState<string[]>([
    "22",
    "23",
    "24",
    "25",
    "26",
    "22",
    "28",
  ]);
  const [currentShifts, setCurrentShifts] = useState<any>(null);

  const datePickerRef = useRef<HTMLDivElement>(null);

  // Month data with sample week dates for each month
  const monthData = {
    0: ["3", "4", "5", "6", "7", "8", "9"], // January
    1: ["7", "8", "9", "10", "11", "12", "13"], // February
    2: ["4", "5", "6", "7", "8", "9", "10"], // March
    3: ["1", "2", "3", "4", "5", "6", "7"], // April
    4: ["6", "7", "8", "9", "10", "11", "12"], // May
    5: ["3", "4", "5", "6", "7", "8", "9"], // June
    6: ["1", "2", "3", "4", "5", "6", "7"], // July
    7: ["5", "6", "7", "8", "9", "10", "11"], // August
    8: ["2", "3", "4", "5", "6", "7", "8"], // September
    9: ["22", "23", "24", "25", "26", "22", "28"], // October
    10: ["6", "7", "8", "9", "10", "11", "12"], // November
    11: ["4", "5", "6", "7", "8", "9", "10"], // December
  };

  // Different shift data for each month
  const monthlyShiftData = {
    // January
    0: [
      {
        name: "Bar staff",
        count: 2,
        employees: [
          {
            id: 1,
            name: "Carol Saragosa",
            position: "Bartender",
            schedule: [
              { day: "3", shifts: [{ time: "10:00-14:00", status: "normal" }] },
              { day: "4", shifts: [{ time: "10:00-14:00", status: "normal" }] },
              {
                day: "5",
                shifts: [
                  {
                    time: "10:00-14:00",
                    status: "approved-leave",
                    note: "Family event",
                  },
                ],
              },
              { day: "6", shifts: [{ time: "10:00-14:00", status: "normal" }] },
              { day: "7", shifts: [{ time: "10:00-14:00", status: "normal" }] },
              { day: "8", shifts: [{ time: "12:00-16:00", status: "normal" }] },
              { day: "9", shifts: [{ time: "12:00-16:00", status: "normal" }] },
            ],
          },
          {
            id: 2,
            name: "John Smith",
            position: "Bartender",
            schedule: [
              { day: "3", shifts: [{ time: "16:00-20:00", status: "normal" }] },
              { day: "4", shifts: [{ time: "16:00-20:00", status: "normal" }] },
              { day: "5", shifts: [{ time: "16:00-20:00", status: "normal" }] },
              {
                day: "6",
                shifts: [
                  {
                    time: "16:00-20:00",
                    status: "approved-leave",
                    note: "Medical",
                  },
                ],
              },
              { day: "7", shifts: [{ time: "16:00-20:00", status: "normal" }] },
              { day: "8", shifts: [{ time: "16:00-20:00", status: "normal" }] },
              { day: "9", shifts: [{ time: "16:00-20:00", status: "normal" }] },
            ],
          },
        ],
      },
      {
        name: "Janitors",
        count: 2,
        employees: [
          {
            id: 3,
            name: "Carol Saragosa",
            position: "Janitor",
            schedule: [
              { day: "3", shifts: [{ time: "07:00-11:00", status: "normal" }] },
              { day: "4", shifts: [{ time: "07:00-11:00", status: "normal" }] },
              {
                day: "5",
                shifts: [
                  {
                    time: "07:00-11:00",
                    status: "approved-leave",
                    note: "Family event",
                  },
                ],
              },
              { day: "6", shifts: [{ time: "07:00-11:00", status: "normal" }] },
              { day: "7", shifts: [{ time: "07:00-11:00", status: "normal" }] },
              { day: "8", shifts: [{ time: "07:00-11:00", status: "normal" }] },
              { day: "9", shifts: [{ time: "07:00-11:00", status: "normal" }] },
            ],
          },
          {
            id: 4,
            name: "John Smith",
            position: "Janitor",
            schedule: [
              { day: "3", shifts: [{ time: "18:00-22:00", status: "normal" }] },
              { day: "4", shifts: [{ time: "18:00-22:00", status: "normal" }] },
              { day: "5", shifts: [{ time: "18:00-22:00", status: "normal" }] },
              {
                day: "6",
                shifts: [
                  {
                    time: "18:00-22:00",
                    status: "approved-leave",
                    note: "Medical",
                  },
                ],
              },
              { day: "7", shifts: [{ time: "18:00-22:00", status: "normal" }] },
              { day: "8", shifts: [{ time: "18:00-22:00", status: "normal" }] },
              { day: "9", shifts: [{ time: "18:00-22:00", status: "normal" }] },
            ],
          },
        ],
      },
      {
        name: "Waiters/Waitresses",
        count: 6,
        employees: [
          {
            id: 5,
            name: "Carol Saragosa",
            position: "Waitress",
            schedule: [
              { day: "3", shifts: [{ time: "11:00-15:00", status: "normal" }] },
              { day: "4", shifts: [{ time: "11:00-15:00", status: "normal" }] },
              {
                day: "5",
                shifts: [
                  {
                    time: "11:00-15:00",
                    status: "approved-leave",
                    note: "Family event",
                  },
                ],
              },
              { day: "6", shifts: [{ time: "11:00-15:00", status: "normal" }] },
              { day: "7", shifts: [{ time: "11:00-15:00", status: "normal" }] },
              { day: "8", shifts: [{ time: "11:00-15:00", status: "normal" }] },
              { day: "9", shifts: [{ time: "11:00-15:00", status: "normal" }] },
            ],
          },
        ],
      },
    ],

    // February
    1: [
      {
        name: "Bar staff",
        count: 2,
        employees: [
          {
            id: 1,
            name: "Carol Saragosa",
            position: "Bartender",
            schedule: [
              { day: "7", shifts: [{ time: "09:00-13:00", status: "normal" }] },
              { day: "8", shifts: [{ time: "09:00-13:00", status: "normal" }] },
              { day: "9", shifts: [{ time: "09:00-13:00", status: "normal" }] },
              {
                day: "10",
                shifts: [{ time: "09:00-13:00", status: "normal" }],
              },
              {
                day: "11",
                shifts: [
                  {
                    time: "09:00-13:00",
                    status: "approved-leave",
                    note: "Valentine's prep",
                  },
                ],
              },
              {
                day: "12",
                shifts: [{ time: "14:00-18:00", status: "normal" }],
              },
              {
                day: "13",
                shifts: [{ time: "14:00-18:00", status: "normal" }],
              },
            ],
          },
          {
            id: 2,
            name: "John Smith",
            position: "Bartender",
            schedule: [
              { day: "7", shifts: [{ time: "14:00-18:00", status: "normal" }] },
              { day: "8", shifts: [{ time: "14:00-18:00", status: "normal" }] },
              { day: "9", shifts: [{ time: "14:00-18:00", status: "normal" }] },
              {
                day: "10",
                shifts: [{ time: "14:00-18:00", status: "normal" }],
              },
              {
                day: "11",
                shifts: [{ time: "14:00-18:00", status: "normal" }],
              },
              {
                day: "12",
                shifts: [
                  {
                    time: "09:00-13:00",
                    status: "approved-leave",
                    note: "All day",
                  },
                ],
              },
              {
                day: "13",
                shifts: [{ time: "09:00-13:00", status: "normal" }],
              },
            ],
          },
        ],
      },
      {
        name: "Janitors",
        count: 2,
        employees: [
          {
            id: 3,
            name: "Carol Saragosa",
            position: "Janitor",
            schedule: [
              { day: "7", shifts: [{ time: "06:00-10:00", status: "normal" }] },
              { day: "8", shifts: [{ time: "06:00-10:00", status: "normal" }] },
              { day: "9", shifts: [{ time: "06:00-10:00", status: "normal" }] },
              {
                day: "10",
                shifts: [{ time: "06:00-10:00", status: "normal" }],
              },
              {
                day: "11",
                shifts: [
                  {
                    time: "06:00-10:00",
                    status: "approved-leave",
                    note: "Valentine's prep",
                  },
                ],
              },
              {
                day: "12",
                shifts: [{ time: "06:00-10:00", status: "normal" }],
              },
              {
                day: "13",
                shifts: [{ time: "06:00-10:00", status: "normal" }],
              },
            ],
          },
          {
            id: 4,
            name: "John Smith",
            position: "Janitor",
            schedule: [
              { day: "7", shifts: [{ time: "19:00-23:00", status: "normal" }] },
              { day: "8", shifts: [{ time: "19:00-23:00", status: "normal" }] },
              { day: "9", shifts: [{ time: "19:00-23:00", status: "normal" }] },
              {
                day: "10",
                shifts: [{ time: "19:00-23:00", status: "normal" }],
              },
              {
                day: "11",
                shifts: [{ time: "19:00-23:00", status: "normal" }],
              },
              {
                day: "12",
                shifts: [
                  {
                    time: "19:00-23:00",
                    status: "approved-leave",
                    note: "All day",
                  },
                ],
              },
              {
                day: "13",
                shifts: [{ time: "19:00-23:00", status: "normal" }],
              },
            ],
          },
        ],
      },
      {
        name: "Waiters/Waitresses",
        count: 6,
        employees: [
          {
            id: 5,
            name: "Carol Saragosa",
            position: "Waitress",
            schedule: [
              { day: "7", shifts: [{ time: "12:00-16:00", status: "normal" }] },
              { day: "8", shifts: [{ time: "12:00-16:00", status: "normal" }] },
              { day: "9", shifts: [{ time: "12:00-16:00", status: "normal" }] },
              {
                day: "10",
                shifts: [{ time: "12:00-16:00", status: "normal" }],
              },
              {
                day: "11",
                shifts: [
                  {
                    time: "12:00-16:00",
                    status: "approved-leave",
                    note: "Valentine's prep",
                  },
                ],
              },
              {
                day: "12",
                shifts: [{ time: "12:00-16:00", status: "normal" }],
              },
              {
                day: "13",
                shifts: [{ time: "12:00-16:00", status: "normal" }],
              },
            ],
          },
        ],
      },
    ],

    // October (default)
    9: [
      {
        name: "Bar staff",
        count: 2,
        employees: [
          {
            id: 1,
            name: "Carol Saragosa",
            position: "Bartender",
            schedule: [
              {
                day: "22",
                shifts: [{ time: "08:00-12:00", status: "normal" }],
              },
              {
                day: "23",
                shifts: [{ time: "08:00-12:00", status: "normal" }],
              },
              {
                day: "24",
                shifts: [
                  { time: "08:00-12:00", status: "normal" },
                  { time: "18:30-23:30", status: "normal" },
                ],
              },
              {
                day: "25",
                shifts: [
                  {
                    time: "08:00-12:00",
                    status: "approved-leave",
                    note: "08:00-16:00",
                  },
                ],
              },
              {
                day: "26",
                shifts: [{ time: "08:00-12:00", status: "normal" }],
              },
              {
                day: "27",
                shifts: [{ time: "08:00-12:00", status: "normal" }],
              },
              {
                day: "28",
                shifts: [{ time: "08:00-12:00", status: "normal" }],
              },
            ],
          },
          {
            id: 2,
            name: "John Smith",
            position: "Bartender",
            schedule: [
              {
                day: "22",
                shifts: [
                  { time: "08:00-12:00", status: "normal" },
                  { time: "15:00-19:00", status: "normal" },
                ],
              },
              {
                day: "23",
                shifts: [
                  {
                    time: "08:00-12:00",
                    status: "approved-leave",
                    note: "All day",
                  },
                ],
              },
              {
                day: "24",
                shifts: [{ time: "08:00-12:00", status: "normal" }],
              },
              {
                day: "25",
                shifts: [
                  { time: "08:00-12:00", status: "normal" },
                  { time: "16:00-19:00", status: "normal" },
                ],
              },
              {
                day: "26",
                shifts: [{ time: "08:00-12:00", status: "normal" }],
              },
              {
                day: "27",
                shifts: [
                  {
                    time: "08:00-12:00",
                    status: "approved-leave",
                    note: "08:00-16:00",
                  },
                ],
              },
              {
                day: "28",
                shifts: [{ time: "08:00-12:00", status: "normal" }],
              },
            ],
          },
        ],
      },
      {
        name: "Janitors",
        count: 2,
        employees: [
          {
            id: 3,
            name: "Carol Saragosa",
            position: "Janitor",
            schedule: [
              {
                day: "22",
                shifts: [{ time: "08:00-12:00", status: "normal" }],
              },
              {
                day: "23",
                shifts: [{ time: "08:00-12:00", status: "normal" }],
              },
              {
                day: "24",
                shifts: [
                  { time: "08:00-12:00", status: "normal" },
                  { time: "18:30-23:30", status: "normal" },
                ],
              },
              {
                day: "25",
                shifts: [
                  {
                    time: "08:00-12:00",
                    status: "approved-leave",
                    note: "08:00-16:00",
                  },
                ],
              },
              {
                day: "26",
                shifts: [{ time: "08:00-12:00", status: "normal" }],
              },
              {
                day: "27",
                shifts: [{ time: "08:00-12:00", status: "normal" }],
              },
              {
                day: "28",
                shifts: [{ time: "08:00-12:00", status: "normal" }],
              },
            ],
          },
          {
            id: 4,
            name: "John Smith",
            position: "Janitor",
            schedule: [
              {
                day: "22",
                shifts: [{ time: "08:00-12:00", status: "normal" }],
              },
              {
                day: "23",
                shifts: [
                  {
                    time: "08:00-12:00",
                    status: "approved-leave",
                    note: "All day",
                  },
                ],
              },
              {
                day: "24",
                shifts: [{ time: "08:00-12:00", status: "normal" }],
              },
              {
                day: "25",
                shifts: [{ time: "08:00-12:00", status: "normal" }],
              },
              {
                day: "26",
                shifts: [{ time: "08:00-12:00", status: "normal" }],
              },
              {
                day: "27",
                shifts: [
                  {
                    time: "08:00-12:00",
                    status: "approved-leave",
                    note: "08:00-16:00",
                  },
                ],
              },
              {
                day: "28",
                shifts: [{ time: "08:00-12:00", status: "normal" }],
              },
            ],
          },
        ],
      },
      {
        name: "Waiters/Waitresses",
        count: 6,
        employees: [
          {
            id: 5,
            name: "Carol Saragosa",
            position: "Waitress",
            schedule: [
              {
                day: "22",
                shifts: [{ time: "08:00-12:00", status: "normal" }],
              },
              {
                day: "23",
                shifts: [{ time: "08:00-12:00", status: "normal" }],
              },
              {
                day: "24",
                shifts: [
                  {
                    time: "08:00-12:00",
                    status: "approved-leave",
                    note: "08:00-16:00",
                  },
                ],
              },
              {
                day: "25",
                shifts: [{ time: "08:00-12:00", status: "normal" }],
              },
              {
                day: "26",
                shifts: [{ time: "08:00-12:00", status: "normal" }],
              },
              {
                day: "27",
                shifts: [{ time: "08:00-12:00", status: "normal" }],
              },
              {
                day: "28",
                shifts: [{ time: "08:00-12:00", status: "normal" }],
              },
            ],
          },
        ],
      },
    ],
  };

  // Generate shift data for months that don't have specific data
  const generateShiftDataForMonth = (
    monthIndex: keyof typeof monthlyShiftData
  ) => {
    if (monthlyShiftData[monthIndex]) {
      return monthlyShiftData[monthIndex];
    }

    // Generate random shift data based on the dates for this month
    const dates = monthData[monthIndex];
    return [
      {
        name: "Bar staff",
        count: 2,
        employees: [
          {
            id: 1,
            name: "Carol Saragosa",
            position: "Bartender",
            schedule: dates.map((date) => ({
              day: date,
              shifts: [
                {
                  time: Math.random() > 0.7 ? "14:00-18:00" : "08:00-12:00",
                  status: Math.random() > 0.8 ? "approved-leave" : "normal",
                  note: Math.random() > 0.8 ? "Personal day" : null,
                },
              ],
            })),
          },
          {
            id: 2,
            name: "John Smith",
            position: "Bartender",
            schedule: dates.map((date) => ({
              day: date,
              shifts: [
                {
                  time: Math.random() > 0.6 ? "16:00-20:00" : "10:00-14:00",
                  status: Math.random() > 0.8 ? "approved-leave" : "normal",
                  note: Math.random() > 0.8 ? "All day" : null,
                },
              ],
            })),
          },
        ],
      },
      {
        name: "Janitors",
        count: 2,
        employees: [
          {
            id: 3,
            name: "Carol Saragosa",
            position: "Janitor",
            schedule: dates.map((date) => ({
              day: date,
              shifts: [
                {
                  time: Math.random() > 0.7 ? "06:00-10:00" : "07:00-11:00",
                  status: Math.random() > 0.8 ? "approved-leave" : "normal",
                  note: Math.random() > 0.8 ? "Personal day" : null,
                },
              ],
            })),
          },
          {
            id: 4,
            name: "John Smith",
            position: "Janitor",
            schedule: dates.map((date) => ({
              day: date,
              shifts: [
                {
                  time: Math.random() > 0.6 ? "18:00-22:00" : "19:00-23:00",
                  status: Math.random() > 0.8 ? "approved-leave" : "normal",
                  note: Math.random() > 0.8 ? "All day" : null,
                },
              ],
            })),
          },
        ],
      },
      {
        name: "Waiters/Waitresses",
        count: 6,
        employees: [
          {
            id: 5,
            name: "Carol Saragosa",
            position: "Waitress",
            schedule: dates.map((date: any) => ({
              day: date,
              shifts: [
                {
                  time: Math.random() > 0.7 ? "11:00-15:00" : "12:00-16:00",
                  status: Math.random() > 0.8 ? "approved-leave" : "normal",
                  note: Math.random() > 0.8 ? "Personal day" : null,
                },
              ],
            })),
          },
        ],
      },
    ];
  };

  const months = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  const days = ["MON", "TUE", "WED", "THU", "FRI", "SAT", "SUN"];

  // Initialize with October data
  useEffect(() => {
    setCurrentShifts(monthlyShiftData[9]);
  }, []);

  // Close date picker when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        datePickerRef.current &&
        !datePickerRef.current.contains(event.target as Node)
      ) {
        setShowDatePicker(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Update week dates and shift data when month changes
  useEffect(() => {
    const month = currentDate.getMonth();
    setCurrentWeek(monthData[month as keyof typeof monthData]);
    setCurrentShifts(generateShiftDataForMonth(month as any));
  }, [currentDate]);

  const formatDate = (date: Date) => {
    return `${date.getDate()} ${
      months[date.getMonth()]
    }, ${date.getFullYear()}`;
  };

  const goToPreviousDay = () => {
    const newDate = new Date(currentDate);
    newDate.setDate(currentDate.getDate() - 1);
    setCurrentDate(newDate);
  };

  const goToNextDay = () => {
    const newDate = new Date(currentDate);
    newDate.setDate(currentDate.getDate() + 1);
    setCurrentDate(newDate);
  };

  const goToToday = () => {
    setCurrentDate(new Date(2024, 9, 20)); // Reset to October 20, 2024 for demo
  };

  const toggleDepartmentCollapse = (deptIndex: number) => {
    setCollapsedDepartments((prev) => {
      if (prev.includes(deptIndex)) {
        return prev.filter((i) => i !== deptIndex);
      } else {
        return [...prev, deptIndex];
      }
    });
  };

  const toggleInfoTooltip = (deptIndex: number) => {
    setShowInfoTooltip((prev) => (prev === deptIndex ? null : deptIndex));
  };

  const selectDate = (day: number) => {
    const newDate = new Date(currentDate);
    newDate.setDate(day);
    setCurrentDate(newDate);
    setShowDatePicker(false);
  };

  const selectMonth = (monthIndex: number) => {
    const newDate = new Date(currentDate);
    newDate.setMonth(monthIndex);
    setCurrentDate(newDate);
  };

  // Generate calendar days for the current month
  const generateCalendarDays = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    const firstDayOfMonth = new Date(year, month, 1).getDay();

    // Adjust for Sunday being 0
    const startingDay = firstDayOfMonth === 0 ? 6 : firstDayOfMonth - 1;

    const calendarDays = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDay; i++) {
      calendarDays.push(null);
    }

    // Add days of the month
    for (let i = 1; i <= daysInMonth; i++) {
      calendarDays.push(i);
    }

    return calendarDays;
  };

  // If shifts haven't loaded yet, show loading
  if (!currentShifts) {
    return <div className="p-4 text-center">Loading schedule...</div>;
  }

  return (
    <div className="w-full bg-white">
      {/* Header */}
      <div className="flex items-center p-2 border-b">
        <button
          onClick={goToPreviousDay}
          className="p-2 rounded-md hover:bg-gray-100"
        >
          <Icon icon="mdi:chevron-left" width="16" />
        </button>
        <button
          onClick={goToNextDay}
          className="p-2 rounded-md hover:bg-gray-100"
        >
          <Icon icon="mdi:chevron-right" width="16" />
        </button>
        <button
          onClick={goToToday}
          className="px-3 py-1 mx-2 text-sm font-medium text-blue-600 border border-blue-600 rounded-md"
        >
          Today
        </button>
        <span className="text-sm font-medium">{formatDate(currentDate)}</span>
        <div className="relative ml-2">
          <button
            className="flex items-center p-2 border rounded-md"
            onClick={() => setShowDatePicker(!showDatePicker)}
          >
            <Icon icon="mdi:calendar" width="16" className="mr-1" />
            <span className="text-sm text-gray-500">Select date</span>
          </button>

          {/* Date Picker */}
          {showDatePicker && (
            <div
              ref={datePickerRef}
              className="absolute z-10 p-4 mt-1 bg-white border rounded-md shadow-lg"
              style={{ width: "300px" }}
            >
              <div className="mb-2 text-sm font-medium">
                {months[currentDate.getMonth()]} {currentDate.getFullYear()}
              </div>
              <div className="grid grid-cols-7 gap-1 mb-2">
                {["M", "T", "W", "T", "F", "S", "S"].map((day, i) => (
                  <div
                    key={i}
                    className="flex items-center justify-center w-8 h-8 text-xs font-medium text-gray-500"
                  >
                    {day}
                  </div>
                ))}
              </div>
              <div className="grid grid-cols-7 gap-1">
                {generateCalendarDays().map((day, i) => (
                  <div key={i} className="flex items-center justify-center">
                    {day !== null ? (
                      <button
                        className={`w-8 h-8 text-sm rounded-full ${
                          day === currentDate.getDate()
                            ? "bg-blue-600 text-white"
                            : "hover:bg-gray-100"
                        }`}
                        onClick={() => selectDate(day)}
                      >
                        {day}
                      </button>
                    ) : (
                      <div className="w-8 h-8"></div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Month selector */}
      <div className="flex overflow-x-auto border-b">
        {months.map((month, index) => (
          <button
            key={index}
            className={`px-4 py-2 text-sm font-medium whitespace-nowrap ${
              index === currentDate.getMonth()
                ? "text-blue-600 border-b-2 border-blue-600"
                : "text-gray-600"
            }`}
            onClick={() => selectMonth(index)}
          >
            {month}
          </button>
        ))}
      </div>

      {/* Calendar grid */}
      <div className="overflow-x-auto">
        <div className="min-w-[800px]">
          {/* Days header */}
          <div className="grid grid-cols-7 text-center border-b bg-gray-50">
            {days.map((day, index) => (
              <div
                key={index}
                className="py-1 text-xs font-medium text-gray-500"
              >
                {day}
              </div>
            ))}
          </div>

          {/* Dates header */}
          <div className="grid grid-cols-7 text-center bg-blue-50 border-b">
            {currentWeek.map((date, index) => (
              <div key={index} className="py-2 font-bold">
                {date}
              </div>
            ))}
          </div>

          {/* Departments and employees */}
          {currentShifts.map((department: any, deptIndex: any) => (
            <div key={deptIndex} className="border-b">
              {/* Department header */}
              <div
                className="flex items-center p-2 bg-gray-50 cursor-pointer"
                onClick={() => toggleDepartmentCollapse(deptIndex)}
              >
                <Icon
                  icon={
                    collapsedDepartments.includes(deptIndex)
                      ? "mdi:chevron-right"
                      : "mdi:chevron-down"
                  }
                  width="16"
                  className="mr-1"
                />
                <span className="font-medium">{department.name}</span>
                <span className="ml-1 text-sm text-gray-500">
                  ({department.count})
                </span>
                <div className="relative ml-auto">
                  <button
                    className="p-1 rounded-full hover:bg-gray-200"
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleInfoTooltip(deptIndex);
                    }}
                  >
                    <Icon
                      icon="mdi:information"
                      width="16"
                      className="text-gray-400"
                    />
                  </button>

                  {/* Info tooltip */}
                  {showInfoTooltip === deptIndex && (
                    <div className="absolute right-0 z-10 p-2 text-sm bg-white border rounded-md shadow-lg whitespace-nowrap">
                      <div className="font-medium">
                        {department.name} Information
                      </div>
                      <div className="mt-1 text-gray-600">
                        Total employees: {department.count}
                      </div>
                      <div className="text-gray-600">
                        Department code:{" "}
                        {department.name.substring(0, 3).toUpperCase()}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Employees - only show if department is not collapsed */}
              {!collapsedDepartments.includes(deptIndex) &&
                department.employees.map((employee: any, empIndex: any) => (
                  <div
                    key={empIndex}
                    className="grid grid-cols-[200px_1fr] border-t"
                  >
                    {/* Employee info */}
                    <div className="p-2">
                      <div className="font-medium">{employee.name}</div>
                      <div className="text-sm text-gray-500">
                        {employee.position}
                      </div>
                    </div>

                    <div className="grid grid-cols-7">
                      {employee.schedule.map((day: any, dayIndex: any) => (
                        <div key={dayIndex} className="border-l p-1">
                          {day.shifts.map((shift: any, shiftIndex: any) => (
                            <div
                              key={shiftIndex}
                              className={`mb-1 p-1 text-xs rounded cursor-pointer hover:opacity-80 ${
                                shift.status === "approved-leave"
                                  ? "bg-orange-100 text-orange-800"
                                  : "bg-blue-100 text-blue-800"
                              }`}
                              onClick={() => {
                                // In a real app, this would open a shift details modal
                                alert(
                                  `Shift details: ${employee.name}, ${
                                    day.day
                                  } ${months[currentDate.getMonth()]}, ${
                                    shift.time
                                  }`
                                );
                              }}
                            >
                              <div className="font-medium">{shift.time}</div>
                              {shift.status === "approved-leave" && (
                                <div className="mt-1">
                                  <div className="text-xs font-medium">
                                    Approved leave
                                  </div>
                                  <div className="text-xs">⌛ {shift.note}</div>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default EmployeeScheduleIndex;
