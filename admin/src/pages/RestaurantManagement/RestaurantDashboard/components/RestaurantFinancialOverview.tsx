import { useState, useEffect } from "react";
import { useGetDailyCashFlow, useGetCashTotals, useGetCounterHistory } from "../../../../server-action/API/Restaurant/restaurantReports";
import { format, subDays } from "date-fns";
import Chart from "react-apexcharts";
import { Icon } from "@iconify/react/dist/iconify.js";
import MasterTable from "../../../../layouts/Table/MasterTable";

interface RestaurantFinancialOverviewProps {
  dateRange: {
    startDate: string;
    endDate: string;
  };
}

export const RestaurantFinancialOverview = ({ dateRange }: RestaurantFinancialOverviewProps) => {
  // Convert date range format for API
  const apiDateRange = {
    fromDate: dateRange.startDate,
    toDate: dateRange.endDate,
  };

  // Fetch data from API
  const { data: cashFlowData, isLoading: isCashFlowLoading } = useGetDailyCashFlow(apiDateRange);
  const { data: cashTotals, isLoading: isCashTotalsLoading } = useGetCashTotals();
  const { data: counterHistory, isLoading: isCounterHistoryLoading } = useGetCounterHistory();

  // Prepare data for cash flow chart
  const cashFlowChartData = {
    options: {
      chart: {
        type: "line" as const,
        height: 350,
        toolbar: {
          show: false,
        },
      },
      stroke: {
        curve: "smooth" as "smooth" | "straight" | "stepline" | "linestep" | "monotoneCubic",
        width: 2,
      },
      xaxis: {
        categories: cashFlowData?.map(item => format(new Date(item.date), "MMM dd")) || [],
        title: {
          text: "Date",
        },
      },
      yaxis: {
        title: {
          text: "Amount (Rs)",
        },
      },
      tooltip: {
        y: {
          formatter: function (val: number) {
            return "Rs " + val.toFixed(2);
          },
        },
      },
      colors: ["#2A3A6D", "#40528F", "#1E265E", "#070C44"],
      legend: {
        position: "top" as "top" | "right" | "bottom" | "left",
      },
    },
    series: [
      {
        name: "Cash In",
        data: cashFlowData?.map(item => item.totalCashIn) || [],
      },
      {
        name: "Cash Out",
        data: cashFlowData?.map(item => item.totalCashOut) || [],
      },
      {
        name: "Net Cash Flow",
        data: cashFlowData?.map(item => item.netCashFlow) || [],
      },
      {
        name: "Order Revenue",
        data: cashFlowData?.map(item => item.orderRevenue) || [],
      },
    ],
  };

  // Table columns for counter history
  const counterHistoryColumns = [
    {
      key: "counterName",
      title: "Counter Name",
    },
    {
      key: "openingBalance",
      title: "Opening Balance",
      render: (row: any) => `Rs ${row.openingBalance.toFixed(2)}`,
    },
    {
      key: "cashIn",
      title: "Cash In",
      render: (row: any) => `Rs ${row.cashIn.toFixed(2)}`,
    },
    {
      key: "cashOut",
      title: "Cash Out",
      render: (row: any) => `Rs ${row.cashOut.toFixed(2)}`,
    },
    {
      key: "bankTransactions",
      title: "Bank Transactions",
      render: (row: any) => `Rs ${row.bankTransactions.toFixed(2)}`,
    },
    {
      key: "closingBalance",
      title: "Closing Balance",
      render: (row: any) => `Rs ${row.closingBalance.toFixed(2)}`,
    },
  ];

  return (
    <div className="flex flex-col gap-6">
      {/* Cash Totals Summary Cards */}
      <div className="grid grid-cols-3 gap-4">
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
          <h3 className="text-lg font-medium mb-3">Weekly Summary</h3>
          <div className="grid grid-cols-3 gap-2">
            <div>
              <p className="text-sm text-gray-500">Total Cash</p>
              <p className="text-xl font-bold">Rs {cashTotals?.weekly.totalCash.toFixed(2) || "0.00"}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Cash In</p>
              <p className="text-xl font-bold text-green-600">Rs {cashTotals?.weekly.cashIn.toFixed(2) || "0.00"}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Cash Out</p>
              <p className="text-xl font-bold text-red-600">Rs {cashTotals?.weekly.cashOut.toFixed(2) || "0.00"}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
          <h3 className="text-lg font-medium mb-3">Monthly Summary</h3>
          <div className="grid grid-cols-3 gap-2">
            <div>
              <p className="text-sm text-gray-500">Total Cash</p>
              <p className="text-xl font-bold">Rs {cashTotals?.monthly.totalCash.toFixed(2) || "0.00"}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Cash In</p>
              <p className="text-xl font-bold text-green-600">Rs {cashTotals?.monthly.cashIn.toFixed(2) || "0.00"}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Cash Out</p>
              <p className="text-xl font-bold text-red-600">Rs {cashTotals?.monthly.cashOut.toFixed(2) || "0.00"}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
          <h3 className="text-lg font-medium mb-3">Yearly Summary</h3>
          <div className="grid grid-cols-3 gap-2">
            <div>
              <p className="text-sm text-gray-500">Total Cash</p>
              <p className="text-xl font-bold">Rs {cashTotals?.yearly.totalCash.toFixed(2) || "0.00"}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Cash In</p>
              <p className="text-xl font-bold text-green-600">Rs {cashTotals?.yearly.cashIn.toFixed(2) || "0.00"}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Cash Out</p>
              <p className="text-xl font-bold text-red-600">Rs {cashTotals?.yearly.cashOut.toFixed(2) || "0.00"}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Date range is controlled by the parent component */}

      {/* Cash Flow Chart */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
        <h3 className="text-lg font-medium mb-4">Daily Cash Flow</h3>
        {isCashFlowLoading ? (
          <div className="flex justify-center items-center h-64">
            <p>Loading chart data...</p>
          </div>
        ) : cashFlowData && cashFlowData.length > 0 ? (
          <Chart
            options={cashFlowChartData.options}
            series={cashFlowChartData.series}
            type="line"
            height={350}
          />
        ) : (
          <div className="flex justify-center items-center h-64">
            <p>No cash flow data available for the selected period.</p>
          </div>
        )}
      </div>

      {/* Counter History Table */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
        <h3 className="text-lg font-medium mb-4">Counter History</h3>
        {isCounterHistoryLoading ? (
          <div className="flex justify-center items-center h-64">
            <p>Loading counter history...</p>
          </div>
        ) : counterHistory && counterHistory.counterSummaries.length > 0 ? (
          <MasterTable
            rows={counterHistory.counterSummaries}
            columns={counterHistoryColumns}
            loading={isCounterHistoryLoading}
          />
        ) : (
          <div className="flex justify-center items-center h-64">
            <p>No counter history data available.</p>
          </div>
        )}
      </div>

      {/* Overall Summary */}
      {counterHistory && counterHistory.overallSummary && (
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
          <h3 className="text-lg font-medium mb-4">Overall Counter Summary</h3>
          <div className="grid grid-cols-5 gap-4">
            <div>
              <p className="text-sm text-gray-500">Opening Balance</p>
              <p className="text-xl font-bold">Rs {counterHistory.overallSummary.totalOpeningBalance.toFixed(2)}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Cash In</p>
              <p className="text-xl font-bold text-green-600">Rs {counterHistory.overallSummary.totalCashIn.toFixed(2)}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Cash Out</p>
              <p className="text-xl font-bold text-red-600">Rs {counterHistory.overallSummary.totalCashOut.toFixed(2)}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Bank Transactions</p>
              <p className="text-xl font-bold">Rs {counterHistory.overallSummary.totalBankTransactions.toFixed(2)}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Closing Balance</p>
              <p className="text-xl font-bold">Rs {counterHistory.overallSummary.totalClosingBalance.toFixed(2)}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
