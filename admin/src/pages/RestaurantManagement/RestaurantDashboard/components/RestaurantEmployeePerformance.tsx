import { useState } from "react";
import { Icon } from "@iconify/react/dist/iconify.js";
import Chart from "react-apexcharts";
import { format, subDays } from "date-fns";
import { useGetWaiterPerformance, useGetAttendanceReport } from "../../../../server-action/API/Restaurant/restaurantReports";

interface RestaurantEmployeePerformanceProps {
  dateRange: {
    startDate: string;
    endDate: string;
  };
}

export const RestaurantEmployeePerformance = ({ dateRange }: RestaurantEmployeePerformanceProps) => {

  // Fetch data from API
  const { data: waiterPerformance, isLoading: isWaiterLoading } = useGetWaiterPerformance(dateRange);
  const { data: attendanceReport, isLoading: isAttendanceLoading } = useGetAttendanceReport(dateRange);

  // Process waiter performance data
  const topWaiters = waiterPerformance || [];

  // Process attendance data
  const attendanceData = attendanceReport?.records || [];

  // Sales performance chart
  const salesPerformanceChart = {
    options: {
      chart: {
        type: "bar" as const,
        height: 350,
        toolbar: {
          show: false,
        },
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: "55%",
          borderRadius: 4,
        },
      },
      dataLabels: {
        enabled: false,
      },
      stroke: {
        show: true,
        width: 2,
        colors: ["transparent"],
      },
      xaxis: {
        categories: topWaiters.slice(0, 5).map(waiter => waiter.name),
      },
      yaxis: [
        {
          title: {
            text: "Orders & Tables",
          },
        },
        {
          opposite: true,
          title: {
            text: "Sales Amount (Rs)",
          },
        },
      ],
      tooltip: {
        y: {
          formatter: function (val: number, opts: any) {
            if (opts.seriesIndex === 2) {
              return "Rs " + val.toFixed(2);
            }
            return val.toString();
          },
        },
      },
      colors: ["#2A3A6D", "#40528F", "#1E265E"],
      legend: {
        position: "top" as "top" | "right" | "bottom" | "left",
      },
    },
    series: [
      {
        name: "Orders Completed",
        data: topWaiters.slice(0, 5).map(waiter => waiter.ordersCompleted),
      },
      {
        name: "Tables Served",
        data: topWaiters.slice(0, 5).map(waiter => waiter.tablesServed),
      },
      {
        name: "Sales Amount",
        data: topWaiters.slice(0, 5).map(waiter => waiter.totalSales / 1000), // Scaled for visibility
      },
    ],
  };

  // Attendance chart
  const attendanceChart = {
    options: {
      chart: {
        type: "bar" as const,
        height: 350,
        stacked: true,
        toolbar: {
          show: false,
        },
      },
      plotOptions: {
        bar: {
          horizontal: true,
          columnWidth: "55%",
          borderRadius: 2,
        },
      },
      dataLabels: {
        enabled: false,
      },
      stroke: {
        show: true,
        width: 1,
        colors: ["transparent"],
      },
      xaxis: {
        categories: attendanceData.slice(0, 8).map(employee => employee.name),
      },
      yaxis: {
        title: {
          text: "Days",
        },
      },
      fill: {
        opacity: 1,
      },
      tooltip: {
        y: {
          formatter: function (val: number) {
            return val + " days";
          },
        },
      },
      colors: ["#2A3A6D", "#DD2025", "#FFA500", "#40528F"],
      legend: {
        position: "top" as "top" | "right" | "bottom" | "left",
      },
    },
    series: [
      {
        name: "Present",
        data: attendanceData.slice(0, 8).map(employee => employee.present),
      },
      {
        name: "Absent",
        data: attendanceData.slice(0, 8).map(employee => employee.absent),
      },
      {
        name: "Late",
        data: attendanceData.slice(0, 8).map(employee => employee.late),
      },
      {
        name: "Leave",
        data: attendanceData.slice(0, 8).map(employee => employee.leave),
      },
    ],
  };

  // Service time chart
  const serviceTimeChart = {
    options: {
      chart: {
        type: "bar" as const,
        height: 350,
        toolbar: {
          show: false,
        },
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: "55%",
          borderRadius: 4,
        },
      },
      dataLabels: {
        enabled: false,
      },
      stroke: {
        show: true,
        width: 2,
        colors: ["transparent"],
      },
      xaxis: {
        categories: topWaiters.slice(0, 8).map(waiter => waiter.name),
      },
      yaxis: {
        title: {
          text: "Minutes",
        },
      },
      fill: {
        opacity: 1,
      },
      tooltip: {
        y: {
          formatter: function (val: number) {
            return val + " minutes";
          },
        },
      },
      colors: ["#2A3A6D"],
    },
    series: [
      {
        name: "Average Service Time",
        data: topWaiters.slice(0, 8).map(waiter => waiter.averageServiceTime),
      },
    ],
  };

  return (
    <div className="flex flex-col gap-6">
      {/* Top Performers */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
        <h3 className="text-lg font-medium mb-4">Top Performing Waiters</h3>

        {isWaiterLoading ? (
          <div className="flex justify-center items-center py-6">
            <Icon icon="mdi:loading" className="animate-spin text-blue-600" width={24} />
            <span className="ml-2">Loading waiter performance data...</span>
          </div>
        ) : topWaiters.length > 0 ? (
          <div className="grid grid-cols-5 gap-4">
            {topWaiters.slice(0, 5).map((waiter, index) => (
              <div key={index} className="bg-gray-50 p-3 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-bold">
                    {waiter.name.charAt(0)}
                  </div>
                  <p className="font-medium truncate">{waiter.name}</p>
                </div>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <p className="text-gray-500">Orders</p>
                    <p className="font-bold">{waiter.ordersCompleted}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Tables</p>
                    <p className="font-bold">{waiter.tablesServed}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Rating</p>
                    <p className="font-bold flex items-center">
                      {waiter.customerRating.toFixed(1)}
                      <Icon icon="mdi:star" className="text-amber-500 ml-1" width={16} />
                    </p>
                  </div>
                  <div>
                    <p className="text-gray-500">Sales</p>
                    <p className="font-bold">Rs {waiter.totalSales.toLocaleString()}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-center py-6">No waiter performance data available</p>
        )}
      </div>

      {/* Sales Performance Chart */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
        <h3 className="text-lg font-medium mb-4">Sales Performance</h3>
        {isWaiterLoading ? (
          <div className="flex justify-center items-center py-6">
            <Icon icon="mdi:loading" className="animate-spin text-blue-600" width={24} />
            <span className="ml-2">Loading sales performance data...</span>
          </div>
        ) : topWaiters.length > 0 ? (
          <Chart
            options={salesPerformanceChart.options}
            series={salesPerformanceChart.series}
            type="bar"
            height={350}
          />
        ) : (
          <p className="text-center py-6">No sales performance data available</p>
        )}
      </div>

      {/* Attendance Chart */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
        <h3 className="text-lg font-medium mb-4">Monthly Attendance</h3>
        {isAttendanceLoading ? (
          <div className="flex justify-center items-center py-6">
            <Icon icon="mdi:loading" className="animate-spin text-blue-600" width={24} />
            <span className="ml-2">Loading attendance data...</span>
          </div>
        ) : attendanceData.length > 0 ? (
          <Chart
            options={attendanceChart.options}
            series={attendanceChart.series}
            type="bar"
            height={350}
          />
        ) : (
          <p className="text-center py-6">No attendance data available</p>
        )}
      </div>

      {/* Service Time Chart */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
        <h3 className="text-lg font-medium mb-4">Average Service Time</h3>
        {isWaiterLoading ? (
          <div className="flex justify-center items-center py-6">
            <Icon icon="mdi:loading" className="animate-spin text-blue-600" width={24} />
            <span className="ml-2">Loading service time data...</span>
          </div>
        ) : topWaiters.length > 0 ? (
          <Chart
            options={serviceTimeChart.options}
            series={serviceTimeChart.series}
            type="bar"
            height={350}
          />
        ) : (
          <p className="text-center py-6">No service time data available</p>
        )}
      </div>
    </div>
  );
};
