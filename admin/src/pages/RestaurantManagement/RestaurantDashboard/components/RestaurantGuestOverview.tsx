import { useState } from "react";
import { format, subDays } from "date-fns";
import {
  useGetGuestStats,
  useGetTableUtilization,
  useGetCustomerTimeFlowData
} from "../../../../server-action/API/Restaurant/restaurantReports";
import { Icon } from "@iconify/react/dist/iconify.js";
import Chart from "react-apexcharts";

interface RestaurantGuestOverviewProps {
  dateRange: {
    startDate: string;
    endDate: string;
  };
}

export const RestaurantGuestOverview = ({ dateRange }: RestaurantGuestOverviewProps) => {

  // Fetch data from API
  const { data: guestStats, isLoading: isGuestStatsLoading } = useGetGuestStats();
  const { data: tableUtilization, isLoading: isTableUtilizationLoading } = useGetTableUtilization();
  const { data: customerTimeFlow, isLoading: isTimeFlowLoading } = useGetCustomerTimeFlowData(dateRange);

  // Mock data for table utilization by type
  const [tableTypeData] = useState([
    { type: "2-Person", total: 10, occupied: 6 },
    { type: "4-Person", total: 15, occupied: 12 },
    { type: "6-Person", total: 8, occupied: 5 },
    { type: "8-Person", total: 5, occupied: 2 },
    { type: "Private Room", total: 3, occupied: 1 },
  ]);

  // Process time flow data for chart
  const timeFlowData = customerTimeFlow?.timeFlowData || [];

  // Log data for debugging
  console.log('Customer Time Flow Data:', customerTimeFlow);

  // Guest flow chart
  const guestFlowChart = {
    options: {
      chart: {
        type: "area" as const,
        height: 350,
        toolbar: {
          show: false,
        },
      },
      dataLabels: {
        enabled: false,
      },
      stroke: {
        curve: "smooth" as "smooth" | "straight" | "stepline" | "linestep" | "monotoneCubic",
        width: 2,
      },
      fill: {
        type: "gradient",
        gradient: {
          shadeIntensity: 1,
          opacityFrom: 0.7,
          opacityTo: 0.3,
          stops: [0, 90, 100],
        },
      },
      xaxis: {
        categories: timeFlowData.map(item => `${item.hour}:00`),
        title: {
          text: "Hour of Day",
        },
      },
      yaxis: {
        title: {
          text: "Guest Count",
        },
      },
      colors: ["#2A3A6D"],
    },
    series: [
      {
        name: "Guest Count",
        data: timeFlowData.map(item => item.customerCount),
      },
    ],
  };

  // Table utilization chart
  const tableUtilizationChart = {
    options: {
      chart: {
        type: "bar" as const,
        height: 350,
        stacked: true,
        toolbar: {
          show: false,
        },
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: "55%",
          borderRadius: 4,
        },
      },
      dataLabels: {
        enabled: false,
      },
      stroke: {
        show: true,
        width: 2,
        colors: ["transparent"],
      },
      xaxis: {
        categories: tableTypeData.map(item => item.type),
      },
      yaxis: {
        title: {
          text: "Tables",
        },
      },
      fill: {
        opacity: 1,
      },
      tooltip: {
        y: {
          formatter: function (val: number) {
            return val + " tables";
          },
        },
      },
      colors: ["#2A3A6D", "#40528F"],
      legend: {
        position: "top" as "top" | "right" | "bottom" | "left",
      },
    },
    series: [
      {
        name: "Occupied",
        data: tableTypeData.map(item => item.occupied),
      },
      {
        name: "Available",
        data: tableTypeData.map(item => item.total - item.occupied),
      },
    ],
  };

  return (
    <div className="flex flex-col gap-6">
      {/* Guest Stats Cards */}
      <div className="grid grid-cols-3 gap-4">
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
          <div className="flex items-center gap-2 mb-2">
            <Icon icon="mdi:account-group" className="text-blue-600" width={24} />
            <p className="text-sm font-medium">Total Guests</p>
          </div>
          <p className="text-2xl font-bold">{guestStats?.totalGuests || 0}</p>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
          <div className="flex items-center gap-2 mb-2">
            <Icon icon="mdi:food-fork-drink" className="text-green-600" width={24} />
            <p className="text-sm font-medium">Total Orders</p>
          </div>
          <p className="text-2xl font-bold">{guestStats?.totalOrders || 0}</p>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
          <div className="flex items-center gap-2 mb-2">
            <Icon icon="mdi:cash-register" className="text-amber-600" width={24} />
            <p className="text-sm font-medium">Total Sales</p>
          </div>
          <p className="text-2xl font-bold">Rs {guestStats?.totalSales?.toLocaleString() || 0}</p>
        </div>
      </div>

      {/* Table Utilization Cards */}
      <div className="grid grid-cols-3 gap-4">
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
          <div className="flex items-center gap-2 mb-2">
            <Icon icon="mdi:account-multiple" className="text-blue-600" width={24} />
            <p className="text-sm font-medium">Guests Today</p>
          </div>
          <p className="text-2xl font-bold">{tableUtilization?.totalGuestsToday || 0}</p>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
          <div className="flex items-center gap-2 mb-2">
            <Icon icon="mdi:account-check" className="text-green-600" width={24} />
            <p className="text-sm font-medium">Current Guests</p>
          </div>
          <p className="text-2xl font-bold">{tableUtilization?.totalGuestsCurrently || 0}</p>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
          <div className="flex items-center gap-2 mb-2">
            <Icon icon="mdi:calendar-clock" className="text-amber-600" width={24} />
            <p className="text-sm font-medium">Reservations</p>
          </div>
          <p className="text-2xl font-bold">{tableUtilization?.totalReservation || 0}</p>
        </div>
      </div>

      {/* Guest Flow Chart */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
        <h3 className="text-lg font-medium mb-4">Guest Flow by Hour</h3>

        {isTimeFlowLoading ? (
          <div className="flex justify-center items-center py-6 h-[350px]">
            <Icon icon="mdi:loading" className="animate-spin text-blue-600" width={24} />
            <span className="ml-2">Loading guest flow data...</span>
          </div>
        ) : timeFlowData.length > 0 ? (
          <div>
            <div className="grid grid-cols-3 gap-4 mb-4">
              <div className="bg-blue-50 p-3 rounded-lg">
                <h4 className="text-sm font-medium text-gray-500">Total Orders</h4>
                <p className="text-2xl font-bold text-blue-600">
                  {timeFlowData.reduce((sum, item) => sum + item.totalOrders, 0)}
                </p>
              </div>
              <div className="bg-green-50 p-3 rounded-lg">
                <h4 className="text-sm font-medium text-gray-500">Total Customers</h4>
                <p className="text-2xl font-bold text-green-600">
                  {timeFlowData.reduce((sum, item) => sum + item.customerCount, 0)}
                </p>
              </div>
              <div className="bg-purple-50 p-3 rounded-lg">
                <h4 className="text-sm font-medium text-gray-500">Order Types</h4>
                <div className="flex gap-2 mt-1">
                  <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                    Dine-in: {timeFlowData.reduce((sum, item) => sum + item.orderTypes.dineIn, 0)}
                  </span>
                  <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                    Takeaway: {timeFlowData.reduce((sum, item) => sum + item.orderTypes.takeaway, 0)}
                  </span>
                  <span className="px-2 py-1 text-xs bg-amber-100 text-amber-800 rounded-full">
                    Delivery: {timeFlowData.reduce((sum, item) => sum + item.orderTypes.delivery, 0)}
                  </span>
                </div>
              </div>
            </div>
            <Chart
              options={guestFlowChart.options}
              series={guestFlowChart.series}
              type="area"
              height={350}
            />
          </div>
        ) : (
          <div className="flex justify-center items-center py-6 h-[350px]">
            <p>No guest flow data available for the selected period</p>
          </div>
        )}
      </div>

      {/* Table Utilization Chart */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
        <h3 className="text-lg font-medium mb-4">Table Utilization by Type</h3>
        <Chart
          options={tableUtilizationChart.options}
          series={tableUtilizationChart.series}
          type="bar"
          height={350}
        />
      </div>

      {/* Peak Hours */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
        <h3 className="text-lg font-medium mb-4">Peak Hours</h3>
        {isTimeFlowLoading ? (
          <div className="flex justify-center items-center py-6">
            <Icon icon="mdi:loading" className="animate-spin text-blue-600" width={24} />
            <span className="ml-2">Loading peak hours data...</span>
          </div>
        ) : customerTimeFlow?.peakHours && customerTimeFlow.peakHours.length > 0 ? (
          <div className="grid grid-cols-3 gap-4">
            {customerTimeFlow.peakHours.map((peak, index) => {
              // Calculate intensity percentage for the progress bar
              const intensityPercentage =
                peak.intensity === "high" ? 90 :
                peak.intensity === "medium" ? 60 : 30;

              // Format the time range
              const timeRange = `${peak.start}:00 - ${peak.end}:00`;

              // Determine the meal type based on the hour
              let mealType = "";
              if (peak.start >= 6 && peak.start <= 10) mealType = "Breakfast";
              else if (peak.start >= 11 && peak.start <= 14) mealType = "Lunch";
              else if (peak.start >= 17 && peak.start <= 22) mealType = "Dinner";
              else mealType = "Off-hours";

              // Determine color based on intensity
              const bgColor =
                peak.intensity === "high" ? "bg-red-50" :
                peak.intensity === "medium" ? "bg-amber-50" : "bg-blue-50";

              const barColor =
                peak.intensity === "high" ? "bg-red-600" :
                peak.intensity === "medium" ? "bg-amber-600" : "bg-blue-600";

              return (
                <div key={index} className={`${bgColor} p-3 rounded-lg`}>
                  <h4 className="text-base font-medium mb-2">{mealType} Peak</h4>
                  <p className="text-sm">{timeRange}</p>
                  <div className="mt-2 bg-gray-200 h-2 rounded-full overflow-hidden">
                    <div
                      className={`${barColor} h-full rounded-full`}
                      style={{ width: `${intensityPercentage}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">{peak.intensity.charAt(0).toUpperCase() + peak.intensity.slice(1)} intensity</p>
                </div>
              );
            })}
          </div>
        ) : (
          <p className="text-center py-4">No peak hours data available</p>
        )}
      </div>

      {/* Popular Items */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
        <h3 className="text-lg font-medium mb-4">Popular Menu Items</h3>
        {isTimeFlowLoading ? (
          <div className="flex justify-center items-center py-6">
            <Icon icon="mdi:loading" className="animate-spin text-blue-600" width={24} />
            <span className="ml-2">Loading popular items data...</span>
          </div>
        ) : timeFlowData.length > 0 ? (
          <div>
            {/* Collect all popular items across all hours */}
            {(() => {
              // Define a type for the popular items
              type PopularItem = {
                name: string;
                count: number;
                itemId?: string;
              };

              const allItems = timeFlowData.flatMap(hour => hour.popularItems || []) as PopularItem[];
              // Add itemId if it doesn't exist
              const itemsWithId = allItems.map(item => ({
                itemId: item.itemId || item.name, // Use name as fallback ID
                name: item.name,
                count: item.count
              }));

              const itemCounts = itemsWithId.reduce((acc, item) => {
                const existingItem = acc.find(i => i.itemId === item.itemId);
                if (existingItem) {
                  existingItem.count += item.count;
                } else {
                  acc.push({...item});
                }
                return acc;
              }, [] as {itemId: string, name: string, count: number}[]);

              // Sort by count in descending order
              const sortedItems = itemCounts.sort((a, b) => b.count - a.count).slice(0, 10);

              // Calculate total count for percentage
              const totalCount = sortedItems.reduce((sum, item) => sum + item.count, 0);

              return (
                <div className="space-y-3">
                  {sortedItems.map((item, index) => {
                    const percentage = Math.round((item.count / totalCount) * 100);
                    return (
                      <div key={index} className="flex items-center">
                        <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-bold mr-3">
                          {index + 1}
                        </div>
                        <div className="flex-1">
                          <div className="flex justify-between">
                            <p className="font-medium">{item.name}</p>
                            <p className="text-sm text-gray-500">{item.count} orders ({percentage}%)</p>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                            <div
                              className="bg-blue-600 h-1.5 rounded-full"
                              style={{ width: `${percentage}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              );
            })()}
          </div>
        ) : (
          <p className="text-center py-4">No popular items data available</p>
        )}
      </div>
    </div>
  );
};
