import { useState } from "react";
import { Icon } from "@iconify/react/dist/iconify.js";
import Chart from "react-apexcharts";
import { format, subDays } from "date-fns";
import { useGetInventoryStock, useGetRestaurantItems } from "../../../../server-action/API/Restaurant/restaurantReports";

interface RestaurantInventoryOverviewProps {
  dateRange: {
    startDate: string;
    endDate: string;
  };
}

export const RestaurantInventoryOverview = ({ dateRange }: RestaurantInventoryOverviewProps) => {

  // Fetch inventory data from API
  const { data: inventoryStock, isLoading: isInventoryLoading } = useGetInventoryStock();
  const { data: restaurantItems, isLoading: isItemsLoading } = useGetRestaurantItems();

  // Process restaurant items data
  const itemsData = restaurantItems || [];

  // Process inventory data for display
  const stockLevels = inventoryStock || [];

  return (
    <div className="flex flex-col gap-6">
      {/* Restaurant Items Overview */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 mb-6">
        <div className="flex items-center gap-2 mb-4">
          <Icon icon="mdi:food-outline" className="text-blue-600" width={24} />
          <h3 className="text-lg font-medium">Restaurant Items Overview</h3>
        </div>

        {isItemsLoading ? (
          <div className="flex justify-center items-center py-6">
            <Icon icon="mdi:loading" className="animate-spin text-blue-600" width={24} />
            <span className="ml-2">Loading restaurant items...</span>
          </div>
        ) : itemsData.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {itemsData.slice(0, 6).map((item, index) => {
              const stockPercentage = Math.min(100, Math.round((item.remainingStock / (item.initialStock || 1)) * 100));
              let stockColor = 'bg-blue-500';

              if (stockPercentage < 30) {
                stockColor = 'bg-red-500';
              } else if (stockPercentage < 70) {
                stockColor = 'bg-yellow-500';
              }

              return (
                <div key={index} className="flex justify-between items-center p-3 bg-blue-50 rounded-md">
                  <div className="w-full">
                    <p className="font-medium">{typeof item.name === 'object' ? 'Unknown' : item.name}</p>
                    <p className="text-sm text-gray-500">
                      {typeof item.category === 'object' ? item.category.name : item.category} •
                      {item.type || 'General'}
                    </p>
                    <div className="flex justify-between mt-1">
                      <span className="text-sm text-gray-500">Stock:</span>
                      <span className="text-sm font-medium text-blue-600">
                        {item.remainingStock} / {item.initialStock || 0}
                      </span>
                    </div>
                    <div className="mt-2 w-full bg-gray-200 rounded-full h-1.5">
                      <div
                        className={`h-1.5 rounded-full ${stockColor}`}
                        style={{ width: `${stockPercentage}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <p className="text-center py-6">No restaurant items found</p>
        )}
      </div>

      {/* Stock Levels Chart */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
        <h3 className="text-lg font-medium mb-4">Restaurant Items Stock Levels</h3>
        {isItemsLoading ? (
          <div className="flex justify-center items-center py-12">
            <Icon icon="mdi:loading" className="animate-spin text-blue-600" width={24} />
            <span className="ml-2">Loading stock data...</span>
          </div>
        ) : itemsData.length > 0 ? (
          <div>
            <div className="mb-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-gray-500">Total Items</h4>
                  <p className="text-2xl font-bold text-blue-600">{itemsData.length}</p>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-gray-500">Total Stock</h4>
                  <p className="text-2xl font-bold text-green-600">
                    {itemsData.reduce((sum, item) => sum + (item.remainingStock || 0), 0).toLocaleString()}
                  </p>
                </div>
                <div className="bg-yellow-50 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-gray-500">Used Stock</h4>
                  <p className="text-2xl font-bold text-yellow-600">
                    {itemsData.reduce((sum, item) => sum + (item.usedStock || 0), 0).toLocaleString()}
                  </p>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-gray-500">Categories</h4>
                  <p className="text-2xl font-bold text-purple-600">
                    {new Set(itemsData.map(item =>
                      typeof item.category === 'object' ? item.category.name : item.category
                    )).size}
                  </p>
                </div>
              </div>
            </div>
            <Chart
              options={{
                chart: {
                  type: "bar" as const,
                  height: 350,
                  stacked: false,
                  toolbar: {
                    show: false,
                  },
                },
                plotOptions: {
                  bar: {
                    horizontal: true,
                    columnWidth: "55%",
                    borderRadius: 2,
                  },
                },
                dataLabels: {
                  enabled: false,
                },
                stroke: {
                  show: true,
                  width: 2,
                  colors: ["transparent"],
                },
                xaxis: {
                  categories: itemsData.slice(0, 10).map(item =>
                    typeof item.name === 'object' ? 'Unknown' : item.name
                  ),
                },
                yaxis: {
                  title: {
                    text: "Quantity",
                  },
                },
                fill: {
                  opacity: 1,
                },
                tooltip: {
                  y: {
                    formatter: function (val: number) {
                      return val + " units";
                    },
                  },
                },
                colors: ["#2A3A6D", "#40528F", "#1E265E"],
                legend: {
                  position: "top",
                },
              }}
              series={[
                {
                  name: "Initial Stock",
                  data: itemsData.slice(0, 10).map(item => item.initialStock || 0),
                },
                {
                  name: "Used Stock",
                  data: itemsData.slice(0, 10).map(item => item.usedStock || 0),
                },
                {
                  name: "Remaining Stock",
                  data: itemsData.slice(0, 10).map(item => item.remainingStock || 0),
                },
              ]}
              type="bar"
              height={350}
            />
          </div>
        ) : (
          <div className="text-center py-12 text-gray-500">
            <Icon icon="mdi:database-off" className="mx-auto mb-4" width={48} />
            <p>No stock data available</p>
          </div>
        )}
      </div>

      {/* Restaurant Items Table */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
        <h3 className="text-lg font-medium mb-4">Restaurant Items Inventory</h3>
        {isItemsLoading ? (
          <div className="flex justify-center items-center py-6">
            <Icon icon="mdi:loading" className="animate-spin text-blue-600" width={24} />
            <span className="ml-2">Loading restaurant items...</span>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Initial Stock</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Used Stock</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remaining Stock</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Threshold</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {itemsData.map((item, index) => {
                  const threshold = item.threshold || 0;
                  const isLowStock = (item.remainingStock < threshold) && threshold > 0;
                  const stockPercentage = Math.min(100, Math.round((item.remainingStock / item.initialStock) * 100));
                  let stockColor = 'bg-green-500';

                  if (stockPercentage < 30) {
                    stockColor = 'bg-red-500';
                  } else if (stockPercentage < 70) {
                    stockColor = 'bg-yellow-500';
                  }

                  return (
                    <tr key={index} className={isLowStock ? "bg-red-50" : ""}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {typeof item.name === 'object' ? 'Unknown' : item.name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {typeof item.category === 'object' ? item.category.name : item.category}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {item.type || 'General'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.initialStock}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.usedStock}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <span className={isLowStock ? "font-medium text-red-600" : ""}>
                          {item.remainingStock}
                        </span>
                        {isLowStock && (
                          <span className="ml-2 px-1 py-0.5 text-xs rounded-sm bg-red-100 text-red-800">
                            !{item.threshold}
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.threshold}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="w-24 bg-gray-200 rounded-full h-1.5">
                          <div
                            className={`h-1.5 rounded-full ${stockColor}`}
                            style={{ width: `${stockPercentage}%` }}
                          ></div>
                        </div>
                        <span className="text-xs text-gray-500 mt-1">{stockPercentage}%</span>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>


    </div>
  );
};
