import { useState, useEffect } from "react";
import { useGetMenuSalesReport, useGetTodayOrderStats } from "../../../../server-action/API/Restaurant/restaurantReports";
import { format, subDays } from "date-fns";
import Chart from "react-apexcharts";
import { Icon } from "@iconify/react/dist/iconify.js";
import MasterTable from "../../../../layouts/Table/MasterTable";

interface RestaurantSalesOverviewProps {
  dateRange: {
    startDate: string;
    endDate: string;
  };
}

export const RestaurantSalesOverview = ({ dateRange }: RestaurantSalesOverviewProps) => {

  // Fetch data from API
  const { data: menuSalesData, isLoading: isMenuSalesLoading } = useGetMenuSalesReport(dateRange);
  const { data: orderStats, isLoading: isOrderStatsLoading } = useGetTodayOrderStats();

  // Log the data for debugging
  useEffect(() => {
    console.log('Menu Sales Data:', menuSalesData);
  }, [menuSalesData]);

  // Prepare data for charts
  const menuSalesChartData = {
    options: {
      chart: {
        type: "bar" as const,
        height: 350,
        toolbar: {
          show: false,
        },
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: "55%",
          borderRadius: 4,
        },
      },
      dataLabels: {
        enabled: false,
      },
      stroke: {
        show: true,
        width: 2,
        colors: ["transparent"],
      },
      xaxis: {
        categories: menuSalesData && Array.isArray(menuSalesData) ?
          menuSalesData.slice(0, 10).map(item => item.itemName) : [],
        title: {
          text: "Menu Items",
        },
      },
      yaxis: {
        title: {
          text: "Amount (Rs)",
        },
      },
      fill: {
        opacity: 1,
      },
      tooltip: {
        y: {
          formatter: function (val: number) {
            return "Rs " + val.toFixed(2);
          },
        },
      },
      colors: ["#2A3A6D", "#40528F"],
    },
    series: [
      {
        name: "Total Sales",
        data: menuSalesData && Array.isArray(menuSalesData) ?
          menuSalesData.slice(0, 10).map(item => item.price * item.totalQuantityOrdered) : [],
      },
      {
        name: "Orders Count",
        data: menuSalesData && Array.isArray(menuSalesData) ?
          menuSalesData.slice(0, 10).map(item => item.numberOfOrders * 100) : [], // Scaled for visibility
      },
    ],
  };

  // Table columns for menu sales
  const menuSalesColumns = [
    {
      key: "itemName",
      title: "Menu Item",
    },
    {
      key: "category",
      title: "Category",
      render: (data: any) => {
        // Handle category IDs
        // Support both formats: direct row or {row, index}
        const row = data.row ? data.row : data;
        if (!row) return 'Food';
        const category = row.category;
        return typeof category === 'string' && category.includes('67c') ? 'Food' : category || 'Food';
      }
    },
    {
      key: "totalSales",
      title: "Total Sales",
      render: (data: any) => {
        // Support both formats: direct row or {row, index}
        const row = data.row ? data.row : data;
        if (!row) return 'Rs 0.00';
        const price = row.price || 0;
        const quantity = row.totalQuantityOrdered || 0;
        const totalSales = price * quantity;
        return `Rs ${totalSales.toFixed(2)}`;
      }
    },
    {
      key: "totalQuantityOrdered",
      title: "Quantity Sold",
    },
    {
      key: "numberOfOrders",
      title: "Orders Count",
    },
    {
      key: "averagePerOrder",
      title: "Avg Per Order",
      render: (data: any) => {
        // Support both formats: direct row or {row, index}
        const row = data.row ? data.row : data;
        if (!row || !row.averagePerOrder) return '0.00';
        return row.averagePerOrder.toFixed(2);
      },
    },
  ];

  return (
    <div className="flex flex-col gap-6">
      {/* Today's Order Stats Cards */}
      <div className="grid grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
          <div className="flex items-center gap-2 mb-2">
            <Icon icon="mdi:food" className="text-blue-600" width={24} />
            <p className="text-sm font-medium">Total Orders</p>
          </div>
          <p className="text-2xl font-bold">{orderStats?.totalOrders || 0}</p>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
          <div className="flex items-center gap-2 mb-2">
            <Icon icon="mdi:progress-clock" className="text-amber-600" width={24} />
            <p className="text-sm font-medium">In Progress</p>
          </div>
          <p className="text-2xl font-bold">{orderStats?.inProgressOrders || 0}</p>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
          <div className="flex items-center gap-2 mb-2">
            <Icon icon="mdi:alert-circle" className="text-red-600" width={24} />
            <p className="text-sm font-medium">Complaints</p>
          </div>
          <p className="text-2xl font-bold">{orderStats?.complaintOrders || 0}</p>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
          <div className="flex items-center gap-2 mb-2">
            <Icon icon="mdi:cancel" className="text-gray-600" width={24} />
            <p className="text-sm font-medium">Canceled</p>
          </div>
          <p className="text-2xl font-bold">{orderStats?.canceledOrders || 0}</p>
        </div>
      </div>

      {/* Date range is controlled by the parent component */}

      {/* Menu Sales Chart */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
        <h3 className="text-lg font-medium mb-4">Top Menu Items by Sales</h3>
        {isMenuSalesLoading ? (
          <div className="flex justify-center items-center h-64">
            <p>Loading chart data...</p>
          </div>
        ) : menuSalesData && Array.isArray(menuSalesData) && menuSalesData.length > 0 ? (
          <Chart
            options={menuSalesChartData.options}
            series={menuSalesChartData.series}
            type="bar"
            height={350}
          />
        ) : (
          <div className="flex justify-center items-center h-64">
            <p>No menu sales data available for the selected period.</p>
          </div>
        )}
      </div>

      {/* Menu Sales Table */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
        <h3 className="text-lg font-medium mb-4">Menu Sales Details</h3>
        {isMenuSalesLoading ? (
          <div className="flex justify-center items-center h-64">
            <p>Loading table data...</p>
          </div>
        ) : menuSalesData && Array.isArray(menuSalesData) && menuSalesData.length > 0 ? (
          <MasterTable
            rows={menuSalesData}
            columns={menuSalesColumns}
            loading={isMenuSalesLoading}
          />
        ) : (
          <div className="flex justify-center items-center h-64">
            <p>No menu sales data available for the selected period.</p>
          </div>
        )}
      </div>
    </div>
  );
};
