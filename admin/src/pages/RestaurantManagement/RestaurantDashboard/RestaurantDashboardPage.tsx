import { useState } from "react";
import { format, subDays } from "date-fns";
import { RestaurantSalesOverview } from "./components/RestaurantSalesOverview";
import { RestaurantFinancialOverview } from "./components/RestaurantFinancialOverview";
import { RestaurantInventoryOverview } from "./components/RestaurantInventoryOverview";
import { RestaurantGuestOverview } from "./components/RestaurantGuestOverview";
import { TabData } from "../../../components/TabData";
import { Card, CardContent } from "../../../components/Card";
import { Icon } from "@iconify/react/dist/iconify.js";
import Breadcrumbs from "../../../components/Breadcrumb";

const RestaurantDashboardPage = () => {
  const [selectedTab, setSelectedTab] = useState("sales");
  const [dateRange, setDateRange] = useState({
    startDate: format(subDays(new Date(), 7), "yyyy-MM-dd"),
    endDate: format(new Date(), "yyyy-MM-dd"),
  });

  const tabData = [
    {
      title: "Sales Overview",
      value: "sales",
    },
    {
      title: "Financial Overview",
      value: "financial",
    },
    {
      title: "Inventory Overview",
      value: "inventory",
    },
    {
      title: "Guest & Table Overview",
      value: "guest",
    },
  ];

  function handleApplyDateRange(
    event: React.MouseEvent<HTMLButtonElement, MouseEvent>
  ): void {
    event.preventDefault();
    // Optionally, you could trigger a data fetch or update here if needed.
    // Currently, the dateRange state is already updated on input change,
    // so this button can be used to trigger any side effects if required.
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between items-center">
        <Breadcrumbs />
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 space-y-4">
          <div className="flex flex-col lg:flex-row lg:items-end gap-6">
            {/* Date Range Inputs */}
            <div className="flex-1">
              <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3">
                {/* Start Date */}
                <div className="relative flex-1 min-w-0">
                  <input
                    type="date"
                    className="w-full border border-gray-300 rounded-lg px-4 py-3 text-sm
                     focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                     hover:border-gray-400 transition-all duration-200
                     bg-white shadow-sm"
                    value={dateRange.startDate}
                    max={dateRange.endDate || format(new Date(), "yyyy-MM-dd")}
                    onChange={(e) =>
                      setDateRange({ ...dateRange, startDate: e.target.value })
                    }
                  />
                  {!dateRange.startDate && (
                    <span className="absolute left-4 top-3 text-sm text-gray-400 pointer-events-none">
                      Start date
                    </span>
                  )}
                </div>

                {/* Separator */}
                <div className="flex items-center justify-center">
                  <div className="bg-gray-200 rounded-full p-2">
                    <Icon
                      icon="mdi:arrow-right"
                      width={16}
                      className="text-gray-600"
                    />
                  </div>
                </div>

                {/* End Date */}
                <div className="relative flex-1 min-w-0">
                  <input
                    type="date"
                    className="w-full border border-gray-300 rounded-lg px-4 py-3 text-sm
                     focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                     hover:border-gray-400 transition-all duration-200
                     bg-white shadow-sm"
                    value={dateRange.endDate}
                    min={dateRange.startDate}
                    max={format(new Date(), "yyyy-MM-dd")}
                    onChange={(e) =>
                      setDateRange({ ...dateRange, endDate: e.target.value })
                    }
                  />
                  {!dateRange.endDate && (
                    <span className="absolute left-4 top-3 text-sm text-gray-400 pointer-events-none">
                      End date
                    </span>
                  )}
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3">
              <button
                className="bg-[#2f478a] hover:bg-blue-700 active:bg-blue-800
                 text-white px-6 py-3 rounded-lg text-sm font-medium
                 flex items-center justify-center gap-2 transition-all duration-200
                 shadow-sm hover:shadow-md transform hover:-translate-y-0.5
                 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                onClick={handleApplyDateRange}
                disabled={!dateRange.startDate || !dateRange.endDate}
              >
                <Icon icon="mdi:filter" width={20} />
                Apply Filter
              </button>

              <button
                className="bg-gray-100 hover:bg-gray-200 active:bg-gray-300
                 text-gray-700 px-6 py-3 rounded-lg text-sm font-medium
                 flex items-center justify-center gap-2 transition-all duration-200
                 shadow-sm hover:shadow-md transform hover:-translate-y-0.5
                 border border-gray-200"
                onClick={() => {
                  setDateRange({
                    startDate: format(subDays(new Date(), 7), "yyyy-MM-dd"),
                    endDate: format(new Date(), "yyyy-MM-dd"),
                  });
                }}
                title="Reset to last 7 days"
              >
                <Icon icon="mdi:refresh" width={20} />
                Reset
              </button>
            </div>
          </div>
        </div>
      </div>

      <TabData
        selectedTabData={selectedTab}
        setSelectedTabData={setSelectedTab}
        tabData={tabData}
      />

      <Card className="bg-white">
        <CardContent>
          {selectedTab === "sales" && (
            <RestaurantSalesOverview dateRange={dateRange} />
          )}
          {selectedTab === "financial" && (
            <RestaurantFinancialOverview dateRange={dateRange} />
          )}
          {selectedTab === "inventory" && (
            <RestaurantInventoryOverview dateRange={dateRange} />
          )}
          {selectedTab === "guest" && (
            <RestaurantGuestOverview dateRange={dateRange} />
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default RestaurantDashboardPage;
