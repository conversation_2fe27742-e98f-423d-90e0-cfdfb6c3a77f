import { useState, useMemo } from "react";
import { Icon } from "@iconify/react/dist/iconify.js";
import { Card, CardContent } from "../../../components/Card";
import {
  useGetRestaurantItems,
  IItem,
} from "../../../server-action/API/Restaurant/restaurantReports";
import GlobalFilter from "../../../layouts/Table/GlobalFilter";
import Breadcrumbs from "../../../components/Breadcrumb";

const RestaurantInventoryPage = () => {
  const [filterValues, setFilterValues] = useState<any>({});

  // Fetch restaurant items data
  const { data: restaurantItems, isLoading } =
    useGetRestaurantItems(filterValues);

  // Process restaurant items data
  const itemsData = restaurantItems || [];

  // Calculate low stock items
  const lowStockItems = useMemo(() => {
    return itemsData.filter((item) => {
      const threshold = item.threshold || 0;
      return item.remainingStock < threshold && threshold > 0;
    });
  }, [itemsData]);

  // Filter fields for the inventory
  const filterFields = [
    {
      id: "category",
      type: "text" as const,
      label: "Category",
      placeholder: "Filter by category",
    },
    {
      id: "type",
      type: "text" as const,
      label: "Type",
      placeholder: "Filter by type",
    },
  ];

  // Handle filter application
  const handleApplyFilter = (values: any) => {
    setFilterValues(values);
  };

  // Handle filter reset
  const handleResetFilter = () => {
    setFilterValues({});
  };

  return (
    <div className="space-y-6 my-8">
      <Breadcrumbs />

      <Card>
        <CardContent>
          <div className="mb-6">
            <GlobalFilter
              fields={filterFields}
              onApply={handleApplyFilter}
              onReset={handleResetFilter}
            />
          </div>

          {/* Low Stock Alert Section */}
          {lowStockItems.length > 0 && (
            <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-3">
                <Icon
                  icon="mdi:alert-circle"
                  className="text-red-600"
                  width={24}
                />
                <h2 className="text-lg font-medium text-red-800">
                  Low Stock Alert
                </h2>
              </div>
              <p className="text-red-700 mb-3">
                {lowStockItems.length} item
                {lowStockItems.length !== 1 ? "s" : ""} below threshold level
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {lowStockItems.map((item, index) => (
                  <div
                    key={index}
                    className="bg-white p-3 rounded-md border border-red-200 shadow-sm"
                  >
                    <h3 className="font-medium text-gray-900">
                      {typeof item.name === "object" ? "Unknown" : item.name}
                    </h3>
                    <div className="flex justify-between mt-1">
                      <span className="text-sm text-gray-500">
                        {typeof item.category === "object"
                          ? item.category.name
                          : item.category}{" "}
                        •{item.type || "General"}
                      </span>
                      <span className="text-sm font-medium text-red-600">
                        {item.remainingStock} / {item.threshold}
                      </span>
                    </div>
                    <div className="mt-2 w-full bg-gray-200 rounded-full h-2.5">
                      <div
                        className="bg-red-600 h-2.5 rounded-full"
                        style={{
                          width: `${Math.min(
                            100,
                            (item.remainingStock / item.threshold) * 100
                          )}%`,
                        }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="bg-white rounded-lg shadow-sm">
            <div className="p-4 border-b border-gray-200">
              <h2 className="text-lg font-medium">
                Restaurant Items Inventory
              </h2>
              <p className="text-sm text-gray-500 mt-1">
                Manage your restaurant inventory items and track stock levels
              </p>
            </div>

            {isLoading ? (
              <div className="flex justify-center items-center py-12">
                <Icon
                  icon="mdi:loading"
                  className="animate-spin text-blue-600 mr-2"
                  width={24}
                />
                <span>Loading restaurant items...</span>
              </div>
            ) : itemsData.length === 0 ? (
              <div className="py-12 text-center">
                <Icon
                  icon="mdi:food-off"
                  className="text-gray-400 mx-auto mb-4"
                  width={48}
                />
                <h3 className="text-lg font-medium text-gray-900">
                  No items found
                </h3>
                <p className="text-gray-500 mt-1">
                  {Object.keys(filterValues).length > 0
                    ? "Try adjusting your filters"
                    : "Add items to your restaurant inventory to see them here"}
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Name
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Category
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Type
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Initial Stock
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Used Stock
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Remaining Stock
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Threshold
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Status
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Stock Level
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {itemsData.map((item, index) => {
                      const threshold = item.threshold || 0;
                      const isLowStock =
                        item.remainingStock < threshold && threshold > 0;
                      const initialStock = item.initialStock || 1; // Prevent division by zero
                      const remainingStock = item.remainingStock || 0;
                      const stockPercentage = Math.min(
                        100,
                        Math.round((remainingStock / initialStock) * 100)
                      );
                      let stockColor = "bg-green-500";

                      if (stockPercentage < 30) {
                        stockColor = "bg-red-500";
                      } else if (stockPercentage < 70) {
                        stockColor = "bg-yellow-500";
                      }

                      return (
                        <tr
                          key={index}
                          className={isLowStock ? "bg-red-50" : ""}
                        >
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {typeof item.name === "object"
                              ? "Unknown"
                              : item.name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {typeof item.category === "object"
                              ? item.category.name
                              : item.category}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {item.type || "General"}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {item.initialStock || 0}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {item.usedStock || 0}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <span
                              className={
                                isLowStock ? "font-medium text-red-600" : ""
                              }
                            >
                              {item.remainingStock || 0}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {threshold || "N/A"}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {isLowStock ? (
                              <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                Low Stock
                              </span>
                            ) : (
                              <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                In Stock
                              </span>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {initialStock > 0 ? (
                              <>
                                <div className="w-24 bg-gray-200 rounded-full h-2.5">
                                  <div
                                    className={`h-2.5 rounded-full ${stockColor}`}
                                    style={{ width: `${stockPercentage}%` }}
                                  ></div>
                                </div>
                                <span className="text-xs text-gray-500 mt-1">
                                  {stockPercentage}%
                                </span>
                              </>
                            ) : (
                              <span className="text-xs text-gray-500">
                                No stock data
                              </span>
                            )}
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RestaurantInventoryPage;
