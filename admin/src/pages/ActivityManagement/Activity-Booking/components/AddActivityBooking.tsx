import { Form, FormikProvider, useFormik } from "formik";
import HeadingPopup from "../../../GuestManagement/components/HeadingPopup";
import { useGetAllActivities } from "../../../../server-action/API/activity";
import { date, object, string } from "yup";
import { useGetAllBooking } from "../../../../server-action/API/BookingManagement/booking";
import { useState } from "react";
import {
  useCreateActivityBooking,
  useUpdateActivityBooking,
} from "../../../../server-action/API/activityBooking";
import { FormField } from "../../../BookingManagement/components/ReservationCustomForm";

interface propTypes {
  editData?: any;
  onClose?: () => void;
}

export const AddActivityBooking = ({ editData, onClose }: propTypes) => {
  const [switchStatus, setSwitchStatus] = useState(
    editData?.paymentStatus === "success" ? true : false
  );

  const { mutateAsync: createActivityBooking } = useCreateActivityBooking();
  const { mutateAsync: updateActivityBooking } = useUpdateActivityBooking();
  const { data: activitiesData } = useGetAllActivities();

  const activitiesOptions = activitiesData?.map((act) => ({
    label: `${act.name} (Rs.${act?.price})`,
    value: act._id,
  }));

  const { data: bookingData } = useGetAllBooking();

  const guestOptions = bookingData?.map((items) => ({
    label: items?.guest?.name,
    value: items?.guest?._id,
  }));
  const bookingOptions = bookingData?.map((items) => ({
    label: items?.bookingId,
    value: items?._id,
  }));

  const yesterday = new Date(new Date().setDate(new Date().getDate() - 1));

  const ActivityBookingValidationSchema = object({
    name: string().required("Guest Name is required"),
    activity: string().required("Activity Name is required"),
    mobile: string().required("Mobile number is required"),
    bookingDate: date()
      .min(yesterday, "date cannot be before today")
      .required("Date is required"),
    booking: string().required("Booking is required"),
  });

  const formik = useFormik({
    initialValues: {
      name: editData?.booking?.guest?._id ?? "",
      activity: editData?.activity?._id ?? "",
      mobile: editData?.booking?.guest?.phoneNumber ?? "",
      bookingDate: editData?.bookingDate ?? "",
      booking: editData?.booking?._id ?? "",
    },
    enableReinitialize: true,
    validationSchema: ActivityBookingValidationSchema,
    onSubmit: async (values) => {
      const toSend = {
        ...values,
        paymentStatus: `${switchStatus ? "success" : "pending"}`,
        price: `${
          activitiesData?.find((i) => i._id === values.activity)?.price
        }`,
        totalPaid: `${
          switchStatus
            ? activitiesData?.find((i) => i._id === values.activity)?.price
            : "0"
        }`,
      };

      try {
        if (editData?._id) {
          await updateActivityBooking({
            body: toSend as any,
            id: editData?._id as string,
          });
        } else {
          createActivityBooking(toSend as any);
        }
        onClose?.();
      } catch (error) {}
    },
  });

  const activityFormData = [
    {
      required: true,
      name: "name",
      label: "Guest Name",
      type: "dropdown",
      options: guestOptions,
      placeholder: "Select Guest Name",
      onChange: (e: any) => {
        const selectedData = bookingData?.find(
          (item) => item?.guest?._id === e.target.value
        );
        formik.setFieldValue(
          "mobile",
          e.target.value ? selectedData?.guest?.phoneNumber : ""
        );
        formik.setFieldValue(
          "booking",
          e.target.value ? selectedData?._id : ""
        );
        formik.setFieldValue("name", e.target.value);
      },
    },
    {
      required: true,
      name: "mobile",
      label: "Mobile",
      type: "text",
      placeholder: "Enter mobile number",
      value: formik.values.mobile,
      readonly: true,
    },
    {
      required: true,
      name: "activity",
      label: "Activity Name",
      type: "dropdown",
      placeholder: "Select Activity",
      options: activitiesOptions,
      onChange: (e: any) => {
        formik.setFieldValue("activity", e.target.value);
      },
    },
    {
      required: true,
      name: "bookingDate",
      label: "Booking Date",
      type: "date",
      placeholder: "Enter Booking date",
    },
    {
      required: true,
      name: "booking",
      label: "Booking",
      type: "dropdown",
      options: bookingOptions,
      placeholder: "Select Booking",
      onChange: (e: any) => {
        const selectedBooking = bookingData?.find(
          (item) => item?._id === e.target.value
        );
        formik.setFieldValue(
          "name",
          e.target.value ? selectedBooking?.guest?._id : ""
        );
        formik.setFieldValue(
          "mobile",
          e.target.value ? selectedBooking?.guest?.phoneNumber : ""
        );
        formik.setFieldValue("booking", e.target.value);
      },
    },
  ];

  const { handleSubmit } = formik;

  const handlePaidSwitch = () => {
    setSwitchStatus(!switchStatus);
  };

  return (
    <HeadingPopup
      onClose={() => onClose?.()}
      className=""
      heading={`${!editData ? "Add" : "Update"} Booking`}
    >
      <FormikProvider value={formik}>
        <Form onSubmit={handleSubmit}>
          <div className="grid md:grid-cols-3 gap-4">
            {activityFormData.map((item, index) => (
              <FormField
                required={item.required}
                key={index}
                label={item.label}
                name={item.name}
                type={item.type}
                placeholder={item.placeholder}
                formik={formik}
                options={item.options}
                readonly={item.readonly}
                onChange={item.onChange}
              />
            ))}
          </div>
          <div className="flex gap-4 items-center my-4">
            <div className="cursor-pointer" onClick={handlePaidSwitch}>
              <div
                className={`w-11 h-5 flex items-center bg-${
                  switchStatus ? "green" : "gray-400"
                } rounded-full p-1 cursor-pointer transition-all duration-300`}
              >
                <div
                  className={`bg-white w-4 h-4 rounded-full shadow-md transform ${
                    switchStatus ? "translate-x-5" : "translate-x-0"
                  } transition-transform duration-300`}
                />
              </div>
            </div>
            <div className="flex">
              <div
                className={`${
                  switchStatus
                    ? "py-1 px-3 w-fit border rounded-md border-green text-green"
                    : "py-1 px-3 w-fit border rounded-md border-red text-red"
                }`}
              >
                {switchStatus ? "Paid" : "Pending"}
              </div>
            </div>
          </div>
          <button
            type="submit"
            className="bg-[#163381] text-white font-bold py-2 px-4 mt-4 flex place-self-end rounded"
          >
            Submit
          </button>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};
