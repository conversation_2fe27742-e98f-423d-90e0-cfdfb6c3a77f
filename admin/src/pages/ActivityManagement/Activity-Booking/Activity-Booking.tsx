import { useEffect, useState } from "react";
import Header from "../../../components/Header";
import { AddActivityBooking } from "./components/AddActivityBooking";
import { TabData } from "../../../components/TabData";
import { useGetAllActivities } from "../../../server-action/API/activity";
import MasterTable from "../../../layouts/Table/MasterTable";
import {
  useDeleteActivityBooking,
  useGetAllActivitiesBooking,
} from "../../../server-action/API/activityBooking";
import { TableAction } from "../../../layouts/Table/TableAction";
import { toast } from "react-toastify";
import ActivityBookingFilter from "./components/ActivityBookingFilter";
import { Status } from "../../../components/Status";

interface FilterOption {
  label: string;
  value: string;
}

export const ActivityBooking = () => {
  // Initialize state variables
  const [openModal, setOpenModal] = useState(false);
  const [selectedData, setSelectedData] = useState();
  const [selectedTabData, setSelectedTabData] = useState<string>("");
  const [initialTabSet, setInitialTabSet] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [date, setDate] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("all");

  // Get activity data
  const { data: activityData, isSuccess } = useGetAllActivities();
  const {
    data: activityBookingData,
    isSuccess: activityBookingDataSuccess,
    isLoading,
  } = useGetAllActivitiesBooking();

  console.log(activityBookingData, "actdata");

  const { mutate: deleteActivityBooking } = useDeleteActivityBooking();

  // Create tab options from activity data
  const tabOptions = isSuccess
    ? activityData?.map((act) => ({
        title: act.name,
        value: act._id,
      }))
    : [];

  // Status options for filter
  const statusOptions: FilterOption[] = [
    { label: "All", value: "all" },
    { label: "Success", value: "success" },
    { label: "Pending", value: "pending" },
  ];

  // Log the tab options for debugging
  useEffect(() => {
    if (tabOptions && tabOptions.length > 0) {
      console.log("Tab options:", tabOptions);

      // If we already have a selected tab that exists in the options, keep it
      if (selectedTabData && initialTabSet) {
        const tabExists = tabOptions.some(
          (tab) => tab.value === selectedTabData
        );
        if (tabExists) {
          console.log("Keeping existing selected tab:", selectedTabData);
        } else {
          console.log("Selected tab no longer exists, selecting first tab");
          setSelectedTabData(tabOptions[0].value);
        }
      }
    }
  }, [tabOptions, selectedTabData, initialTabSet]);

  // Set initial tab selection
  useEffect(() => {
    if (
      !initialTabSet &&
      isSuccess &&
      tabOptions &&
      tabOptions.length > 0 &&
      tabOptions[0]?.value
    ) {
      console.log(
        "Setting initial tab to:",
        tabOptions[0].title,
        "with value:",
        tabOptions[0].value
      );
      setSelectedTabData(tabOptions[0].value);
      setInitialTabSet(true);
    }
  }, [isSuccess, tabOptions, initialTabSet]);

  // Log when selectedTabData changes
  useEffect(() => {
    console.log("selectedTabData changed to:", selectedTabData);
  }, [selectedTabData]);

  // Table data
  const tableData = {
    column: [
      {
        title: "Booking ID",
        key: "bookingId",
      },
      {
        title: "Guest Name",
        key: "name",
      },
      {
        title: "Contact",
        key: "contact",
      },
      {
        title: "Activity name",
        key: "activityName",
      },
      {
        title: "Price(NPR)",
        key: "price",
      },
      {
        title: "Booking Date",
        key: "date",
      },
      {
        title: "Payment Status",
        key: "payment",
      },
      {
        title: "Actions",
        key: "action",
      },
    ],
    rows:
      activityBookingDataSuccess &&
      activityBookingData &&
      Array.isArray(activityBookingData)
        ? activityBookingData
            .filter((i) => i?.activity && i.activity?._id)
            .filter((i) =>
              selectedTabData ? i.activity._id === selectedTabData : true
            )
            .filter((i) => {
              const matchesSearch = searchTerm
                ? i?.activity?.name
                    ?.toLowerCase()
                    .includes(searchTerm.toLowerCase())
                : true;
              const matchesPaymentStatus =
                selectedStatus !== "all"
                  ? i?.paymentStatus === selectedStatus
                  : true;
              const matchesDate = date
                ? i?.bookingDate?.startsWith(date)
                : true;
              return matchesSearch && matchesPaymentStatus && matchesDate;
            })
            .map((row: any, index: any) => ({
              key: index,
              bookingId: row?.booking?.bookingId || "N/A",
              name: row?.booking?.guest || "N/A",
              contact: row?.booking?.guest?.phoneNumber || "N/A",
              activityName: row?.activity?.name || "N/A",
              price: row?.activity?.price || "N/A",
              date: row?.bookingDate || "N/A",
              payment: <Status status={row?.paymentStatus} />,
              action: (
                <TableAction
                  onEdit={() => {
                    setSelectedData(row);
                    setOpenModal(true);
                  }}
                  onDelete={() => {
                    if (row?._id) {
                      deleteActivityBooking(row._id);
                    } else {
                      console.error(
                        "Cannot delete activity booking: Missing ID"
                      );
                      toast.error("Error: Cannot delete this booking");
                    }
                  }}
                />
              ),
            }))
        : [],
  };

  return (
    <div className="flex flex-col gap-2">
      <Header
        title="Book Activity"
        onAddClick={() => {
          setOpenModal(true);
        }}
      />
      <div className="p-3 bg-white border rounded-md">
        <ActivityBookingFilter
          onSearch={setSearchTerm}
          onStatusChange={setSelectedStatus}
          onDateChange={setDate}
          statusOptions={statusOptions}
        />
      </div>
      <TabData
        selectedTabData={selectedTabData}
        setSelectedTabData={(newValue) => {
          console.log(
            "Setting selectedTabData from TabData component:",
            newValue
          );
          setSelectedTabData(newValue);
          if (!initialTabSet) {
            setInitialTabSet(true);
          }
        }}
        tabData={tabOptions ?? []}
      />
      {openModal && (
        <AddActivityBooking
          editData={selectedData}
          onClose={() => setOpenModal(false)}
        />
      )}

      <MasterTable
        columns={tableData.column ?? []}
        rows={tableData.rows ?? []}
        loading={isLoading}
      />
    </div>
  );
};
