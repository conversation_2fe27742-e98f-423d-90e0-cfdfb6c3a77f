import { Form, FormikProvider, useFormik } from "formik";
import HeadingPopup from "../../../GuestManagement/components/HeadingPopup";
import { GlobalForm } from "../../../../components/GlobalForm/GlobalFormComponent";
import {
  useCreateActivity,
  useUpdateActivity,
} from "../../../../server-action/API/activity";
import { array, object, string, number } from "yup";

interface propTypes {
  editData?: any;
  onClose?: () => void;
}

const dayOptions = [
  { label: "Sunday", value: "Sunday" },
  { label: "Monday", value: "Monday" },
  { label: "Tuesday", value: "Tuesday" },
  { label: "Wednesday", value: "Wednesday" },
  { label: "Thursday", value: "Thursday" },
  { label: "Friday", value: "Friday" },
  { label: "Saturday", value: "Saturday" },
];

const activityFormData = [
  {
    required: true,
    field: "name",
    label: "Activity Name",
    type: "text",
    placeholder: "Enter activity name",
  },
  {
    required: true,
    field: "days",
    label: "Day",
    type: "multiple",
    options: dayOptions,
  },
  {
    required: true,
    field: "startTime",
    label: "Start Time",
    type: "time",
    placeholder: "Enter activity name",
  },
  {
    required: true,
    field: "endTime",
    label: "End Time",
    type: "time",
    placeholder: "Enter end time",
  },
];

const statusOptions = [
  {
    label: "Active",
    value: "true",
  },
  {
    label: "Inactive",
    value: "false",
  },
];

const restOfActivityFormData = [
  {
    required: true,
    field: "maxCapacity",
    label: "Max No of People",
    type: "number",
    placeholder: "Enter max capacity",
    min: "0",
  },
  {
    required: true,
    field: "price",
    label: "Price Per Person",
    type: "number",
    placeholder: "Enter price",
    min: "0",
  },
  {
    required: true,
    field: "isActive",
    label: "Status",
    type: "select",
    options: statusOptions,
    placeholder: "Status",
  },

  {
    field: "remarks",
    label: "Remarks",
    type: "textarea",
    placeholder: "Enter remarks",
  },
];

export const AddActivity = ({ editData, onClose }: propTypes) => {
  const { mutateAsync: createActivity } = useCreateActivity();
  const { mutateAsync: updateActivity } = useUpdateActivity();
  const ActivityValidationSchema = object({
    name: string().required("Activity name is required"),

    days: array()
      .of(string().required("Each day is required"))
      .min(1, "Select at least one day")
      .required("Days are required"),

    startTime: string().required("Start time is required"),

    endTime: string()
      .required("End time is required")
      .test(
        "is-after-start",
        "End time must be after start time",
        function (value) {
          const { startTime } = this.parent;
          if (!startTime || !value) return true;
          return (
            new Date(`1970-01-01T${value}`) >
            new Date(`1970-01-01T${startTime}`)
          );
        }
      ),

    maxCapacity: number()
      .typeError("Max Capacity must be a number")
      .required("Max Capacity is required")
      .min(1, "Max Capacity must be at least 1"),

    price: number()
      .typeError("Price must be a number")
      .required("Price is required")
      .min(0, "Price cannot be negative"),

    isActive: string().required("Status is required"),
  });
  const formik = useFormik({
    initialValues: {
      name: editData?.name ?? "",
      days: editData?.days ?? [],
      isActive: editData?.isActive ?? "",
      startTime: editData?.time.slice(0, 5) ?? "",
      endTime: editData?.time.slice(8, 13) ?? "",
      maxCapacity: editData?.maxCapacity ?? "",
      price: editData?.price ?? "",
      status: editData?.status ?? "",
      remarks: editData?.remarks ?? "",
    },
    enableReinitialize: true,
    validationSchema: ActivityValidationSchema,
    onSubmit: async (values) => {
      console.log(values, "values");

      const toSend = {
        ...values,
        time: `${values.startTime} - ${values.endTime}`,
        status: values.status === "true" ? true : false,
        availability: values.isActive,
      };
      console.log(toSend, "toSend");

      try {
        console.log(values);
        if (editData?._id) {
          await updateActivity({
            body: toSend as any,
            id: editData?._id as string,
          });
        } else {
          createActivity(toSend as any);
        }
        onClose?.();
      } catch (error) {}
    },
  });

  const { handleSubmit, getFieldProps } = formik;
  return (
    <HeadingPopup
      onClose={() => onClose?.()}
      className="w-[750px] "
      heading={`${!editData ? "Add" : "Update"} Activity`}
    >
      <FormikProvider value={formik}>
        <Form onSubmit={handleSubmit}>
          <div className="grid grid-cols-4 gap-2">
            <GlobalForm
              formik={formik}
              formDatails={activityFormData}
              getFieldProps={getFieldProps}
            />
          </div>

          <div className="grid grid-cols-3 gap-3 mt-3">
            <GlobalForm
              formik={formik}
              formDatails={restOfActivityFormData}
              getFieldProps={getFieldProps}
            />
          </div>
          <button
            className="bg-[#163381] text-white font-bold py-2 px-4 mt-4 flex place-self-end rounded"
            type="submit"
          >
            Submit
          </button>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};
