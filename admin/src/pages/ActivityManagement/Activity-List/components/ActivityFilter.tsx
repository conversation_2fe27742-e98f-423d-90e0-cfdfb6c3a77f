import type React from "react";
import { useState } from "react";
import CustomSelect from "../../../../components/GlobalForm/CustomSelect";

interface FilterOption {
  label: string;
  value: string;
}

interface ActivityFilterProps {
  onSearch: (value: string) => void;
  onFilterChange: (filters: {
    activity: string;
    price: string;
    availability: string;
  }) => void;
  activityOptions: FilterOption[];
  priceOptions: FilterOption[];
  availabilityOptions: FilterOption[];
}

export default function ActivityFilter({
  onSearch,
  onFilterChange,
  activityOptions,
  priceOptions,
  availabilityOptions,
}: ActivityFilterProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [activity, setActivity] = useState("");
  const [price, setPrice] = useState("");
  const [availability, setAvailability] = useState("");

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    onSearch(value);
  };

  const handleApplyFilters = () => {
    onFilterChange({ activity, price, availability });
  };

  const handleResetFilters = () => {
    setActivity("");
    setPrice("");
    setAvailability("");
    onFilterChange({ activity: "", price: "", availability: "" });
  };

  // Combine default "All" options with provided options
  const allActivityOptions = [
    { label: "All Activities", value: "" },
    ...activityOptions,
  ];
  const allPriceOptions = [{ label: "All Prices", value: "" }, ...priceOptions];
  const allAvailabilityOptions = [
    { label: "All Status", value: "" },
    ...availabilityOptions,
  ];

  return (
    <div className=" flex-col md:flex-row flex items-center gap-4 bg-white rounded-md">
      {/* Filter Row */}
      <div className="flex flex-col md:flex-row gap-4 items-end w-full">
        {/* Activity Dropdown */}
        <div className="w-full md:w-48">
          <CustomSelect
            label="Activity"
            value={activity}
            options={allActivityOptions}
            onChange={(value) => setActivity(value)}
            className="w-full"
            isForm={false}
          />
        </div>

        {/* Price Dropdown */}
        <div className="w-full md:w-48">
          <CustomSelect
            label="Price"
            value={price}
            options={allPriceOptions}
            onChange={(value) => setPrice(value)}
            className="w-full"
            isForm={false}
          />
        </div>

        {/* Availability Dropdown */}
        <div className="w-full md:w-48">
          <CustomSelect
            label="Availability"
            value={availability}
            options={allAvailabilityOptions}
            onChange={(value) => setAvailability(value)}
            className="w-full"
            isForm={false}
          />
        </div>

        {/* Buttons */}
        <div className="flex gap-2">
          <button
            onClick={handleResetFilters}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100 focus:outline-none"
          >
            Reset
          </button>
          <button
            onClick={handleApplyFilters}
            className="px-4 py-2 bg-[#2A3A6D] text-white rounded-md focus:outline-none"
          >
            Apply Filters
          </button>
        </div>
      </div>

      {/* Search Bar */}
      <div className="flex-1 flex justify-end">
        <div className="relative w-full md:w-64">
          <input
            type="text"
            placeholder="Search..."
            value={searchTerm}
            onChange={handleSearchChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 pl-10"
          />
          <svg
            className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
      </div>
    </div>
  );
}
