import { useCallback, useMemo, useState } from "react";
import { DeleteDialog } from "../../../components";
import MasterTable from "../../../layouts/Table/MasterTable";
// import { LaundryCategoryData } from "./LaundryCategoryData";
import { TableAction } from "../../../layouts/Table/TableAction";
import { Card, CardContent } from "../../../components/Card";
import { CustomTabs } from "../../../components/CustomTab";
import { Form, FormikProvider, useFormik } from "formik";
import { FormField } from "../../BookingManagement/components/ReservationCustomForm";
import ClothesName from "./components/ClothesName";
import PaginationHeader from "./components/PaginationHeader";
import PaginationFooter from "./components/PaginationFooter";
import Header from "../../../components/Header";
import { useGetAllLaundryService } from "../../../server-action/API/LaundryManagement/laundryService";
import {
  useDeleteLaundryCategory,
  useGetAllLaundryCategory,
} from "../../../server-action/API/LaundryManagement/laundryCategory";
import EditCategoryPrices from "./components/EditCategoryPrices";
interface FormState {
  state: boolean;
  edit: boolean;
  editData?: any;
}
const LaundryCategory = () => {
  const [popup, setPopup] = useState("");
  const closePopup = useCallback(() => setPopup(""), []);
  const [tab, setTab] = useState("Clothes List");
  const onTabChange = useCallback((status: string) => setTab(status), []);
  const [formState, setFormState] = useState<FormState>({
    state: false,
    edit: true,
    editData: null,
  });
  const [priceState, setPriceState] = useState<FormState>({
    state: false,
    edit: true,
    editData: null,
  });

  const { data: LaundryCategoryData, isSuccess: laundryCategorySuccess } =
    useGetAllLaundryCategory();
  const { mutate: deleteLaundryCategory } = useDeleteLaundryCategory();

  // const [dataLen, setDataLen] = useState(0);
  const { data: LaundryServicesData } = useGetAllLaundryService();
  // useEffect(() => {
  //   setDataLen(isSuccess ? LaundryServicesData.length : 0);
  // }, [LaundryServicesData]);

  const [pageIndex, setPageIndex] = useState<number>(0);
  const pageSize: number = 5;
  const start = pageIndex * pageSize;
  const end = Math.min(
    (pageIndex + 1) * pageSize,
    laundryCategorySuccess ? LaundryCategoryData.length : 0
  );
  const CurrentPageData = laundryCategorySuccess
    ? LaundryCategoryData.slice(start, end)
    : [];
  const tabOptions = useMemo(() => ["Clothes List", "Regular", "Express"], []);
  const LaundryCategory = {
    columns: [
      { key: "sn", title: "SN" },
      { key: "clothes", title: "Clothes" },
      { key: "action", title: "Action" },
    ],
    rows: laundryCategorySuccess
      ? LaundryCategoryData.map((row, index) => ({
          key: index,
          sn: index + 1,
          clothes: row.name,
          action: (
            <TableAction
              onEdit={() => {
                setFormState({
                  edit: true,
                  state: true,
                  editData: row,
                });
              }}
              onDelete={() => {
                // setPopup("delete");
                deleteLaundryCategory(row._id ?? "");
              }}
            />
          ),
        }))
      : [],
  };
  const formik = useFormik({
    initialValues: {
      clothesname: "",
      wash: "",
      drycleaning: "",
      washandiron: "",
    },
    onSubmit: (values) => {
      console.log(values);
    },
  });

  return (
    <div>
      <Header
        title="Clothes Name"
        onAddClick={() => {
          setPopup("clothesname");
        }}
      />
      <Card className="bg-white mb-2">
        <CardContent className="flex items-center justify-between">
          <CustomTabs
            tabs={tabOptions}
            defaultTab={tab}
            onTabChange={onTabChange}
          />
        </CardContent>
      </Card>
      {popup === "clothesname" && <ClothesName onClose={closePopup} />}
      {popup === "editPrices" && (
        <EditCategoryPrices
          edit={priceState.edit}
          editData={priceState.editData}
          onClose={closePopup}
          tab={tab}
        />
      )}
      {popup === "delete" && (
        <DeleteDialog confirmAction={true} onClose={closePopup} />
      )}
      {formState.state && (
        <ClothesName
          onClose={() =>
            setFormState({ state: false, edit: false, editData: null })
          }
          edit={formState.edit}
          editData={formState.editData}
        />
      )}
      <Card className="bg-white border-none">
        {tab === "Clothes List" ? (
          <MasterTable
            columns={LaundryCategory?.columns}
            rows={LaundryCategory.rows}
            loading={false}
          />
        ) : CurrentPageData.length > 0 ? (
          <div>
            <PaginationHeader
              pageSize={pageSize}
              pageIndex={pageIndex}
              setPageIndex={setPageIndex}
              totalItems={
                laundryCategorySuccess ? LaundryCategoryData.length : 0
              }
            />
            <div className="max-w-[100rem] relative flex">
              <FormikProvider value={formik}>
                <div className="w-fit bg-white">
                  {CurrentPageData.map((item, index) => {
                    return (
                      <Form
                        key={index}
                        onSubmit={formik.handleSubmit}
                        className="space-y-3 mt-5 px-5"
                      >
                        <div className="flex gap-4">
                          <div className="flex flex-col">
                            <FormField
                              label="Clothes Name"
                              type="text"
                              name={item.name ?? ""}
                              formik={formik}
                              placeholder="T-Shirt"
                              value={item.name}
                            />
                          </div>
                        </div>
                      </Form>
                    );
                  })}
                </div>
                <div className="overflow-y-scroll max-w-[80rem] border-x ">
                  {CurrentPageData.map((item, index) => {
                    return (
                      <Form
                        key={index}
                        onSubmit={formik.handleSubmit}
                        className="space-y-3 mt-5 px-5"
                      >
                        <div className="flex gap-4">
                          {tab === "Regular"
                            ? LaundryServicesData?.filter((i) =>
                                i.type?.includes("regular")
                              ).map((i, ind) => {
                                const matchedService = item?.services.find(
                                  (s: any) =>
                                    s.service?._id === i._id &&
                                    s.type === "regular"
                                );
                                console.log(matchedService, "matched servive");

                                return (
                                  <div key={ind} className="max-w-48">
                                    <FormField
                                      label={i.name}
                                      type="text"
                                      name={`${item.name}${i.name}`}
                                      formik={formik}
                                      placeholder="20"
                                      value={matchedService?.price ?? 0}
                                      disabled
                                    />
                                  </div>
                                );
                              })
                            : LaundryServicesData?.filter((i) =>
                                i.type?.includes("express")
                              ).map((i, ind) => {
                                console.log(item.services, "item example");

                                const matchedService = item?.services.find(
                                  (s: any) =>
                                    s.service?._id === i._id &&
                                    s.type === "express"
                                );

                                return (
                                  <div key={ind} className="max-w-48">
                                    <FormField
                                      label={i.name}
                                      type="text"
                                      name={`${item.name}${i.name}`}
                                      formik={formik}
                                      placeholder="20"
                                      value={matchedService?.price ?? 0}
                                      disabled
                                    />
                                  </div>
                                );
                              })}
                        </div>
                      </Form>
                    );
                  })}
                </div>
                <div className="flex flex-col items-center gap-5 mt-8">
                  {CurrentPageData.map((item, index) => {
                    return (
                      <div key={index} className="py-5 px-3">
                        <TableAction
                          onEdit={() => {
                            setPopup("editPrices");
                            setPriceState({
                              edit: true,
                              state: true,
                              editData: item,
                            });
                          }}
                          onDelete={() => {
                            deleteLaundryCategory(item._id ?? "");
                          }}
                        />
                      </div>
                    );
                  })}
                </div>
              </FormikProvider>
            </div>
            <PaginationFooter
              pageSize={pageSize}
              pageIndex={pageIndex}
              setPageIndex={setPageIndex}
              totalItems={
                laundryCategorySuccess ? LaundryCategoryData.length : 0
              }
            />
          </div>
        ) : (
          <div className="flex justify-center py-10">No Data Available</div>
        )}
      </Card>
    </div>
  );
};

export default LaundryCategory;
