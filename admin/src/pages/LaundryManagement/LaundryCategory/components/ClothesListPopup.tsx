import { Form, FormikProvider, useFormik } from "formik";
import HeadingPopup from "../../../GuestManagement/components/HeadingPopup";
import { FormField } from "../../../BookingManagement/components/ReservationCustomForm";

const ClothesListPopup = ({ onClose }: { onClose?: () => void }) => {
  const handleClose = () => {
    onClose?.();
  };
  const formik = useFormik({
    initialValues: {
      name: "",
      price: "",
    },
    onSubmit: (values) => {
      console.log(values);
    },
  });
  return (
    <HeadingPopup
      onClose={handleClose}
      className="w-full max-w-screen-sm"
      heading=" Laundry Category"
    >
      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit} className="space-y-3">
          <div className="grid grid-cols-2 gap-x-4 gap-y-3">
            <FormField
              label="Name"
              type="text"
              name="name"
              formik={formik}
              placeholder="Clothes Name"
            />
            <FormField
              label="Price"
              type="text"
              name="price"
              formik={formik}
              placeholder="100"
            />
          </div>
          <div className="flex items-center justify-end mt-4">
            <button
              type="submit"
              className="px-4 py-2 bg-[#2A3A6D] text-white rounded-md"
            >
              Submit
            </button>
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default ClothesListPopup;
