import { Form, FormikProvider, useFormik } from "formik";
import HeadingPopup from "../../../GuestManagement/components/HeadingPopup";
import { FormField } from "../../../BookingManagement/components/ReservationCustomForm";
import { get } from "lodash";
import * as Yup from "yup";
export const LaundryClothValidationSchema = Yup.object().shape({
  name: Yup.string()
    .required("Name is required ")
    .min(3, "Name Must me at least 3 Charater Long"),
});
import {
  useCreateLaundryCategory,
  useUpdateLaundryCategory,
} from "../../../../server-action/API/LaundryManagement/laundryCategory";

const ClothesName = ({
  onClose,
  edit,
  editData,
}: {
  onClose?: () => void;
  edit?: boolean;
  editData?: any;
}) => {
  const { mutate: createLaundryCategory } = useCreateLaundryCategory();
  const { mutate: updateLaundryCategory } = useUpdateLaundryCategory();
  const handleClose = () => {
    onClose?.();
  };
  const formik = useFormik({
    validationSchema: LaundryClothValidationSchema,
    initialValues: {
      name: edit ? get(editData, "name", "") : "",
    },
    onSubmit: async (values) => {
      if (edit) await updateLaundryCategory({ id: editData._id, body: values });
      else await createLaundryCategory(values);
      handleClose();
    },
  });
  return (
    <HeadingPopup
      onClose={handleClose}
      className="w-full max-w-screen-sm"
      heading="Clothes Name"
    >
      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit} className="space-y-3">
          <div className="grid grid-cols-1 gap-x-4 gap-y-3">
            <FormField
              label="Name"
              type="text"
              name="name"
              formik={formik}
              placeholder="Clothes Name"
            />
          </div>
          <div className="flex items-center justify-end mt-4">
            <button
              type="submit"
              className="px-4 py-2 bg-[#2A3A6D] text-white rounded-md"
            >
              Submit
            </button>
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default ClothesName;
