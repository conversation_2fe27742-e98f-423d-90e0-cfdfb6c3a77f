import { Form, FormikProvider, useFormik } from "formik";
import HeadingPopup from "../../../GuestManagement/components/HeadingPopup";
import { FormField } from "../../../BookingManagement/components/ReservationCustomForm";
import { useGetAllLaundryService } from "../../../../server-action/API/LaundryManagement/laundryService";
import MasterTable from "../../../../layouts/Table/MasterTable";
import { useState } from "react";
import { TableAction } from "../../../../layouts/Table/TableAction";
import { number, object, string } from "yup";
import { useUpdateLaundryCategory } from "../../../../server-action/API/LaundryManagement/laundryCategory";

const EditCategoryPrices = ({
  onClose,
  editData,
  edit,
  tab,
}: {
  onClose?: () => void;
  editData?: any;
  edit?: boolean;
  tab?: string;
}) => {
  //   const [serviceList, setServiceList] = useState<Array<any>>([]);4
  const [formValues, setFormValues] = useState({
    service: "",
    price: "",
    type: "",
  });
  const { data: laundryServiceData } = useGetAllLaundryService();
  console.log(laundryServiceData, "laundryServiceData");

  const [error, setError] = useState<boolean>(false);
  const { mutate: updateLaundryCategory } = useUpdateLaundryCategory();
  const categoryOptions = laundryServiceData
    ?.filter((i: any) => i.type.includes(tab?.toLowerCase() || ""))
    .map((item) => ({
      label: item?.name ?? "",
      value: item._id as string,
    }));
  const handleClose = () => {
    onClose?.();
  };
  const ValidationSchema = object({
    service: string().required("Service is required"),
    price: number().required("Price is required"),
  });

  const formik = useFormik({
    initialValues: {
      name: editData?.name || "",
      services:
        edit && editData?.services?.length > 0
          ? editData?.services?.map((i: any) => {
              return {
                service: i.service?._id,
                price: i.price,
                type: i.type,
              };
            })
          : [
              {
                service: "",
                price: "",
                type: tab?.toLowerCase() || "",
              },
            ],
    },
    validationSchema: ValidationSchema,
    enableReinitialize: true,
    onSubmit: (values) => {
      console.log(values, "values");

      const toSend = values?.services.filter((i: any) => i.service !== "");
      console.log(toSend, "toSend Values");
      updateLaundryCategory({
        id: editData._id,
        body: {
          services: toSend,
        },
      });
      handleClose();
    },
  });
  console.log(formik.values.services, "Formik values");

  const tableData = {
    column: [
      {
        key: "clothesName",
        title: "Clothes Name",
      },
      {
        key: "category",
        title: "Category",
      },
      {
        key: "type",
        title: "Type",
      },

      {
        key: "price",
        title: "Price",
      },
      {
        key: "action",
        title: "Action",
      },
    ],
    row: formik.values.services
      .filter((item: any) => item.service !== "")
      ?.map((item: any, index: number) => ({
        key: index,
        clothesName: editData?.name,
        category: laundryServiceData?.find((i) => i._id === item.service)?.name,
        price: item.price,
        type: item.type,
        action: (
          <TableAction
            onDelete={() => {
              formik.setFieldValue(
                "services",
                formik.values.services.filter(
                  (_: any, i: number) => i !== index
                )
              );
            }}
          />
        ),
      })),
  };
  console.log(formik.values.services, "Formik values services type");

  const handleClick = async () => {
    const singleEntrySchema = object({
      service: string().required("Service is required"),
      price: number()
        .required("Price is required")
        .typeError("Price must be a number"),
    });

    try {
      await singleEntrySchema.validate(formValues, { abortEarly: false });

      // Check for duplicates BEFORE updating formik state
      const alreadyExists = formik.values.services.some(
        (item: any) =>
          item.service === formValues.service &&
          item.type === (tab?.toLowerCase() || "")
      );

      if (alreadyExists) {
        setError(true);
        return;
      } else {
        setError(false);
      }

      // If no duplicates, then add to Formik state
      formik.setFieldValue("services", [
        ...formik.values.services,
        {
          ...formValues,
          type: tab?.toLowerCase() || "",
        },
      ]);

      setFormValues({ service: "", price: "", type: "" }); // Reset form state
    } catch (err: any) {
      if (err.inner) {
        err.inner.forEach((error: any) => {
          formik.setFieldError(error.path, error.message);
        });
      }
    }
  };

  return (
    <HeadingPopup
      onClose={handleClose}
      className="w-full max-w-screen-sm"
      heading="Edit Category Price"
    >
      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit} className="space-y-3">
          <div className="grid grid-cols-2 gap-x-4 gap-y-3">
            <FormField
              label="Name"
              type="text"
              name="name"
              formik={formik}
              placeholder="Clothes Name"
              readonly
            />
            <FormField
              label="Category"
              type="dropdown"
              name="service"
              formik={formik}
              value={formValues.service}
              onChange={(e) =>
                setFormValues((prev) => ({
                  ...prev,
                  service: e.target.value,
                }))
              }
              placeholder="Category"
              options={categoryOptions}
            />
            <FormField
              label="Price"
              type="number"
              min="0"
              name="price"
              formik={formik}
              value={formValues.price}
              onChange={(e) =>
                setFormValues((prev) => ({ ...prev, price: e.target.value }))
              }
              placeholder="Price"
            />
            <FormField
              label="Type"
              type="text"
              name="type"
              formik={formik}
              placeholder="Type"
              value={tab?.toLowerCase() || ""}
              readonly
            />
          </div>
          <div className={`text-red ${error ? "block" : "hidden"}`}>
            Category of type {tab} already exist. Remove it form the table first
          </div>
          <div className="flex items-center justify-end mt-4">
            <button
              type="button"
              className="px-4 py-2 bg-[#2A3A6D] text-white rounded-md"
              onClick={handleClick}
            >
              Add
            </button>
          </div>
          <div className="my-4">
            <MasterTable
              rows={tableData?.row}
              columns={tableData?.column}
              loading={false}
            />
          </div>
          <div className="flex items-center justify-end mt-4">
            <button
              type="submit"
              className="px-4 py-2 bg-[#2A3A6D] text-white rounded-md"
            >
              Submit
            </button>
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default EditCategoryPrices;
