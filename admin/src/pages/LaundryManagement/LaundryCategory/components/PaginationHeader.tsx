const PaginationHeader = ({
  totalItems = 1,
  pageIndex,
  setPageIndex,
  pageSize,
}: {
  totalItems?: number;
  pageIndex: number;
  setPageIndex: React.Dispatch<React.SetStateAction<number>>;
  pageSize: number;
}) => {
  const totalPages = Math.ceil(totalItems / pageSize);

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setPageIndex(newPage);
  };
  return (
    <div className="flex items-center justify-between px-4 py-2 bg-white border-b">
      <div className="flex items-center">
        <span className="text-sm font-medium text-gray-600">Page</span>
        <div className="relative mx-2">
          <select
            className="px-3 py-1 pr-8 text-sm bg-white border border-gray-300 rounded appearance-none focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            value={pageIndex + 1}
            onChange={(e) => handlePageChange(Number(e.target.value) - 1)}
          >
            {Array.from({ length: Math.max(1, totalPages) }, (_, i) => (
              <option key={i} value={i + 1}>
                {i + 1}
              </option>
            ))}
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 pointer-events-none">
            <svg
              className="w-4 h-4 fill-current"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
            >
              <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
            </svg>
          </div>
        </div>
        <span className="text-sm text-gray-600">
          of {Math.max(1, totalPages)}
        </span>
      </div>
    </div>
  );
};

export default PaginationHeader;
