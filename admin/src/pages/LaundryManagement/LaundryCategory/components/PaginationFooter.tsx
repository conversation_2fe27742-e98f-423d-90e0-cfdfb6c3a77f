const PaginationFooter = ({
  totalItems = 1,
  pageIndex,
  setPageIndex,
  pageSize,
}: {
  pageSize: number;
  totalItems?: number;
  pageIndex: number;
  setPageIndex: React.Dispatch<React.SetStateAction<number>>;
}) => {
  const totalPages = Math.ceil(totalItems / pageSize);

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setPageIndex(newPage);
  };
  return (
    <div className="flex items-center justify-between p-4">
      <div className="flex items-center">
        <span className="text-sm text-gray-700">
          Showing {pageIndex + 1}of {totalPages}
        </span>
      </div>
      <div className="flex items-center space-x-1">
        <button
          onClick={() => handlePageChange(Math.max(pageIndex - 1, 0))}
          disabled={pageIndex === 0}
          className={`relative inline-flex items-center px-2 py-2 rounded-full text-sm font-medium ${
            pageIndex === 0
              ? "text-gray-300 cursor-not-allowed"
              : "text-gray-500 hover:bg-gray-100"
          }`}
        >
          <span className="sr-only">Previous</span>
          <svg
            className="w-5 h-5"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
              clipRule="evenodd"
            />
          </svg>
        </button>

        {/* Page number buttons */}
        {Array.from(
          { length: Math.min(Math.max(1, totalPages), 5) },
          (_, i) => {
            // Adjust the range to show current page in the middle when possible
            let pageNum: number;
            if (totalPages <= 5) {
              pageNum = i;
            } else if (pageIndex < 2) {
              pageNum = i;
            } else if (pageIndex > totalPages - 3) {
              pageNum = totalPages - 5 + i;
            } else {
              pageNum = pageIndex - 2 + i;
            }

            return (
              <button
                key={pageNum}
                onClick={() => handlePageChange(pageNum)}
                className={`relative inline-flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                  pageIndex === pageNum
                    ? "bg-blue text-white"
                    : "text-gray-500 hover:bg-gray-100"
                }`}
              >
                {pageNum + 1}
              </button>
            );
          }
        )}

        <button
          onClick={() =>
            handlePageChange(Math.min(pageIndex + 1, totalPages - 1))
          }
          disabled={pageIndex === totalPages - 1 || totalPages === 0}
          className={`relative inline-flex items-center px-2 py-2 rounded-full text-sm font-medium ${
            pageIndex === totalPages - 1 || totalPages === 0
              ? "text-gray-300 cursor-not-allowed"
              : "text-gray-500 hover:bg-gray-100"
          }`}
        >
          <span className="sr-only">Next</span>
          <svg
            className="w-5 h-5"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
              clipRule="evenodd"
            />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default PaginationFooter;
