import { useCallback, useState } from "react";
import { Icon } from "@iconify/react/dist/iconify.js";
import MasterTable from "../../../layouts/Table/MasterTable";
import { TableAction } from "../../../layouts/Table/TableAction";
import { DeleteDialog } from "../../../components";
import LaundryServicePopup from "./components/LaundryServicePopup";
import {} from "@iconify/react";
import Header from "../../../components/Header";
import {
  useDeleteLaundryService,
  useGetAllLaundryService,
} from "../../../server-action/API/LaundryManagement/laundryService";
interface FormState {
  state: boolean;
  edit: boolean;
  editData?: any;
}

const LaundryServices = () => {
  const [popup, setPopup] = useState("");
  const closePopup = useCallback(() => setPopup(""), []);
  const [formState, setFormState] = useState<FormState>({
    state: false,
    edit: true,
    editData: null,
  });
  const { data: LaundryServicesData, isSuccess: LaundryServicesSuccess } =
    useGetAllLaundryService();
  const { mutate: deleteLaundryService } = useDeleteLaundryService();
  const LaundryCategory = {
    columns: [
      { key: "sn", title: "SN" },
      { key: "servicename", title: "Service Name" },
      { key: "express", title: "Express" },
      { key: "regular", title: "Regular" },
      { key: "action", title: "Action" },
    ],
    rows: LaundryServicesSuccess
      ? LaundryServicesData.map((row, index) => ({
          key: index,
          sn: index + 1,
          servicename: row.name,
          regular: (
            <div className="flex justify-center">
              {row.type?.includes("regular") ? (
                <Icon
                  icon={"mdi:checkbox-marked"}
                  className="text-2xl text-black"
                />
              ) : (
                <p>-</p>
              )}
            </div>
          ),
          express: (
            <div className="flex justify-center">
              {row.type?.includes("express") ? (
                <Icon
                  icon={"mdi:checkbox-marked"}
                  className="text-2xl text-black"
                />
              ) : (
                <p>-</p>
              )}
            </div>
          ),
          action: (
            <TableAction
              onEdit={() => {
                setFormState({
                  edit: true,
                  state: true,
                  editData: row,
                });
              }}
              onDelete={() => {
                // setPopup("delete");
                deleteLaundryService(row._id ?? "");
              }}
            />
          ),
        }))
      : [],
  };

  return (
    <div>
      <Header
        title="Laundry Service"
        onAddClick={() => {
          setPopup("laundryservice");
        }}
      />
      <MasterTable
        columns={LaundryCategory?.columns}
        rows={LaundryCategory.rows}
        loading={false}
        pagination={{
          currentPage: 1,
          totalPage: 200,
          limit: 5,
          onClick: () => {},
        }}
      />

      {popup === "laundryservice" && (
        <LaundryServicePopup editData={null} onClose={closePopup} />
      )}
      {/* {popup === "edit" && <ClothesListPopup onClose={closePopup} />} */}
      {formState.state && (
        <LaundryServicePopup
          onClose={() =>
            setFormState({ state: false, edit: false, editData: null })
          }
          edit={formState.edit}
          editData={formState.editData}
        />
      )}
      {popup === "delete" && (
        <DeleteDialog confirmAction={true} onClose={closePopup} />
      )}
    </div>
  );
};

export default LaundryServices;
