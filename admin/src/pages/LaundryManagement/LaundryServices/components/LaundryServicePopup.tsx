import { Form, FormikProvider, useFormik } from "formik";
import { Icon } from "@iconify/react"; // Import Iconify
import HeadingPopup from "../../../GuestManagement/components/HeadingPopup";
import { FormField } from "../../../BookingManagement/components/ReservationCustomForm";
import { get } from "lodash";
import {
  useCreateLaundryService,
  useUpdateLaundryService,
} from "../../../../server-action/API/LaundryManagement/laundryService";
import * as Yup from "yup";

const LaundryServicePopup = ({
  onClose,
  edit,
  editData,
}: {
  onClose?: () => void;
  edit?: boolean;
  editData: any;
}) => {
  const validationSchema = Yup.object().shape({
    name: Yup.string().required("Laundry service name is required"),
    type: Yup.array()
      .of(Yup.string().oneOf(["regular", "express"]))
      .min(1, "Select at least one service type"),
  });

  const handleClose = () => {
    onClose?.();
  };
  const { mutate: createLaundryService } = useCreateLaundryService();
  const { mutate: updateLaundryService } = useUpdateLaundryService();
  const formik = useFormik({
    initialValues: {
      name: edit ? get(editData, "name", "") : "",
      type: edit
        ? Array.isArray(get(editData, "type"))
          ? get(editData, "type")
          : [get(editData, "type", "")]
        : [],
    },
    validationSchema,
    onSubmit: async (values) => {
      if (edit) await updateLaundryService({ id: editData._id, body: values });
      else await createLaundryService(values);
      handleClose();
    },
  });

  const handleCheckboxChange = (value: string) => {
    const current = formik.values.type;
    if (current.includes(value)) {
      formik.setFieldValue(
        "type",
        current.filter((v: any) => v !== value)
      );
    } else {
      formik.setFieldValue("type", [...current, value]);
    }
  };

  return (
    <HeadingPopup
      onClose={handleClose}
      className="w-full max-w-screen-sm"
      heading="Laundry Service"
    >
      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit} className="space-y-3">
          <div className="grid grid-cols-1 gap-x-4 gap-y-3">
            <FormField
              label="Laundry Service"
              type="text"
              name="name"
              formik={formik}
              placeholder="Add Laundry Service"
            />
          </div>

          <div className="flex items-center space-x-4 mt-2">
            {["regular", "express"].map((val) => (
              <label
                key={val}
                className="flex items-center space-x-2 cursor-pointer"
              >
                <input
                  type="checkbox"
                  name="type"
                  value={val}
                  checked={formik.values.type.includes(val)}
                  onChange={() => handleCheckboxChange(val)}
                  className="hidden"
                />
                <Icon
                  icon={
                    formik.values.type.includes(val)
                      ? "mdi:checkbox-marked"
                      : "mdi:checkbox-blank-outline"
                  }
                  className="text-2xl text-black"
                />
                <span className="text-sm capitalize">{val}</span>
              </label>
            ))}
          </div>
          {formik.touched["type"] && formik.errors["type"] && (
            <div className="mt-1 text-xs text-red">{`**${formik.errors["type"]}`}</div>
          )}

          <div className="flex items-center justify-end mt-4">
            <button
              type="submit"
              className="px-4 py-2 bg-[#2A3A6D] text-white rounded-md"
            >
              Create
            </button>
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default LaundryServicePopup;
