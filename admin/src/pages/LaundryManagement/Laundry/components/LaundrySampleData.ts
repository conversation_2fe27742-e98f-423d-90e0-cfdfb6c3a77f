export const LaundrySampleData = [
  {
    guestname: "john doe",
    room: "4",
    serviceType: "good",
    items: ["socks", "jacket", "pants"],
    status: "pending",
    date: "3/5/2025",
    category: "Lenin & Bedding",
    product: "Bedsheet",
    bedtype: "King Size",
    quantity: "5",
    action: "action",
    requestDate: "2025-04-09",
    completionDate: "2025-04-05",
    totalCost: "5000",
    price: "5000",
    requestTime: "07:21",
    completionTime: "01:21",
  },
  {
    guestname: "john hero",
    room: "1",
    serviceType: "good",
    items: ["socks", "jacket", "pants"],
    status: "pending",
    date: "6/5/2025",
    category: "Lenin & Bedding",
    product: "Bedsheet",
    bedtype: "King Size",
    quantity: "5",
    action: "action",
    requestDate: "2025-04-09",
    completionDate: "2025-04-05",
    totalCost: "2500",
    price: "2500",
    requestTime: "07:21",
    completionTime: "09:21",
  },
  {
    guestname: "son doe",
    room: "2",
    serviceType: "good",
    items: ["socks", "jacket", "pants"],
    status: "pending",
    date: "3/5/2025",
    category: "Lenin & Bedding",
    product: "Bedsheet",
    bedtype: "King Size",
    quantity: "5",
    action: "action",
    requestDate: "2025-04-09",
    completionDate: "2025-04-05",
    totalCost: "2000",
    price: "2000",
    requestTime: "13:21",
    completionTime: "07:21",
  },
  {
    guestname: "john khatiwada",
    room: "3",
    serviceType: "good",
    items: ["socks", "jacket", "pants"],
    status: "pending",
    date: "3/5/2025",
    category: "category1",
    product: "Bedsheet",
    bedtype: "King Size",
    quantity: "4",
    action: "action",
    requestDate: "2025-04-09",
    completionDate: "2025-04-05",
    totalCost: "3000",
    price: "3000",
    requestTime: "11:21",
    completionTime: "07:21",
  },
];
