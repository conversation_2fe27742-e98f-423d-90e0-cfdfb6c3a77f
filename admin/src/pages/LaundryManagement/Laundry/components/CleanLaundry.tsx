import { FormikProvider, useFormik, Form } from "formik";
import HeadingPopup from "../../../GuestManagement/components/HeadingPopup";
import { FormField } from "../../../BookingManagement/components/ReservationCustomForm";
const CleanLaundry = ({
  onClose,
  data,
  onSuccess,
}: {
  onClose?: () => void;
  data?: any;
  onSuccess?: () => void;
}) => {
  const handleClose = () => {
    onClose?.();
  };
  const formik = useFormik({
    initialValues: {
      date: "",
      product: "",
      quantity: "",
    },
    onSubmit: (values) => {
      console.log(values);
    },
  });
  return (
    <HeadingPopup
      onClose={handleClose}
      className="w-full max-w-screen-sm"
      heading="Transfer"
    >
      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit} className="space-y-3">
          <div className="grid grid-cols-3 gap-x-4 gap-y-3">
            <FormField
              label="Date"
              type="text"
              name="date"
              formik={formik}
              value={data.date}
            />
            <FormField
              label="Product"
              type="text"
              name="product"
              formik={formik}
              value={data.category}
            />
            <FormField
              label="Quantity"
              type="number"
              name="quantity"
              formik={formik}
              placeholder="Quantity"
            />
          </div>
          <div className="flex items-center justify-end mt-4">
            <button
              type="submit"
              className="px-4 py-2 bg-[#2A3A6D] text-white rounded-md"
            >
              Submit
            </button>
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default CleanLaundry;
