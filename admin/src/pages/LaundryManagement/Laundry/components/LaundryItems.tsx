import { useState, useEffect } from "react";
import MasterTable from "../../../../layouts/Table/MasterTable";
import { TableAction } from "../../../../layouts/Table/TableAction";
import {
  useDeleteGuestLaundry,
  useGetAllGuestLaundry,
} from "../../../../server-action/API/LaundryManagement/guestLaundry";
import GuestLaundryDetails from "./GuestLaundryDetails";
import { Card } from "../../../../components/Card";
import LaundryItemsForm from "./LaundryItemsForm";
import { toast } from "react-toastify";
import { DeleteDialog } from "../../../../components";
import { useAuth } from "../../../../hooks";
import { useGetAllUser } from "../../../../server-action/API/user";
import { useGetAllRooms } from "../../../../server-action/API/HotelConfiguration/room";
import { useQuery } from "@tanstack/react-query";
import { apiClient } from "../../../../server-action/utils/ApiGateway";
import { useGetAllLaundryCategory } from "../../../../server-action/API/LaundryManagement/laundryCategory";

interface LaundryItemsProps {
  triggerAdd?: boolean;
  onAddTriggered?: () => void;
}

/**
 * LaundryItems component for displaying the actual laundry items data
 */
const LaundryItems: React.FC<LaundryItemsProps> = ({
  triggerAdd,
  onAddTriggered,
}) => {
  // State for pagination
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);

  // State for selected laundry item details
  const [selectedLaundry, setSelectedLaundry] = useState<any>(null);
  const [showDetails, setShowDetails] = useState<boolean>(false);

  // State for form
  const [showForm, setShowForm] = useState<boolean>(false);
  const [isEditMode, setIsEditMode] = useState<boolean>(false);

  // State for delete confirmation
  const [showDeleteDialog, setShowDeleteDialog] = useState<boolean>(false);

  // Fetch guest laundry data
  const {
    data: laundryData,
    isLoading,
    error,
    refetch,
    isError,
  } = useGetAllGuestLaundry();

  // Delete mutation
  const { mutate: deleteLaundry, isPending: isDeleting } =
    useDeleteGuestLaundry();

  // Combined loading state
  const isLoadingAny = isLoading || isDeleting;

  // Get current user from auth
  const { data: authData } = useAuth();
  const currentUser = authData?.user;
  const isGuestUser = currentUser?.role === "guest";

  // Fetch guest users to get names for display
  const { data: guestUsers, isSuccess: isGuestUsersSuccess } = useGetAllUser({
    role: "guest",
  });

  // Fetch rooms data to get room numbers
  const { data: rooms, isSuccess: isRoomsSuccess } = useGetAllRooms();

  // Fetch laundry categories
  const { data: categories, isSuccess: isCategoriesSuccess } =
    useGetAllLaundryCategory();

  // Fetch all bookings to get room-guest relationships
  const { data: bookings, isSuccess: isBookingsSuccess } = useQuery({
    queryKey: ["bookings"],
    queryFn: async () => {
      try {
        const response = await apiClient.get("booking");

        return response.data.data || [];
      } catch (error) {
        console.error("Error fetching bookings:", error);
        return [];
      }
    },
  });

  // Handle add new laundry items
  const handleAddLaundry = () => {
    try {
      setIsEditMode(false);
      setSelectedLaundry(null);
      setShowForm(true);
    } catch (error) {
      console.error("Error in handleAddLaundry:", error);
      toast.error("An error occurred while trying to open the form");
    }
  };

  // Handle API errors
  useEffect(() => {
    if (isError && error) {
      console.error("Error fetching laundry data:", error);
      toast.error("Failed to load laundry data. Please try again.");
    }
  }, [isError, error]);

  // Handle triggerAdd prop from parent component
  useEffect(() => {
    if (triggerAdd) {
      handleAddLaundry();
      onAddTriggered?.();
    }
  }, [triggerAdd, onAddTriggered]);

  // Filter and paginate data
  const getFilteredData = () => {
    if (!laundryData) return [];

    // If the current user is a guest, only show their laundry items
    let filteredData = laundryData;
    if (isGuestUser && currentUser?._id) {
      filteredData = laundryData.filter((item: any) => {
        // Check if the item belongs to the current user

        // Check user field
        if (typeof item.user === "string") {
          if (item.user === currentUser._id) return true;
        } else if (
          item.user &&
          typeof item.user === "object" &&
          item.user._id
        ) {
          if (item.user._id === currentUser._id) return true;
        }

        // Check guest field
        if (typeof item.guest === "string") {
          if (item.guest === currentUser._id) return true;
        } else if (
          item.guest &&
          typeof item.guest === "object" &&
          item.guest._id
        ) {
          if (item.guest._id === currentUser._id) return true;
        }

        // Check booking.guest field
        if (item.booking?.guest) {
          if (typeof item.booking.guest === "string") {
            if (item.booking.guest === currentUser._id) return true;
          } else if (
            item.booking.guest &&
            typeof item.booking.guest === "object" &&
            item.booking.guest._id
          ) {
            if (item.booking.guest._id === currentUser._id) return true;
          }
        }

        return false;
      });
    }

    return filteredData;
  };

  // Get paginated data for the current page
  const getPaginatedData = () => {
    const filteredData = getFilteredData();
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;

    // Return the slice of data for the current page
    return filteredData.slice(startIndex, endIndex);
  };

  // Handle view details
  const handleViewDetails = (data: any) => {
    console.log("Opening details view for item:", data._id);
    setSelectedLaundry(data);
    setShowDetails(true);
  };

  // Close details modal
  const closeDetails = () => {
    setShowDetails(false);
    setSelectedLaundry(null);
  };

  // Handle edit laundry items
  const handleEditLaundry = (item: any) => {
    // Find the row data for this item to get the guest name and room number
    const rowData = tableData.rows.find((row: any) => row.id === item._id);

    // Create an enhanced data object with the guest name and room number from the table
    const enhancedData = {
      ...item,
      // Add the display values from the table
      displayGuestName: rowData ? rowData.guestName : "",
      displayRoomNo:
        rowData && rowData.roomNo
          ? // Extract text content from the React element
            (typeof rowData.roomNo === "object" &&
              rowData.roomNo.props &&
              rowData.roomNo.props.children) ||
            ""
          : "",
    };

    // Ensure room data is properly set for the edit form
    // If we have a room ID but no room object, create a proper room object
    if (typeof enhancedData.room === "string" && enhancedData.room) {
      const roomId = enhancedData.room;
      // Try to find the room in the rooms data
      if (isRoomsSuccess && rooms) {
        const foundRoom = rooms.find((r: any) => r._id === roomId);
        if (foundRoom) {
          // Replace the string with the full room object
          enhancedData.room = foundRoom;
        }
      }
    }

    // If we have a roomNo but no room object, add it to the room object
    if (
      enhancedData.roomNo &&
      (!enhancedData.room ||
        (typeof enhancedData.room === "object" && !enhancedData.room.roomNo))
    ) {
      if (typeof enhancedData.room === "object") {
        enhancedData.room = {
          ...enhancedData.room,
          roomNo: enhancedData.roomNo,
        };
      } else if (typeof enhancedData.room === "string") {
        enhancedData.room = {
          _id: enhancedData.room,
          roomNo: enhancedData.roomNo,
        };
      }
    }

    // Ensure guest data is properly set for the edit form
    // If we have a user ID but no user object, create a proper user object
    if (typeof enhancedData.user === "string" && enhancedData.user) {
      const userId = enhancedData.user;
      // Try to find the user in the guestUsers data
      if (isGuestUsersSuccess && guestUsers) {
        const foundUser = guestUsers.find((u: any) => u._id === userId);
        if (foundUser) {
          // Replace the string with the full user object
          enhancedData.user = foundUser;
        }
      }
    }

    // If we have a guest name but no user object, add it to the user object
    if (
      enhancedData.displayGuestName &&
      (!enhancedData.user ||
        (typeof enhancedData.user === "object" && !enhancedData.user.name))
    ) {
      if (typeof enhancedData.user === "object") {
        enhancedData.user = {
          ...enhancedData.user,
          name: enhancedData.displayGuestName,
        };
      } else if (typeof enhancedData.user === "string") {
        enhancedData.user = {
          _id: enhancedData.user,
          name: enhancedData.displayGuestName,
        };
      }
    }

    // If we have a guest object but no user object, copy guest to user
    if (!enhancedData.user && enhancedData.guest) {
      enhancedData.user = enhancedData.guest;
    }

    // Ensure each item in the items array has a properly set serviceType
    if (enhancedData.items && Array.isArray(enhancedData.items)) {
      enhancedData.items = enhancedData.items.map((item: any) => {
        // Make a copy of the item
        const updatedItem = { ...item };

        // Ensure serviceType is set and normalized
        if (updatedItem.serviceType) {
          const serviceTypeLower =
            typeof updatedItem.serviceType === "string"
              ? updatedItem.serviceType.toLowerCase()
              : "";

          if (serviceTypeLower === "regular") {
            updatedItem.serviceType = "regular";
          } else if (serviceTypeLower === "express") {
            updatedItem.serviceType = "express";
          }
        } else {
          // If serviceType is not set, default to 'regular'
          updatedItem.serviceType = "regular";
        }

        return updatedItem;
      });
    }

    setSelectedLaundry(enhancedData);
    setIsEditMode(true);
    setShowForm(true);
  };

  // Handle delete laundry items
  const handleDeleteLaundry = (data: any) => {
    setSelectedLaundry(data);
    setShowDeleteDialog(true);
  };

  // Confirm delete laundry
  const confirmDeleteLaundry = () => {
    if (selectedLaundry && selectedLaundry._id) {
      deleteLaundry(selectedLaundry._id, {
        onSuccess: () => {
          toast.success("Laundry items deleted successfully");
          setShowDeleteDialog(false);
          setSelectedLaundry(null);
          refetch();
        },
        onError: (error) => {
          console.error("Error deleting laundry items:", error);
          toast.error("Failed to delete laundry items");
        },
      });
    }
  };

  // Table data configuration
  const tableData = {
    columns: [
      { key: "sn", title: "S.N" },
      { key: "guestName", title: "Guest Name" },
      { key: "roomNo", title: "Room No" },
      { key: "serviceType", title: "Service Type" },
      { key: "serviceDetails", title: "Category" },
      { key: "status", title: "Status" },
      { key: "totalItems", title: "Total Items" },
      { key: "totalCost", title: "Total Cost" },
      { key: "action", title: "Actions" },
    ],
    rows: laundryData
      ? getPaginatedData().map((item: any, index: number) => {
          // Calculate the serial number based on the current page
          const serialNumber = (currentPage - 1) * itemsPerPage + index + 1;

          // Calculate total items
          const totalItems =
            item.items?.reduce(
              (acc: number, item: any) => acc + (item.quantity || 0),
              0
            ) || 0;

          // Get guest name from user field or guest field
          let guestName = "N/A";
          let userId = "";

          // First, try to get the user ID from user field
          if (item.user) {
            if (typeof item.user === "string") {
              userId = item.user;
            } else if (
              item.user &&
              typeof item.user === "object" &&
              item.user._id
            ) {
              userId = item.user._id;
              // If the object has a name property, use it directly
              if (item.user.name) {
                guestName = item.user.name;
              }
            }
          }

          // If user ID is not found, try to get it from guest field
          if (!userId && item.guest) {
            if (typeof item.guest === "string") {
              userId = item.guest;
            } else if (
              item.guest &&
              typeof item.guest === "object" &&
              item.guest._id
            ) {
              userId = item.guest._id;
              // If the object has a name property, use it directly
              if (item.guest.name) {
                guestName = item.guest.name;
              }
            }
          }

          // If user ID is not found, try to get it from booking.guest field
          if (!userId && item.booking?.guest) {
            if (typeof item.booking.guest === "string") {
              userId = item.booking.guest;
            } else if (
              item.booking.guest &&
              typeof item.booking.guest === "object" &&
              item.booking.guest._id
            ) {
              userId = item.booking.guest._id;
              // If the object has a name property, use it directly
              if (item.booking.guest.name) {
                guestName = item.booking.guest.name;
              }
            }
          }

          // If we have a user ID but no name yet, try to find the name in the guest users list
          if (
            userId &&
            guestName === "N/A" &&
            isGuestUsersSuccess &&
            guestUsers
          ) {
            const foundUser = guestUsers.find(
              (user: any) => user._id === userId
            );
            if (foundUser) {
              guestName = foundUser.name;
            }
          }

          // If the current user is the guest user and this item belongs to them, highlight it
          if (isGuestUser && currentUser?._id === userId) {
            guestName = `${guestName} (You)`;
          }

          // Get room number from room field or booking.room field
          let roomNo = "N/A";
          let roomId = "";

          // First try to get room from the room field
          if (item.room) {
            if (typeof item.room === "string") {
              roomId = item.room;
            } else if (
              item.room &&
              typeof item.room === "object" &&
              item.room._id
            ) {
              roomId = item.room._id;
              if (item.room.roomNo) {
                roomNo = item.room.roomNo;
              }
            }
          }

          // If room not found, try to get it from booking.room
          if (roomNo === "N/A" && item.booking?.room) {
            if (typeof item.booking.room === "string") {
              roomId = item.booking.room;
            } else if (
              item.booking.room &&
              typeof item.booking.room === "object" &&
              item.booking.room._id
            ) {
              roomId = item.booking.room._id;
              if (item.booking.room.roomNo) {
                roomNo = item.booking.room.roomNo;
              }
            }
          }

          // If roomNo is still N/A, try to get it from roomNo field directly
          if (roomNo === "N/A" && item.roomNo) {
            roomNo = item.roomNo;
          }

          // If booking has roomNo field, use that
          if (roomNo === "N/A" && item.booking?.roomNo) {
            roomNo = item.booking.roomNo;
          }

          // If we have a roomId but no roomNo, try to find the room in the rooms data
          if (roomNo === "N/A" && roomId && isRoomsSuccess && rooms) {
            const foundRoom = rooms.find((room: any) => room._id === roomId);
            if (foundRoom) {
              roomNo = foundRoom.roomNo;
            }
          }

          // If we still don't have a room number, try to find an active booking for this guest
          if (
            roomNo === "N/A" &&
            userId &&
            isBookingsSuccess &&
            bookings &&
            bookings.length > 0
          ) {
            // Find active bookings for this guest
            const guestBookings = bookings.filter((booking: any) => {
              const bookingGuestId =
                typeof booking.guest === "string"
                  ? booking.guest
                  : booking.guest?._id;

              return (
                bookingGuestId === userId &&
                (booking.status === "checked-in" ||
                  booking.status === "confirmed")
              );
            });

            if (guestBookings && guestBookings.length > 0) {
              // Get the room from the first active booking
              const activeBooking = guestBookings[0];

              if (activeBooking && activeBooking.room) {
                const bookingRoomId =
                  typeof activeBooking.room === "string"
                    ? activeBooking.room
                    : activeBooking.room._id;

                if (bookingRoomId && isRoomsSuccess && rooms) {
                  const foundRoom = rooms.find(
                    (room: any) => room._id === bookingRoomId
                  );
                  if (foundRoom && foundRoom.roomNo) {
                    roomNo = foundRoom.roomNo;
                  }
                }
              }
            }
          }

          // Create a custom room number cell with data attributes for dynamic updates
          const roomNoCell = (
            <div data-cell="roomNo" data-item-id={item._id}>
              {roomNo}
            </div>
          );

          // Get service details from items
          const serviceDetails =
            item.items && item.items.length > 0
              ? item.items
                  .map((laundryItem: any) => {
                    // Get category name
                    let categoryName = "";
                    if (laundryItem.category) {
                      if (typeof laundryItem.category === "string") {
                        // Try to find category in categories data
                        if (isCategoriesSuccess && categories) {
                          const category = categories.find(
                            (cat: any) => cat._id === laundryItem.category
                          );
                          if (category && category.name) {
                            categoryName = category.name;
                          } else {
                            // If category not found in categories data, use the ID as fallback
                            categoryName = laundryItem.category;
                          }
                        } else {
                          // If categories data not available, use the ID as fallback
                          categoryName = laundryItem.category;
                        }
                      } else if (
                        typeof laundryItem.category === "object" &&
                        laundryItem.category.name
                      ) {
                        categoryName = laundryItem.category.name;
                      }
                    }

                    // Get service type for display
                    const serviceType = laundryItem.serviceType
                      ? laundryItem.serviceType.toLowerCase() === "regular"
                        ? "Regular"
                        : laundryItem.serviceType.toLowerCase() === "express"
                        ? "Express"
                        : laundryItem.serviceType
                      : "";

                    // Return category with service type in parentheses if both exist
                    return categoryName
                      ? serviceType
                        ? `${categoryName} (${serviceType})`
                        : categoryName
                      : serviceType || "";
                  })
                  .filter((detail: string) => detail) // Remove empty strings
                  .join(", ")
              : "";

          return {
            key: item._id,
            id: item._id, // Add id for row identification
            sn: serialNumber,
            guestName: guestName,
            roomNo: roomNoCell, // Use the custom cell with data attributes
            serviceType: item.serviceType || "regular",
            serviceDetails: serviceDetails,
            status: item.ticketStatus || item.status || "pending",
            totalItems: totalItems,
            totalCost: item.totalCost || 0,
            action: (
              <TableAction
                onShow={() => handleViewDetails(item)}
                onEdit={() => handleEditLaundry(item)}
                onDelete={() => handleDeleteLaundry(item)}
              />
            ),
          };
        })
      : [],
  };

  return (
    <div>
      {/* Error message */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          Failed to load laundry data.{" "}
          <button onClick={() => refetch()} className="text-blue-600 underline">
            Retry
          </button>
        </div>
      )}

      {/* Loading state */}
      {isLoadingAny && (
        <Card className="py-8">
          <div className="flex justify-center items-center">
            <p>Loading laundry data...</p>
          </div>
        </Card>
      )}

      {/* Data table */}
      {!isLoadingAny && laundryData && laundryData.length > 0 && (
        <Card className="mt-2">
          <MasterTable
            columns={tableData.columns}
            rows={tableData.rows}
            loading={isLoadingAny}
            pagination={{
              currentPage: currentPage,
              totalPage: Math.ceil(
                (getFilteredData()?.length || 0) / itemsPerPage
              ),
              limit: itemsPerPage,
              onClick: (page) => {
                setCurrentPage(page);
              },
            }}
          />
        </Card>
      )}

      {/* Empty state */}
      {!isLoadingAny && (!laundryData || laundryData.length === 0) && (
        <Card className="py-8">
          <div className="text-center text-gray-500">
            No laundry items found
          </div>
        </Card>
      )}

      {/* Details modal */}
      {showDetails && selectedLaundry && (
        <GuestLaundryDetails
          id={selectedLaundry._id}
          data={selectedLaundry}
          onClose={closeDetails}
        />
      )}

      {/* Add/Edit Form */}
      {showForm ? (
        <LaundryItemsForm
          close={() => {
            setShowForm(false);
          }}
          edit={isEditMode}
          editData={selectedLaundry}
          onSuccess={() => {
            setShowForm(false);
            refetch();
          }}
        />
      ) : (
        <div style={{ display: "none" }}>Form is hidden</div>
      )}

      {/* Delete Confirmation Dialog */}
      {showDeleteDialog && (
        <DeleteDialog
          confirmAction={true}
          onClose={() => setShowDeleteDialog(false)}
          onConfirm={confirmDeleteLaundry}
          title="Delete Laundry Items"
          des="Are you sure you want to delete these laundry items? This action cannot be undone."
        />
      )}
    </div>
  );
};

export default LaundryItems;
