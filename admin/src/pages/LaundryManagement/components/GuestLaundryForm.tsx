import { Form, FormikProvider, useFormik } from "formik";
import React, { useState, useMemo } from "react";
import { FormField } from "../../BookingManagement/components/ReservationCustomForm";
import HeadingPopup from "../../GuestManagement/components/HeadingPopup";
import { guestFormObj } from "./laundryobj";
import { get } from "lodash";
import { useGetAllRooms } from "../../../server-action/API/HotelConfiguration/room";
import {
  useCreateTicket,
  useUpdateTicket,
} from "../../../server-action/API/Ticket/Ticket";
import * as Yup from "yup";
import { toast } from "react-toastify";
import { useQuery } from "@tanstack/react-query";
import { apiClient } from "../../../server-action/utils/ApiGateway";

interface GuestLaundryFormProps {
  close: () => void;
  edit?: boolean;
  editData?: any;
  onSubmit?: (id: any, data: any) => void;
  isLoading?: boolean;
  onSuccess?: () => void; // Callback function to be called after successful submission
  housekeeperUsers?: any[]; // Array of housekeeper users passed from parent component
}

const GuestLaundryForm: React.FC<GuestLaundryFormProps> = ({
  close,
  edit = false,
  editData,
  onSuccess,
  housekeeperUsers: externalHousekeeperUsers,
}) => {
  // API hooks
  const { mutate: createTicket, isPending: isCreating } = useCreateTicket();
  const { mutate: updateTicket, isPending: isUpdating } = useUpdateTicket();
  const { data: roomData, isSuccess: isRoomSuccess } = useGetAllRooms();
  const isLoading = isCreating || isUpdating;
  // Extract room ID from editData, handling both string and object formats
  const getRoomIdFromEditData = () => {
    if (!edit || !editData) return "";

    const room = get(editData, "room", "");
    if (typeof room === "string") return room;
    if (typeof room === "object" && room?._id) return room._id;
    return "";
  };

  const [selectedRoom, setSelectedRoom] = useState<string>(
    getRoomIdFromEditData()
  );

  console.log(roomData, "ropm");
  // Room options for dropdown
  const roomOption = isRoomSuccess
    ? roomData.map((item) => ({
        label: item.roomNo ?? "",
        value: item._id as string,
      }))
    : [];

  // Get the current user from localStorage
  const userString = localStorage.getItem("user");
  const currentUser = userString ? JSON.parse(userString) : null;
  const userId = currentUser?._id || "";

  // Use external housekeeper users if provided, otherwise fetch them
  const {
    data: fetchedHousekeeperUsers,
    isSuccess: isFetchedHousekeeperUsersSuccess,
  } = useQuery({
    queryKey: ["users", "housekeeper"],
    queryFn: async () => {
      console.log("Fetching housekeeper users for GuestLaundryForm");
      const res = await apiClient.get("/user", {
        params: { role: "housekeeper" },
      });
      console.log(
        "Housekeeper users fetched in GuestLaundryForm:",
        res.data.data
      );
      return res.data.data;
    },
    // Skip the query if external data is provided
    enabled: !externalHousekeeperUsers,
  });

  // Use external data if provided, otherwise use fetched data
  const housekeeperUsers = externalHousekeeperUsers || fetchedHousekeeperUsers;
  const isHousekeeperUsersSuccess = externalHousekeeperUsers
    ? true
    : isFetchedHousekeeperUsersSuccess;

  // Log the housekeeper users for debugging
  console.log("Using housekeeper users in form:", housekeeperUsers);

  // Get the housekeeper ID from editData
  const getHousekeeperIdFromEditData = () => {
    if (!edit || !editData) return "";

    // Debug log to see the structure of editData
    console.log("Edit data for housekeeper selection:", editData);

    // Try to get the user field first (which should contain the housekeeper ID)
    const user = get(editData, "user", "");
    console.log("User field from editData:", user);

    if (user) {
      // If user is a string (ID), return it directly
      if (typeof user === "string") return user;
      // If user is an object with _id, return the _id
      if (typeof user === "object" && user?._id) return user._id;
    }

    // Fallback to reportedBy if user is not available (for backward compatibility)
    const reportedBy = get(editData, "reportedBy", "");
    console.log("ReportedBy field from editData:", reportedBy);

    if (reportedBy) {
      if (typeof reportedBy === "string") return reportedBy;
      if (typeof reportedBy === "object" && reportedBy?._id)
        return reportedBy._id;
    }

    return "";
  };

  // Housekeeper users options for reportedTo dropdown
  const housekeeperOptions = useMemo(() => {
    if (!isHousekeeperUsersSuccess || !housekeeperUsers) return [];

    console.log("Housekeeper users for options:", housekeeperUsers);

    const options = housekeeperUsers.map((user: any) => ({
      label: user.name || "Unknown", // Show name in the dropdown
      value: user._id || "", // Use ID as the actual value
      // Store both ID and name for later use
      userData: {
        id: user._id,
        name: user.name,
      },
    }));

    console.log("Housekeeper options for dropdown:", options);

    // If in edit mode, log the selected housekeeper ID for debugging
    if (edit && editData) {
      const selectedId = getHousekeeperIdFromEditData();
      console.log("Selected housekeeper ID:", selectedId);
      console.log(
        "Matching option:",
        options.find((opt: { value: string }) => opt.value === selectedId)
      );
    }

    return options;
  }, [housekeeperUsers, isHousekeeperUsersSuccess, edit, editData]);

  // Form validation schema
  const validationSchema = Yup.object({
    room: Yup.string().required("Room Number is required"),
    ticketStatus: Yup.string().required("Status is required"),
    priority: Yup.string().required("Priority is required"),
    reportedTo: Yup.string().required("Housekeeper is required"),
  });

  const handleRoomChange = (e: any) => {
    const value = e.target ? e.target.value : e;
    setSelectedRoom(value);
    formik.setFieldValue("room", value);
  };

  const formik = useFormik({
    initialValues: {
      date: edit
        ? get(editData, "date", "")
        : new Date().toISOString().split("T")[0],
      room: getRoomIdFromEditData(),
      ticketStatus: edit ? get(editData, "ticketStatus", "") : "pending",
      status: edit ? get(editData, "ticketStatus", "") : "pending", // For backward compatibility with form fields
      priority: edit ? get(editData, "priority", "") : "medium",
      complaintBy: edit ? get(editData, "reportedBy", "") : userId || "", // Map reportedBy to complaintBy (guest user ID)
      // For reportedTo, we need to use the ID when editing
      reportedTo: edit ? getHousekeeperIdFromEditData() : "", // Get the housekeeper ID using our helper function
      ticketCategory: "laundry",
      isLaundry: true,
      // Store the housekeeper name separately for display purposes
      housekeeperName: edit ? get(editData, "housekeeperName", "") : "",
    },
    validationSchema,
    enableReinitialize: true,
    onSubmit: async (values) => {
      try {
        // Prepare the data to send
        const { reportedTo, complaintBy, ...otherValues } = values; // Extract fields we don't want to send directly

        // Find the selected housekeeper to get both ID and name
        const selectedHousekeeper = housekeeperOptions.find(
          (option: { value: string; label: string; userData: any }) =>
            option.value === reportedTo
        );

        // Get the housekeeper name from the userData
        const housekeeperName = selectedHousekeeper?.userData?.name || "";

        // Create a ticket object that matches the Ticket interface
        const ticketData = {
          ...otherValues,
          room: values.room, // Room ID
          ticketCategory: "laundry", // Set the ticket category to laundry
          // IMPORTANT: For reportedBy, we need to use the guest user's ID (ObjectId)
          // The backend expects an ObjectId for reportedBy
          reportedBy: userId, // Guest user ID - this is now an ObjectId
          // The user field will now store the housekeeper ID
          user: reportedTo, // Housekeeper ID
          // Also include the housekeeper name for display purposes
          housekeeperName: housekeeperName, // Store the name for display
          priority: values.priority || "medium", // Priority level
          // Use ticketStatus if available, otherwise fall back to status for backward compatibility
          ticketStatus: values.ticketStatus || values.status || "pending",
          date: values.date || new Date().toISOString().split("T")[0],
          // Set laundry-specific flags
          isLaundry: true,
          isMissing: false,
          isDamaged: false,
          isAlright: false,
        };

        // Create a FormData object for the API
        const formData = new FormData();

        // Add all ticket data to the FormData
        Object.entries(ticketData).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            formData.append(key, String(value));
          }
        });

        if (edit && editData?._id) {
          // Update existing ticket
          console.log("Updating ticket with ID:", editData._id);
          try {
            updateTicket(
              {
                id: editData._id,
                body: formData,
              },
              {
                onSuccess: () => {
                  console.log("Update request sent successfully");
                  // Call the onSuccess callback if provided
                  if (onSuccess) {
                    onSuccess();
                  }
                  close();
                },
                onError: (error: any) => {
                  console.error("Update error:", error);
                },
              }
            );
          } catch (error) {
            console.error("Update error:", error);
            // The error toast is handled in the API hook
          }
        } else {
          // Create new ticket
          console.log("Creating new ticket");
          try {
            createTicket(formData, {
              onSuccess: () => {
                console.log("Create request sent successfully");
                // Call the onSuccess callback if provided
                if (onSuccess) {
                  onSuccess();
                }
                close();
              },
              onError: (error: any) => {
                console.error("Create error:", error);
              },
            });
          } catch (error) {
            console.error("Create error:", error);
            // The error toast is handled in the API hook
          }
        }
      } catch (error) {
        console.error("Submission error:", error);
        toast.error("An error occurred during submission");
      }
    },
  });

  const getRoomNumber = (roomId: string) => {
    if (!isRoomSuccess || !roomId || !roomData) return roomId;
    const room = roomData.find((room) => room._id === roomId);
    return room ? room.roomNo : roomId;
  };

  return (
    <HeadingPopup
      heading={edit ? "Edit Laundry Ticket" : "New Laundry Ticket"}
      className="w-full max-w-screen-sm"
      onClose={close}
    >
      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit} className="overflow-y-auto  ">
          <div className="grid grid-cols-2 gap-x-4 gap-y-2">
            <FormField
              name="room"
              label="Room No"
              type="dropdown"
              placeholder="Select Room no"
              options={roomOption}
              formik={formik}
              required
              onChange={handleRoomChange}
              disabled={edit}
            />

            {selectedRoom && (
              <div className="col-span-2 p-3 bg-blue-50 rounded-md mb-2">
                <p className="text-sm text-blue-800">
                  <strong>Room:</strong> {getRoomNumber(selectedRoom)}
                  {/* Removed guest information since we're not using booking data */}
                </p>
              </div>
            )}

            {guestFormObj.map(({ name, label, type, placeholder }) => {
              // Use housekeeperOptions for reportedTo field
              const options =
                name === "reportedTo"
                  ? housekeeperOptions
                  : name === "priority"
                  ? [
                      { value: "low", label: "Low" },
                      { value: "medium", label: "Medium" },
                      { value: "high", label: "High" },
                    ]
                  : name === "status"
                  ? [
                      { value: "pending", label: "Pending" },
                      { value: "in-progress", label: "In Progress" },
                      { value: "completed", label: "Completed" },
                    ]
                  : [];

              return (
                <FormField
                  key={name}
                  name={name}
                  label={label}
                  type={type}
                  placeholder={placeholder}
                  options={options}
                  formik={formik}
                  required={
                    name === "status" ||
                    name === "priority" ||
                    name === "reportedTo"
                  }
                  onChange={(e) => {
                    // If this is the status field, also update ticketStatus
                    if (name === "status") {
                      formik.setFieldValue("ticketStatus", e.target.value);
                    }
                    formik.handleChange(e);
                  }}
                />
              );
            })}
          </div>

          <div className="flex justify-end mt-6 space-x-2">
            <button
              type="button"
              className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
              onClick={close}
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 text-white bg-[#163381] rounded-md hover:bg-[#0f2563] transition-colors"
              disabled={isLoading}
            >
              {isLoading ? (
                <span className="flex items-center">
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  {edit ? "Updating..." : "Creating..."}
                </span>
              ) : edit ? (
                "Update"
              ) : (
                "Create"
              )}
            </button>
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default GuestLaundryForm;
