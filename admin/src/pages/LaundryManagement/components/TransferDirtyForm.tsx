import { Form, FormikProvider, useFormik } from "formik";
import HeadingPopup from "../../GuestManagement/components/HeadingPopup";
import { FormField } from "../../BookingManagement/components/ReservationCustomForm";

const TransferDirtyForm = () => {
  const formik = useFormik({
    initialValues: {
      room: "",
    },
    enableReinitialize: false,
    onSubmit: (values) => {
      console.log(values);
    },
  });
  return (
    <HeadingPopup
      heading="Transfer Dirty"
      className="w-full max-w-screen-sm"
      onClose={close}
    >
      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit}>
          <div className="grid grid-cols-2 gap-x-4 gap-y-2">
            <FormField
              name="room"
              label="Room No"
              type="dropdown"
              placeholder="Select Room no"
              options={[]}
              formik={formik}
            />
          </div>
          {/* <div className="grid grid-cols-3 gap-x-4 gap-y-2">
            <FormField
              name="category"
              label="Category"
              type="dropdown"
              placeholder="Select Category"
              options={[{ label: "Laundry", value: "laundry" }]}
              formik={formik}
            />
            <FormField
              name="quantity"
              label="Quantity"
              type="text"
              placeholder="Quantity"
              formik={formik}
            />
            <FormField
              name="price"
              label="Price"
              type="text"
              placeholder="Price"
              formik={formik}
            />
          </div>
          <button className="px-4 py-2 text-white bg-[#163381] rounded-md">
            Add
          </button>
          <div className="grid grid-cols-3 gap-x-4 gap-y-2">
            <FormField
              name="totalCost"
              label="Total Cost"
              type="text"
              placeholder="Total cost"
              formik={formik}
            />
          </div> */}
          <div className="flex justify-end mt-4">
            <button className="px-4 py-2 text-white bg-[#163381] rounded-md">
              Create
            </button>
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default TransferDirtyForm;
