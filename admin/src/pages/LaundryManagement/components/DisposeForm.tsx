import { Form, FormikProvider, useFormik } from "formik";
import HeadingPopup from "../../GuestManagement/components/HeadingPopup";
import { FormField } from "../../BookingManagement/components/ReservationCustomForm";

const DisposeForm = ({ onClose }: { onClose: () => void }) => {
  const formik = useFormik({
    initialValues: {
      item: "",
      quantity: "",
    },
    onSubmit: (values) => {
      console.log(values);
    },
  });
  return (
    <HeadingPopup
      onClose={onClose}
      className="w-full max-w-screen-sm"
      heading="Dispose"
    >
      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit} className="space-y-3">
          <div className="grid grid-cols-2 gap-x-4 gap-y-3">
            <FormField
              label="Item"
              type="dropdown"
              name="item"
              formik={formik}
              placeholder="Select Item"
              options={[{ label: "Success", value: "success" }]}
            />
            <FormField
              label="Quantity"
              type="number"
              name="quantity"
              formik={formik}
              placeholder="Quantity"
            />
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default DisposeForm;
