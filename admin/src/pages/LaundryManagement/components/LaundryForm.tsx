import { Form, FormikProvider, useFormik } from "formik";
import HeadingPopup from "../../GuestManagement/components/HeadingPopup";
import { FormField } from "../../BookingManagement/components/ReservationCustomForm";

const LaundryForm = ({ onClose }: { onClose: () => void }) => {
  const formik = useFormik({
    initialValues: {
      room: "",
      item: "",
      bedType: "",
      quantity: "",
    },
    onSubmit: (values) => {
      console.log(values);
    },
  });
  return (
    <HeadingPopup
      onClose={onClose}
      className="w-full max-w-screen-sm"
      heading="Add Item"
    >
      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit} className="space-y-3">
          <FormField
            label="Select Room"
            type="dropdown"
            name="room"
            formik={formik}
            placeholder="Select Room number"
            options={[{ label: "Success", value: "success" }]}
          />
          <FormField
            label="Item"
            type="dropdown"
            name="item"
            formik={formik}
            placeholder="Select Item"
            options={[{ label: "Success", value: "success" }]}
          />
          <div className="grid grid-cols-2 gap-x-4 gap-y-3">
            <FormField
              label="Bed Type"
              type="dropdown"
              name="bedType"
              formik={formik}
              placeholder="Select bed type"
              options={[{ label: "Success", value: "success" }]}
            />
            <FormField
              label="Quantity"
              type="number"
              name="quantity"
              formik={formik}
              placeholder="Quantity"
            />
          </div>
          <div className="flex items-center mt-4">
            <button
              type="submit"
              className="px-4 py-2 bg-[#2A3A6D] text-white rounded-md"
            >
              Add
            </button>
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default LaundryForm;
