import { useState } from "react";
import { Card, CardContent } from "../../../components/Card";
import { TabData } from "../../../components/TabData";
import InventoryStatus from "./components/InventoryStatus";
import SendToLaundry from "./components/SendToLaundry";
import ProcessItems from "./components/ProcessItems";
import ReturnCleanItems from "./components/ReturnCleanItems";
import Header from "../../../components/Header";

const LaundryDashboard = () => {
  const [selectedTab, setSelectedTab] = useState("status");

  const tabData = [
    {
      title: "Inventory Status",
      value: "status",
    },
    {
      title: "Send to Laundry",
      value: "send",
    },
    {
      title: "Process Items",
      value: "process",
    },
    {
      title: "Return Clean Items",
      value: "return",
    },
  ];

  return (
    <div className="flex flex-col gap-2">
      <div className="flex justify-between items-center">
        <Header title="Laundry Inventory " showButton={false} />
      </div>

      <TabData
        selectedTabData={selectedTab}
        setSelectedTabData={setSelectedTab}
        tabData={tabData}
      />

      <Card className="bg-white">
        <CardContent>
          {selectedTab === "status" && <InventoryStatus />}
          {selectedTab === "send" && <SendToLaundry />}
          {selectedTab === "process" && <ProcessItems />}
          {selectedTab === "return" && <ReturnCleanItems />}
        </CardContent>
      </Card>
    </div>
  );
};

export default LaundryDashboard;
