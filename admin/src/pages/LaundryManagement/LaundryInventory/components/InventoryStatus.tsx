/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/ban-ts-comment */
import { useState } from "react";
import { useGetLaundryInventoryStatus } from "../../../../server-action/API/LaundryManagement/laundryInventory";
import MasterTable from "../../../../layouts/Table/MasterTable";
import { Status } from "../../../../components/Status";
import { calculateTotalQuantity } from "../../../../utils/inventoryHelpers";

const InventoryStatus = () => {
  const [selectedStore, setSelectedStore] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);

  const {
    data: inventoryStatusData,
    isLoading,
    error,
  } = useGetLaundryInventoryStatus({
    page: currentPage,
    limit: pageSize,
    storeType: selectedStore !== "all" ? selectedStore : undefined,
  });

  // Extract data from the paginated response
  const inventoryStatus = inventoryStatusData?.data || [];
  const totalItems = inventoryStatusData?.total || 0;
  const currentPageFromResponse = inventoryStatusData?.page || 1;
  const pageSizeFromResponse = inventoryStatusData?.limit || 10;

  // Prepare data for the table
  const tableData = {
    columns: [
      { key: "sn", title: "S.N" },
      { key: "store", title: "Store" },
      { key: "item", title: "Item" },
      { key: "clean", title: "Clean Count" },
      { key: "dirty", title: "Dirty Count" },
      { key: "total", title: "Total Count" },
      { key: "status", title: "Status" },
    ],
    rows: [],
  };

  // Handle different possible data structures
  let stores = [];

  console.log("Inventory status data:", inventoryStatus);

  if (inventoryStatus && Array.isArray(inventoryStatus)) {
    stores = inventoryStatus;
    console.log("Using array structure directly:", stores);
  }

  // Get unique store names for filter
  const storeOptions = [
    { value: "all", label: "All Stores" },
    ...(stores || []).map((store) => ({
      value: store?.id || "",
      label: store?.name || store.store?.type || "Unknown Store",
    })),
  ];

  console.log("Store options:", storeOptions);

  // Filter data based on selected store and status
  let filteredStores = stores;

  if (selectedStore !== "all") {
    filteredStores = stores.filter(
      (store) => store.store?.id === selectedStore
    );
    console.log("Filtered stores by ID:", selectedStore, filteredStores);
  }

  // Process the filtered data
  if (filteredStores && Array.isArray(filteredStores)) {
    let rowIndex = 1;
    filteredStores.forEach((storeData) => {
      if (storeData && storeData.items && Array.isArray(storeData.items)) {
        let filteredItems = storeData.items;

        // Filter by status if needed
        if (selectedStatus !== "all") {
          // @ts-ignore
          filteredItems = storeData.items.filter((item) => {
            if (selectedStatus === "clean") {
              return item.cleanQuantity > 0 && item.dirtyQuantity === 0;
            } else if (selectedStatus === "dirty") {
              return item.dirtyQuantity > 0 && item.cleanQuantity === 0;
            } else if (selectedStatus === "mixed") {
              return item.cleanQuantity > 0 && item.dirtyQuantity > 0;
            }
            return true;
          });
        }

        // @ts-ignore
        filteredItems.forEach((itemData) => {
          if (itemData) {
            const storeName =
              storeData.store?.name || storeData.store?.type || "Unknown Store";
            const itemName = itemData.item?.name || "Unknown Item";
            const cleanCount = itemData.cleanQuantity || 0;
            const dirtyCount = itemData.dirtyQuantity || 0;

            // Calculate total quantity using the helper function
            const totalCount = calculateTotalQuantity(itemData);
            console.log(`Item ${itemData._id} quantity:`, itemData.quantity);
            console.log(`Item ${itemData._id} cleanQuantity:`, cleanCount);
            console.log(`Item ${itemData._id} dirtyQuantity:`, dirtyCount);
            console.log(`Item ${itemData._id} calculated total:`, totalCount);

            let statusValue = "available";
            if (cleanCount > 0 && dirtyCount === 0) {
              statusValue = "available";
            } else if (dirtyCount > 0 && cleanCount === 0) {
              statusValue = "pending";
            } else if (cleanCount > 0 && dirtyCount > 0) {
              statusValue = "partial";
            } else {
              statusValue = "unavailable";
            }

            // @ts-ignore
            tableData.rows.push({
              key: `${
                storeData.store?._id || storeData.store?.id || "unknown"
              }-${itemData._id || "unknown"}`,
              sn: rowIndex++,
              store: storeName,
              item: itemName,
              clean: cleanCount,
              dirty: dirtyCount,
              total: totalCount,
              status: <Status status={statusValue} />,
            });
            console.log(tableData, "table");
          }
        });
      }
    });
  }

  return (
    <div className="flex flex-col gap-4">
      <h2 className="text-xl font-semibold">Inventory Status Summary</h2>

      <div className="flex gap-4 mb-4">
        <div className="w-1/3">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Filter by Store
          </label>
          <select
            className="w-full p-2 border border-gray-300 rounded-md"
            value={selectedStore}
            onChange={(e) => {
              setSelectedStore(e.target.value);
              setCurrentPage(1); // Reset to first page when filter changes
            }}
          >
            {storeOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        <div className="w-1/3">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Filter by Status
          </label>
          <select
            className="w-full p-2 border border-gray-300 rounded-md"
            value={selectedStatus}
            onChange={(e) => {
              setSelectedStatus(e.target.value);
              setCurrentPage(1); // Reset to first page when filter changes
            }}
          >
            <option value="all">All Status</option>
            <option value="clean">Clean Only</option>
            <option value="dirty">Dirty Only</option>
            <option value="mixed">Mixed</option>
          </select>
        </div>
      </div>

      <MasterTable
        columns={tableData.columns}
        rows={tableData.rows}
        loading={isLoading}
        apiPagination={true}
        totalItems={totalItems}
        onPageChange={(page, limit) => {
          setCurrentPage(page);
          setPageSize(limit);
        }}
      />

      <div className="mt-4 grid grid-cols-4 gap-4">
        <div className="bg-green-100 p-4 rounded-md">
          <h3 className="font-semibold">Clean Items</h3>
          <p className="text-2xl font-bold text-green-600">
            {/* @ts-ignore */}
            {tableData.rows.reduce((sum, row) => sum + row.clean, 0)}
          </p>
        </div>
        <div className="bg-red-100 p-4 rounded-md">
          <h3 className="font-semibold">Dirty Items</h3>
          <p className="text-2xl font-bold text-red-600">
            {/* @ts-ignore */}
            {tableData.rows.reduce((sum, row) => sum + row.dirty, 0)}
          </p>
        </div>
        <div className="bg-blue-100 p-4 rounded-md">
          <h3 className="font-semibold">Total Items</h3>
          <p className="text-2xl font-bold text-blue-600">
            {/* @ts-ignore */}
            {tableData.rows.reduce((sum, row) => sum + row.total, 0)}
          </p>
        </div>
        <div className="bg-yellow-100 p-4 rounded-md">
          <h3 className="font-semibold">Stores</h3>
          <p className="text-2xl font-bold text-yellow-600">
            {stores?.length || 0}
          </p>
        </div>
      </div>
    </div>
  );
};

export default InventoryStatus;
