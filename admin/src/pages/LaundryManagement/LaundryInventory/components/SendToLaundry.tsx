/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/ban-ts-comment */
import { useState } from "react";
import {
  useGetLaundryInventoryStatus,
  useGetAllStores,
  useSendToLaundry,
} from "../../../../server-action/API/LaundryManagement/laundryInventory";
import MasterTable from "../../../../layouts/Table/MasterTable";
// import { TableAction } from "../../../../layouts/Table/TableAction";
import SendToLaundryForm from "./forms/SendToLaundryForm";
import {
  ILaundryInventoryItem,
  IStore,
} from "../../../../Interface/laundryInventory.interface";
import { calculateTotalQuantity } from "../../../../utils/inventoryHelpers";

const SendToLaundry = () => {
  const [selectedStore, setSelectedStore] = useState<string>("all");
  const [showForm, setShowForm] = useState<boolean>(false);
  const [selectedItem, setSelectedItem] =
    useState<ILaundryInventoryItem | null>(null);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);

  const { data: inventoryStatusData, isLoading: isLoadingInventory } =
    useGetLaundryInventoryStatus({
      page: currentPage,
      limit: pageSize,
      storeType: selectedStore !== "all" ? selectedStore : undefined,
    });
  const { data: stores, isLoading: isLoadingStores } = useGetAllStores();

  // Extract data from the paginated response
  const inventoryStatus = inventoryStatusData?.data || [];
  const totalItems = inventoryStatusData?.total || 0;

  const handleSendToLaundry = (item: ILaundryInventoryItem) => {
    // Verify we have the correct item ID
    if (!item.item?._id) {
      // Missing required item ID
    }

    setSelectedItem(item);
    setShowForm(true);
  };

  // Prepare data for the table
  const tableData = {
    columns: [
      { key: "sn", title: "S.N" },
      { key: "store", title: "Store" },
      { key: "item", title: "Item" },
      { key: "clean", title: "Clean Count" },
      { key: "dirty", title: "Dirty Count" },
      { key: "total", title: "Total Count" },
      { key: "action", title: "Action" },
    ],
    rows: [],
  };

  // Get store options for filter
  const storeOptions = [
    { value: "all", label: "All Stores" },

    ...(stores || [])
      .filter((store: any) => store?.type !== "laundry") // Exclude laundry store
      .map((store: any) => ({
        value: store?._id || "",
        label: store?.name || store?.type || "Unknown Store",
      })),
  ];

  // Process inventory data
  let storesData = [];

  if (inventoryStatus && Array.isArray(inventoryStatus)) {
    storesData = inventoryStatus;
  }

  // Filter stores based on selection (exclude laundry store)
  let filteredStores = storesData.filter(
    (store) => store.store?.type !== "laundry"
  );

  if (selectedStore !== "all") {
    filteredStores = filteredStores.filter(
      (store) => store.store?.id === selectedStore
    );
  }
  if (filteredStores && Array.isArray(filteredStores)) {
    let rowIndex = 1;
    filteredStores.forEach((storeData) => {
      if (storeData && storeData.items && Array.isArray(storeData.items)) {
        // Show all items that have a total quantity > 0
        // @ts-ignore
        const itemsWithQuantity = storeData.items.filter((item) => {
          const totalQuantity = calculateTotalQuantity(item);
          return totalQuantity > 0;
        });

        // @ts-ignore
        itemsWithQuantity.forEach((itemData) => {
          if (itemData) {
            const storeName =
              storeData.store?.name || storeData.store?.type || "Unknown Store";
            const itemName = itemData.item?.name || "Unknown Item";
            const cleanCount = itemData.cleanQuantity || 0;
            const dirtyCount = itemData.dirtyQuantity || 0;

            // Calculate total quantity using the helper function
            const totalCount = calculateTotalQuantity(itemData);

            // @ts-ignore
            tableData.rows.push({
              key: `${
                storeData.store?._id || storeData.store?.id || "unknown"
              }-${itemData._id || "unknown"}`,
              sn: rowIndex++,
              store: storeName,
              item: itemName,
              clean: cleanCount,
              dirty: dirtyCount,
              total: totalCount,
              action: (
                <button
                  className="px-3 py-1 bg-[#163381] text-white rounded hover:bg-blue-600"
                  onClick={() => {
                    // Ensure we have valid IDs before passing to the form
                    // Make sure we pass the correct item object with its _id
                    // The ACTUAL item ID needed by the backend is item.item._id
                    const itemWithValidIds = {
                      ...itemData,
                      // Ensure the item object is preserved with its _id intact
                      item: {
                        ...itemData.item,
                        _id: itemData.item?._id, // This is the ACTUAL item ID needed
                      },
                      store: {
                        ...storeData.store,
                        _id: storeData.store?._id || storeData.store?.id || "",
                      },
                    };

                    // Log the ACTUAL item ID we're going to use
                    console.log(
                      "SendToLaundry - ACTUAL Item ID to use (item.item._id):",
                      itemData.item?._id
                    );

                    // Verify we have the correct item ID
                    if (!itemData.item?._id) {
                      console.error(
                        "Missing required item.item._id! This will cause issues with the backend."
                      );
                    }
                    console.log(
                      "SendToLaundry - Prepared item with IDs:",
                      itemWithValidIds
                    );
                    handleSendToLaundry(itemWithValidIds);
                  }}
                  disabled={totalCount === 0}
                >
                  Send to Laundry
                </button>
              ),
            });
          }
        });
      }
    });
  }

  const isLoading = isLoadingInventory || isLoadingStores;

  return (
    <div className="flex flex-col gap-4">
      <h2 className="text-xl font-semibold">Send Items to Laundry</h2>

      <div className="w-1/3 mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Filter by Store
        </label>
        <select
          className="w-full p-2 border border-gray-300 rounded-md"
          value={selectedStore}
          onChange={(e) => {
            setSelectedStore(e.target.value);
            setCurrentPage(1); // Reset to first page when filter changes
          }}
        >
          {storeOptions.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>

      <MasterTable
        columns={tableData.columns}
        rows={tableData.rows}
        loading={isLoading}
        apiPagination={true}
        totalItems={totalItems}
        onPageChange={(page, limit) => {
          setCurrentPage(page);
          setPageSize(limit);
        }}
      />

      {tableData.rows.length === 0 && !isLoading && (
        <div className="text-center py-8 bg-gray-50 rounded-md">
          <p className="text-gray-500">
            No items found with available quantity
          </p>
        </div>
      )}

      {showForm && selectedItem && (
        <SendToLaundryForm
          item={selectedItem}
          onClose={() => {
            setShowForm(false);
            setSelectedItem(null);
          }}
        />
      )}
    </div>
  );
};

export default SendToLaundry;
