/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useEffect } from "react";
import { useGetInventoryStatus } from "../../../../server-action/API/inventoryApi";
import MasterTable from "../../../../layouts/Table/MasterTable";
import { Status } from "../../../../components/Status";
import { calculateTotalQuantity } from "../../../../utils/inventoryHelpers";

interface InventoryItem {
  _id: string;
  name: string;
  description?: string;
  category: {
    _id: string;
    name: string;
  };
  unit: string;
  totalQuantity: number;
  status: string;
}

interface TableRow {
  id: string;
  tokenid: string;
  name: string;
  category: string;
  currentStock: number;
  unit: string;
  status: React.ReactNode;
}

const GeneralInventory = () => {
  const [aggregatedItems, setAggregatedItems] = useState<TableRow[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Fetch inventory data from all stores
  const { data: inventoryStatusData, isLoading: inventoryLoading } = useGetInventoryStatus();
  console.log("Inventory Status Data:", inventoryStatusData);
  useEffect(() => {
    if (inventoryStatusData && !inventoryLoading) {
      console.log("Processing inventory data:", inventoryStatusData);
      // Process the inventory data to aggregate items across all stores
      const itemMap = new Map<string, any>();

      // Process all stores and their items
      inventoryStatusData.data.forEach(storeData => {
        if (storeData.items && Array.isArray(storeData.items)) {
          storeData.items.forEach(itemData => {
            // Use item._id if available, otherwise use the inventory item's own id
            const itemId = itemData.item?._id || itemData.id;
            // Create a safe name for the item, with fallback if id is not available or not a string
            let itemName = itemData.item?.name;
            if (!itemName) {
              try {
                itemName = `Item #${typeof itemData.id === 'string' ? itemData.id.substring(0, 8) : itemData.id}`;
              } catch (e) {
                itemName = `Item #${itemData.id}`;
              }
            }

            // Debug log for each item
            console.log("Processing item:", {
              id: itemData.id,
              itemId,
              itemName,
              item: itemData.item,
              quantity: itemData.quantity,
              cleanQuantity: itemData.cleanQuantity,
              dirtyQuantity: itemData.dirtyQuantity,
              store: storeData.store.name
            });

            if (itemId) {
              // Calculate total quantity for this item in this store
              const quantity = calculateTotalQuantity(itemData);

              if (itemMap.has(itemId)) {
                // Update existing item
                const existingItem = itemMap.get(itemId);
                existingItem.totalQuantity += quantity;
              } else {
                // Add new item
                itemMap.set(itemId, {
                  _id: itemId,
                  name: itemName,
                  description: itemData.item?.description || "-",
                  category: itemData.item?.category?.name || "Uncategorized",
                  unit: itemData.item?.unit || "-",
                  totalQuantity: quantity,
                  status: quantity > 0 ? "active" : "out-of-stock",
                  storeId: storeData.store.id,  // Store the store ID for reference
                  storeName: storeData.store.name || "Unknown Store"  // Store the store name
                });
              }
            }
          });
        }
      });

      // Update status for all items based on final quantity
      itemMap.forEach(item => {
        item.status = item.totalQuantity > 0 ? "active" : "out-of-stock";
      });

      // Convert the map to an array of table rows
      const rows = Array.from(itemMap.values()).map((item, index) => ({
        id: item._id,
        tokenid: `INV-${index + 1}`,
        name: item.name,
        category: item.category,
        currentStock: item.totalQuantity,
        unit: item.unit || "-",
        store: item.storeName || "-",  // Add store name to the table row
        status: <Status status={item.status} />
      }));

      // Sort by name for better readability
      rows.sort((a, b) => a.name.localeCompare(b.name));

      console.log("Processed inventory rows:", rows);
      setAggregatedItems(rows);
      setIsLoading(false);
    }
  }, [inventoryStatusData, inventoryLoading]);

  const tableData = {
    columns: [
      { title: "Id", key: "tokenid" },
      { title: "Name", key: "name" },
      { title: "Category", key: "category" },
      { title: "Store", key: "store" },  // Add store column
      { title: "Total Stock", key: "currentStock" },
      { title: "Unit", key: "unit" },
      { title: "Status", key: "status" },
    ],
    rows: aggregatedItems
  };

  return (
    <div className="bg-white">
      <MasterTable
        columns={tableData.columns}
        rows={tableData.rows}
        loading={isLoading}
      />
    </div>
  );
};

export default GeneralInventory;
