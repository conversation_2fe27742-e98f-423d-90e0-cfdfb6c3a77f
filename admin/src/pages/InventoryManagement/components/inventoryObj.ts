export const inventoryFormObj = [
    {
      name: "itemName",
      label: "Item Name",
      type: "text",
      placeholder: "Enter Item Name",
    },
    {
      name: "category",
      label: "Category",
      type: "dropdown",
      placeholder: "Select Category",
      options: [],
    },
    {
      name: "stockQuantity",
      label: "Stock Quantity",
      type: "number",
      placeholder: "Enter Number",
    },
    {
      name: "lastRestockedDate",
      label: "Last Restocked Date",
      type: "date",
      placeholder: "Enter Date",
    },
    {
      name: "vendorName",
      label: "Vendor Name",
      type: "text",
      placeholder: "Enter Vendor Name",
    },
    {
      name: "contact<PERSON><PERSON>",
      label: "Contact Person",
      type: "text",
      placeholder: "Enter Name",
    },
    {
      name: "phoneNumber",
      label: "Phone Number",
      type: "text",
      placeholder: "Enter Phone Number",
    },
    {
      name: "lastOrdered",
      label: "Last Ordered",
      type: "date",
      placeholder: "Enter Date",
    },
    {
      name: "quantityOrdered",
      label: "Quantity Ordered",
      type: "number",
      placeholder: "Enter Quantity",
    },
    {
      name: "costPerUnit",
      label: "Cost Per Unit",
      type: "number",
      placeholder: "Enter Amount",
    },
    {
      name: "totalCost",
      label: "Total Cost",
      type: "number",
      placeholder: "Enter Amount",
    },
    {
      name: "usagePurpose",
      label: "Usage Purpose",
      type: "text",
      placeholder: "Add Purpose",
    },
  ];
  