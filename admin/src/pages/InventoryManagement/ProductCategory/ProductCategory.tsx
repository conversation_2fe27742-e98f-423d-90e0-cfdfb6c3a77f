import { useMemo, useState } from "react";
import Header from "../../../components/Header";
import MasterTable from "../../../layouts/Table/MasterTable";
import { TableAction } from "../../../layouts/Table/TableAction";
import {
  useDeleteCategory,
  useGetAllCategory,
} from "../../../server-action/API/productApi";
import CategoryForm from "./CategoryForm";
import { get } from "lodash";

const ProductCategory = () => {
  const [openForm, setOpenForm] = useState({
    state: false,
    edit: false,
    editData: null,
  });
  const { data, isSuccess, isLoading } = useGetAllCategory();
  const { mutate: deleteCategory } = useDeleteCategory();

  const columns = useMemo(
    () => [
      {
        title: "S.N",
        key: "sn",
      },
      {
        title: "Name",
        key: "name",
      },
      {
        title: "Description",
        key: "description",
      },
      {
        title: "action",
        key: "action",
      },
    ],
    []
  );
  const rows = isSuccess
    ? data?.map((item: any, index: number) => ({
        sn: index + 1,
        name: get(item, "name", "-"),
        description: get(item, "description", "-"),
        action: (
          <TableAction
            onEdit={() =>
              setOpenForm({ state: true, edit: true, editData: item })
            }
            onDelete={() => deleteCategory(item._id)}
          />
        ),
      }))
    : [];
  return (
    <section>
      <Header
        title="Category"
        onAddClick={() =>
          setOpenForm({ state: true, edit: false, editData: null })
        }
      />
      {openForm.state && (
        <CategoryForm
          {...openForm}
          close={() => setOpenForm((prev) => ({ ...prev, state: false }))}
        />
      )}
      <div>
        <MasterTable rows={rows} columns={columns} loading={isLoading} />
      </div>
    </section>
  );
};

export default ProductCategory;
