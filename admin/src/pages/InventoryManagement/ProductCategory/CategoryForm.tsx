import React, { useEffect } from "react";
import Purchase from "../Inventorypurchase/components/Purchase";
import { IForm } from "../Inventorypurchase/components/VendorForm";
import {
  useCreateCategory,
  useUpdateCategory,
} from "../../../server-action/API/productApi";
import * as yup from "yup";

const validationSchema = yup.object().shape({
  name: yup.string().required("Category name is required"),
  description: yup.string().required("Description is required"),
});

const CategoryForm: React.FC<IForm> = ({ close, edit = false, editData }) => {
  const { mutate: createCategory, isSuccess: createSuccess } =
    useCreateCategory();
  const { mutate: updateCategory, isSuccess: updateSuccess } =
    useUpdateCategory();

  // Close form after successful create or update
  useEffect(() => {
    if (createSuccess || updateSuccess) {
      close();
    }
  }, [createSuccess, updateSuccess, close]);

  const formFields = [
    {
      name: "name",
      placeholder: "Category Name",
      label: "Category Name",
      type: "text",
    },
    {
      name: "description",
      placeholder: "Description",
      label: "Description",
      type: "text",
    },
  ];

  const onSubmit = (values: any) => {
    if (edit) updateCategory({ id: editData._id, body: values });
    else createCategory(values);
  };

  return (
    <Purchase
      headerText={edit ? "Edit Category" : "Add Category"}
      edit={edit}
      editData={editData}
      onClose={close}
      formFileds={formFields}
      headerClassName="max-w-screen-md"
      formDivClassName="md:grid-cols-2"
      submitFn={onSubmit}
      validationSchema={validationSchema}
    />
  );
};

export default CategoryForm;
