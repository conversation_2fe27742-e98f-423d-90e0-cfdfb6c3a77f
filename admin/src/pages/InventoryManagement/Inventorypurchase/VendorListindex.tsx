import { useMemo, useState } from "react";
import MasterTable from "../../../layouts/Table/MasterTable";
import { TableAction } from "../../../layouts/Table/TableAction";
import {
  useDeleteVendor,
  useGetAllVendor,
} from "../../../server-action/API/vendorApi";
import VendorForm from "./components/VendorForm";

const VendorListindex = () => {
  const [modal, setModal] = useState({
    state: "",
    edit: true,
    editData: null,
  });
  const { mutate: deleteVendor } = useDeleteVendor();
  const { data, isLoading, isSuccess } = useGetAllVendor();
  const columns = useMemo(
    () => [
      {
        title: "Vendor Name",
        key: "name",
        sortable: true,
      },

      {
        title: "Contact Person",
        key: "contactPerson",
      },
      {
        title: "Contact No",
        key: "phone",
      },
      {
        title: "Email",
        key: "email",
      },
      {
        title: "Address",
        key: "address",
      },
      { title: "action", key: "action" },
    ],
    []
  );

  const deleteHandler = (id: string) => {
    deleteVendor(id);
  };
  const rows = isSuccess
    ? data?.map((item: any) => ({
        ...item,
        // Ensure createdAt is included for sorting
        createdAt: item.createdAt || new Date().toISOString(),
        action: (
          <TableAction
            onEdit={() =>
              setModal((prev) => ({ ...prev, state: "edit", editData: item }))
            }
            onDelete={() => deleteHandler(item._id)}
          />
        ),
      }))
    : [];

  return (
    <div>
      <div className="bg-white">
        <MasterTable
          columns={columns}
          rows={rows}
          loading={isLoading}
          sortBy="createdAt"
          sortOrder="desc"
        />
        {(modal.state === "edit" || modal.state === "add") && (
          <VendorForm
            close={() => setModal((prev) => ({ ...prev, state: "" }))}
            edit={modal.edit}
            editData={modal.editData}
          />
        )}
      </div>
    </div>
  );
};
export default VendorListindex;
