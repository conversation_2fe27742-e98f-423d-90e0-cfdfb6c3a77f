import { Form, FormikProvider, useFormik } from "formik";
import moment from "moment";
import React, { useEffect, useMemo, useState } from "react";
import * as Yup from "yup";

// Component and Layout Imports
import MasterTable from "../../../../layouts/Table/MasterTable";
import { TableAction } from "../../../../layouts/Table/TableAction";
import {
  useGetAllCategory,
  useGetAllProduct,
} from "../../../../server-action/API/productApi";
import {
  useGetAllPurchase,
  useUpdatePurchaseMutation,
} from "../../../../server-action/API/purchaseApi";
import { useGetAllVendor } from "../../../../server-action/API/vendorApi";
import { FormField } from "../../../BookingManagement/components/ReservationCustomForm";
import HeadingPopup from "../../../GuestManagement/components/HeadingPopup";

// API Hooks

// Type Definitions
interface Vendor {
  _id: string;
  name: string;
}

interface Product {
  _id: string;
  name: string;
  category?: { _id: string };
}

interface PurchaseItem {
  item: string;
  category: string;
  unit: string;
  orderedQuantity: string;
}

interface DetailedPurchaseItem {
  item: {
    _id: string;
    name: string;
    category: {
      _id: string;
      name: string;
    };
  };
  unit: string;
  orderedQuantity: string;
}

export interface Purchase {
  _id: string;
  vendor: Vendor;
  orderNo: string;
  billNo: string;
  orderedDate: string;
  purchaseItems: DetailedPurchaseItem[];
}

interface PurchaseFormValues {
  vendor: string;
  orderNo: string;
  billNo: string;
  date: string;
  items: PurchaseItem[];
  status?: string;
}

interface PurchaseListFormProps {
  close: () => void;
  edit?: boolean;
  editData?: any;
}

const PurchaseListForm: React.FC<PurchaseListFormProps> = ({
  close,
  edit = false,
  editData,
}) => {
  // API Data Hooks
  const { data: vendors = [], isSuccess: vendorSuccess } = useGetAllVendor();
  const { data: products = [] } = useGetAllProduct();
  const { data: categories = [] } = useGetAllCategory();
  const { data: purchases = [] } = useGetAllPurchase();

  // Mutation Hooks
  const { mutate: updateOrder, isSuccess: updateSuccess } =
    useUpdatePurchaseMutation();

  // Initial Form Values
  const initialFormValues: PurchaseFormValues = {
    vendor: edit && editData && editData.vendor ? editData.vendor._id : "",
    orderNo: edit && editData ? editData.orderNo : "",
    billNo: edit && editData ? editData.billNo : "",
    date:
      edit && editData ? moment(editData.orderedDate).format("YYYY-MM-DD") : "",
    items:
      edit && editData && editData.purchaseItems
        ? editData.purchaseItems.map((item: any) => ({
            item: item.item?._id || "",
            category: item.item?.category?._id || "",
            unit: item.unitCost || "",
            orderedQuantity: item.orderedQuantity || "",
          }))
        : [],
  };

  // Validation Schema
  const validationSchema = Yup.object().shape({
    vendor: Yup.string().required("Vendor is required"),
    date: Yup.string().required("Date is required"),
    items: Yup.array().of(
      Yup.object().shape({
        item: Yup.string().required("Item is required"),
        category: Yup.string().required("Category is required"),
        unit: Yup.string().required("Unit is required"),
        orderedQuantity: Yup.string().required("Quantity is required"),
      })
    ),
  });

  // Form State for New Item
  const [formValues, setFormValues] = useState<PurchaseItem>({
    item: "",
    category: "",
    unit: "",
    orderedQuantity: "",
  });

  // Formik Setup
  const formik = useFormik<PurchaseFormValues>({
    initialValues: initialFormValues,
    validationSchema,
    enableReinitialize: true,
    onSubmit: async (values) => {
      const currentPurchase = purchases.find(
        (item: any) => item.orderNo === values.orderNo
      );

      if (!currentPurchase) {
        console.error("Purchase not found");
        return;
      }

      const finalData = {
        ...currentPurchase,
        purchasedDate: values.date,
        vendor: values.vendor,
        billNo: values.billNo,
        purchaseItems:
          currentPurchase.purchaseItems?.map((item: any) => ({
            ...item,
            item: values.items[0]?.item || item.item?._id || "",
            unitCost: values.items[0]?.unit || item.unit || "",
            quantity:
              values.items[0]?.orderedQuantity || item.orderedQuantity || "",
          })) || [],
      };

      updateOrder({
        id: currentPurchase._id,
        body: finalData,
      });
    },
  });

  // Effect to handle form submission
  useEffect(() => {
    if (updateSuccess) {
      formik.resetForm();
      close();
    }
  }, [updateSuccess]);

  // Add Item Handler
  const handleAddItem = () => {
    const { item, category, orderedQuantity, unit } = formValues;

    if (item && category && orderedQuantity && unit) {
      formik.setFieldValue("items", [...formik.values.items, formValues]);
      setFormValues({ item: "", category: "", unit: "", orderedQuantity: "" });
    }
  };

  // Table Columns
  const columns = useMemo(
    () => [
      { title: "Item Name", key: "name" },
      { title: "Category", key: "category" },
      { title: "Ordered Quantity", key: "orderedQuantity" },
      { title: "UnitCost", key: "unit" },
      { title: "Total Amount", key: "totalAmount" },
      { title: "Action", key: "action" },
    ],
    []
  );

  // Table Rows
  const rows = formik.values.items.map((item, index) => ({
    ...item,
    category:
      categories.find((cat: any) => cat._id === item.category)?.name ||
      "Unknown",
    name:
      products.find((prod: any) => prod._id === item.item)?.name || "Unknown",
    totalAmount: Number(item.orderedQuantity) * Number(item.unit),
    action: (
      <TableAction
        onDelete={() =>
          formik.setFieldValue(
            "items",
            formik.values.items.filter((_, i) => i !== index)
          )
        }
      />
    ),
  }));

  // Vendor Options
  const vendorOptions = useMemo(
    () =>
      vendorSuccess
        ? vendors.map((v: Vendor) => ({
            label: v?.name,
            value: v._id,
          }))
        : [],
    [vendorSuccess, vendors]
  );

  // Order Number Options
  const orderNoOptions = useMemo(
    () =>
      formik.values.vendor
        ? purchases
            .filter(
              (p: Purchase) =>
                p?.vendor && p?.vendor?._id === formik.values.vendor
            )
            .map((item: Purchase) => ({
              label: item?.orderNo,
              value: item?.orderNo,
            }))
        : [],
    [formik.values.vendor, purchases]
  );

  // Item Options
  const itemOptions = useMemo(() => {
    if (!formik.values.orderNo) return [];

    const matchingPurchase = purchases.find(
      (p: Purchase) => p.orderNo === formik.values.orderNo
    );

    if (!matchingPurchase || !matchingPurchase.purchaseItems) return [];

    return matchingPurchase.purchaseItems.map((item: DetailedPurchaseItem) => ({
      label: item?.item?.name,
      value: item?.item?._id,
      unit: item?.unit || "",
      orderedQuantity: item?.orderedQuantity || "",
    }));
  }, [formik.values.orderNo, purchases]);

  return (
    <HeadingPopup
      heading={edit ? "Edit Purchase List" : "Add Purchase List"}
      onClose={close}
      className="lg:max-w-screen-xl"
    >
      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit}>
          <div className="flex flex-col gap-4">
            {/* Header Form Fields */}
            <div className="grid grid-cols-1 gap-4 lg:grid-cols-4 sm:grid-cols-2">
              <FormField
                label="Purchase Date"
                formik={formik}
                name="date"
                type="date"
              />
              <FormField
                label="Vendor Name"
                name="vendor"
                type="dropdown"
                formik={formik}
                options={vendorOptions}
                placeholder="Select Vendor"
              />

              <FormField
                label="Order Id"
                name="orderNo"
                type="dropdown"
                formik={formik}
                options={orderNoOptions}
                placeholder="Select Order Id"
              />
              <FormField
                label="Bill No"
                name="billNo"
                type="text"
                formik={formik}
                placeholder="Enter Bill Number"
              />
            </div>

            {/* Item Details */}
            <div className="grid grid-cols-3 gap-4">
              <FormField
                label="Item Name"
                name="item"
                type="dropdown"
                formik={formik}
                options={itemOptions}
                placeholder="Select Item"
                value={formValues.item}
                onChange={(e) => {
                  const selectedItem = products.find(
                    (p: Product) => p._id === e.target.value
                  );

                  // Find the item in itemOptions to get the unit price
                  const selectedItemOption = itemOptions.find(
                    (opt: any) => opt.value === e.target.value
                  );

                  // Get unit price from the selected item option
                  const unitPrice = selectedItemOption?.unit || "";
                  const quantity = selectedItemOption?.orderedQuantity || "";

                  setFormValues({
                    ...formValues,
                    item: e.target.value,
                    category: selectedItem?.category?._id || "",
                    unit: unitPrice,
                    orderedQuantity: quantity,
                  });
                }}
              />

              <FormField
                label="Quantity"
                name="quantity"
                type="text"
                formik={formik}
                placeholder="Enter Quantity"
                value={formValues.orderedQuantity}
                onChange={(e) =>
                  setFormValues({
                    ...formValues,
                    orderedQuantity: e.target.value,
                  })
                }
              />

              <FormField
                label="Unit Price"
                name="unit"
                type="text"
                formik={formik}
                placeholder="Enter Unit"
                value={formValues.unit}
                onChange={(e) =>
                  setFormValues({
                    ...formValues,
                    unit: e.target.value,
                  })
                }
              />
            </div>

            {/* Add Item Button */}
            <div className="flex items-center justify-end">
              <button
                type="button"
                className="px-4 py-2 text-white rounded bg-light-secondary"
                onClick={handleAddItem}
                disabled={
                  !formValues.item ||
                  !formValues.category ||
                  !formValues.orderedQuantity ||
                  !formValues.unit
                }
              >
                Add Item
              </button>
            </div>
          </div>

          {/* Items Table */}
          <div className="mt-4">
            <MasterTable columns={columns} loading={false} rows={rows} />
          </div>

          {/* Submit Button */}
          <div className="flex justify-end mt-4">
            <button
              type="submit"
              className="px-4 py-2 text-white rounded bg-light-secondary"
            >
              {edit ? "Update Order" : "Create Order"}
            </button>
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default PurchaseListForm;
