import { Form, FormikProvider, useFormik } from "formik";
import { get } from "lodash";
import moment from "moment";
import React, { useEffect, useMemo, useState } from "react";
import * as Yup from "yup";
import MasterTable from "../../../../layouts/Table/MasterTable";
import { TableAction } from "../../../../layouts/Table/TableAction";
import {
  useGetAllCategory,
  useGetAllProduct,
} from "../../../../server-action/API/productApi";
import {
  useCreatePurchaseMutation,
  useUpdatePurchaseMutation,
} from "../../../../server-action/API/purchaseApi";
import { useGetAllVendor } from "../../../../server-action/API/vendorApi";
import { FormField } from "../../../BookingManagement/components/ReservationCustomForm";
import HeadingPopup from "../../../GuestManagement/components/HeadingPopup";
import { IForm } from "./VendorForm";

// Define types for form state
interface Item {
  item: string;
  category: string;
  unit: string;
  orderedQuantity: string;
}

interface IFormData {
  status?: string;
  vendor?: string;
  orderedDate?: string;
  purchaseItems?: Item[];
}
// Form validation schema
const validationSchema = Yup.object().shape({
  vendor: Yup.string().required("Vendor is required"),
  date: Yup.string().required("Date is required"),
  items: Yup.array().of(
    Yup.object().shape({
      item: Yup.string().required("Item is required"),
      category: Yup.string().required("Category is required"),
      unit: Yup.string().required("Unit is required"),
      orderedQuantity: Yup.string().required("Quantity is required"),
    })
  ),
});

const PurchaseOrderForm: React.FC<IForm> = ({ close, edit, editData }) => {
  const { data: vendors, isSuccess: vendorSuccess } = useGetAllVendor();
  const { data: products, isSuccess: productSuccess } = useGetAllProduct();

  const { data: categories, isSuccess: categorySuccess } = useGetAllCategory();
  const { mutate: createOrder, isSuccess } = useCreatePurchaseMutation();
  const { mutate: updateOrder, isSuccess: updateSuccess } =
    useUpdatePurchaseMutation();
  const formik = useFormik({
    initialValues: {
      purchasedDate: edit ? get(editData, "purchasedDate", "") : "",
      vendor:
        edit && get(editData, "vendor") ? get(editData, "vendor._id", "") : "",
      date: edit
        ? moment(get(editData, "orderedDate", "")).format("YYYY-MM-DD")
        : "",
      status: edit ? get(editData, "status", "") : "",
      items:
        edit && get(editData, "purchaseItems", []).length > 0
          ? get(editData, "purchaseItems", []).map((item: any) => ({
              ...item,
              item: get(item, "item._id", ""),
              category: get(item, "item.category._id", ""),
              unit: get(item, "unit", ""),
              orderedQuantity: get(item, "orderedQuantity", ""),
            }))
          : [],
    },
    validationSchema,
    enableReinitialize: true,
    onSubmit: async (values) => {
      // Create the final data object for submission
      const finaldata: IFormData = {
        vendor: values.vendor,
        orderedDate: values.date,
        purchaseItems: values.items,
        status: edit ? values.status : "completed", // Set status to completed by default for new orders
      };
      if (edit) {
        updateOrder({ id: editData._id, body: finaldata });
      } else createOrder(finaldata);
    },
  });

  useEffect(() => {
    if (isSuccess || updateSuccess) {
      formik.resetForm();
      close();
    }
  }, [isSuccess, updateSuccess]);

  const [formValues, setFormValues] = useState<Item>({
    item: "",
    category: "",
    unit: "",
    orderedQuantity: "",
  });

  const handleAddItem = () => {
    if (formValues.item && formValues.category && formValues.orderedQuantity) {
      formik.setFieldValue("items", [...formik.values.items, formValues]);
      setFormValues({ item: "", category: "", unit: "", orderedQuantity: "" });
    }
  };
  // Remove console logs to prevent continuous re-rendering
  // Table columns
  const columns = useMemo(
    () => [
      { title: "Item Name", key: "name" },
      { title: "Category", key: "category" },
      { title: "Ordered Quantity", key: "orderedQuantity" },
      { title: "Unit", key: "unit" },
      { title: "Action", key: "action" },
    ],
    []
  );

  // Table rows
  const rows = formik.values.items.map((item: Item, index: number) => {
    // Find category and product with null checks
    const category = categories?.find(
      (cat: any) => cat && cat._id === item.category
    );
    const product = products?.find(
      (prod: any) => prod && prod._id === item.item
    );

    return {
      ...item,
      category: category?.name || "Unknown",
      name: product?.name || "Unknown",
      action: (
        <TableAction
          onDelete={() =>
            formik.setFieldValue(
              "items",
              formik.values.items.filter((_: string, i: number) => i !== index)
            )
          }
        />
      ),
    };
  });

  return (
    <HeadingPopup
      heading={edit ? "Edit Purchase Order" : "Add Purchase Order"}
      onClose={close}
      className="lg:max-w-screen-xl"
    >
      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit}>
          <div className="flex flex-col gap-4">
            {/* Vendor & Date Fields */}
            <div
              className={`grid ${edit ? "grid-cols-3" : "grid-cols-2"} gap-4`}
            >
              <FormField
                label="Vendor Name"
                name="vendor"
                type="dropdown"
                formik={formik}
                options={
                  vendorSuccess && vendors
                    ? vendors
                        .filter((v: any) => v && v._id)
                        .map((v: any) => ({
                          label: v.name || "Unknown",
                          value: v._id,
                        }))
                    : []
                }
                placeholder="Select Vendor"
              />
              <FormField
                label="Order Date"
                formik={formik}
                name="date"
                type="date"
              />
              {edit && (
                <FormField
                  label="Order Status"
                  name="status"
                  type="dropdown"
                  formik={formik}
                  options={[
                    { label: "Pending", value: "pending" },
                    { label: "Completed", value: "completed" },
                  ]}
                  placeholder="Select Vendor"
                />
              )}
            </div>

            {/* Item Details */}
            <div className="grid grid-cols-4 gap-4">
              {/* Item Name */}
              <FormField
                label="Item Name"
                name="item"
                type="dropdown"
                formik={formik}
                options={
                  productSuccess && products
                    ? products
                        .filter((p: any) => p && p._id)
                        .map((p: any) => ({
                          label: p.name || "Unknown",
                          value: p._id,
                        }))
                    : []
                }
                placeholder="Select Item"
                value={formValues.item}
                onChange={(e) => {
                  if (!e.target.value) return;

                  const selectedItem = products?.find(
                    (p: any) => p && p._id === e.target.value
                  );

                  // Get the unit price from the edit data if available
                  let unitPrice = "";
                  if (edit && editData && editData.purchaseItems) {
                    const existingItem = editData.purchaseItems.find(
                      (i: any) => get(i, "item._id", "") === e.target.value
                    );
                    if (existingItem) {
                      unitPrice = get(existingItem, "unit", "");
                    }
                  }

                  setFormValues({
                    ...formValues,
                    item: e.target.value,
                    category: selectedItem?.category?._id || "",
                    unit: unitPrice || "",
                  });
                }}
              />

              {/* Category */}
              <FormField
                label="Category"
                name="category"
                type="dropdown"
                formik={formik}
                options={
                  categorySuccess && categories
                    ? categories
                        .filter((c: any) => c && c._id)
                        .map((c: any) => ({
                          label: c.name || "Unknown",
                          value: c._id,
                        }))
                    : []
                }
                value={formValues.category}
              />

              {/* Ordered Quantity */}
              <FormField
                label="Ordered Quantity"
                name="orderedQuantity"
                type="text"
                formik={formik}
                placeholder="Enter Quantity"
                value={formValues.orderedQuantity}
                onChange={(e) =>
                  setFormValues({
                    ...formValues,
                    orderedQuantity: e.target.value,
                  })
                }
              />

              {/* Unit */}
              <FormField
                label="Unit"
                name="unit"
                type="text"
                formik={formik}
                placeholder="Enter Unit"
                value={formValues.unit}
                onChange={(e) =>
                  setFormValues({ ...formValues, unit: e.target.value })
                }
              />
            </div>

            {/* Add Item Button */}
            <div className="flex items-center justify-end">
              <button
                type="button"
                className="px-4 py-2 text-white rounded bg-light-secondary"
                onClick={handleAddItem}
                disabled={
                  !formValues.item ||
                  !formValues.category ||
                  !formValues.orderedQuantity
                }
              >
                Add Item
              </button>
            </div>
          </div>

          <div className="mt-4">
            <MasterTable columns={columns} loading={false} rows={rows} />
          </div>
          <div className="flex justify-end mt-4">
            <button
              type="submit"
              className="px-4 py-2 text-white rounded bg-light-secondary"
            >
              {edit ? "Update Order" : "Create Order"}
            </button>
          </div>
        </Form>
      </FormikProvider>

      {/* Table Display */}
    </HeadingPopup>
  );
};

export default PurchaseOrderForm;
