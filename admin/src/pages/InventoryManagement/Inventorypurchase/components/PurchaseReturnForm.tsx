import { Form, FormikProvider, useFormik } from "formik";
import { get } from "lodash";
import moment from "moment";
import React, { useEffect, useMemo, useState } from "react";
import * as Yup from "yup";

// Component and Layout Imports
import MasterTable from "../../../../layouts/Table/MasterTable";
import { TableAction } from "../../../../layouts/Table/TableAction";
import { useGetAllProduct } from "../../../../server-action/API/productApi";
import {
  IPurchaseItems as BaseIPurchaseItems,
  useCreatePurchaseReturnMutation,
  useUpdatePurchaseReturnMutation,
} from "../../../../server-action/API/purchaseApi";

import { useGetAllVendor } from "../../../../server-action/API/vendorApi";

// Extended interface to include optional name property for display purposes
interface IPurchaseItems extends BaseIPurchaseItems {
  name?: string;
}

import { FormField } from "../../../BookingManagement/components/ReservationCustomForm";
import HeadingPopup from "../../../GuestManagement/components/HeadingPopup";

// Type Definitions
interface Vendor {
  _id: string;
  name: string;
}

// Product interface used for type checking
interface Product {
  _id: string;
  name: string;
  category?: { _id: string };
  [key: string]: any;
}

// interface Item {
//   item: string;
//   category: string;
//   unit: string;
//   orderedQuantity: string;
//   reason?: string;
// }

interface DetailedPurchaseItem {
  item: {
    _id: string;
    name: string;
    category: {
      _id: string;
      name: string;
    };
  };
  unit: string;
  orderedQuantity: string;
}

export interface Purchase {
  _id: string;
  vendor: Vendor;
  orderNo: string;
  billNo: string;
  orderedDate: string;
  purchaseItems: DetailedPurchaseItem[];
}

interface PurchaseFormValues {
  vendor: string;
  orderNo: string;
  billNo: string;
  reason: string;
  date: string;
  items: IPurchaseItems[];
}

interface PurchaseListFormProps {
  close: () => void;
  edit?: boolean;
  editData?: any;
  returnItems?: any;
  purchaseData?: any;
}

const PurchaseReturnForm: React.FC<PurchaseListFormProps> = ({
  close,
  edit = false,
  editData,
  returnItems = [],
  purchaseData,
}) => {
  // API Data Hooks
  const { data: vendors = [], isSuccess: vendorSuccess } = useGetAllVendor();
  const { data: products = [] } = useGetAllProduct();

  // Mutation Hooks
  const { mutate: updateReturn, isSuccess: updateSuccess } =
    useUpdatePurchaseReturnMutation();
  const { mutate: createReturn, isSuccess: createSuccess } =
    useCreatePurchaseReturnMutation();

  // Process return items to ensure they have proper structure
  const processInitialReturnItems = (items: any[]) => {
    return items.map((item: any) => {
      // If the item is already in the correct format, return it as is
      if (typeof item.item === "string") {
        return {
          ...item,
          name:
            products.find((p: any) => p._id === item.item)?.name ||
            get(item, "name", "Unknown"),
        };
      }

      // If the item has a nested structure, flatten it
      if (item.item && item.item._id) {
        return {
          ...item,
          name: item.item.name, // Store name for display
          item: item.item._id, // Store ID for API
        };
      }

      return item;
    });
  };

  const initialFormValues = {
    vendor: edit
      ? get(purchaseData, "vendor._id", "") ||
        get(editData, "purchase.vendor._id", "")
      : get(editData, "vendor._id", ""),
    orderNo: edit
      ? get(purchaseData, "orderNo", "") ||
        get(editData, "purchase.orderNo", "")
      : get(editData, "orderNo", ""),
    billNo: edit
      ? get(purchaseData, "billNo", "") ||
        get(editData, "purchase.billNo", "") ||
        get(editData, "billNo", "")
      : get(editData, "billNo", ""),
    reason: edit ? get(editData, "reason", "") : "",
    date: (() => {
      // For edit mode, try to get the date from multiple possible sources
      if (edit && editData) {
        // First try to get the date directly from the purchase return
        const returnDate = get(editData, "returnDate", "");
        if (returnDate) {
          return moment(returnDate).format("YYYY-MM-DD");
        }

        // Then try purchasedDate which might be used in some cases
        const purchasedDate = get(editData, "purchasedDate", "");
        if (purchasedDate) {
          return moment(purchasedDate).format("YYYY-MM-DD");
        }

        // Finally try orderedDate as a fallback
        const orderedDate = get(editData, "orderedDate", "");
        if (orderedDate) {
          return moment(orderedDate).format("YYYY-MM-DD");
        }
      }

      // Default to current date if no date is found or in create mode
      return moment().format("YYYY-MM-DD");
    })(),
    items:
      edit && returnItems.length > 0
        ? processInitialReturnItems(returnItems)
        : [],
  };

  // Helper function to get purchased quantity for an item
  const getPurchasedQuantity = (itemId: string) => {
    // Find the item in the purchase items
    const purchaseItem = purchaseItemsSource.find(
      (item: any) => get(item, "item._id", "") === itemId
    );

    // Return the ordered quantity if found, otherwise 0
    return purchaseItem ? Number(get(purchaseItem, "orderedQuantity", 0)) : 0;
  };

  // Helper function to get the total quantity already added to the form for a specific item
  const getFormReturnedQuantity = (itemId: string) => {
    // Sum up quantities for this item in the current form
    return formik.values.items
      .filter((item) => item.item === itemId)
      .reduce((total, item) => total + Number(item.quantity), 0);
  };

  // Validation Schema
  const validationSchema = Yup.object().shape({
    vendor: Yup.string().required("Vendor is required"),
    reason: Yup.string().required("Reason is required"),
    date: Yup.string()
      .required("Return date is required")
      .test("is-valid-date", "Return date must be a valid date", (value) => {
        if (!value) return false;
        return moment(value, "YYYY-MM-DD", true).isValid();
      })
      .test("is-not-future", "Return date cannot be in the future", (value) => {
        if (!value) return true; // Skip this validation if no date is provided
        const selectedDate = moment(value, "YYYY-MM-DD");
        const today = moment().startOf("day");
        return selectedDate.isSameOrBefore(today);
      }),
    items: Yup.array().of(
      Yup.object().shape({
        item: Yup.string().required("Item is required"),
        unitCost: Yup.string().required("Unit cost is required"),
        quantity: Yup.string()
          .required("Quantity is required")
          .test(
            "is-positive",
            "Quantity must be greater than 0",
            (value) => Number(value) > 0
          )
          .test(
            "not-zero",
            "Quantity cannot be 0",
            (value) => Number(value) !== 0
          )
          .test("not-more-than-purchased", function (value) {
            const itemId = this.parent.item;
            const returnQuantity = Number(value);
            const purchasedQuantity = getPurchasedQuantity(itemId);

            // For simplicity, we'll just check if the current quantity exceeds the purchased quantity
            // The more complex validation for total quantities is handled in the handleAddItem function
            if (returnQuantity > purchasedQuantity) {
              return this.createError({
                message: `Cannot return more than purchased quantity. You can return up to ${purchasedQuantity} items.`,
              });
            }
            return true;
          }),
        unit: Yup.string().required("Unit is required"),
      })
    ),
  });

  // State for form item input
  const [formValues, setFormValues] = useState<IPurchaseItems>({
    item: "",
    unitCost: 0,
    unit: "",
    quantity: 0,
  });

  // Formik Setup
  const formik = useFormik<PurchaseFormValues>({
    initialValues: initialFormValues,
    validationSchema,
    enableReinitialize: true,
    onSubmit: async (values) => {
      const totalCost = values.items.reduce(
        (acc: number, item) =>
          acc + Number(item.unitCost) * Number(item.quantity),
        0
      );

      if (edit && editData) {
        // For update operation, use the purchase return ID from editData
        const purchaseReturnId = get(editData, "_id", "");
        const purchaseId =
          get(editData, "purchase._id", "") ||
          (purchaseData ? get(purchaseData, "_id", "") : "");

        if (!purchaseReturnId) {
          return;
        }

        const submissionData = {
          purchase: purchaseId,
          returnItems: values.items,
          totalCost,
          reason: values.reason,
          returnDate: values.date,
        };

        updateReturn({
          id: purchaseReturnId,
          body: submissionData,
        });
      } else {
        // For create operation
        const purchaseId = get(editData, "_id", "");

        if (!purchaseId) {
          return;
        }

        const submissionData = {
          purchase: purchaseId,
          returnItems: values.items,
          totalCost,
          reason: values.reason,
          returnDate: values.date,
        };

        createReturn(submissionData);
      }
    },
  });

  // Effect to handle form submission
  useEffect(() => {
    if (updateSuccess || createSuccess) {
      formik.resetForm();
      close();
    }
  }, [updateSuccess, createSuccess]);

  // Add Item Handler
  const handleAddItem = () => {
    const { item, quantity, unitCost, unit } = formValues;

    if (item && quantity && unitCost && unit) {
      // Check if quantity is zero
      const newReturnQuantity = Number(quantity);
      if (newReturnQuantity === 0) {
        formik.setFieldError("items", "Quantity cannot be 0");
        return;
      }

      // Check if return quantity exceeds purchased quantity
      const purchasedQuantity = getPurchasedQuantity(item);
      const existingReturnQuantity = getFormReturnedQuantity(item);
      const totalReturnQuantity = existingReturnQuantity + newReturnQuantity;

      if (totalReturnQuantity > purchasedQuantity) {
        // Calculate how many more items can be returned
        const remainingQuantity = Math.max(
          0,
          purchasedQuantity - existingReturnQuantity
        );

        // Show error message
        formik.setFieldError(
          "items",
          `Cannot return more than purchased quantity. You can return up to ${remainingQuantity} items.`
        );
        return;
      }

      formik.setFieldValue("items", [...formik.values.items, formValues]);
      setFormValues({
        item,
        quantity,
        unitCost,
        unit,
      });
    }
  };

  const columns = useMemo(
    () => [
      { title: "Item Name", key: "name" },
      { title: "Returned Quantity", key: "returnedQuantity" },
      { title: "Unit Cost", key: "unit" },
      { title: "Total Amount", key: "totalAmount" },
      { title: "Action", key: "action" },
    ],
    []
  );

  // Table Rows
  const rows = formik.values.items.map((item, index) => {
    // Try to find the product name from multiple sources
    const productName = (() => {
      // First try to find in products array
      const productFromList = products.find(
        (prod: any) => prod._id === item?.item
      );
      if (productFromList?.name) {
        return productFromList.name;
      }

      // Then try to find in itemOptions
      const itemOption = itemOptions.find(
        (opt: any) => opt.value === item?.item
      );
      if (itemOption?.label && itemOption.label !== "-") {
        return itemOption.label;
      }

      // Finally check if the item itself has a name property (from returnItems)
      if (item?.name) {
        return item.name;
      }

      // If all else fails, return Unknown
      return "Unknown";
    })();

    return {
      ...item,
      name: productName,
      returnedQuantity: item.quantity,
      unitCost: item.unitCost,
      unit: item.unit,
      totalAmount: Number(item.quantity) * Number(item.unitCost),
      action: (
        <TableAction
          onDelete={() =>
            formik.setFieldValue(
              "items",
              formik.values.items.filter((_, i) => i !== index)
            )
          }
        />
      ),
    };
  });

  // Vendor Options
  const vendorOptions = useMemo(
    () =>
      vendorSuccess
        ? vendors.map((v: Vendor) => ({
            label: v.name,
            value: v._id,
          }))
        : [],
    [vendorSuccess, vendors]
  );

  // Get purchase items from the appropriate source based on edit mode
  const purchaseItemsSource = edit
    ? purchaseData
      ? get(purchaseData, "purchaseItems", [])
      : get(editData, "purchase.purchaseItems", []) || []
    : get(editData, "purchaseItems", []);

  const itemOptions = purchaseItemsSource.map((item: any) => {
    const itemName = get(item, "item.name", "-");
    const itemId = get(item, "item._id", "-");
    const unitCost = get(item, "unitCost", 0);
    const unitType = get(item, "unit", "");

    // Create a product item with the correct structure
    const productItem: Product = {
      _id: itemId,
      name: itemName,
      category: get(item, "item.category", undefined),
      unitCost: unitCost,
      unit: unitType,
    };

    return {
      label: productItem.name,
      value: productItem._id,
      unitCost: unitCost,
      unit: unitType,
      ...item,
    };
  });

  return (
    <HeadingPopup
      heading="Purchase Return"
      onClose={close}
      className="lg:max-w-screen-xl"
    >
      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit}>
          <div className="flex flex-col gap-4">
            {/* Header Form Fields */}
            <div className="grid grid-cols-1 gap-4 lg:grid-cols-3 sm:cd md:grid-cols-2">
              <FormField
                label="Return Date"
                formik={formik}
                name="date"
                type="date"
                required={true}
                placeholder="Select Return Date"
              />
              <FormField
                label="Vendor Name"
                name="vendor"
                type="dropdown"
                formik={formik}
                options={vendorOptions}
                placeholder="Select Vendor"
              />
              <FormField
                label="Bill No"
                name="billNo"
                type="text"
                formik={formik}
                placeholder="Enter Bill Number"
              />
              <FormField
                label="Reason"
                name="reason"
                type="text"
                formik={formik}
                placeholder="Enter Reason"
              />
            </div>

            {/* Item Details */}
            <div className="grid grid-cols-4 gap-4">
              <FormField
                label="Item Name"
                name="item"
                type="dropdown"
                formik={formik}
                options={itemOptions}
                placeholder="Select Item"
                value={formValues.item}
                onChange={(e) => {
                  const selectedValue = e.target.value;
                  // Try to find the selected item in itemOptions
                  const selectedItem = itemOptions.find(
                    (p: any) =>
                      p.value === selectedValue ||
                      p?.item?._id === selectedValue
                  );

                  if (selectedItem) {
                    // Get product details from the selected item
                    const productName =
                      selectedItem.label ||
                      (selectedItem.item && selectedItem.item.name) ||
                      "Unknown";

                    // Ensure we get the unit cost from the selected item
                    const unitCost = selectedItem.unitCost || 0;
                    const unitType = selectedItem.unit || "";

                    setFormValues({
                      ...formValues,
                      item: selectedValue,
                      name: productName,
                      unitCost: unitCost,
                      unit: unitType,
                    });
                  } else {
                    setFormValues({
                      ...formValues,
                      item: selectedValue,
                    });
                  }
                }}
              />

              <FormField
                label="Quantity"
                name="quantity"
                type="number"
                formik={formik}
                placeholder="Enter Quantity"
                value={formValues.quantity}
                min="1"
                onChange={(e) => {
                  // Ensure the value is at least 1
                  const value = Math.max(1, parseInt(e.target.value) || 0);
                  setFormValues({
                    ...formValues,
                    quantity: value,
                  });
                }}
              />

              <FormField
                label="Unit Cost"
                name="unitCost"
                type="text"
                formik={formik}
                placeholder="Enter Unit Cost"
                value={formValues.unitCost}
                onChange={(e) =>
                  setFormValues({
                    ...formValues,
                    unitCost: e.target.value,
                  })
                }
              />
              <FormField
                label="Unit "
                name="unit"
                type="text"
                formik={formik}
                placeholder="Enter Unit Type"
                value={formValues.unit}
                onChange={(e) =>
                  setFormValues({
                    ...formValues,
                    unit: e.target.value,
                  })
                }
              />
            </div>

            <div className="flex flex-col items-end gap-2">
              {typeof formik.errors.items === "string" && (
                <div className="text-red text-sm">{formik.errors.items}</div>
              )}
              <button
                type="button"
                className="px-4 py-2 text-white rounded disabled:cursor-not-allowed bg-light-secondary"
                onClick={handleAddItem}
                disabled={
                  !formValues.item ||
                  !formValues.quantity ||
                  !formValues.unitCost ||
                  !formValues.unit
                }
              >
                Add Item
              </button>
            </div>
          </div>

          {/* Items Table */}
          <div className="mt-4">
            <MasterTable columns={columns} loading={false} rows={rows} />
          </div>

          {/* Submit Button */}
          <div className="flex justify-end mt-4">
            <button
              type="submit"
              className="px-4 py-2 text-white rounded bg-light-secondary"
            >
              {edit ? "Update Return" : "Create Return"}
            </button>
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default PurchaseReturnForm;
