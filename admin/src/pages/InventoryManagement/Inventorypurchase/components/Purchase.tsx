import { Form, FormikProvider, useFormik } from "formik";
import HeadingPopup from "../../../GuestManagement/components/HeadingPopup";
import { FormField } from "../../../BookingManagement/components/ReservationCustomForm";
import classNames from "classnames";

export interface IOption {
  label: string;
  value: string;
}
export interface IFieldConfig {
  type: string;
  name: string;
  label: string;
  option?: IOption[];
  placeholder?: string;
  disabled?: boolean;
  defaultValue?: string | string[];
  isMultipleFile?: boolean;
}
interface IPurchase {
  headerText: string;
  formFileds: IFieldConfig[];
  onClose: () => void;
  edit?: boolean;
  editData?: any;
  headerClassName?: string;
  formDivClassName?: string;
  submitFn: (values: any) => void;
  validationSchema: any;
}

const generateInitialValues = (fields: IFieldConfig[], editData?: any) => {
  return fields.reduce((values, field) => {
    values[field.name] = editData?.[field.name] ?? "";
    return values;
  }, {} as Record<string, string>);
};

const Purchase: React.FC<IPurchase> = ({
  headerText,
  formFileds,
  onClose,
  edit,
  editData,
  headerClassName = "w-full max-w-screen-lg",
  formDivClassName = "lg:grid-cols-3 md:grid-cols-2",
  submitFn,
  validationSchema, // ✅ added
}) => {
  const formik = useFormik({
    initialValues: generateInitialValues(
      formFileds,
      edit ? editData : undefined
    ),
    validationSchema, // ✅ added
    enableReinitialize: true,
    onSubmit: async (values) => {
      console.log("submitting form values", values);
      await submitFn(values);
    },
  });

  return (
    <HeadingPopup
      onClose={onClose}
      heading={headerText}
      className={headerClassName}
    >
      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit}>
          <div
            className={classNames("grid grid-cols-1 gap-4", formDivClassName)}
          >
            {formFileds.map((item) => (
              <FormField {...item} formik={formik} />
            ))}
          </div>
          <div className="flex items-center justify-end mt-4">
            <button
              type="submit"
              className="px-4 py-2 bg-[#2A3A6D] text-white rounded-md"
            >
              Submit
            </button>
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default Purchase;
