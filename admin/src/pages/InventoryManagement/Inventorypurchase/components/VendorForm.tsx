import React, { useEffect } from "react";
import Purchase from "./Purchase";
import {
  useCreateVendorMutation,
  useUpdateVendorMutation,
} from "../../../../server-action/API/vendorApi";
import * as yup from "yup";

const validationSchema = yup.object().shape({
  name: yup.string().required("Vendor name is required"),
  email: yup
    .string()
    .email("Invalid email format")
    .required("Email is required"),
  address: yup.string().required("Address is required"),
  contactPerson: yup.string().required("Contact person is required"),
  phone: yup.string().required("Phone number is required"),
  description: yup.string().required("Description is required"),
});

export interface IForm {
  close: () => void;
  edit?: boolean;
  editData?: any;
}

const VendorForm: React.FC<IForm> = ({ close, edit, editData }) => {
  const { mutate: createVendor, isSuccess } = useCreateVendorMutation();
  const { mutate: updateVendor, isSuccess: updateSuccess } =
    useUpdateVendorMutation();

  useEffect(() => {
    if (isSuccess || updateSuccess) {
      close();
    }
  }, [isSuccess, updateSuccess]);

  const formFields = [
    {
      name: "name",
      label: "Vendor Name",
      placeholder: "John Doe",
      type: "text",
    },
    {
      name: "email",
      label: "Email",
      placeholder: "<EMAIL>",
      type: "email",
    },
    {
      name: "address",
      label: "Address",
      placeholder: "Address",
      type: "text",
    },
    {
      name: "contactPerson",
      label: "Contact Person",
      placeholder: "John Doe",
      type: "text",
    },
    {
      name: "phone",
      label: "Phone Number",
      placeholder: "1234567890",
      type: "number",
    },
    {
      name: "description",
      label: "Description",
      placeholder: "Description",
      type: "text",
    },
  ];

  const onSubmit = (values: any) => {
    if (edit) updateVendor({ id: editData._id, body: values });
    else createVendor(values);
  };

  return (
    <Purchase
      submitFn={onSubmit}
      edit={edit}
      onClose={close}
      editData={editData}
      headerText={edit ? "Edit Vendor" : "Add Vendor"}
      formFileds={formFields}
      validationSchema={validationSchema}
    />
  );
};

export default VendorForm;
