import { get } from "lodash";
import moment from "moment";
import { useState, useEffect } from "react";
import MasterTable from "../../../layouts/Table/MasterTable";
import { TableAction } from "../../../layouts/Table/TableAction";
import {
  useDeletePurchaseReturnMutation,
  useGetAllPurchase,
  useGetAllPurchaseReturn,
} from "../../../server-action/API/purchaseApi";
import HeadingPopup from "../../GuestManagement/components/HeadingPopup";
import PurchaseReturnForm from "./components/PurchaseReturnForm";
interface IPopup {
  state: string;
  data: any;
  returnItems: any;
  purchaseData?: any;
}

// Helper function to format dates
const formatDate = (dateString: string) => {
  if (!dateString || dateString === "-") return "-";

  try {
    return moment(dateString).format("YYYY-MM-DD");
  } catch (error) {
    return dateString;
  }
};

const PurchaseReturnIndex = () => {
  const [showPopup, setShowPopup] = useState({
    state: "",
    data: null,
    returnItems: null,
  } as IPopup);
  const { data, isLoading, isSuccess } = useGetAllPurchaseReturn();
  const { data: purchaseData } = useGetAllPurchase();
  const { mutate: deletePurchaseReturn } = useDeletePurchaseReturnMutation();

  // Debug: Log the data structure to help identify issues
  useEffect(() => {
    if (isSuccess && data && data.length > 0) {
      console.log("Purchase Return Data Structure:", data[0]);
      console.log("Purchase Data Structure:", purchaseData?.[0]);
    }
  }, [data, purchaseData, isSuccess]);
  const tableData = {
    columns: [
      { title: "Return Bill No", key: "billNo" },
      { title: "Vendor Name", key: "vendor" },
      { title: "Total Return Cost ", key: "cost" },
      { title: "Return Date", key: "date" },
      { title: "Reason", key: "reason" },
      { title: "Action", key: "action" },
    ],
    rows: isSuccess
      ? data?.map((item: any) => {
          // Find the corresponding purchase data for this return
          const purchaseItem = purchaseData?.find(
            (p: any) =>
              p._id === get(item, "purchase", "") ||
              p._id === get(item, "purchase._id", "")
          );

          return {
            id: item._id,
            // Try multiple paths to get the bill number
            billNo:
              get(item, "purchase.billNo", "") ||
              get(purchaseItem, "billNo", "-"),
            // Try multiple paths to get the vendor name
            vendor:
              get(item, "purchase.vendor.name", "") ||
              get(purchaseItem, "vendor.name", "-"),
            cost: get(item, "totalCost", "-"),
            date: formatDate(
              get(item, "returnDate", get(item, "purchasedDate", "-"))
            ),
            reason: get(item, "reason", "-"),
            action: (
              <TableAction
                onShow={() =>
                  setShowPopup({ state: "view", data: item, returnItems: null })
                }
                onEdit={() => {
                  const selectedPurchase = purchaseData?.find(
                    (purchase: any) =>
                      purchase._id === get(item, "purchase._id", "") ||
                      purchase._id === get(item, "purchase", "")
                  );
                  setShowPopup({
                    state: "edit",
                    data: item,
                    returnItems: item.returnItems,
                    purchaseData: selectedPurchase,
                  });
                }}
                onDelete={() => deletePurchaseReturn(item._id)}
              />
            ),
          };
        })
      : [],
  };

  const viewColumns = [
    {
      title: "ITEM NAME",
      key: "item",
    },
    {
      title: "QUANTITY",
      key: "quantity",
    },
    {
      title: "UNIT COST",
      key: "cost",
    },
    {
      title: "UNIT",
      key: "unit",
    },
    {
      title: "TOTAL COST",
      key: "amount",
    },
  ];

  const viewRows = showPopup?.data?.returnItems
    ? showPopup?.data?.returnItems.map((item: any) => {
        const quantity = get(item, "quantity", 0);
        const unitCost = get(item, "unitCost", 0);
        const totalAmount = quantity * unitCost;

        return {
          item: get(item, "item.name", "-"),
          quantity: quantity || "-",
          cost: unitCost || "-",
          unit: get(item, "item.unit", "-"),
          amount: totalAmount ? `Rs ${totalAmount.toFixed(2)}` : "-",
        };
      })
    : [];
  // Find the corresponding purchase data for the selected item in the popup
  const selectedPurchaseData = showPopup.data
    ? purchaseData?.find(
        (p: any) =>
          p._id === get(showPopup, "data.purchase._id", "") ||
          p._id === get(showPopup, "data.purchase", "")
      )
    : null;

  const viewObj = {
    "Return Date": formatDate(
      get(
        showPopup,
        "data.returnDate",
        get(showPopup, "data.purchasedDate", "-")
      )
    ),
    "Bill No":
      get(showPopup, "data.purchase.billNo", "") ||
      get(selectedPurchaseData, "billNo", "-"),
    "Supplier Name":
      get(showPopup, "data.purchase.vendor.name", "") ||
      get(selectedPurchaseData, "vendor.name", "-"),
    Reason: get(showPopup, "data.reason", "-"),
  };
  return (
    <div>
      <div className="bg-white ">
        <MasterTable
          columns={tableData?.columns}
          rows={tableData.rows}
          loading={isLoading}
        />
      </div>
      {showPopup.state === "view" && (
        <HeadingPopup
          className="w-full max-w-xl"
          onClose={() =>
            setShowPopup({ state: "", data: null, returnItems: null })
          }
          heading="Purchase Return"
        >
          <div className="grid grid-cols-3 gap-4 mb-4">
            {Object.entries(viewObj).map(([key, value]) => (
              <div key={key} className="flex flex-col space-y-2">
                <label className="text-sm font-semibold text-gray-700">
                  {key}
                </label>
                <span className="p-2 text-sm text-gray-600 border-2 rounded-lg">
                  {value}
                </span>
              </div>
            ))}
          </div>
          <MasterTable
            showTopPageSelector={false}
            canSearch={false}
            columns={viewColumns}
            rows={viewRows}
            loading={false}
            entriesPerPage={10}
            canSelect={false}
          />
        </HeadingPopup>
      )}

      {showPopup.state === "edit" && (
        <PurchaseReturnForm
          edit={true}
          returnItems={showPopup.returnItems}
          editData={showPopup.data}
          purchaseData={showPopup.purchaseData}
          close={() =>
            setShowPopup({
              state: "",
              data: null,
              returnItems: null,
              purchaseData: null,
            })
          }
        />
      )}
    </div>
  );
};

export default PurchaseReturnIndex;
