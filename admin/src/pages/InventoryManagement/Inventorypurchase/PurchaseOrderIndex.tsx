import { get } from "lodash";
import moment from "moment";
import { useState } from "react";
import MasterTable from "../../../layouts/Table/MasterTable";
import { TableAction } from "../../../layouts/Table/TableAction";
import {
  useDeletePurchaseMutation,
  useGetAllPurchase,
} from "../../../server-action/API/purchaseApi";
import PurchaseOrderForm from "./components/PurchaseOrderForm";

interface IModal {
  state: string;
  edit: boolean;
  editData: any;
}
const PurchaseOrderIndex = () => {
  const [modal, setModal] = useState<IModal>({
    state: "",
    edit: true,
    editData: null,
  });
  const { data: purchases, isSuccess, isLoading } = useGetAllPurchase();
  const { mutate: deletePurchase } = useDeletePurchaseMutation();
  const tableData = {
    columns: [
      { title: "Order No", key: "order" },
      { title: "vendor Name", key: "vendor" },
      { title: "Date", key: "date" },
      { title: "status", key: "status" },
      { title: "Action", key: "action" },
    ],
    rows: isSuccess
      ? purchases?.map((item: any) => ({
          order: get(item, "orderNo", "-"),
          vendor: get(item, "vendor.name", "-"),
          date: moment(get(item, "orderedDate", "")).format("MMM-DD-YYYY"),
          status: get(item, "status", "-"),
          action: (
            <TableAction
              onEdit={() =>
                openModal({ state: "form", edit: true, editData: item })
              }
              onDelete={async () => await deletePurchase(item._id)}
            />
          ),
        }))
      : [],
  };
  const closeModal = () => {
    setModal({ state: "", edit: false, editData: null });
  };

  const openModal = ({ state, edit = false, editData = null }: IModal) => {
    setModal({ state, edit, editData });
  };
  return (
    <div>
      {/* <ReservationCustomForm /> */}
      <div className="bg-white ">
        <MasterTable
          columns={tableData?.columns}
          rows={tableData.rows}
          loading={isLoading}
        />
        {modal.state === "form" && (
          <PurchaseOrderForm
            close={closeModal}
            edit={modal.edit}
            editData={modal.editData}
          />
        )}
      </div>
    </div>
  );
};

export default PurchaseOrderIndex;
