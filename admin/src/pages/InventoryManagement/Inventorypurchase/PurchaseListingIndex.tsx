import { get } from "lodash";
import moment from "moment";
import { useMemo, useState } from "react";
import MasterTable from "../../../layouts/Table/MasterTable";
import { TableAction } from "../../../layouts/Table/TableAction";
import { useGetAllPurchase } from "../../../server-action/API/purchaseApi";
import HeadingPopup from "../../GuestManagement/components/HeadingPopup";
import PurchaseListForm from "./components/PurchaseListForm";
import PurchaseReturnForm from "./components/PurchaseReturnForm";
import { Status } from "../../../components/Status";

interface IPopup {
  state: string;
  editData: any;
}

const PurchaseListingIndex = () => {
  const [showPopup, setShowPopup] = useState<IPopup>({
    state: "",
    editData: null,
  });

  const { data: purchase, isSuccess, isLoading } = useGetAllPurchase();

  const columns = useMemo(
    () => [
      { key: "purchasedDate", title: "Purchased Date" },
      { key: "billNo", title: "Bill NO" },
      { key: "vendor", title: "Vendor" },
      { key: "status", title: "Status" },
      { key: "action", title: "Action" },
    ],
    []
  );

  const rows = isSuccess
    ? purchase.map((item: any) => ({
        orderNo: moment(get(item, "orderNo")),
        purchasedDate: moment(get(item, "purchasedDate")).format("MMM-DD-YYYY"),
        billNo: get(item, "billNo", "-"),
        vendor: get(item, "vendor.name", "-"),
        totalCost: get(item, "totalCost", "-"),
        status: <Status status={item?.status} />,
        action: (
          <div className="flex">
            <TableAction
              onShow={() => setShowPopup({ state: "view", editData: item })}
              onReturn={() => setShowPopup({ state: "add", editData: item })}
              onEdit={() => setShowPopup({ state: "edit", editData: item })}
            />
          </div>
        ),
      }))
    : [];

  const purchaseReturnColumns = useMemo(
    () => [
      { key: "item", title: "ITEM NAME" },
      { key: "category", title: "CATEGORY" },
      { key: "quantity", title: "QUANTITY" },
      { key: "cost", title: "UNIT COST" },
      { key: "totalCost", title: "TOTAL COST" },
    ],
    []
  );

  // Log purchase items data for debugging
  if (showPopup.state === "view") {
    console.log("Purchase Data:", showPopup.editData);
    if (showPopup?.editData?.purchaseItems) {
      console.log("Purchase Items Data:", showPopup.editData.purchaseItems);
    }
  }

  const returnPopup =
    showPopup.state === "view" && showPopup?.editData?.purchaseItems
      ? showPopup.editData.purchaseItems.map((item: any) => {
          // Extract item details with proper fallbacks
          // Handle both nested and flat structures
          const itemName =
            typeof item.item === "object"
              ? get(item, "item.name", "-")
              : get(item, "name", "-");

          const categoryName =
            typeof item.item === "object"
              ? get(item, "item.category.name", "-")
              : get(item, "category.name", get(item, "category", "-"));

          // For quantity, check multiple possible field names
          const quantity = get(
            item,
            "quantity",
            get(
              item,
              "orderedQuantity",
              get(
                item,
                "quantity",
                typeof item.item === "object"
                  ? get(item, "item.quantity", 0)
                  : 0
              )
            )
          );

          // For unit cost, check multiple possible field names
          const unitCost = get(
            item,
            "unitCost",
            get(
              item,
              "unit",
              get(
                item,
                "cost",
                typeof item.item === "object"
                  ? get(item, "item.unitCost", 0)
                  : 0
              )
            )
          );

          // Calculate total cost
          const totalCost = Number(quantity) * Number(unitCost);

          // Log individual item mapping for debugging
          console.log(
            `Item: ${itemName}, Quantity: ${quantity}, UnitCost: ${unitCost}, Total: ${totalCost}`
          );

          // Format the values for display
          const formattedQuantity = quantity !== 0 ? quantity : "-";
          const formattedUnitCost = unitCost !== 0 ? unitCost : "-";
          const formattedTotalCost =
            totalCost !== 0 ? `Rs ${totalCost.toFixed(2)}` : "-";

          return {
            item: itemName,
            category: categoryName,
            quantity: formattedQuantity,
            cost: formattedUnitCost,
            totalCost: formattedTotalCost,
          };
        })
      : [];

  return (
    <div>
      <div className="bg-white">
        <MasterTable
          canSelect={false}
          columns={columns}
          rows={rows}
          loading={isLoading}
        />
      </div>

      {/* Edit Form */}
      {showPopup.state === "edit" && (
        <PurchaseListForm
          edit={true}
          editData={showPopup.editData}
          close={() => setShowPopup({ state: "", editData: null })}
        />
      )}

      {/* Return Form */}
      {showPopup.state === "add" && (
        <PurchaseReturnForm
          editData={showPopup.editData}
          close={() => setShowPopup({ state: "", editData: null })}
        />
      )}

      {/* View Popup */}
      {showPopup.state === "view" && (
        <HeadingPopup
          className="w-full max-w-screen-md"
          heading="Purchase Details"
          onClose={() => setShowPopup({ state: "", editData: null })}
        >
          <div className="space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-3 sm:grid-cols-2">
              <div className="space-y-1">
                <p className="text-base font-medium">Vendor Name</p>
                <p className="px-2 py-1 text-sm border border-gray-200 rounded-md">
                  {get(showPopup, "editData.vendor.name", "-")}
                </p>
              </div>

              <div className="space-y-1">
                <p className="text-base font-medium">Purchase Date</p>
                <p className="px-2 py-1 text-sm border border-gray-200 rounded-md">
                  {moment(get(showPopup, "editData.purchasedDate", "-")).format(
                    "MMM-DD-YYYY"
                  )}
                </p>
              </div>

              <div className="space-y-1">
                <p className="text-base font-medium">Order No</p>
                <p className="px-2 py-1 text-sm border border-gray-200 rounded-md">
                  {get(showPopup, "editData.orderNo", "-")}
                </p>
              </div>

              <div className="space-y-1">
                <p className="text-base font-medium">Bill No</p>
                <p className="px-2 py-1 text-sm border border-gray-200 rounded-md">
                  {get(showPopup, "editData.billNo", "-")}
                </p>
              </div>

              <div className="space-y-1">
                <p className="text-base font-medium">Status</p>
                <p className="px-2 py-1 text-sm border border-gray-200 rounded-md capitalize">
                  {get(showPopup, "editData.status", "-")}
                </p>
              </div>

              <div className="space-y-1">
                <p className="text-base font-medium">Ordered Date</p>
                <p className="px-2 py-1 text-sm border border-gray-200 rounded-md">
                  {moment(get(showPopup, "editData.orderedDate", "-")).format(
                    "MMM-DD-YYYY"
                  )}
                </p>
              </div>
            </div>

            {/* Purchase Items Table */}
            <MasterTable
              canSelect={false}
              entriesPerPage={10}
              showTopPageSelector={false}
              canSearch={false}
              columns={purchaseReturnColumns}
              rows={returnPopup}
              loading={false}
              apiPagination={false}
            />
          </div>
        </HeadingPopup>
      )}
    </div>
  );
};

export default PurchaseListingIndex;
