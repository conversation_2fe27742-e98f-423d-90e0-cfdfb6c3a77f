import React from "react";
import * as Yup from "yup";
import {
  useCreateProduct,
  useGetAllCategory,
  useUpdateProduct,
} from "../../../server-action/API/productApi";
import Purchase from "../Inventorypurchase/components/Purchase";
import { IForm } from "../Inventorypurchase/components/VendorForm";

// Yup schema
const ProductValidationSchema = Yup.object().shape({
  category: Yup.string().required("Category is required"),
  name: Yup.string().required("Product name is required"),
  unit: Yup.string().required("Unit is required"),
  description: Yup.string().required("Description is required"),
});

const ProductListForm: React.FC<IForm> = ({
  close,
  edit = false,
  editData,
}) => {
  const { data, isSuccess } = useGetAllCategory();
  const { mutate: createProduct } = useCreateProduct();
  const { mutate: updateProduct } = useUpdateProduct();

  const formFields = [
    {
      name: "category",
      placeholder: "Select Category",
      label: "Category",
      type: "dropdown",
      options: isSuccess
        ? data?.map((item: any) => ({ label: item.name, value: item._id }))
        : [],
    },
    {
      name: "name",
      placeholder: "Product Name",
      label: "Product Name",
      type: "text",
    },
    {
      name: "unit",
      placeholder: "Unit",
      label: "Unit",
      type: "text",
    },
    {
      name: "description",
      placeholder: "Description",
      label: "Description",
      type: "text",
    },
  ];

  const onSubmit = (values: any) => {
    if (edit) updateProduct({ id: editData._id, body: values });
    else createProduct(values);
    close();
  };

  return (
    <Purchase
      headerText={edit ? "Edit Category" : "Add Category"}
      edit={edit}
      editData={editData}
      onClose={close}
      formFileds={formFields}
      headerClassName="max-w-screen-lg"
      formDivClassName="lg:grid-cols-3 md:grid-cols-2"
      submitFn={onSubmit}
      validationSchema={ProductValidationSchema}
    />
  );
};

export default ProductListForm;
