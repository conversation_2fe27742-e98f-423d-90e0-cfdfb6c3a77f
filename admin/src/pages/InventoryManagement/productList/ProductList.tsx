import { get } from "lodash";
import { useMemo, useState } from "react";
import Header from "../../../components/Header";
import MasterTable from "../../../layouts/Table/MasterTable";
import { TableAction } from "../../../layouts/Table/TableAction";
import {
  useDeleteProduct,
  useGetAllProduct,
} from "../../../server-action/API/productApi";
import ProductListForm from "./ProductListForm";

const ProductList = () => {
  const [openForm, setOpenForm] = useState({
    state: false,
    edit: false,
    editData: null,
  });
  const { data, isSuccess, isLoading } = useGetAllProduct();
  const { mutate: deleteProduct } = useDeleteProduct();

  const columns = useMemo(
    () => [
      {
        title: "S.N",
        key: "sn",
      },
      {
        title: "category",
        key: "category",
      },
      {
        title: "Produt Name",
        key: "name",
      },
      {
        title: "Unit",
        key: "unit",
      },
      {
        title: "Description",
        key: "description",
      },
      {
        title: "action",
        key: "action",
      },
    ],
    []
  );
  console.log(data, "data");
  const rows = isSuccess
    ? data?.map((item: any, index: number) => ({
        sn: index + 1,
        name: get(item, "name", "-"),
        description: get(item, "description", "-"),
        category: get(item, "category.name", "-"),
        unit: get(item, "unit", "-"),
        action: (
          <TableAction
            onEdit={() =>
              setOpenForm({
                state: true,
                edit: true,
                editData: { ...item, category: get(item, "category._id", "") },
              })
            }
            onDelete={() => deleteProduct(item._id)}
          />
        ),
      }))
    : [];
  return (
    <section>
      <Header
        title="Product"
        onAddClick={() =>
          setOpenForm({ state: true, edit: false, editData: null })
        }
      />
      {openForm.state && (
        <ProductListForm
          {...openForm}
          close={() => setOpenForm((prev) => ({ ...prev, state: false }))}
        />
      )}
      <div>
        <MasterTable rows={rows} columns={columns} loading={isLoading} />
      </div>
    </section>
  );
};

export default ProductList;
