import { useState } from "react";
import { Card } from "../../../components/Card";
import Header from "../../../components/Header";
import { AddSubStore } from "./componets/AddSubStore";
import SubstoreList from "./componets/SubstoreList";

export const SubStorePage = () => {
  const [modal, setModal] = useState(false);

  return (
    <div className="flex flex-col gap-4">
      <div>
        <Header title="Sub Store" onAddClick={() => setModal(true)} />
      </div>

      {modal && <AddSubStore onClose={() => setModal(false)} />}
      <SubstoreList />
    </div>
  );
};
