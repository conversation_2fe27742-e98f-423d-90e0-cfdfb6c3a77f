import { useState } from "react";
import { useGetInventory } from "../../../../server-action/API/inventoryApi";
import MasterTable from "../../../../layouts/Table/MasterTable";
import { TableAction } from "../../../../layouts/Table/TableAction";
import TransferSupplies from "../../MainStore/components/TransferSupplies";
import AssignHouseKeeping from "../../MainStore/components/AssignHouseKeeping";

const Substore = ({ id }: { id?: string }) => {
  const [popup, setPopup] = useState("");
  const [rowData, setRowData] = useState<any>(null);
  const { data, isLoading } = useGetInventory({ params: id });

  const closePopup = () => {
    setPopup("");
    setRowData(null);
  };

  const columns = [
    {
      key: "sn",
      title: "S.N",
    },
    {
      key: "name",
      title: "Item Name",
    },
    {
      key: "qty",
      title: "QTY in stock",
    },
    {
      key: "unit",
      title: "Unit",
    },
    {
      key: "reorder",
      title: "Reorder",
    },
  ];

  const rows =
    data?.map((item: any, index: number) => ({
      key: item.item?._id || index,
      sn: index + 1,
      name: item.item?.name || "N/A",
      qty: item.quantity || 0,
      unit: item.item?.unit || "N/A",
      reorder: item.reorder || 0,
      action: (
        <TableAction
          onTransfer={() => {
            setPopup("transfer");
            setRowData(item);
          }}
          onReturn={() => {
            setPopup("return");
            setRowData(item);
          }}
          onAssign={() => {
            setPopup("housekeeping");
            setRowData(item);
          }}
        />
      ),
    })) || [];

  return (
    <div>
      <MasterTable
        columns={columns}
        rows={rows}
        loading={isLoading}
        sortBy="createdAt"
        sortOrder="desc"
      />
    </div>
  );
};

export default Substore;
