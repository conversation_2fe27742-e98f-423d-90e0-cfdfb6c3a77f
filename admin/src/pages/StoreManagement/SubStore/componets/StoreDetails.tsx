import { format } from "date-fns";
import HeadingPopup from "../../../GuestManagement/components/HeadingPopup";

interface StoreDetailsProps {
  store: any;
  onClose: () => void;
}

const StoreDetails = ({ store, onClose }: StoreDetailsProps) => {
  if (!store) return null;

  const formatDate = (dateString: string) => {
    if (!dateString) return "N/A";
    try {
      return format(new Date(dateString), "dd MMM yyyy, hh:mm a");
    } catch (error) {
      return "Invalid Date";
    }
  };

  return (
    <HeadingPopup
      onClose={onClose}
      className="w-full max-w-screen-sm"
      heading="Store Details"
    >
      <div className="p-4">
        <div className="bg-gray-50 p-6 rounded-lg shadow-sm">
          <div className="grid grid-cols-2 gap-4">
            <div className="col-span-2">
              <h3 className="text-lg font-semibold text-blue-700 mb-2">
                {store.name}
              </h3>
              <div className="h-0.5 bg-gray-200 mb-4"></div>
            </div>

            <div>
              <p className="text-sm text-gray-500">Store Type</p>
              <p className="font-medium">
                {store.type === "floor" ? "Floor Store" : store.type}
              </p>
            </div>

            <div>
              <p className="text-sm text-gray-500">Location</p>
              <p className="font-medium">{store.location || "N/A"}</p>
            </div>

            <div>
              <p className="text-sm text-gray-500">Status</p>
              <p
                className={`font-medium ${
                  store.isActive ? "text-green-600" : "text-red-600"
                }`}
              >
                {store.isActive ? "Active" : "Inactive"}
              </p>
            </div>

            <div>
              <p className="text-sm text-gray-500">Hotel</p>
              <p className="font-medium">{store.hotel?.name || "N/A"}</p>
            </div>

            {store.description && (
              <div className="col-span-2 mt-2">
                <p className="text-sm text-gray-500">Description</p>
                <p className="font-medium">{store.description}</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </HeadingPopup>
  );
};

export default StoreDetails;
