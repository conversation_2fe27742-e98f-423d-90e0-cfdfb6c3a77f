import { Form, FormikProvider, useFormik } from "formik";
import HeadingPopup from "../../../GuestManagement/components/HeadingPopup";
import { GlobalForm } from "../../../../components/GlobalForm/GlobalFormComponent";

import { useCreateStore, useUpdateStore } from "../../../../server-action/API/StoreManagement/store";
import * as Yup from "yup";

interface propTypes {
  editData?: any;
  onClose: () => void;
}

const validationSchema = Yup.object().shape({
  name: Yup.string().required("Store name is required"),
  location: Yup.string().required("Location is required for floor stores"),
});

export const AddSubStore = ({ onClose, editData }: propTypes) => {
  const { mutate: createStore, isPending: isCreating } = useCreateStore();
  const { mutate: updateStore, isPending: isUpdating } = useUpdateStore();

  const subStoreData = [
    {
      field: "name",
      label: "Name",
      type: "text",
      placeholder: "Enter store name",
      required: true,
    },
    {
      field: "location",
      label: "Location",
      type: "text",
      placeholder: "Enter store location",
      required: true,
    },
    {
      field: "isActive",
      label: "Status",
      type: "select",
      options: [
        { label: "Active", value: "true" },
        { label: "Inactive", value: "false" },
      ],
    },
  ];

  const formik = useFormik({
    initialValues: {
      name: editData?.name ?? "",
      type: "floor",
      location: editData?.location ?? "",
      isActive: editData?.isActive ?? true,
    },
    validationSchema,
    enableReinitialize: true,
    onSubmit: async (values) => {
      if (editData) {
        await updateStore({ id: editData._id, data: values });
      } else {
        await createStore(values);
      }
      onClose();
    },
  });

  const { handleSubmit, getFieldProps } = formik;
  const isPending = isCreating || isUpdating;

  return (
    <HeadingPopup
      onClose={onClose}
      className="w-full max-w-screen-sm"
      heading={`${!editData ? "Add" : "Update"} Sub Store`}
    >
      <FormikProvider value={formik}>
        <Form onSubmit={handleSubmit}>
          <div className="grid grid-cols-2 gap-3">
            <GlobalForm
              formik={formik}
              formDatails={subStoreData}
              getFieldProps={getFieldProps}
            />
          </div>
          <div className="flex justify-end mt-4 gap-3">
            <button
              type="button"
              onClick={onClose}
              className="bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded"
            >
              Cancel
            </button>
            <button
              className="bg-[#163381] text-white font-bold py-2 px-4 rounded"
              type="submit"
              disabled={isPending}
            >
              {isPending ? "Saving..." : "Submit"}
            </button>
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default AddSubStore;
