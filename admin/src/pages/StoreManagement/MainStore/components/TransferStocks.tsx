import { useGetInventory } from "../../../../server-action/API/inventoryApi";

const TransferStocks = ({ id }: { id: string }) => {
  const { data: inventoryData } = useGetInventory({
    params: { id },
  });
  console.log(id, "id");
  console.log("data", inventoryData);

  console.log(inventoryData, "inventory data");

  return (
    <span className="px-5 py-1 rounded-xl bg-white shadow-sm shadow-gray-400">
      {/* {isSuccess ? inventoryData?.quantity : 0} */}
    </span>
  );
};

export default TransferStocks;
