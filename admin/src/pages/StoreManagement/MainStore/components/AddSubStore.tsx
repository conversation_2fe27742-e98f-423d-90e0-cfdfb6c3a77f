import { Form, FormikProvider, useFormik } from "formik";
import HeadingPopup from "../../../GuestManagement/components/HeadingPopup";
import { GlobalForm } from "../../../../components/GlobalForm/GlobalFormComponent";
import { useGetAllFloorPlans } from "../../../../server-action/API/HotelConfiguration/floorPlan";
import { useCreateStore } from "../../../../server-action/API/StoreManagement/store";

interface propTypes {
  editData?: any;
  onClose: () => void;
}
export const AddSubStore = ({ onClose, editData }: propTypes) => {
  const { data: floorData } = useGetAllFloorPlans();
  const { mutate: createStore, isPending } = useCreateStore();
  const floorOptions = floorData?.map((floor) => ({
    label: floor?.name ?? "",
    value: floor?._id ?? "",
  }));
  const subStoreData = [
    {
      field: "name",
      label: "Name",
      type: "text",
      placeholder: "Enter name",
    },
    {
      field: "floor",
      label: "Floor",
      type: "select",
      options: floorOptions ?? [],
    },
  ];

  const formik = useFormik({
    initialValues: {
      name: editData?.name ?? "",
      floor: editData?.floor ?? "",
      type: "floor",
    },
    enableReinitialize: true,
    onSubmit: async (values) => {
      console.log(values);
      await createStore(values);
      onClose(); // Close the modal after successful store creation
    },
  });

  const { handleSubmit, getFieldProps } = formik;

  return (
    <HeadingPopup
      onClose={onClose}
      className="w-full max-w-screen-sm"
      heading={`${!editData ? "Add" : "Update"} Sub Store`}
    >
      <FormikProvider value={formik}>
        <Form onSubmit={handleSubmit}>
          <div className="grid grid-cols-2 gap-3">
            <GlobalForm
              formik={formik}
              formDatails={subStoreData}
              getFieldProps={getFieldProps}
            />
          </div>
          <button
            className="bg-[#163381] text-white font-bold py-2 px-4 mt-4 flex place-self-end rounded"
            type="submit"
            disabled={isPending}
          >
            Submit
          </button>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};
