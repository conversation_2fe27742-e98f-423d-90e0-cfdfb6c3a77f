import { Form, FormikProvider, useFormik } from "formik";
import HeadingPopup from "../../../GuestManagement/components/HeadingPopup";
import { useGetAllStore } from "../../../../server-action/API/StoreManagement/store";
import TransferStocks from "./TransferStocks";
const TransferSupplies = ({
  close,
}: {
  close?: () => void;
  edit?: boolean;
  editData?: any;
}) => {
  const { data: storeData } = useGetAllStore();
  const handleClose = () => {
    close?.();
  };
  const formik = useFormik({
    initialValues: {},
    enableReinitialize: false,
    onSubmit: (values) => {
      console.log(values);
    },
  });
  console.log("storeData", storeData);
  return (
    <HeadingPopup
      heading="Transfer Supplies"
      className="w-full max-w-screen-sm"
      onClose={handleClose}
    >
      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit}>
          <table className=" w-full text-center ">
            {storeData?.map((item, i) => {
              // console.log("item", item);
              return (
                <tbody>
                  <tr
                    className={` h-12 border-t border-gray-300 ${
                      i == 0 ? "bg-[#F1F6FD]" : ""
                    }`}
                  >
                    <td>{item.name ?? item.type}</td>
                    <td>
                      <TransferStocks id={item._id} />
                      Stock
                    </td>
                    <td>
                      {" "}
                      <input
                        name={item.name}
                        type="number"
                        className={`w-14 focus:outline-none rounded-xl px-2 bg-white shadow-sm shadow-gray-400 ${
                          i == 0 ? "hidden" : ""
                        }`}
                        min={0}
                        onChange={formik.handleChange}
                      />
                      Transfer
                    </td>
                  </tr>
                </tbody>
              );
            })}
          </table>
          <button className="px-4 py-2 text-white bg-[#163381] rounded-md">
            Transfer
          </button>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default TransferSupplies;
