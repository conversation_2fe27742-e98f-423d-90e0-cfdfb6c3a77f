import { Form, FormikProvider, useFormik } from "formik";
import HeadingPopup from "../../../GuestManagement/components/HeadingPopup";
import { FormField } from "../../../BookingManagement/components/ReservationCustomForm";
const AssignHouseKeeping = ({
  close,
  data,
}: {
  close?: () => void;
  data?: any;
}) => {
  const handleClose = () => {
    close?.();
  };
  const formik = useFormik({
    initialValues: {
      housekeepingStaff: "",
      itemName: data.item?.name,
      quantity: "",
    },
    enableReinitialize: false,
    onSubmit: (values) => {
      console.log(values);
    },
  });
  return (
    <HeadingPopup
      heading="Assign Housekeeping"
      className="w-full max-w-screen-sm"
      onClose={handleClose}
    >
      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit}>
          <div className="flex flex-col items-end gap-4 mt-4">
            <div className="grid grid-cols-3 gap-x-4 gap-y-2">
              <FormField
                name="housekeepingStaff"
                label="Housekeeping Staff"
                type="dropdown"
                placeholder="Select"
                formik={formik}
                options={[
                  { label: "John Doe", value: "john doe" },
                  { label: "Jane Doe", value: "jane doe" },
                ]}
              />
              <FormField
                name="itemName"
                label="Item Name"
                type="text"
                placeholder="Item Name"
                value={data.item?.name}
                formik={formik}
                readonly={true}
              />
              <FormField
                name="quantity"
                label="Quantity"
                type="number"
                min="0"
                placeholder="Quantity"
                formik={formik}
              />
            </div>
            <button className="px-4 py-2 text-white bg-[#163381] rounded-md">
              Submit
            </button>
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default AssignHouseKeeping;
