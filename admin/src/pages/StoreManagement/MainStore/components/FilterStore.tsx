import { Card } from "../../../../components/Card";
import { FormField } from "../../../BookingManagement/components/ReservationCustomForm";
import { Form, FormikProvider, useFormik } from "formik";
import { useGlobalStore, store } from "../../../../store/store";
import { useGetAllItemCategory } from "../../../../server-action/API/category";

const FilterStore = () => {
  const { data: category, isSuccess } = useGetAllItemCategory();
  const formValues = useGlobalStore().formValues;
  const categoryOption = isSuccess
    ? category?.map((item: any) => ({ label: item.name, value: item.name }))
    : [];

  const formik = useFormik({
    initialValues: {
      all: "",
      filter: formValues.filter,
    },
    enableReinitialize: false,
    onSubmit: (values) => {
      store.setState((prev) => {
        return {
          ...prev,
          formValues: {
            all: values.all,
            filter: values.filter,
          },
        };
      });
    },
    onReset: () => {
      store.setState((prev) => ({
        ...prev,
        formValues: {
          all: "",
          filter: "",
        },
      }));
    },
  });

  return (
    <Card className="bg-white">
      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit}>
          <div className="flex justify-between p-4">
            <div className="flex gap-4">
              <FormField
                name="all"
                type="dropdown"
                placeholder="All"
                options={[
                  { label: "Cleaning Supplies", value: "Cleaning Supplies" },
                ]}
                formik={formik}
              />
              <FormField
                name="filter"
                type="dropdown"
                placeholder="All"
                options={categoryOption}
                formik={formik}
              />
            </div>
            <div className="flex gap-4">
              <button
                type="submit"
                className="border border-blue-600 bg-blue-600 text-white px-5 rounded-lg hover:bg-blue-700 hover:border-blue-700 transition-all duration-300"
              >
                Apply
              </button>
              <button
                onClick={(e) => {
                  formik.handleReset(e);
                }}
                className="border border-gray-300 px-5 rounded-lg hover:bg-[#163381] hover:text-white transition-all duration-300"
              >
                Reset Filters
              </button>
            </div>
          </div>
        </Form>
      </FormikProvider>
    </Card>
  );
};

export default FilterStore;
