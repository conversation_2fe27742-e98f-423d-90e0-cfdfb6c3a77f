import { useCallback, useState } from "react";
import MasterTable from "../../../../layouts/Table/MasterTable";
import { TableAction } from "../../../../layouts/Table/TableAction";
import TransferSupplies from "./TransferSupplies";
import AssignHouseKeeping from "./AssignHouseKeeping";
import { useGetInventory } from "../../../../server-action/API/inventoryApi";
import { useGlobalStore } from "../../../../store/store";

export const MainStore = () => {
  // const store=useGlobalStore()
  const { formValues } = useGlobalStore();
  const { data: storeKoData, isSuccess } = useGetInventory({});
  const storeData = storeKoData?.filter(
    (item: any) => item.store.type === "main"
  );

  const [rowData, setRowData] = useState<any>();
  const [popup, setPopup] = useState("");
  const closePopup = useCallback(() => setPopup(""), []);

  const tableData = {
    column: [
      {
        key: "sn",
        title: "S.N",
      },
      {
        key: "name",
        title: "Item Name",
      },
      {
        key: "qty",
        title: "QTY in stock",
      },
      {
        key: "unit",
        title: "Unit",
      },
      {
        key: "reorder",
        title: "Reorder",
      },
    ],
    row: isSuccess
      ? storeData
          ?.filter((item: any) =>
            formValues.filter
              ? item.item?.category?.name === formValues.filter
              : true
          )
          .map((row: any, index: number) => ({
            key: index,
            sn: index + 1,
            name: row.item?.name,
            qty: row.quantity,
            unit: row.item?.unit,
            reorder: row.reorder,
          }))
      : [],
  };
  return (
    <div>
      <MasterTable columns={tableData.column} rows={tableData?.row ?? []} />
      {popup === "transfer" && <TransferSupplies close={closePopup} />}
      {popup === "housekeeping" && (
        <AssignHouseKeeping data={rowData} close={closePopup} />
      )}
    </div>
  );
};
