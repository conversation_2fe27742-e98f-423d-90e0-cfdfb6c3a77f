import { useState } from "react";
import Header from "../../../components/Header";
import { TabData } from "../../../components/TabData";
import { AddSubStore } from "./components/AddSubStore";
import { MainStore } from "./components/MainStore";

export const MainStorePage = () => {
  const [modal, setModal] = useState(false);
  const [selectedTab, setSelectedTab] = useState("main");

  const tabData = [
    {
      title: "Main Store",
      value: "main",
    },
    {
      title: "Dirty",
      value: "dirty",
    },
    {
      title: "Clean",
      value: "clean",
    },
  ];

  return (
    <div className="flex flex-col gap-4">
      <div>
        {selectedTab === "main" ? (
          <Header title="Sub Store" onAddClick={() => setModal(true)} />
        ) : (
          <Header />
        )}
      </div>

      {modal && <AddSubStore onClose={() => setModal(false)} />}
      <TabData
        selectedTabData={selectedTab}
        setSelectedTabData={setSelectedTab}
        tabData={tabData}
      />

      {selectedTab === "main" && <MainStore />}
      {/* {selectedTab === "dirty" && <Dirty />}
      {selectedTab === "clean" && <Clean />} */}
    </div>
  );
};
