/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/ban-ts-comment */
import { useState } from "react";
import {
  IStoreWithItems,
  IStoreInventoryItem,
} from "../../../../Interface/storeInventory.interface";
import { TableAction } from "../../../../layouts/Table/TableAction";
import MasterTable from "../../../../layouts/Table/MasterTable";
import { Icon } from "@iconify/react";

interface GroupedStoreInventoryProps {
  storeData: IStoreWithItems[];
  isLoading: boolean;
  onTransferItem?: (item: IStoreInventoryItem) => void;
  onProcessItem?: (item: IStoreInventoryItem) => void;
}

const GroupedStoreInventory = ({
  storeData,
  isLoading,
  onTransferItem,
  onProcessItem,
}: GroupedStoreInventoryProps) => {
  const [expandedStores, setExpandedStores] = useState<string[]>([]);

  const toggleStore = (storeId: string) => {
    setExpandedStores((prev) =>
      prev.includes(storeId)
        ? prev.filter((id) => id !== storeId)
        : [...prev, storeId]
    );
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-700"></div>
      </div>
    );
  }

  if (!storeData || storeData.length === 0) {
    return (
      <div className="text-center py-8 bg-gray-50 rounded-md">
        <p className="text-gray-500">No inventory items found</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-md shadow overflow-hidden">
      {storeData.map((store) => {
        const storeId = store.store?._id || store.store?.id || "";
        const storeName =
          store.store?.name || store.store?.type || "Unknown Store";
        const isExpanded = expandedStores.includes(storeId);
        const totalItems = store.items?.length || 0;
        const totalQuantity =
          store.items?.reduce((sum, item) => sum + (item.quantity || 0), 0) ||
          0;

        return (
          <div key={storeId} className="border-b last:border-b-0">
            {/* Store Header - Clickable */}
            <div
              className="flex items-center justify-between px-4 py-3 bg-gray-50 cursor-pointer hover:bg-gray-100 transition-colors group relative"
              onClick={() => toggleStore(storeId)}
            >
              <div className="flex items-center space-x-2">
                <span className="h-5 w-5 text-blue-600 inline-flex items-center justify-center">
                  {isExpanded ? "▼" : "▶"}
                </span>
                <h3 className="font-medium text-gray-800">{storeName}</h3>
                <div className="absolute z-10 invisible group-hover:visible bg-gray-800 text-white text-xs rounded py-2 px-3 left-1/2 -translate-x-1/2 top-10 w-48 text-center whitespace-normal">
                  Click to {isExpanded ? "collapse" : "expand"} store items
                </div>
              </div>
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <span>{totalItems} items</span>
                <span>{totalQuantity} total quantity</span>
              </div>
            </div>

            {/* Store Items - Collapsible */}
            {isExpanded && (
              <div className="bg-white">
                <MasterTable
                  columns={[
                    { key: "item", title: "Item" },
                    { key: "clean", title: "Clean" },
                    { key: "dirty", title: "Dirty" },
                    { key: "total", title: "Total" },
                    { key: "unit", title: "Unit" },
                    { key: "actions", title: "Item Actions" },
                  ]}
                  rows={
                    store.items?.map((item, index) => ({
                      id: `${storeId}-item-${index}`,
                      item: item.item?.name || "Unknown Item",
                      clean: item.cleanQuantity || 0,
                      dirty: item.dirtyQuantity || 0,
                      total: item.quantity || 0,
                      unit: item.item?.unit || "-",
                      actions: item.quantity > 0 && (
                        <div className="flex place-items-baseline gap-2">
                          {/* Process/Edit Button */}
                          <div className="relative group  p-2">
                            <button
                              onClick={() => {
                                const itemWithStore = {
                                  ...item,
                                  store: store.store,
                                };
                                onProcessItem && onProcessItem(itemWithStore);
                              }}
                              className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors flex items-center gap-1"
                            >
                              <Icon
                                icon="flowbite:edit-outline"
                                className="text-white"
                              />
                            </button>
                            <div className="absolute z-10 invisible group-hover:visible bg-gray-800 text-white text-xs rounded py-2 px-3 left-1/2 -translate-x-1/2 bottom-full mb-2 w-40 text-center whitespace-normal">
                              Process this item (clean/dirty quantities)
                            </div>
                          </div>

                          {/* Transfer Button */}
                          <div className="relative group p-2">
                            <button
                              onClick={() => {
                                const itemWithStore = {
                                  ...item,
                                  store: store.store,
                                };
                                onTransferItem && onTransferItem(itemWithStore);
                              }}
                              className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors flex items-center gap-1"
                            >
                              <Icon icon="mingcute:transfer-3-line" />
                            </button>
                            <div className="absolute z-10 invisible group-hover:visible bg-gray-800 text-white text-xs rounded py-2 px-3 left-1/2 -translate-x-1/2 bottom-full mb-2 w-40 text-center whitespace-normal">
                              Transfer item to another store
                            </div>
                          </div>
                        </div>
                      ),
                    })) || []
                  }
                  canSearch={false}
                  canSelect={false}
                  loading={false}
                  showTopPageSelector={false}
                  showLimit={false}
                  pagination={undefined}
                />
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default GroupedStoreInventory;
