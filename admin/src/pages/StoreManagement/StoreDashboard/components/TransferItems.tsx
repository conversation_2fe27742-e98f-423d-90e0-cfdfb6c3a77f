/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/ban-ts-comment */
import { useState } from "react";
import {
  useGetStoreInventoryStatus,
  useGetAllStores,
} from "../../../../server-action/API/StoreManagement/storeInventory";
import MasterTable from "../../../../layouts/Table/MasterTable";
import { TableAction } from "../../../../layouts/Table/TableAction";
import TransferItemsForm from "./forms/TransferItemsForm";
import {
  IStoreInventoryItem,
  IStore,
} from "../../../../Interface/storeInventory.interface";
// import { calculateTotalQuantity } from "../../../../utils/inventoryHelpers";
import { toast } from "react-toastify";

const TransferItems = () => {
  const [showForm, setShowForm] = useState<boolean>(false);
  const [selectedItem, setSelectedItem] = useState<IStoreInventoryItem | null>(
    null
  );
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(100);

  const { data: inventoryStatusData, isLoading } = useGetStoreInventoryStatus({
    page: currentPage,
    limit: pageSize,
  });
  const { data: stores } = useGetAllStores();

  // Extract data from the paginated response
  const inventoryStatus = inventoryStatusData?.data || [];
  const totalItems = inventoryStatusData?.total || 0;
  const currentPageFromResponse = inventoryStatusData?.page || 1;
  const pageSizeFromResponse = inventoryStatusData?.limit || 10;

  const handleTransferItems = (item: IStoreInventoryItem) => {
    console.log("Selected item for transfer:", item);
    // Make sure the item has the store property properly set
    if (item && !item.store) {
      console.error("Item missing store information:", item);
      toast.error("Cannot transfer item: Missing store information");
      return;
    }
    setSelectedItem(item);
    setShowForm(true);
  };

  const handleCloseForm = () => {
    setShowForm(false);
    setSelectedItem(null);
  };

  // Prepare data for the table
  const tableData = {
    columns: [
      { key: "sn", title: "S.N" },
      { key: "store", title: "Store" },
      { key: "item", title: "Item" },
      { key: "clean", title: "Clean Quantity" },
      { key: "dirty", title: "Dirty Quantity" },
      { key: "total", title: "Total Quantity" },
      { key: "action", title: "Action" },
    ],
    rows: [],
  };

  // Get store options for filter
  const storeOptions = [
    { value: "all", label: "All Stores" },
    ...(stores || []).map((store: IStore) => ({
      value: store._id,
      label: store.name || store.type || "Unknown Store",
    })),
  ];

  // Process inventory data for display
  if (inventoryStatus && Array.isArray(inventoryStatus)) {
    let rowIndex = 0;

    console.log(
      "Processing inventory data for TransferItems:",
      inventoryStatus
    );

    inventoryStatus.forEach((storeData) => {
      console.log("Processing store data:", storeData);
      if (storeData.items && Array.isArray(storeData.items)) {
        storeData.items.forEach((item) => {
          console.log("Processing item:", item);
          if (item.quantity > 0) {
            // Make sure each item has the store property from its parent storeData
            const itemWithStore = {
              ...item,
              store: storeData.store, // Ensure store info is properly set
            };

            console.log("Item with store:", itemWithStore);
            const storeName =
              storeData.store?.name || storeData.store?.type || "Unknown Store";

            // @ts-ignore
            tableData.rows.push({
              key: `${rowIndex++}`,
              sn: rowIndex,
              store: storeName,
              item: item.item?.name || "Unknown Item",
              clean: item.cleanQuantity || 0,
              dirty: item.dirtyQuantity || 0,
              total: item.quantity || 0,
              action: (
                <TableAction
                  onTransfer={() => handleTransferItems(itemWithStore)}
                />
              ),
            });
          }
        });
      }
    });
  }

  return (
    <div className="flex flex-col gap-4">
      <h2 className="text-xl font-semibold">Transfer Items Between Stores</h2>

      <div className="bg-blue-50  rounded-md ">
        <p className="text-blue-800">
          This section allows you to transfer items between different stores.
          Select an item and click "Transfer" to move it to another store.
        </p>
      </div>

      <MasterTable
        columns={tableData.columns}
        rows={tableData.rows}
        loading={isLoading}
        apiPagination={true}
        totalItems={totalItems}
        onPageChange={(page, limit) => {
          setCurrentPage(page);
          setPageSize(limit);
        }}
      />

      {tableData.rows.length === 0 && !isLoading && (
        <div className="text-center py-8 bg-gray-50 rounded-md">
          <p className="text-gray-500">No items available to transfer</p>
        </div>
      )}

      {showForm && selectedItem && (
        <TransferItemsForm item={selectedItem} onClose={handleCloseForm} />
      )}
    </div>
  );
};

export default TransferItems;
