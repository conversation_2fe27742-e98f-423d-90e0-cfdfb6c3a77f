import { Form, FormikProvider, useFormik } from "formik";
import * as Yup from "yup";
import HeadingPopup from "../../../../GuestManagement/components/HeadingPopup";
import { FormField } from "../../../../BookingManagement/components/ReservationCustomForm";
import {
  useTransferStoreItems,
  useGetAllStores,
} from "../../../../../server-action/API/StoreManagement/storeInventory";
import {
  IStoreInventoryItem,
  IStore,
} from "../../../../../Interface/storeInventory.interface";
import { toast } from "react-toastify";
import { useState, useEffect } from "react";

interface TransferItemsFormProps {
  item: IStoreInventoryItem | null;
  onClose: () => void;
}

const TransferItemsForm: React.FC<TransferItemsFormProps> = ({
  item,
  onClose,
}) => {
  const { mutateAsync: transferItems, isPending } = useTransferStoreItems();
  const { data: stores } = useGetAllStores();

  // State to track which type of items to transfer (clean or dirty)
  const [itemStatus, setItemStatus] = useState<"clean" | "dirty">("clean");

  // Get the available quantities if item is provided
  const cleanQuantity = item?.cleanQuantity || 0;
  const dirtyQuantity = item?.dirtyQuantity || 0;

  // Set the available quantity based on selected item status
  const availableQuantity =
    itemStatus === "clean" ? cleanQuantity : dirtyQuantity;

  // Get the current store ID if item is provided
  const fromStoreId = item?.store?._id || item?.store?.id || "";

  // Get the current store name with better fallback handling
  const fromStoreName = item?.store?.name || item?.store?.type || "";
  console.log("Store object:", item?.store);

  // Filter out the current store from destination options
  const destinationStores = (stores || [])
    .filter((store: IStore) => (store._id || store.id) !== fromStoreId)
    .map((store: IStore) => ({
      value: store._id || store.id,
      label: store.name || store.type || "Unknown Store",
    }));

  // Update validation schema based on selected item status
  const validationSchema = Yup.object({
    quantity: Yup.number()
      .required("Quantity is required")
      .positive("Quantity must be positive")
      .max(
        availableQuantity,
        `Maximum available ${itemStatus} quantity is ${availableQuantity}`
      ),
    toStoreId: Yup.string().required("Destination store is required"),
    itemStatus: Yup.string()
      .required("Item status is required")
      .oneOf(["clean", "dirty"], "Status must be either clean or dirty"),
  });

  // Get the item ID - this MUST be the _id from the nested item object
  const itemId = item?.item?._id || "";

  // Log the item data for debugging
  console.log("Item data in TransferItemsForm:", {
    item,
    itemId,
    fromStoreId,
    fromStoreName,
  });

  // Create store options for the source store dropdown when no item is selected
  const sourceStores = (stores || []).map((store: IStore) => ({
    value: store._id || store.id,
    label: store.name || store.type || "Unknown Store",
  }));

  // Create item options for the item dropdown when no item is selected
  const [itemOptions, setItemOptions] = useState<
    { value: string; label: string }[]
  >([]);
  const [selectedSourceStore, setSelectedSourceStore] = useState<string>("");

  // Update item options when source store changes
  useEffect(() => {
    if (!selectedSourceStore) {
      setItemOptions([]);
      return;
    }

    // Find the selected store in the inventory data
    const storeInventory = (stores || []).find(
      (store: IStore) => (store._id || store.id) === selectedSourceStore
    );

    if (storeInventory) {
      // Get items from the selected store
      // This is a simplified approach - in a real implementation, you would fetch items for the selected store
      setItemOptions([
        {
          value: "",
          label: "Select an item",
        },
      ]);
    }
  }, [selectedSourceStore, stores]);

  const formik = useFormik({
    initialValues: {
      itemId: item ? itemId : "",
      itemName: item ? item.item?.name || "Unknown Item" : "",
      fromStoreId: item ? fromStoreId : "",
      fromStoreName: item ? fromStoreName : "",
      toStoreId: "",
      availableQuantity: cleanQuantity,
      dirtyQuantity: dirtyQuantity,
      quantity: 1,
      itemStatus: "clean",
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        // Ensure we have valid IDs before submission
        if (!values.itemId || !values.fromStoreId || !values.toStoreId) {
          console.error("Missing required IDs:", {
            itemId: values.itemId,
            fromStoreId: values.fromStoreId,
            toStoreId: values.toStoreId,
          });
          toast.error("Missing required information");
          return;
        }

        await transferItems({
          itemId: values.itemId,
          quantity: values.quantity,
          fromStoreId: values.fromStoreId,
          toStoreId: values.toStoreId,
          itemStatus: values.itemStatus as "clean" | "dirty",
        });

        toast.success("Items transferred successfully");
        onClose();
      } catch (error) {
        toast.error("Failed to transfer items: " + error);
      }
    },
  });

  return (
    <HeadingPopup
      heading="Transfer Items"
      onClose={onClose}
      className="w-full max-w-md"
    >
      <FormikProvider value={formik}>
        <Form>
          <div className="space-y-4 mt-4">
            {item ? (
              // If an item is selected, show item details
              <>
                <FormField
                  label="Item Name"
                  name="itemName"
                  type="text"
                  disabled={true}
                  formik={formik}
                />

                <FormField
                  label="From Store"
                  name="fromStoreName"
                  type="text"
                  disabled={true}
                  formik={formik}
                />
              </>
            ) : (
              // If no item is selected, show dropdowns to select store and item
              <>
                <FormField
                  label="From Store"
                  name="fromStoreId"
                  type="dropdown"
                  options={sourceStores}
                  formik={formik}
                  onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {
                    const storeId = e.target.value;
                    formik.setFieldValue("fromStoreId", storeId);
                    setSelectedSourceStore(storeId);
                  }}
                />

                <FormField
                  label="Select Item"
                  name="itemId"
                  type="dropdown"
                  options={itemOptions}
                  formik={formik}
                  disabled={!selectedSourceStore}
                />
              </>
            )}

            <FormField
              label="To Store"
              name="toStoreId"
              type="dropdown"
              options={destinationStores}
              formik={formik}
            />

            <FormField
              label="Clean Quantity"
              name="availableQuantity"
              type="number"
              disabled={true}
              formik={formik}
            />

            <FormField
              label="Dirty Quantity"
              name="dirtyQuantity"
              type="number"
              disabled={true}
              formik={formik}
            />

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Item Status to Transfer
              </label>
              <div className="flex space-x-4">
                <label className="inline-flex items-center">
                  <input
                    type="radio"
                    name="itemStatus"
                    value="clean"
                    checked={formik.values.itemStatus === "clean"}
                    onChange={() => {
                      formik.setFieldValue("itemStatus", "clean");
                      setItemStatus("clean");
                      // Reset quantity if it exceeds the new max
                      if (formik.values.quantity > cleanQuantity) {
                        formik.setFieldValue(
                          "quantity",
                          Math.min(1, cleanQuantity)
                        );
                      }
                    }}
                    className="form-radio h-4 w-4 text-blue-600"
                  />
                  <span className="ml-2">Clean</span>
                </label>
                <label className="inline-flex items-center">
                  <input
                    type="radio"
                    name="itemStatus"
                    value="dirty"
                    checked={formik.values.itemStatus === "dirty"}
                    onChange={() => {
                      formik.setFieldValue("itemStatus", "dirty");
                      setItemStatus("dirty");
                      // Reset quantity if it exceeds the new max
                      if (formik.values.quantity > dirtyQuantity) {
                        formik.setFieldValue(
                          "quantity",
                          Math.min(1, dirtyQuantity)
                        );
                      }
                    }}
                    className="form-radio h-4 w-4 text-blue-600"
                  />
                  <span className="ml-2">Dirty</span>
                </label>
              </div>
              {formik.touched.itemStatus && formik.errors.itemStatus && (
                <div className="text-red-500 text-xs mt-1">
                  {formik.errors.itemStatus as string}
                </div>
              )}
            </div>

            <FormField
              label={`Quantity to Transfer (${itemStatus})`}
              name="quantity"
              type="number"
              min="1"
              max={availableQuantity.toString()}
              formik={formik}
            />

            <div className="flex justify-end space-x-2 mt-6">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isPending}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300"
              >
                {isPending ? "Transferring..." : "Transfer Items"}
              </button>
            </div>
          </div>
        </Form>
      </FormikProvider>
    </HeadingPopup>
  );
};

export default TransferItemsForm;
