import { Form, FormikProvider, useFormik } from "formik";
import * as Yup from "yup";
import HeadingPopup from "../../../../GuestManagement/components/HeadingPopup";
import { FormField } from "../../../../BookingManagement/components/ReservationCustomForm";
import { useProcessStoreItems } from "../../../../../server-action/API/StoreManagement/storeInventory";
import { IStoreInventoryItem } from "../../../../../Interface/storeInventory.interface";
import { toast } from "react-toastify";
import { useState, useEffect } from "react";
import { useGetAllStores } from "../../../../../server-action/API/StoreManagement/storeInventory";

interface ProcessItemsFormProps {
  item: IStoreInventoryItem | null;
  onClose: () => void;
}

const ProcessItemsForm: React.FC<ProcessItemsFormProps> = ({
  item,
  onClose,
}) => {
  const { mutateAsync: processItems, isPending } = useProcessStoreItems();

  // State to track which status we want to change items to (clean or dirty)
  const [targetStatus, setTargetStatus] = useState<'clean' | 'dirty'>('clean');

  const { data: stores } = useGetAllStores();

  // Get the available quantities if item is provided
  const cleanQuantity = item?.cleanQuantity || 0;
  const dirtyQuantity = item?.dirtyQuantity || 0;

  // Set the available quantity based on the opposite of target status
  // If we want to change to clean, we need dirty items, and vice versa
  const availableQuantity = targetStatus === 'clean' ? dirtyQuantity : cleanQuantity;

  // Get the store ID if item is provided
  const storeId = item?.store?._id || item?.store?.id || "";

  // Get the store name with better fallback handling
  const storeName = item?.store?.name || item?.store?.type || "";
  console.log("Store object:", item?.store);

  const validationSchema = Yup.object({
    quantity: Yup.number()
      .required("Quantity is required")
      .positive("Quantity must be positive")
      .max(
        availableQuantity,
        `Maximum available ${targetStatus === 'clean' ? 'dirty' : 'clean'} quantity is ${availableQuantity}`
      ),
    targetStatus: Yup.string()
      .required("Target status is required")
      .oneOf(['clean', 'dirty'], "Status must be either clean or dirty"),
  });

  // Get the item ID - this MUST be the _id from the nested item object
  const itemId = item?.item?._id || "";

  // Log the item data for debugging
  console.log("Item data in ProcessItemsForm:", {
    item,
    itemId,
    storeId,
    storeName
  });

  // Create store options for the store dropdown when no item is selected
  const storeOptions = (stores || [])
    .map((store: any) => ({
      value: store._id || store.id,
      label: store.name || store.type || "Unknown Store"
    }));

  // Create item options for the item dropdown when no item is selected
  const [itemOptions, setItemOptions] = useState<{value: string, label: string}[]>([]);
  const [selectedStore, setSelectedStore] = useState<string>("");

  // Update item options when store changes
  useEffect(() => {
    if (!selectedStore) {
      setItemOptions([]);
      return;
    }

    // Find the selected store in the inventory data
    const storeInventory = (stores || [])
      .find((store: any) => (store._id || store.id) === selectedStore);

    if (storeInventory) {
      // Get items from the selected store
      // This is a simplified approach - in a real implementation, you would fetch items for the selected store
      setItemOptions([{
        value: "",
        label: "Select an item"
      }]);
    }
  }, [selectedStore, stores]);

  const formik = useFormik({
    initialValues: {
      itemId: item ? itemId : "",
      itemName: item ? (item.item?.name || "Unknown Item") : "",
      storeName: item ? storeName : "",
      storeId: item ? storeId : "",
      cleanQuantity: item ? (item.cleanQuantity || 0) : 0,
      dirtyQuantity: item ? (item.dirtyQuantity || 0) : 0,
      availableQuantity: availableQuantity,
      quantity: 1,
      targetStatus: 'clean',
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        // Ensure we have valid IDs before submission
        if (!values.itemId || !values.storeId) {
          console.error('Missing required IDs:', {
            itemId: values.itemId,
            storeId: values.storeId
          });
          toast.error("Missing required information");
          return;
        }

        await processItems({
          itemId: values.itemId,
          quantity: values.quantity,
          storeId: values.storeId,
          targetStatus: values.targetStatus as 'clean' | 'dirty',
        });

        toast.success("Items processed successfully");
        onClose();
      } catch (error) {
        toast.error("Failed to process items: " + error);
      }
    },
  });

  return (
    <HeadingPopup
      heading="Change Item Status"
      onClose={onClose}
      className="w-full max-w-md"
    >
        <FormikProvider value={formik}>
          <Form>
            <div className="space-y-4 mt-4">
              {item ? (
                // If an item is selected, show item details
                <>
                  <FormField
                    label="Item Name"
                    name="itemName"
                    type="text"
                    disabled={true}
                    formik={formik}
                  />

                  <FormField
                    label="Store"
                    name="storeName"
                    type="text"
                    disabled={true}
                    formik={formik}
                  />
                </>
              ) : (
                // If no item is selected, show dropdowns to select store and item
                <>
                  <FormField
                    label="Store"
                    name="storeId"
                    type="dropdown"
                    options={storeOptions}
                    formik={formik}
                    onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {
                      const storeId = e.target.value;
                      formik.setFieldValue('storeId', storeId);
                      setSelectedStore(storeId);
                    }}
                  />

                  <FormField
                    label="Select Item"
                    name="itemId"
                    type="dropdown"
                    options={itemOptions}
                    formik={formik}
                    disabled={!selectedStore}
                  />
                </>
              )}

              <FormField
                label="Clean Quantity"
                name="cleanQuantity"
                type="number"
                disabled={true}
                formik={formik}
              />

              <FormField
                label="Dirty Quantity"
                name="dirtyQuantity"
                type="number"
                disabled={true}
                formik={formik}
              />

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">Change Items Status To</label>
                <div className="flex space-x-4">
                  <label className="inline-flex items-center">
                    <input
                      type="radio"
                      name="targetStatus"
                      value="clean"
                      checked={formik.values.targetStatus === 'clean'}
                      onChange={() => {
                        formik.setFieldValue('targetStatus', 'clean');
                        setTargetStatus('clean');
                        // Reset quantity if it exceeds the new max
                        if (formik.values.quantity > dirtyQuantity) {
                          formik.setFieldValue('quantity', Math.min(1, dirtyQuantity));
                        }
                      }}
                      className="form-radio h-4 w-4 text-blue-600"
                    />
                    <span className="ml-2">Clean</span>
                  </label>
                  <label className="inline-flex items-center">
                    <input
                      type="radio"
                      name="targetStatus"
                      value="dirty"
                      checked={formik.values.targetStatus === 'dirty'}
                      onChange={() => {
                        formik.setFieldValue('targetStatus', 'dirty');
                        setTargetStatus('dirty');
                        // Reset quantity if it exceeds the new max
                        if (formik.values.quantity > cleanQuantity) {
                          formik.setFieldValue('quantity', Math.min(1, cleanQuantity));
                        }
                      }}
                      className="form-radio h-4 w-4 text-blue-600"
                    />
                    <span className="ml-2">Dirty</span>
                  </label>
                </div>
                {formik.touched.targetStatus && formik.errors.targetStatus && (
                  <div className="text-red-500 text-xs mt-1">{formik.errors.targetStatus as string}</div>
                )}
              </div>

              <FormField
                label={`Quantity to Change (${targetStatus === 'clean' ? 'Dirty → Clean' : 'Clean → Dirty'})`}
                name="quantity"
                type="number"
                min="1"
                max={availableQuantity.toString()}
                formik={formik}
              />

              <div className="flex justify-end space-x-2 mt-6">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isPending}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300"
                >
                  {isPending ? "Processing..." : "Change Status"}
                </button>
              </div>
            </div>
          </Form>
        </FormikProvider>
    </HeadingPopup>
  );
};

export default ProcessItemsForm;
