/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/ban-ts-comment */
import { useState } from "react";
import {
  useGetStoreInventoryStatus,
  useGetAllStores,
} from "../../../../server-action/API/StoreManagement/storeInventory";
import {
  IStore,
  IStoreInventoryItem,
} from "../../../../Interface/storeInventory.interface";
import { FormField } from "../../../BookingManagement/components/ReservationCustomForm";
import { Form, FormikProvider, useFormik } from "formik";
import GroupedStoreInventory from "./GroupedStoreInventory";
import TransferItemsForm from "./forms/TransferItemsForm";
import ProcessItemsForm from "./forms/ProcessItemsForm";
import { toast } from "react-toastify";
import { useDeleteStore } from "../../../../server-action/API/StoreManagement/store";

const InventoryStatus = () => {
  const [selectedStore, setSelectedStore] = useState<string>("all");
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(100);
  const [showTransferForm, setShowTransferForm] = useState<boolean>(false);
  const [showProcessForm, setShowProcessForm] = useState<boolean>(false);
  const [selectedItem, setSelectedItem] = useState<IStoreInventoryItem | null>(
    null
  );

  const {
    data: inventoryStatusData,
    isLoading,
    error,
  } = useGetStoreInventoryStatus({
    page: currentPage,
    limit: pageSize,
    storeId: selectedStore !== "all" ? selectedStore : undefined,
  });

  const { data: stores } = useGetAllStores();
  const { mutateAsync: deleteStore } = useDeleteStore();

  const inventoryStatus = inventoryStatusData?.data || [];
  const totalItems = inventoryStatusData?.total || 0;

  const storeOptions = [
    { value: "all", label: "All Stores" },
    ...(stores || []).map((store: IStore) => ({
      value: store._id || store.id,
      label: store.name || store.type || "Unknown Store",
    })),
  ];

  const formik = useFormik({
    initialValues: {
      storeId: selectedStore,
    },
    enableReinitialize: true,
    onSubmit: (values) => {
      setSelectedStore(values.storeId);
      setCurrentPage(1);
    },
  });

  // Filter stores based on selection
  const filteredStores =
    selectedStore === "all"
      ? inventoryStatus
      : inventoryStatus.filter(
          (store) => (store.store?._id || store.store?.id) === selectedStore
        );

  console.log("Filtered stores:", filteredStores);

  // Handle transfer items
  const handleTransferItems = (item: IStoreInventoryItem) => {
    console.log("Selected item for transfer:", item);
    // Make sure the item has the store property properly set
    if (item && !item.store) {
      console.error("Item missing store information:", item);
      toast.error("Cannot transfer item: Missing store information");
      return;
    }
    setSelectedItem(item);
    setShowTransferForm(true);
  };

  // Handle process items
  const handleProcessItems = (item: IStoreInventoryItem) => {
    console.log("Selected item for processing:", item);
    // Make sure the item has the store property properly set
    if (item && !item.store) {
      console.error("Item missing store information:", item);
      toast.error("Cannot process item: Missing store information");
      return;
    }
    setSelectedItem(item);
    setShowProcessForm(true);
  };

  // Close forms
  const handleCloseForm = () => {
    setShowTransferForm(false);
    setShowProcessForm(false);
    setSelectedItem(null);
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Store Inventory Status</h2>
      </div>

      <div className="bg-blue-50 rounded-md ">
        <p className="text-blue-800">
          This section shows the current inventory status across all stores.
          Click on a store name to expand and view its items. Hover over action
          icons to see what they do. You can transfer items between stores or
          change their clean/dirty status.
        </p>
      </div>

      <GroupedStoreInventory
        storeData={filteredStores}
        isLoading={isLoading}
        onTransferItem={handleTransferItems}
        onProcessItem={handleProcessItems}
      />

      {showTransferForm && (
        <TransferItemsForm item={selectedItem} onClose={handleCloseForm} />
      )}

      {showProcessForm && (
        <ProcessItemsForm item={selectedItem} onClose={handleCloseForm} />
      )}
    </div>
  );
};

export default InventoryStatus;
