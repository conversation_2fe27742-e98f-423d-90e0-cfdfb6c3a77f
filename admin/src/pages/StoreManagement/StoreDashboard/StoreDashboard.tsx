import { Card, CardContent } from "../../../components/Card";
import Header from "../../../components/Header";
import InventoryStatus from "./components/InventoryStatus";

const StoreDashboard = () => {
  return (
    <div className="flex flex-col">
      <Header hideHeader={true} title="" />
      <div className="mb-2">
        {/* <TabData
          selectedTabData={selectedTab}
          setSelectedTabData={setSelectedTab}
          tabData={tabData}
        /> */}
      </div>
      <Card className="bg-white">
        <CardContent>
          <InventoryStatus />
        </CardContent>
      </Card>
    </div>
  );
};

export default StoreDashboard;
