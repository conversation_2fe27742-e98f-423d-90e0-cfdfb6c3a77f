import { useState } from "react";
import {
  useDeleteExpense,
  useGetAllExpense,
  useGetAllCategory,
} from "../../../server-action/API/expenseApi";
import MasterTable from "../../../layouts/Table/MasterTable";
import { TableAction } from "../../../layouts/Table/TableAction";
import HeadingPopup from "../../GuestManagement/components/HeadingPopup";
import ExpenseListForm from "./components/ExpenseListForm";
import Header from "../../../components/Header";
import { IMAGE_URL } from "../../../constant/constant";
import ImageModal from "../../ImageModal";
import { DateForamter } from "../../../components/DateFormater";

interface Expense {
  _id: string;
  sn: number;
  date: string;
  title: string;
  category:
    | string
    | {
        name?: string;
        _id?: string;
        hotel?: string;
        isActive?: boolean;
        __v?: number;
      }
    | any;
  amount: number;
  remarks: string;
  images: string[];
}

const ExpensesIndex = () => {
  const [showPopup, setShowPopup] = useState("");
  const [expenseToPreview, setExpenseToPreview] = useState<Expense | null>(
    null
  );
  const [expenseToEdit, setExpenseToEdit] = useState<Expense | null>(null);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  const {
    data: expenseResponse,
    isLoading: expenseLoading,
    error: expenseError,
  } = useGetAllExpense();
  const {
    data: categoryResponse,
    isLoading: categoryLoading,
    error: categoryError,
  } = useGetAllCategory();
  const { mutate: deleteExpense } = useDeleteExpense();

  const expenses = expenseResponse || [];
  const categories = categoryResponse || [];

  const categoryMap = categories.reduce(
    (acc: Record<string, string>, category: { _id: string; name: string }) => {
      acc[category._id] = category?.name;
      return acc;
    },
    {}
  );

  const tableData = {
    columns: [
      { title: "SN", key: "sn" },
      { title: "Date", key: "date" },
      { title: "Title", key: "title" },
      { title: "Category", key: "category" },
      { title: "Total Cost(NPR)", key: "amount" },
      { title: "Remarks", key: "remarks" },
      { title: "Action", key: "action" },
    ],
    rows:
      expenses?.map((expense: Expense, index: number) => {
        const { _id, date, title, category, amount, remarks } = expense;

        return {
          key: index,
          sn: index + 1,
          date: DateForamter(date) || "-",
          title,
          category,
          amount: `Rs. ${amount}`,
          remarks,
          action: (
            <TableAction
              onShow={() => {
                if (expense) setExpenseToPreview(expense);
                setShowPopup("view");
              }}
              onEdit={() => {
                if (expense) setExpenseToEdit(expense);
                setShowPopup("edit");
              }}
              onDelete={() => {
                // Directly delete without showing a popup

                deleteExpense(_id);
              }}
            />
          ),
        };
      }) || [],
  };

  if (expenseLoading || categoryLoading) {
    return <div>Loading...</div>;
  }

  if (expenseError || categoryError) {
    return <div>Error: {expenseError?.message || categoryError?.message}</div>;
  }

  return (
    <div>
      <Header
        title="Expense"
        onAddClick={() => {
          setShowPopup("wakeup");
        }}
      />

      {showPopup === "wakeup" && (
        <ExpenseListForm
          close={() => setShowPopup("")}
          categories={categories}
        />
      )}

      <ImageModal
        selectedImage={selectedImage}
        onClose={() => setSelectedImage(null)}
        printable
      />

      {showPopup === "view" && expenseToPreview && (
        <HeadingPopup
          onClose={() => setShowPopup("")}
          className="w-full max-w-screen-md h-auto"
          heading="Expense Details"
        >
          <div className="p-6 bg-white border rounded-lg shadow-sm space-y-4">
            {/* Date, Title, Category */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-y-4">
              <div className="flex items-center gap-2">
                <p className="text-sm text-gray-500">Date:</p>
                <p className="text-sm font-medium text-black">
                  {expenseToPreview.date || "-"}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <p className="text-sm text-gray-500">Title:</p>
                <p className="text-sm font-medium text-black">
                  {expenseToPreview.title || "-"}
                </p>
              </div>
              <div className="grid grid-cols-2  md:col-span-2">
                <div className="flex gap-1">
                  <p className="text-sm text-gray-500 whitespace-nowrap">
                    Expenses Category:
                  </p>
                  <p className="text-sm font-medium text-black">
                    {categoryMap[expenseToPreview.category] ||
                      (typeof expenseToPreview.category === "string"
                        ? expenseToPreview.category
                        : expenseToPreview.category?.name ||
                          JSON.stringify(expenseToPreview.category))}
                  </p>
                </div>
                {/* Attachment Section */}
                <div className="flex  gap-2">
                  <p className="text-sm text-gray-500">Attachment:</p>
                  {expenseToPreview.images &&
                  expenseToPreview.images.length > 0 ? (
                    <div className="flex items-center gap-2">
                      <p className="text-sm font-medium text-black">
                        image.{expenseToPreview.images[0]?.split(".").pop()}
                      </p>
                      <span
                        onClick={() => {
                          setSelectedImage(
                            `${IMAGE_URL}${expenseToPreview.images[0]}`
                          );
                          setShowPopup("");
                        }}
                        className="cursor-pointer text-blue-500 hover:text-blue-700"
                      >
                        <svg
                          width="50"
                          height="24"
                          viewBox="0 0 24 22"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <rect
                            x="0.40625"
                            y="0.177734"
                            width="23.1909"
                            height="21.6449"
                            rx="6.18425"
                            fill="#3498DB"
                          />
                          <path
                            d="M11.9969 13.255C13.2421 13.255 14.2515 12.2455 14.2515 11.0003C14.2515 9.75506 13.2421 8.74561 11.9969 8.74561C10.7516 8.74561 9.74219 9.75506 9.74219 11.0003C9.74219 12.2455 10.7516 13.255 11.9969 13.255Z"
                            stroke="#FCFCFC"
                            strokeWidth="0.773032"
                            strokeLinejoin="round"
                          />
                          <path
                            d="M17.2719 10.3132C17.5218 10.6173 17.6468 10.7687 17.6468 10.9999C17.6468 11.2312 17.5218 11.3826 17.2719 11.6866C16.3571 12.7972 14.3395 14.8651 11.9972 14.8651C9.65494 14.8651 7.63733 12.7972 6.72258 11.6866C6.47263 11.3826 6.34766 11.2312 6.34766 10.9999C6.34766 10.7687 6.47263 10.6173 6.72258 10.3132C7.63733 9.20263 9.65494 7.13477 11.9972 7.13477C14.3395 7.13477 16.3571 9.20263 17.2719 10.3132Z"
                            stroke="#FCFCFC"
                            strokeWidth="0.773032"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </span>
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500">
                      No images available.
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Amount and Remarks */}
            <div className="grid grid-cols-1 md:grid-cols-2  gap-y-4">
              <div className="flex items-center gap-2">
                <p className="text-sm text-gray-500">Total Costs:</p>
                <p className="text-sm font-medium text-black">
                  Rs. {expenseToPreview.amount || "-"}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <p className="text-sm text-gray-500">Remarks:</p>
                <p className="text-sm font-medium text-black">
                  {expenseToPreview.remarks || "-"}
                </p>
              </div>
            </div>
          </div>
        </HeadingPopup>
      )}

      {showPopup === "edit" && expenseToEdit && (
        <ExpenseListForm
          close={() => setShowPopup("")}
          categories={categories}
          edit={true}
          editData={expenseToEdit}
        />
      )}

      <div className="bg-white">
        <MasterTable
          columns={tableData.columns}
          rows={tableData.rows}
          loading={expenseLoading || categoryLoading}
          pagination={{
            currentPage: 1,
            totalPage: 200,
            limit: 5,
            onClick: () => {},
          }}
        />
      </div>
    </div>
  );
};

export default ExpensesIndex;
