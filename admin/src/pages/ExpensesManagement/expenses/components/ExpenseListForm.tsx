import { Form, FormikProvider, useFormik } from "formik";
import { PopupModal } from "../../../../components";
import { Icon } from "@iconify/react/dist/iconify.js";
import { FormField } from "../../../BookingManagement/components/ReservationCustomForm";
import { galleryIcon } from "../../../../components/svgExports";
import moment from "moment";
import { get } from "lodash";
import * as Yup from "yup";

import {
  useCreateExpense,
  useUpdateExpense,
} from "../../../../server-action/API/expenseApi";
import { IMAGE_URL } from "../../../../constant/constant";

interface ExpenseFormProps {
  close: () => void;
  categories: { _id: string; name: string }[];
  edit?: boolean;
  editData?: any;
}

const ExpenseValidationSchema = Yup.object().shape({
  date: Yup.string().required("Date is required"),
  title: Yup.string()
    .required("Title is required")
    .min(3, "Title must be at least 3 characters"),
  category: Yup.string().required("Category is required"),
  amount: Yup.number()
    .required("Amount is required")
    .min(1, "Amount must be greater than 0"),
  remarks: Yup.string().optional(),
  images: Yup.array().max(5, "You can upload up to 5 images only"),
});

const ExpenseListForm: React.FC<ExpenseFormProps> = ({
  close,
  edit = false,
  editData,
  categories,
}) => {
  const { mutate: createExpense, isPending: isCreating } = useCreateExpense();
  const { mutate: updateExpense, isPending: isUpdating } = useUpdateExpense();

  console.log(editData, "edit");

  const formik = useFormik({
    initialValues: {
      date: edit ? moment(get(editData, "date", "")).format("YYYY-MM-DD") : "",
      title: edit ? get(editData, "title", "") : "",
      category: edit ? get(editData, "category._id", "") : "",
      amount: edit ? get(editData, "amount", "") : "",
      images: edit ? get(editData, "images", []) : [],
      remarks: edit ? get(editData, "remarks", "") : "",
    },
    validationSchema: ExpenseValidationSchema,

    onSubmit: async (values) => {
      const formData = new FormData();

      // Append form fields to FormData
      formData.append("date", values.date);
      formData.append("title", values.title);
      formData.append("category", values.category);
      formData.append("amount", values.amount.toString());
      formData.append("remarks", values.remarks);

      // Append images to FormData (if there are any)
      if (values.images?.length > 0) {
        values.images.forEach((file: File) => {
          formData.append("images", file);
        });
      }

      // Use the correct mutation (create or update)
      if (edit) {
        // If editing, send the FormData to update the expense
        await updateExpense({ id: editData._id, body: formData });
      } else {
        // If creating, send the FormData to create the expense
        await createExpense(formData);
      }

      close(); // Close the modal after success
    },
  });

  return (
    <PopupModal onClose={close} classname="w-full max-w-screen-sm">
      <div className="relative flex items-center jusify-between bg-[#F1F6FD]">
        <h1 className="w-full p-4 text-center text-semibold">
          {edit ? "Edit Expense" : "Add Expense"}
        </h1>
        <button
          className="absolute flex items-center justify-center p-1 bg-black rounded-full top-4 right-4"
          onClick={close}
        >
          <Icon
            icon="fluent-mdl2:calculator-multiply"
            width="14"
            height="14"
            className="text-white"
          />
        </button>
      </div>
      <div className="p-4">
        <FormikProvider value={formik}>
          <Form onSubmit={formik.handleSubmit}>
            <div className="grid grid-cols-2 gap-2">
              <FormField
                name="date"
                label="Date"
                type="date"
                placeholder=""
                formik={formik}
              />
              <FormField
                name="title"
                label="Title"
                type="text"
                placeholder="Enter Expense Title"
                formik={formik}
              />
              <FormField
                name="category"
                label="Expense Category"
                type="dropdown"
                placeholder="Select Expense Category"
                options={categories.map((category) => ({
                  label: category.name,
                  value: category._id,
                }))}
                formik={formik}
              />

              <FormField
                name="amount"
                label="Amount"
                type="number"
                placeholder="Enter Amount"
                formik={formik}
              />

              <FormField
                name="remarks"
                label="Remarks"
                type="text"
                placeholder="Enter Remarks"
                formik={formik}
              />

              <div className="flex flex-col">
                <div>
                  <label className="mb-1 text-sm">Upload Photo</label>
                  <div className="flex items-center w-full space-x-2 border-2 rounded-md">
                    <label
                      htmlFor="expense-list-form"
                      className="flex items-center gap-1 p-2 text-white bg-[#163381] cursor-pointer rounded-l-md"
                    >
                      {galleryIcon({ color: "white", size: "18" })}
                      Choose File
                    </label>
                    <input
                      name="file"
                      id="expense-list-form"
                      multiple={true}
                      type="file"
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                        const input = e.target;
                        const files = input.files
                          ? Array.from(input.files)
                          : [];

                        // Combine existing string images with new File objects
                        const existingImages = formik.values.images.filter(
                          (img: any) => typeof img === "string"
                        );

                        formik.setFieldValue("images", [
                          ...existingImages,
                          ...files,
                        ]);

                        // ✅ Reset file input so same file can be selected again
                        input.value = "";
                      }}
                      className="hidden"
                    />
                    <span className="text-sm text-gray-500">
                      {formik.values.images?.length} files selected
                    </span>
                  </div>
                </div>
                {formik.values.images?.length > 0 && (
                  <div className="flex flex-wrap p-4 mt-2 border-2 border-gray-300 rounded-md">
                    {formik.values.images.map((file: any, index: number) => {
                      const isString = typeof file === "string";
                      const imageUrl = isString
                        ? `${IMAGE_URL}${file}`
                        : URL.createObjectURL(file);

                      return (
                        <div key={index} className="relative w-24 h-24 m-2">
                          <img
                            src={imageUrl}
                            alt={`preview-${index}`}
                            className="object-cover w-full h-full rounded-md"
                          />
                          <button
                            type="button"
                            className="absolute top-0 right-0 p-1 bg-red-500 rounded-full"
                            onClick={() => {
                              const newFiles = formik.values.images.filter(
                                (_: any, i: number) => i !== index
                              );
                              formik.setFieldValue("images", newFiles);
                            }}
                          >
                            <Icon
                              icon="fluent-mdl2:calculator-multiply"
                              width="16"
                              height="16"
                              className="text-red bg-white rounded-full p-[2px] border"
                            />
                          </button>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            </div>
            <div className="flex items-center justify-end mt-4">
              <button
                type="submit"
                disabled={isCreating || isUpdating}
                className="py-2 px-6 text-white rounded-md bg-[#163381]"
              >
                {isCreating || isUpdating ? (
                  <Icon icon="line-md:loading-loop" width="18" height="18" />
                ) : (
                  "Save"
                )}
              </button>
            </div>
          </Form>
        </FormikProvider>
      </div>
    </PopupModal>
  );
};

export default ExpenseListForm;
