import React from "react";

interface DeletePopupProps {
  isOpen: boolean;
  onClose: () => void;
  onDelete: () => void;
}

const DeletePopup: React.FC<DeletePopupProps> = ({
  isOpen,
  onClose,
  onDelete,
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-[#334182] rounded-lg w-96 p-6 shadow-lg">
        <h2 className="text-xl font-semibold text-center text-white mb-4">
          Are you sure?
        </h2>
        <p className="text-sm text-center text-white mb-6">
          Do you really want to delete this item? This action cannot be undone.
        </p>
        <div className="flex justify-center gap-4">
          <button
            onClick={onDelete}
            className="bg-red-500  px-6 text-gray-700 bg-gray-300 py-2 rounded-lg hover:bg-red-600 focus:outline-none hover:bg-gray-400  "
          >
            Ok
          </button>
          <button
            onClick={onClose}
            className="bg-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-400 focus:outline-none"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
};

export default DeletePopup;
