import { Form, FormikProvider, useFormik } from "formik";
import { PopupModal } from "../../../../components";
import { Icon } from "@iconify/react/dist/iconify.js";
import { FormField } from "../../../BookingManagement/components/ReservationCustomForm";
// import { galleryIcon } from "../../../../components/svgExports";
// import moment from "moment";
import { get } from "lodash";
// import { toast } from "react-toastify";
import * as Yup from "yup";
import {
  useCreateCategory,
  useUpdateCategory,
} from "../../../../server-action/API/expenseApi";

const expenseCategoryValidationSchema = Yup.object().shape({
  category: Yup.string()
    .required("Expense Category Name is required")
    .min(3, "Expense Category Must be altleast 3 charaters"),
});
interface ExpenseFormProps {
  close: () => void;
  categories: { _id: string; name: string }[];
  edit?: boolean;
  editData?: any;
}

const ExpenseCategoryForm: React.FC<ExpenseFormProps> = ({
  close,
  edit = false,
  editData,
}) => {
  const { mutate: createCategory, isPending: isCreating } = useCreateCategory();
  const { mutate: updateCategory, isPending: isUpdating } = useUpdateCategory();

  console.log("eidit", editData);
  const formik = useFormik({
    validationSchema: expenseCategoryValidationSchema,
    initialValues: {
      category: edit ? get(editData, "name", "") : "",
    },

    onSubmit: async (values) => {
      const finalData = {
        name: values.category,
      };

      if (edit) {
        updateCategory({ id: editData._id, body: finalData });
      } else {
        createCategory(finalData);
      }

      close(); // Close the modal after success
    },
  });

  return (
    <PopupModal onClose={close} classname="w-full max-w-screen-sm">
      <div className="relative flex items-center jusify-between bg-[#F1F6FD]">
        <h1 className="w-full p-4 text-center text-semibold">
          {edit ? "Edit Category" : "Add Category"}
        </h1>
        <button
          className="absolute flex items-center justify-center p-1 bg-black rounded-full top-4 right-4"
          onClick={close}
        >
          <Icon
            icon="fluent-mdl2:calculator-multiply"
            width="14"
            height="14"
            className="text-white"
          />
        </button>
      </div>
      <div className="p-4">
        <FormikProvider value={formik}>
          <Form onSubmit={formik.handleSubmit}>
            <div className="grid grid-cols-1 gap-2">
              <FormField
                name="category"
                label="Name"
                type="text"
                placeholder="Category Name"
                formik={formik}
              />
            </div>
            <div className="flex items-center justify-end mt-4">
              <button
                type="submit"
                disabled={isCreating || isUpdating}
                className="py-2 px-6 text-white rounded-md bg-[#163381]"
              >
                {isCreating || isUpdating ? (
                  <Icon icon="line-md:loading-loop" width="18" height="18" />
                ) : (
                  "Save"
                )}
              </button>
            </div>
          </Form>
        </FormikProvider>
      </div>
    </PopupModal>
  );
};

export default ExpenseCategoryForm;
