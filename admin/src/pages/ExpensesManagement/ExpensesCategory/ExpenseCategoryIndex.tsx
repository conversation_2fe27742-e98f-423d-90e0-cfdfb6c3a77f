import { useState } from "react";
import {
  useDeleteCategory,
  useGetAllExpense,
} from "../../../server-action/API/expenseApi";
import { useGetAllCategory } from "../../../server-action/API/expenseApi";
// import { Status } from "../../../components/Status";
import MasterTable from "../../../layouts/Table/MasterTable";
import { TableAction } from "../../../layouts/Table/TableAction";
import HeadingPopup from "../../GuestManagement/components/HeadingPopup";
import ExpenseCategoryForm from "./components/ExpenseCategoryForm";
import Header from "../../../components/Header";
import DeletePopup from "../expenses/components/deletePopup";
import { IMAGE_URL } from "../../../constant/constant";

interface Expense {
  _id: string;
  sn: number;
  date: string;
  title: string;
  category: string;
  amount: number;
  remarks: string;
  images: string[];
}

const ExpenseCategoryIndex = () => {
  const [showPopup, setShowPopup] = useState("");

  const [expenseToPreview] = useState<Expense | null>(null);
  const [expenseToEdit, setExpenseToEdit] = useState<Expense | null>(null);
  const { isLoading: expenseLoading, error: expenseError } = useGetAllExpense();
  const {
    data: categoryResponse,
    isLoading: categoryLoading,
    error: categoryError,
  } = useGetAllCategory();
  const { mutate: deleteExpenseCategory } = useDeleteCategory();

  // const expenses = expenseResponse || [];
  const categories = categoryResponse || [];

  const categoryMap = categories.reduce(
    (acc: Record<string, string>, category: { _id: string; name: string }) => {
      acc[category._id] = category.name;
      return acc;
    },
    {}
  );

  const tableData = {
    columns: [
      { title: "S.N", key: "sn" },
      { title: "Category Name", key: "category" },

      { title: "Action", key: "action" },
    ],
    rows:
      categoryResponse?.map((item: any, index: number) => {
        // Make sure we're accessing the name property correctly
        const categoryName =
          typeof item.name === "string"
            ? item.name
            : item.name && typeof item.name === "object"
            ? JSON.stringify(item.name)
            : "Unknown";

        return {
          key: index,
          sn: index + 1,
          category: categoryName, // Use the extracted name string
          action: (
            <TableAction
              onEdit={() => {
                setExpenseToEdit(item);
                setShowPopup("edit");
              }}
              onDelete={() => {
                deleteExpenseCategory(item._id);
                setShowPopup("delete");
              }}
            />
          ),
        };
      }) || [],
  };

  if (expenseLoading || categoryLoading) {
    return <div>Loading...</div>;
  }

  if (expenseError || categoryError) {
    return <div>Error: {expenseError?.message || categoryError?.message}</div>;
  }

  return (
    <div>
      <Header
        title="Expense Category"
        onAddClick={() => {
          console.log("clicked");
          setShowPopup("wakeup");
        }}
      />
      {showPopup === "edit" && expenseToEdit && (
        <ExpenseCategoryForm
          close={() => setShowPopup("")}
          categories={categories}
          edit={true}
          editData={expenseToEdit} // Make sure you are passing the correct category data
        />
      )}
      {showPopup === "wakeup" && (
        <ExpenseCategoryForm
          close={() => setShowPopup("")}
          categories={categories}
        />
      )}
      {/* {showPopup === "view" && (
        <HeadingPopup
          onClose={() => setShowPopup("")}
          className="w-full max-w-screen-sm"
          heading="Expense Details"
        >
          <div className="grid grid-cols-3 gap-4 p-3 border-2 border-gray-300 rounded-md">
            {Object.entries(detailsObjects).map(([key, value], index) => (
              <div className="flex items-center gap-2" key={index}>
                <p className="text-sm text-gray-500">{key}</p>:
                <p className="text-sm text-black">{value}</p>
              </div>
            ))}
          </div>
        </HeadingPopup>
      )} */}
      {showPopup === "view" && expenseToPreview && (
        <HeadingPopup
          onClose={() => setShowPopup("")}
          className="w-full max-w-screen-sm"
          heading="Expense Details"
        >
          <div className="grid grid-cols-3 gap-4 p-3 border-2 border-gray-300 rounded-md">
            {/* Use expenseToPreview for dynamic data */}
            <div className="flex items-center gap-2">
              <p className="text-sm text-gray-500">Date</p>:
              <p className="text-sm text-black">{expenseToPreview.date}</p>
            </div>
            <div className="flex items-center gap-2">
              <p className="text-sm text-gray-500">Title</p>:
              <p className="text-sm text-black">{expenseToPreview.title}</p>
            </div>
            <div className="flex items-center gap-2">
              <p className="text-sm text-gray-500">Amount</p>:
              <p className="text-sm text-black">{expenseToPreview.amount}</p>
            </div>
            <div className="flex items-center gap-2">
              <p className="text-sm text-gray-500">Expense Category</p>:
              <p className="text-sm text-black">
                {categoryMap[expenseToPreview.category] ||
                  expenseToPreview.category}
              </p>
            </div>
            <div className="flex items-center gap-2">
              <p className="text-sm text-gray-500">Remarks</p>:
              <p className="text-sm text-black">{expenseToPreview.remarks}</p>
            </div>

            {/* {console.log(expenseToPreview, "expenseToPreview")} */}
            {/* Render images if available */}
            {expenseToPreview.images && expenseToPreview.images.length > 0 ? (
              <div className="col-span-3">
                <h3 className="text-sm text-gray-500">Images</h3>
                <div className="grid grid-cols-2 gap-2">
                  {expenseToPreview.images.map((image, index) => {
                    const imageUrl = `${IMAGE_URL}${image}`; // Construct the image URL using the base
                    return (
                      <img
                        key={index}
                        src={imageUrl}
                        alt={`expense-image-${index}`}
                        className="w-full h-auto object-cover rounded"
                      />
                    );
                  })}
                </div>
              </div>
            ) : (
              <div className="col-span-3">
                <p className="text-sm text-gray-500">No images available.</p>
              </div>
            )}
          </div>
        </HeadingPopup>
      )}

      {showPopup === "edit" && expenseToEdit && (
        <ExpenseCategoryForm
          close={() => setShowPopup("")}
          categories={categories}
          edit={true}
          editData={expenseToEdit}
        />
      )}
      {/* {showPopup === "delete" && (
        <DeletePopup
          isOpen={showPopup === "delete"}
          onClose={closeDeletePopup}
          onDelete={handleDelete}
        />
      )} */}
      <div className="bg-white">
        <MasterTable
          columns={tableData.columns}
          rows={tableData.rows}
          loading={expenseLoading || categoryLoading}
          pagination={{
            currentPage: 1,
            totalPage: 200,
            limit: 5,
            onClick: () => {},
          }}
        />
      </div>
    </div>
  );
};

export default ExpenseCategoryIndex;
