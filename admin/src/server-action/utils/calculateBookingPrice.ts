import { apiClient } from "./ApiGateway";
import { calculateMembershipDiscount } from "./membershipDiscount";

/**
 * Interface for booking price calculation request
 */
export interface IBookingPriceRequest {
  roomId: string;
  checkInDate: string;
  checkOutDate: string;
  userId?: string;
}

/**
 * Interface for booking price calculation response
 */
export interface IBookingPriceResponse {
  basePrice: number;
  totalPrice: number;
  nights: number;
  membershipDiscount?: {
    name: string;
    discountPercentage: number;
    discountAmount: number;
  };
}

/**
 * Calculate the total price for a booking, including any applicable membership discount
 * @param bookingData - The booking data including room, dates, and optional user ID
 * @returns Promise with the calculated price information
 */
export const calculateBookingPrice = async (
  bookingData: IBookingPriceRequest
): Promise<IBookingPriceResponse> => {
  try {
    // First get the base price calculation from the API
    const response = await apiClient.post("price/calculate", {
      roomId: bookingData.roomId,
      checkInDate: bookingData.checkInDate,
      checkOutDate: bookingData.checkOutDate,
    });

    const priceData = response.data?.data;

    if (!priceData) {
      throw new Error("Failed to calculate room price");
    }

    const result: IBookingPriceResponse = {
      basePrice: priceData.basePrice,
      totalPrice: priceData.totalPrice,
      nights: priceData.nights,
    };

    // If a user ID is provided, check for membership discount
    if (bookingData.userId) {
      try {
        const membershipDiscount = await calculateMembershipDiscount(
          bookingData.userId,
          result.totalPrice
        );

        // If a discount applies, update the total price and add discount info
        if (membershipDiscount) {
          // Apply the discount to the total price
          result.totalPrice -= membershipDiscount.discountAmount;
          // Add membership discount info to the response
          result.membershipDiscount = membershipDiscount;
        }
      } catch (error) {
        console.error("Error applying membership discount:", error);
      }
    }

    return result;
  } catch (error) {
    console.error("Error calculating booking price:", error);
    throw error;
  }
};
