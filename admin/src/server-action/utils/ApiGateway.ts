import axios from "axios";

// Create an axios instance
export const apiClient = axios.create({
  baseURL: "https://hotel-api.webstudiomatrix.com/api/v1/en/",
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
});

// Store ongoing requests
const controllers = new Map<string, AbortController>();

// Request interceptor
apiClient.interceptors.request.use(
  (config) => {
    // Get token from localStorage - use the correct key
    const token = localStorage.getItem("_UPLFMMATRIX");

    // Only add authorization header if token exists
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Cancel any previous request to the same endpoint
    if (config.url && controllers.has(config.url)) {
      controllers.get(config.url)?.abort();
      controllers.delete(config.url);
    }

    // Create a new AbortController and attach signal to request
    const controller = new AbortController();
    config.signal = controller.signal;
    if (config.url) controllers.set(config.url, controller);

    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor
apiClient.interceptors.response.use(
  (response) => {
    // Remove completed requests from map
    if (response.config.url) {
      controllers.delete(response.config.url);
    }
    return response;
  },
  (error: any) => {
    // Handle unauthorized access
    if (error.response?.status === 401) {
      localStorage.removeItem("_UPLFMMATRIX");
      localStorage.removeItem("user");
      localStorage.removeItem("_GBLFMATRIX");
      localStorage.removeItem("_TFDSFUMATRIX");
      window.location.href = "/auth-login";
    }

    // If request was canceled, do not show error
    if (axios.isCancel(error as any)) {
      return Promise.resolve({ canceled: true });
    }

    // Check for specific error types
    if (error.response?.data?.message === "bank already exists.") {
      console.log("Duplicate bank error detected in API Gateway");
      return Promise.reject({
        message: "bank already exists.",
        isDuplicate: true,
        originalError: error.response.data,
      });
    }

    // Check for booking conflict errors
    if (
      error.response?.data?.error &&
      Array.isArray(error.response.data.error) &&
      error.response.data.error.length > 0
    ) {
      const errorMsg = error.response.data.error[0]?.message || "";

      // Check if it's a room booking conflict error
      if (errorMsg.includes("Room is already booked for the selected dates")) {
        console.log("Room booking conflict detected in API Gateway");

        // Extract booking ID if available
        const bookingIdMatch = errorMsg.match(/Booking ID: ([A-Z0-9-]+)/);
        const conflictingBookingId = bookingIdMatch ? bookingIdMatch[1] : null;

        return Promise.reject({
          message: errorMsg,
          isBookingConflict: true,
          conflictingBookingId,
          originalError: error.response.data,
        });
      }
    }

    // Format error message for other errors
    const errorMessage =
      error.response?.data?.message ||
      error.message ||
      "An unexpected error occurred";

    return Promise.reject(errorMessage);
  }
);

// Optional: Add TypeScript interfaces
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  total: number;
  page: number;
  limit: number;
}

// Booking API specific response interfaces
export interface BookingPagination {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

export interface BookingApiResponse<T> {
  success: boolean;
  message: string;
  data: {
    bookings: T[];
    pagination: BookingPagination;
  };
  responseTime: string;
}

export interface SingleBookingApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
  responseTime?: string;
}
