import { useMemo } from "react";
import { useGetPermission } from "../API/Permission.api";
import { useAuth } from "../../hooks";

export interface ISidebarDashboardRoutes {
  title?: string;
  routes: {
    id: number;
    path?: string;
    title: string;
    name?: string;
    icon?: React.FC<{ className?: string }>;
    children?: {
      path: string;
      id: number;
      title: string;
      name?: string;
    }[];
  }[];
}

interface PermissionModule {
  module?: {
    name?: string;
    permissions?: string[];
    subModule?: {
      name?: string;
      permissions?: string[];
    }[];
  }[];
}

export const useFilteredRoutes = (
  routesConfig: ISidebarDashboardRoutes,
  showAllRoutes: boolean = false
) => {
  const { data: userData } = useAuth();
  const { data: permissionData, isLoading } = useGetPermission();

  const isAdmin = userData?.user?.role === "admin";

  const filteredPermissions: PermissionModule | undefined =
    permissionData?.decodedPermissions?.find(
      (per: any) => per?.user?._id === userData?.user?._id
    );

  const filteredRoutes = useMemo(() => {
    if (showAllRoutes || isAdmin || !permissionData) {
      return routesConfig;
    }

    const hasReadPermission = (name?: string): boolean => {
      if (!name) return false;

      const lowerCaseName = name.toLowerCase();

      const moduleWithName = filteredPermissions?.module?.find(
        (module) => module?.name?.toLowerCase() === lowerCaseName
      );

      if (moduleWithName?.permissions?.includes("READ")) {
        return true;
      }

      return (
        filteredPermissions?.module?.some((module) =>
          module?.subModule?.some(
            (sub) =>
              sub?.name?.toLowerCase() === lowerCaseName &&
              sub?.permissions?.includes("READ")
          )
        ) || false
      );
    };

    const filtered = routesConfig?.routes?.filter((route) => {
      if (hasReadPermission(route?.name)) {
        return true;
      }

      const filteredChildren = route?.children?.filter((child) =>
        hasReadPermission(child?.name)
      );

      if (filteredChildren?.length) {
        route.children = filteredChildren;
        return true;
      }

      return false;
    });

    return {
      ...routesConfig,
      routes: filtered ?? [],
    };
  }, [routesConfig, permissionData, showAllRoutes, isAdmin]);

  return {
    filteredRoutes,
    isLoading,
    hasPermissionData: !!permissionData,
  };
};
