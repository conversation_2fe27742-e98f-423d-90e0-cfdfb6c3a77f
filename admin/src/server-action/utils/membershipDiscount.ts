import { apiClient } from './ApiGateway';

/**
 * Interface for membership discount result
 */
export interface IMembershipDiscount {
  name: string;
  discountPercentage: number;
  discountAmount: number;
}

/**
 * Calculate membership discount for a user
 * @param userId - The user ID to check for membership
 * @param totalAmount - The total amount to apply discount to
 * @returns Promise with membership discount information or null if no discount applies
 */
export const calculateMembershipDiscount = async (
  userId: string,
  totalAmount: number
): Promise<IMembershipDiscount | null> => {
  try {
    if (!userId || !totalAmount) return null;

    // Get user details with membership information
    const userResponse = await apiClient.get(`user/${userId}`);
    const user = userResponse.data?.data;
    
    if (!user || !user.membership) return null;
    
    // Get membership details
    const membershipResponse = await apiClient.get(`membership/${user.membership}`);
    const membership = membershipResponse.data?.data;
    
    if (!membership || !membership.discount) return null;
    
    // Calculate discount amount
    const discountPercentage = membership.discount;
    const discountAmount = (totalAmount * discountPercentage) / 100;
    
    return {
      name: membership.name,
      discountPercentage,
      discountAmount
    };
  } catch (error) {
    console.error('Error calculating membership discount:', error);
    return null;
  }
};
