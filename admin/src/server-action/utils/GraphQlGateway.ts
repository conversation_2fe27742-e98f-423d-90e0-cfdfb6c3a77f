// src/utils/graphql-client.ts
import { request, ClientError, gql } from 'graphql-request';

// const local = 'http://localhost:8080/graphql';
// Configure GraphQL endpoint from environment variables
const GRAPHQL_ENDPOINT = `${import.meta.env.VITE_API_BASE_URL}graphql`; // Adjust based on your API structure

type GraphQLRequestConfig = {
  token?: string;
  headers?: Record<string, string>;
};

const getHeaders = (config?: GraphQLRequestConfig) => ({
  'Content-Type': 'application/json',
  Authorization: config?.token ? `Bearer ${config.token}` : '',
  ...config?.headers,
});
const cache = new Map<string, { data: any; expiry: number }>();
const CACHE_EXPIRY_MS = 5 * 60 * 1000; // Cache expiry in milliseconds (e.g., 5 minutes)

const generateCacheKey = (document: string, variables?: any): string => {
  const variablesString = variables ? JSON.stringify(variables) : '';
  return `${document}:${variablesString}`;
};

export const graphqlClient = async <T>(
  document: string,
  variables?: any,
  config?: GraphQLRequestConfig
): Promise<T> => {
  const token = localStorage.getItem('token') ?? '';
  const cacheKey = generateCacheKey(document, variables);

  // Check cache validity
  const cached = cache.get(cacheKey);
  if (cached && cached.expiry > Date.now()) {
    return cached.data as T;
  }

  // Fetch from API if not cached or expired
  try {
    const response = await request<T>(
      GRAPHQL_ENDPOINT,
      document,
      variables,
      getHeaders({ ...config, token })
    );
    // Store the response in the cache with expiry
    cache.set(cacheKey, {
      data: response,
      expiry: Date.now() + CACHE_EXPIRY_MS,
    });
    return response;
  } catch (error) {
    if (error instanceof ClientError) {
      if (error.response.status === 401) {
        localStorage.removeItem('token');
        const redirectPath = encodeURIComponent(window.location.pathname);
        window.location.href = `/auth-login?redirect=${redirectPath}`;
      }
      throw new Error(error.message);
    }
    throw error;
  }
};

export { gql };
