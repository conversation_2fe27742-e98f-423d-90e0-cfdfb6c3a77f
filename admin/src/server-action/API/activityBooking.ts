import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../utils/ApiGateway";
import { toast } from "react-toastify";
import { IActivityBooking } from "../../Interface/activities.interface";

export const useGetAllActivitiesBooking = () => {
  return useQuery({
    queryKey: ["activity-booking"],
    queryFn: async () => {
      const res = await apiClient.get<{ data: IActivityBooking[] }>(
        "/activitybooking"
      );
      return res.data.data;
    },
  });
};

export const useGetActivityBookingById = (id: string) => {
  return useQuery({
    queryKey: ["activity-booking", id],
    queryFn: async () => {
      const res = await apiClient.get<{ data: IActivityBooking }>(
        `activitybooking/${id}`
      );
      return res.data.data;
    },
    enabled: !!id, // Only fetch if id is provided
  });
};

export const useCreateActivityBooking = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (body) => {
      const res = await apiClient.post<{ data: IActivityBooking }>(
        "activitybooking",
        body
      );
      return res.data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["activity-booking"] });
      toast.success("Activity created successfully");
    },
    onError: () => {
      toast.error("Error creating activity");
    },
  });
};

export const useUpdateActivityBooking = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      id,
      body,
    }: {
      id: string;
      body: IActivityBooking;
    }) => {
      const res = await apiClient.patch<{ data: IActivityBooking }>(
        `activitybooking/${id}`,
        body
      );
      console.log("response", res);
      return res.data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["activity-booking"] });
      toast.success("Activity updated successfully");
    },
    onError: (error) => {
      console.log("error", error);
      toast.error("Error updating package");
    },
  });
};

export const useDeleteActivityBooking = () => {
  const queryClient = useQueryClient();
  return useMutation<void, Error, string>({
    mutationFn: async (id) => {
      await apiClient.delete(`activitybooking/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["activity-booking"] });
      toast.success("Activity deleted successfully");
    },
    onError: () => {
      toast.error("Error deleting package");
    },
  });
};
