import {
  useMutation,
  useQuery,
  use<PERSON>uery<PERSON>lient,
  UseQueryResult,
} from "@tanstack/react-query";
import { apiClient } from "../utils/ApiGateway";
import { toast } from "react-toastify";

export const useGetAllUser = (params = {}) => {
  return useQuery({
    queryKey: ["user", "booking", params],
    queryFn: async () => {
      const res = await apiClient.get("/user", { params });
      return res.data.data;
    },
  });
};

export const useGetUserById = (id: string): UseQueryResult => {
  return useQuery({
    queryKey: ["user", id],
    queryFn: async () => {
      if (!id) return null;
      const response = await apiClient.get(`user/${id}`);
      return response?.data?.data;
    },
    enabled: !!id,
  });
};

export const useDeleteUser = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: string) => {
      const { data } = await apiClient.delete(`/user/${id}`);
      return data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["user"] });
      toast.success("Employee deleted successfully");
    },
    onError: (error: any) => {
      toast.error(error || "Error deleting employee");
    },
  });
};
