import { useQuery } from "@tanstack/react-query";
import { apiClient } from "../utils/ApiGateway";

interface CategoryModule {
  id: string;
  name?: string;
  description?: string;
  [key: string]: any;
}
export const useGetAllItemCategory = () => {
  return useQuery<CategoryModule[]>({
    queryKey: ["items-category"],
    queryFn: async () => {
      const res = await apiClient.get<{ data: CategoryModule[] }>(
        "item/category"
      );
      return res.data.data;
    },
  });
};
