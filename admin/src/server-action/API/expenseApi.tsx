import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-toastify";
import { apiClient } from "../utils/ApiGateway";

//expense category api
export const useGetAllCategory = () => {
  return useQuery({
    queryKey: ["category"],
    queryFn: async () => {
      const res = await apiClient.get("expense/category");
      return res.data.data;
    },
  });
};

export const useCreateCategory = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["category"],
    mutationFn: async (categoryData: any) => {
      const res = await apiClient.post("expense/category", categoryData);
      return res.data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["category"] });
      toast.success("Expense Category created successfully");
    },
    onError: (error: any) => {
      toast.error(error);
    },
  });
};

export const useUpdateCategory = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["category"],
    mutationFn: async ({ id, body }: any) => {
      const res = await apiClient.patch(`expense/category/${id}`, body);
      return res.data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["category"] });
      toast.success("Category updated successfully");
    },
    onError: () => {
      toast.error("Error updating category");
    },
  });
};

export const useDeleteCategory = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["category"],
    mutationFn: async (id: string) => {
      const res = await apiClient.delete(`expense/category/${id}`);
      return res.data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["category"] });
      toast.success("Category deleted successfully");
    },
    onError: () => {
      toast.error("Error deleting category");
    },
  });
};

//expense api
export const useGetAllExpense = () => {
  return useQuery({
    queryKey: ["expense"],
    queryFn: async () => {
      const res = await apiClient.get("expense");

      return res.data.data;
    },
  });
};

export const useCreateExpense = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["expense"],
    mutationFn: async (expenseData: any) => {
      let config = {};
      if (expenseData instanceof FormData) {
        config = {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        };
      }

      const res = await apiClient.post("expense", expenseData, config);
      return res.data.data;
    },

    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["expense"] });
      toast.success("Expense created successfully");
    },
    onError: () => {
      toast.error("Error creating expense");
    },
  });
};

export const useUpdateExpense = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["expense"],
    mutationFn: async ({ id, body }: any) => {
      const config =
        body instanceof FormData
          ? { headers: { "Content-Type": "multipart/form-data" } }
          : { headers: { "Content-Type": "application/json" } };

      const res = await apiClient.patch(`expense/${id}`, body, config);
      return res.data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["expense"] });
      toast.success("Expense updated successfully");
    },
    onError: () => {
      toast.error("Error updating expense");
    },
  });
};

export const useDeleteExpense = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["expense"],

    mutationFn: async (id: string) => {
      const res = await apiClient.delete(`expense/${id}`);
      return res.data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["expense"] });
      toast.success("Product deleted successfully");
    },
    onError: () => {
      toast.error("Error deleting product");
    },
  });
};
