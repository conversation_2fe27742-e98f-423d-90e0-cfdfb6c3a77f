import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";
import { toast } from "react-toastify";

export interface ITransaction {
  _id?: string;
  booking: string;
  guest: string;
  hotel: string;
  date: string;
  description: string;
  referenceNo: string;
  debitAmount: number;
  creditAmount: number;
  balance: number;
  createdBy?: string;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Hook to get all transactions for a specific guest
 * @param guestId - The ID of the guest
 * @returns Query result with transaction data
 */
export const useGetGuestTransactions = (guestId?: string) => {
  return useQuery({
    queryKey: ["guest-transactions", guestId],
    queryFn: async () => {
      try {
        const response = await apiClient.get("transactions", { 
          params: { guest: guestId } 
        });
        return response.data.data;
      } catch (error) {
        console.error("Error fetching guest transactions:", error);
        return [];
      }
    },
    enabled: !!guestId, // Only run the query if guestId is provided
  });
};

/**
 * Hook to get all transactions for a specific booking
 * @param bookingId - The ID of the booking
 * @returns Query result with transaction data
 */
export const useGetBookingTransactions = (bookingId?: string) => {
  return useQuery({
    queryKey: ["booking-transactions", bookingId],
    queryFn: async () => {
      try {
        const response = await apiClient.get("transactions", { 
          params: { booking: bookingId } 
        });
        return response.data.data;
      } catch (error) {
        console.error("Error fetching booking transactions:", error);
        return [];
      }
    },
    enabled: !!bookingId, // Only run the query if bookingId is provided
  });
};

/**
 * Hook to create a new transaction
 * @returns Mutation function to create a transaction
 */
export const useCreateTransaction = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (transactionData: Omit<ITransaction, '_id' | 'createdAt' | 'updatedAt'>) => {
      const response = await apiClient.post("transactions", transactionData);
      return response.data;
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ["guest-transactions", variables.guest] });
      queryClient.invalidateQueries({ queryKey: ["booking-transactions", variables.booking] });
      toast.success("Transaction created successfully");
    },
    onError: (error: any) => {
      console.error("Error creating transaction:", error);
      toast.error(error?.response?.data?.message || "Error creating transaction");
    },
  });
};

/**
 * Hook to update an existing transaction
 * @returns Mutation function to update a transaction
 */
export const useUpdateTransaction = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<ITransaction> }) => {
      const response = await apiClient.put(`transactions/${id}`, data);
      return response.data;
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ["guest-transactions"] });
      queryClient.invalidateQueries({ queryKey: ["booking-transactions"] });
      toast.success("Transaction updated successfully");
    },
    onError: (error: any) => {
      console.error("Error updating transaction:", error);
      toast.error(error?.response?.data?.message || "Error updating transaction");
    },
  });
};

/**
 * Hook to delete a transaction
 * @returns Mutation function to delete a transaction
 */
export const useDeleteTransaction = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: string) => {
      const response = await apiClient.delete(`transactions/${id}`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["guest-transactions"] });
      queryClient.invalidateQueries({ queryKey: ["booking-transactions"] });
      toast.success("Transaction deleted successfully");
    },
    onError: (error: any) => {
      console.error("Error deleting transaction:", error);
      toast.error(error?.response?.data?.message || "Error deleting transaction");
    },
  });
};
