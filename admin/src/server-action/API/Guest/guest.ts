import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";
import { toast } from "react-toastify";

export interface IGuestCreateData {
  name: string;
  email: string;
  phoneNumber: string;
  address?: string;
  nationality?: string;
  idType?: "passport" | "national_id" | "driving_license";
  idNumber?: string;
  dateOfBirth?: string;
  gender?: "Male" | "Female" | "Other";
}

export interface IGuestCreateResponse {
  success: boolean;
  message: string;
  data: {
    _id: string;
    name: string;
    email: string;
    phoneNumber: string;
    address?: string;
    nationality?: string;
    idType?: string;
    idNumber?: string;
    dateOfBirth?: string;
    gender?: string;
    hotel: string;
    role: string;
    emailSent: boolean;
    responseTime: string;
  };
}

/**
 * Hook to create a new guest
 * @returns Mutation function to create a guest
 */
export const useCreateGuest = () => {
  const queryClient = useQueryClient();

  return useMutation<IGuestCreateResponse, Error, IGuestCreateData>({
    mutationFn: async (guestData: IGuestCreateData) => {
      console.log("Creating guest with data:", guestData);

      const response = await apiClient.post("guest", guestData);
      return response.data;
    },
    onSuccess: (data) => {
      // Invalidate user queries to refresh guest lists
      queryClient.invalidateQueries({ queryKey: ["user"] });
      queryClient.invalidateQueries({ queryKey: ["user", "booking"] });

      toast.success(data.message || "Guest created successfully");

      console.log("Guest created successfully:", data);
      return data;
    },
    onError: (error: any) => {
      console.error("Error creating guest:", error);

      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        "Error creating guest";

      toast.error(errorMessage);
    },
  });
};
