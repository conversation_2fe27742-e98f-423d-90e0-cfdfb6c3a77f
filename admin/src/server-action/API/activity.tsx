import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../utils/ApiGateway";
import { toast } from "react-toastify";
import { IActivity } from "../../Interface/activities.interface";

// Get all packages
export const useGetAllActivities = () => {
  return useQuery({
    queryKey: ["activity"],
    queryFn: async () => {
      const res = await apiClient.get<{ data: IActivity[] }>("/activity");
      return res.data.data;
    },
  });
};

// Get package by ID
export const useGetActivityById = (id: string) => {
  return useQuery({
    queryKey: ["activity", id],
    queryFn: async () => {
      const res = await apiClient.get<{ data: IActivity }>(`activity/${id}`);
      return res.data.data;
    },
    enabled: !!id, // Only fetch if id is provided
  });
};

// Create a package
export const useCreateActivity = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (body) => {
      const res = await apiClient.post<{ data: IActivity }>("activity", body);
      return res.data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["activity"] });
      toast.success("Activity created successfully");
    },
    onError: (error: any) => {
      toast.error(error || "Error creating activity");
    },
  });
};

// Update (PATCH) a package
export const useUpdateActivity = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ id, body }: { id: string; body: IActivity }) => {
      const res = await apiClient.patch<{ data: IActivity }>(
        `activity/${id}`,
        body
      );
      console.log("response", res);
      return res.data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["activity"] });
      toast.success("Activity updated successfully");
    },
    onError: (error: any) => {
      toast.error(error || "Error updating package");
    },
  });
};

// Delete a package
export const useDeleteActivity = () => {
  const queryClient = useQueryClient();
  return useMutation<void, Error, string>({
    mutationFn: async (id) => {
      await apiClient.delete(`activity/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["activity"] });
      toast.success("Activity deleted successfully");
    },
    onError: (error: any) => {
      toast.error(error || "Error deleting package");
    },
  });
};
