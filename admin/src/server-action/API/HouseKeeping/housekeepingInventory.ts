import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";
import { toast } from "react-toastify";
import {
  IHousekeepingInventoryItem,
  IAddInventoryItemRequest,
  IStoreWithItems
} from "../../../Interface/housekeepingInventory.interface";
/*eslint-disable @typescript-eslint/no-explicit-any */
// Define the response type for inventory status
interface InventoryStatusResponse {
  data: IStoreWithItems[];
  total: number;
  page: number;
  limit: number;
}

// Get clean items available for housekeeping
export const useGetCleanItems = () => {
  return useQuery<IHousekeepingInventoryItem[]>({
    queryKey: ["housekeeping-inventory-clean"],
    queryFn: async () => {
      const res = await apiClient.get("housekeeping/inventory/clean");
      return res.data.data;
    },
  });
};

// Get inventory status summary - this is what we'll use for all inventory views
export const useGetInventoryStatus = (params: { page?: number; limit?: number; storeType?: string } = {}) => {
  return useQuery<InventoryStatusResponse>({
    queryKey: ["housekeeping-inventory-status", params],
    queryFn: async () => {
      try {
        console.log('Fetching inventory status with params:', params);
        // Check if token exists
        const token = localStorage.getItem("_UPLFMMATRIX");
        if (!token) {
          console.error('No authentication token found');
          throw new Error('Authentication required');
        }

        // Make sure we're using the correct endpoint path
        const res = await apiClient.get("laundry/inventory/status", { params });
        // If the above doesn't work, try with the full path
        // const res = await apiClient.get("/api/v1/en/housekeeping/inventory/status");
        console.log('Inventory status response:', res.data);

        // Handle different response structures
        if (res.data && res.data.data && res.data.data.inventorySummary && Array.isArray(res.data.data.inventorySummary)) {
          console.log('Using new inventorySummary structure');
          console.log('inventorySummary:', res.data.data.inventorySummary);
          console.log('Pagination data:', res.data.data.pagination);

          return {
            data: res.data.data.inventorySummary,
            total: res.data.data.pagination?.total || res.data.data.inventorySummary.length,
            page: res.data.data.pagination?.page || params.page || 1,
            limit: res.data.data.pagination?.limit || params.limit || 10
          };
        } else if (res.data && res.data.data) {
          return {
            data: res.data.data,
            total: res.data.total || res.data.data.length,
            page: res.data.page || params.page || 1,
            limit: res.data.limit || params.limit || 10
          };
        } else if (res.data && Array.isArray(res.data)) {
          return {
            data: res.data,
            total: res.data.length,
            page: params.page || 1,
            limit: params.limit || 10
          };
        } else {
          console.error('Unexpected response structure:', res.data);
          return {
            data: [],
            total: 0,
            page: params.page || 1,
            limit: params.limit || 10
          };
        }
      } catch (error) {
        console.error('Error fetching inventory status:', error);
        throw error;
      }
    },
  });
};

// Take clean items for housekeeping
export const useTakeCleanItems = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["housekeeping-inventory-take-clean"],
    mutationFn: async (data: { itemId: string; quantity: number }) => {
      try {
        console.log('Taking clean items with data:', data);
        // Check if token exists
        const token = localStorage.getItem("_UPLFMMATRIX");
        if (!token) {
          console.error('No authentication token found');
          throw new Error('Authentication required');
        }

        const res = await apiClient.post("housekeeping/inventory/take-clean", data, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
        console.log('Take clean items response:', res.data);
        return res.data.data;
      } catch (error) {
        console.error('Error taking clean items:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["housekeeping-inventory-clean"] });
      queryClient.invalidateQueries({ queryKey: ["housekeeping-inventory-status"] });
      toast.success("Clean items taken successfully");
    },
    onError: (error: any) => {
      toast.error(error || "Error taking clean items");
    },
  });
};

// Return dirty items to laundry
export const useReturnDirtyItems = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["housekeeping-inventory-return-dirty"],
    mutationFn: async (data: { itemId: string; quantity: number }) => {
      const res = await apiClient.post("housekeeping/inventory/return-dirty", data);
      return res.data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["housekeeping-inventory-status"] });
      toast.success("Dirty items returned to laundry successfully");
    },
    onError: (error: any) => {
      toast.error(error || "Error returning dirty items");
    },
  });
};

// Process items in laundry (dirty to clean)
export const useProcessLaundryItems = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["housekeeping-inventory-process-laundry"],
    mutationFn: async (data: { itemId: string; quantity: number }) => {
      const res = await apiClient.post("housekeeping/inventory/process-laundry", data);
      return res.data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["housekeeping-inventory-status"] });
      toast.success("Items processed successfully");
    },
    onError: (error: any) => {
      toast.error(error || "Error processing items");
    },
  });
};

// Return clean items from laundry to main store
export const useReturnCleanItems = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["housekeeping-inventory-return-clean"],
    mutationFn: async (data: { itemId: string; quantity: number }) => {
      const res = await apiClient.post("housekeeping/inventory/return-clean", data);
      return res.data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["housekeeping-inventory-clean"] });
      queryClient.invalidateQueries({ queryKey: ["housekeeping-inventory-status"] });
      toast.success("Clean items returned to main store successfully");
    },
    onError: (error: any) => {
      toast.error(error || "Error returning clean items");
    },
  });
};

// Get inventory categories
export const useGetInventoryCategories = () => {
  return useQuery({
    queryKey: ["housekeeping-inventory-categories"],
    queryFn: async () => {
      try {
        console.log('Fetching inventory categories...');
        const token = localStorage.getItem("_UPLFMMATRIX");
        if (!token) {
          console.error('No authentication token found');
          throw new Error('Authentication required');
        }

        const res = await apiClient.get("item/category");
        console.log('Inventory categories response:', res.data);

        if (res.data && res.data.data) {
          return res.data.data;
        } else {
          console.error('Unexpected response structure:', res.data);
          return [];
        }
      } catch (error) {
        console.error('Error fetching inventory categories:', error);
        throw error;
      }
    },
  });
};

// Get inventory stores
export const useGetInventoryStores = () => {
  return useQuery({
    queryKey: ["housekeeping-inventory-stores"],
    queryFn: async () => {
      try {
        console.log('Fetching inventory stores...');
        const token = localStorage.getItem("_UPLFMMATRIX");
        if (!token) {
          console.error('No authentication token found');
          throw new Error('Authentication required');
        }

        const res = await apiClient.get("store");
        console.log('Inventory stores response:', res.data);

        if (res.data && res.data.data) {
          return res.data.data;
        } else {
          console.error('Unexpected response structure:', res.data);
          return [];
        }
      } catch (error) {
        console.error('Error fetching inventory stores:', error);
        throw error;
      }
    },
  });
};

// Add new inventory item
export const useAddInventoryItem = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["housekeeping-inventory-add-item"],
    mutationFn: async (data: IAddInventoryItemRequest) => {
      try {

        const res = await apiClient.post("housekeeping/inventory/add", data, {
        });
        console.log('Add inventory item response:', res.data);
        return res.data.data;
      } catch (error) {
        console.error('Error adding inventory item:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["housekeeping-inventory-status"] });
      toast.success("Inventory item added successfully");
    },
    onError: (error: any) => {
      toast.error(error || "Error adding inventory item");
    },
  });
};


