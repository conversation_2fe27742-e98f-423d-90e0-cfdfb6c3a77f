import { useQuery } from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";

export interface RoomDayAvailability {
  _id: string;
  roomNo: string;
  roomType: {
    _id: string;
    hotel: string;
    name: string;
    isActive: boolean;
  };
  basePrice: number;
  floor: number;
  capacity: {
    standard: number;
    maximum: number;
  };
  currentStatus: string;
  status: string;
  bookingId: string | null;
  guestId: string | null;
  guest?: {
    _id: string;
    name: string;
    email?: string;
    phoneNumber?: string;
  };
  booking?: {
    _id: string;
    bookingId: string;
    reservationDate?: string;
    checkIn?: string | null;
    expectedCheckOut?: string | null;
    status?: string;
    amount?: number;
    amountPaid?: number;
    paymentStatus?: string;
    specialRequests?: string;
    pax?: {
      adults: number;
      children: number;
      infants: number;
    };
  };
}

export interface RoomDayAvailabilityResponse {
  success: boolean;
  data: {
    day: number;
    month: number;
    year: number;
    date: string;
    rooms: RoomDayAvailability[];
  };
}

export const useGetRoomDayAvailability = (date?: string, roomType?: string) => {
  return useQuery<RoomDayAvailabilityResponse['data']>({
    queryKey: ["room-day-availability", date, roomType],
    queryFn: async () => {
      try {
        if (!date) {
          throw new Error("Date is required for day availability");
        }

        const queryParams = new URLSearchParams();
        queryParams.append("date", date);

        if (roomType && roomType !== "all") {
          queryParams.append("roomType", roomType);
        }

        const url = `rooms/availability/day?${queryParams.toString()}`;

        console.log('Fetching room day availability from:', url);
        const response = await apiClient.get<RoomDayAvailabilityResponse>(url);
        console.log('Room day availability response:', response.data);
        return response.data.data;
      } catch (error) {
        console.error('Error fetching room day availability:', error);
        throw error;
      }
    },
    enabled: !!date, // Only run the query if date is provided
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};
