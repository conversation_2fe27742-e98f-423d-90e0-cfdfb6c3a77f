import { useQuery } from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";
import { IRoom } from "../../../Interface/room.interface";


export interface RoomFilterParams {
  roomType?: string;
  acType?: string;
  dateFrom?: string;
  dateTo?: string;
  bedTypes?: string;
  status?: string;
  floor?: number;
  isVip?: boolean;
  hasSmoking?: boolean;
}

export const useGetAllRoom = (filters?: RoomFilterParams) => {
  return useQuery<IRoom[]>({
    queryKey: ["room", filters],
    queryFn: async () => {
      const queryParams = new URLSearchParams();

      if (filters) {
        if (filters.roomType && filters.roomType !== 'all') {
          queryParams.append("roomType", filters.roomType);
        }

        if (filters.acType && filters.acType !== 'all') {
          queryParams.append("features.acType", filters.acType);
        }

        if (filters.dateFrom) {
          queryParams.append("dateFrom", filters.dateFrom);
        }

        if (filters.dateTo) {
          queryParams.append("dateTo", filters.dateTo);
        }

        if (filters.bedTypes && filters.bedTypes !== 'all') {
          queryParams.append("beds.types", filters.bedTypes);
        }

        if (filters.status) {
          queryParams.append("status", filters.status);
        }

        if (filters.floor) {
          queryParams.append("floor", String(filters.floor));
        }

        if (filters.isVip !== undefined) {
          queryParams.append("isVip", String(filters.isVip));
        }

        if (filters.hasSmoking !== undefined) {
          queryParams.append("hasSmoking", String(filters.hasSmoking));
        }
      }

      const queryString = queryParams.toString();
      const url = queryString ? `room?${queryString}` : "room";

      const response = await apiClient.get(url);
      return response?.data?.data;
    },
  });
};
