import { useQuery } from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";
import { IRoom, RoomStatus } from "../../../Interface/room.interface";

// Summary interfaces
export interface DailySummary {
  day: number;
  date: string;
  available: number;
  booked: number;
  occupied: number;
  "check-in": number;
  "check-out": number;
  maintenance: number;
  cleaning: number;
  total: number;
  occupancyRate: number;
}

export interface MonthSummary {
  totalRooms: number;
  averageOccupancyRate: number;
  peakOccupancyDay: DailySummary;
  lowestOccupancyDay: DailySummary;
  totalCheckIns: number;
  totalCheckOuts: number;
}

export interface RoomAvailabilitySummary {
  month: number;
  year: number;
  daysInMonth: number;
  dailySummary: DailySummary[];
  monthSummary: MonthSummary;
}

export interface RoomAvailabilitySummaryResponse {
  success: boolean;
  data: RoomAvailabilitySummary;
}

// Detailed room availability interfaces
export interface DailyStatus {
  day: number;
  date: string;
  status: RoomStatus;
  bookingId: string | null;
  guestId: string | null;
}

export interface RoomAvailabilityDetail {
  _id: string;
  roomNo: string;
  roomType: string;
  basePrice: number;
  floor: number;
  capacity: {
    standard: number;
    maximum: number;
  };
  currentStatus: RoomStatus;
  dailyStatus: DailyStatus[];
}

export interface RoomAvailabilityDetailResponse {
  success: boolean;
  data: {
    month: number;
    year: number;
    daysInMonth: number;
    rooms: RoomAvailabilityDetail[];
  };
}

// Summary hook
export const useGetRoomAvailabilitySummary = (date?: string, roomType?: string) => {
  return useQuery<RoomAvailabilitySummary>({
    queryKey: ["room-availability-summary", date, roomType],
    queryFn: async () => {
      const queryParams = new URLSearchParams();

      if (date) {
        queryParams.append("date", date);
      }

      if (roomType && roomType !== "all") {
        queryParams.append("roomType", roomType);
      }

      const queryString = queryParams.toString();
      const url = queryString ? `rooms/availability/summary?${queryString}` : "rooms/availability/summary";

      const response = await apiClient.get<RoomAvailabilitySummaryResponse>(url);
      return response.data.data;
    },
  });
};

// Detailed room availability hook
export const useGetRoomAvailabilityDetail = (date?: string, roomType?: string) => {
  return useQuery<RoomAvailabilityDetailResponse['data']>({
    queryKey: ["room-availability-detail", date, roomType],
    queryFn: async () => {
      try {
        const queryParams = new URLSearchParams();

        if (date) {
          queryParams.append("date", date);
        }

        if (roomType && roomType !== "all") {
          queryParams.append("roomType", roomType);
        }

        const queryString = queryParams.toString();
        const url = queryString ? `rooms/availability?${queryString}` : "rooms/availability";

        console.log('Fetching room availability from:', url);
        const token = localStorage.getItem("_UPLFMMATRIX");
        console.log('Auth token exists:', !!token);

        const response = await apiClient.get<RoomAvailabilityDetailResponse>(url);
        console.log('Room availability response:', response.data);
        return response.data.data;
      } catch (error) {
        console.error('Error fetching room availability:', error);
        throw error;
      }
    },
    retry: 1,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};
