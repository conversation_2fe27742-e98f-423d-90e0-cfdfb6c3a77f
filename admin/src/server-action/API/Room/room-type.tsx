import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";
import { toast } from "react-toastify";

export interface RoomType {
  _id: string;
  name: string;
  hotel?: string;
  [key: string]: any;
}

interface RoomTypeFormValue {
  name: string;
}
export const useGetAllRoomType = () => {
  return useQuery<RoomType[]>({
    queryKey: ["room-type"],
    queryFn: async () => {
      const response = await apiClient.get("roomtype");
      return response?.data?.data;
    },
  });
};

export const useCreateRoomType = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (body: RoomTypeFormValue) => {
      const res = await apiClient.post("roomtype", body);
      return res.data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["room-type"] });
      toast.success("Room Type created successfully");
    },
    onError: () => {
      toast.error("Error creating activity");
    },
  });
};

export const useUpdateRoomType = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      roomTypeData,
      _id,
    }: {
      roomTypeData: RoomTypeFormValue;
      _id: string;
    }) => {
      if (!_id) throw new Error("Room Type ID is required");
      return apiClient.patch(`roomtype/${_id}`, roomTypeData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["room-type"] });
      toast.success("Room Type Updated Successfully");
    },
    onError: () => {
      toast.error("Error Updating Room Type");
    },
  });
};

export const useDeleteRoomType = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (_id: string) => {
      if (!_id) throw new Error("Room Type ID is required");
      return apiClient.delete(`roomtype/${_id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["room-type"] });
      toast.success("Room Type Deleted Successfully");
    },
    onError: () => {
      toast.error("Error Deleting Room Type");
    },
  });
};
