import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-toastify";
import { apiClient } from "../utils/ApiGateway";

// Interface for financial transaction
export interface IFinancialTransaction {
  _id?: string;
  hotel?: string;
  transactionType: "income" | "expense";
  amount: number;
  date: string;
  description?: string;
  category:
    | string
    | {
        _id: string;
        hotel: string;
        name: string;
        isActive: boolean;
        __v: number;
      };
  paymentMethod?: string;
  status?: string;
  reference?: string;
  expense?: {
    _id: string;
    category: {
      _id: string;
      hotel: string;
      name: string;
      isActive: boolean;
      __v: number;
    };
    title: string;
  };
  notes?: string;
  attachments?: string[];
  booking?: {
    _id: string;
    bookingId: string;
  };
  guest?: {
    _id: string;
    name: string;
    email: string;
  };
  vendor?: string;
  bank?: string;
  createdBy?: {
    _id: string;
    name: string;
  };
  createdAt?: string;
  updatedAt?: string;
  __v?: number;
}

// Interface for financial dashboard overview
export interface IFinancialDashboardOverview {
  period: {
    name: string;
    startDate: string;
    endDate: string;
  };
  summary: {
    totalIncome: number;
    totalExpenses: number;
    netProfit: number;
    incomeTransactionCount: number;
    expenseTransactionCount: number;
  };
  topIncomeCategories: {
    _id: string;
    total: number;
    count: number;
  }[];
  topExpenseCategories: {
    _id: string;
    total: number;
    count: number;
  }[];
  recentTransactions: IFinancialTransaction[];
}

// Interface for transaction summary
export interface ITransactionSummary {
  totalIncome: number;
  totalExpense: number;
  netBalance: number;
  period: string;
}

// Interface for category breakdown
export interface ICategoryBreakdown {
  category: string;
  amount: number;
  percentage: number;
  type: "income" | "expense";
}

// Get all financial transactions with optional filtering
export const useGetAllFinancialTransactions = (params = {}) => {
  return useQuery({
    queryKey: ["financial-transactions", params],
    queryFn: async () => {
      try {
        console.log("Fetching financial transactions with params:", params);
        // Try different possible API endpoints
        let res;
        try {
          res = await apiClient.get("financial-transactions", { params });
        } catch (e) {
          console.log("Trying alternative endpoint");
          try {
            res = await apiClient.get("transaction/financial", { params });
          } catch (e2) {
            console.log("Trying another alternative endpoint");
            res = await apiClient.get("transaction", { params });
          }
        }
        console.log("Financial transactions API response:", res.data);

        // Check if the response has the expected structure
        if (
          res.data &&
          res.data.data &&
          Array.isArray(res.data.data.transactions)
        ) {
          // New API format with pagination
          return {
            transactions: res.data.data.transactions,
            pagination: res.data.data.pagination || {
              total: res.data.data.transactions.length,
              page: 1,
              limit: res.data.data.transactions.length,
              pages: 1,
            },
          };
        } else if (res.data && Array.isArray(res.data.data)) {
          // Alternative structure
          return {
            transactions: res.data.data,
            pagination: {
              total: res.data.data.length,
              page: 1,
              limit: res.data.data.length,
              pages: 1,
            },
          };
        } else if (res.data && Array.isArray(res.data)) {
          // Direct array response
          return {
            transactions: res.data,
            pagination: {
              total: res.data.length,
              page: 1,
              limit: res.data.length,
              pages: 1,
            },
          };
        } else {
          console.error("Unexpected API response structure:", res.data);
          return {
            transactions: [],
            pagination: { total: 0, page: 1, limit: 10, pages: 0 },
          };
        }
      } catch (error) {
        console.error("Error fetching financial transactions:", error);
        return {
          transactions: [],
          pagination: { total: 0, page: 1, limit: 10, pages: 0 },
        };
      }
    },
  });
};

// Get a specific financial transaction by ID
export const useGetFinancialTransactionById = (id: string) => {
  return useQuery({
    queryKey: ["financial-transaction", id],
    queryFn: async () => {
      if (!id) return null;
      const res = await apiClient.get(`financial-transactions/${id}`);
      return res.data.data;
    },
    enabled: !!id, // Only run when ID is available
  });
};

// Create a new financial transaction
export const useCreateFinancialTransaction = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["financial-transaction"],
    mutationFn: async (transactionData: any) => {
      let config = {};
      if (transactionData instanceof FormData) {
        config = {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        };
      }

      try {
        console.log(
          "Creating financial transaction with data:",
          transactionData
        );
        const res = await apiClient.post(
          "financial-transactions",
          transactionData,
          config
        );
        console.log("Create transaction API response:", res.data);
        return res.data.data;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["financial-transactions"] });
      toast.success("Transaction created successfully");
    },
    onError: (error) => {
      toast.error("Error creating transaction");
    },
  });
};

// Update a financial transaction
export const useUpdateFinancialTransaction = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["financial-transaction"],
    mutationFn: async ({ id, body }: any) => {
      const config =
        body instanceof FormData
          ? { headers: { "Content-Type": "multipart/form-data" } }
          : { headers: { "Content-Type": "application/json" } };

      try {
        const res = await apiClient.put(
          `financial-transactions/${id}`,
          body,
          config
        );
        return res.data.data;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["financial-transactions"] });
      toast.success("Transaction updated successfully");
    },
    onError: (error) => {
      toast.error("Error updating transaction");
    },
  });
};

// Delete a financial transaction
export const useDeleteFinancialTransaction = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["financial-transaction"],
    mutationFn: async (id: string) => {
      try {
        const res = await apiClient.delete(`financial-transactions/${id}`);
        return res.data.data;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["financial-transactions"] });
      toast.success("Transaction deleted successfully");
    },
    onError: (error) => {
      toast.error("Error deleting transaction");
    },
  });
};

// Get financial dashboard overview
export const useGetFinancialDashboardOverview = (params = {}) => {
  return useQuery({
    queryKey: ["financial-dashboard-overview", params],
    queryFn: async () => {
      try {
        const res = await apiClient.get("financial-dashboard/overview", {
          params,
        });

        if (res.data && res.data.data) {
          return res.data.data;
        } else if (res.data) {
          return res.data;
        } else {
          return {
            period: {
              name: "month",
              startDate: "",
              endDate: "",
            },
            summary: {
              totalIncome: 0,
              totalExpenses: 0,
              netProfit: 0,
              incomeTransactionCount: 0,
              expenseTransactionCount: 0,
            },
            topIncomeCategories: [],
            topExpenseCategories: [],
            recentTransactions: [],
          };
        }
      } catch (error) {
        console.error("Error fetching financial dashboard overview:", error);
        return {
          period: {
            name: "month",
            startDate: "",
            endDate: "",
          },
          summary: {
            totalIncome: 0,
            totalExpenses: 0,
            netProfit: 0,
            incomeTransactionCount: 0,
            expenseTransactionCount: 0,
          },
          topIncomeCategories: [],
          topExpenseCategories: [],
          recentTransactions: [],
        };
      }
    },
  });
};

// Get transaction summary report (legacy - keeping for backward compatibility)
export const useGetTransactionSummary = (params = {}) => {
  return useQuery({
    queryKey: ["transaction-summary", params],
    queryFn: async () => {
      try {
        console.log("Fetching transaction summary with params:", params);
        // Try the new endpoint first, then fall back to the old one
        try {
          const res = await apiClient.get("financial-dashboard/overview", {
            params,
          });
          console.log("Financial dashboard overview API response:", res.data);

          if (res.data && res.data.data && res.data.data.summary) {
            // Convert from new format to old format
            return {
              totalIncome: res.data.data.summary.totalIncome || 0,
              totalExpense: res.data.data.summary.totalExpenses || 0,
              netBalance: res.data.data.summary.netProfit || 0,
              period: res.data.data.period?.name || "month",
            };
          }
        } catch (e) {}

        // Try the old endpoint
        const res = await apiClient.get(
          "financial-transactions/reports/summary",
          { params }
        );

        if (res.data && res.data.data) {
          return res.data.data;
        } else if (res.data) {
          return res.data;
        } else {
          return { totalIncome: 0, totalExpense: 0, netBalance: 0 };
        }
      } catch (error) {
        return { totalIncome: 0, totalExpense: 0, netBalance: 0 };
      }
    },
  });
};

// Get category breakdown report
export const useGetCategoryBreakdown = (params = {}) => {
  return useQuery({
    queryKey: ["category-breakdown", params],
    queryFn: async () => {
      try {
        const res = await apiClient.get(
          "financial-transactions/reports/category-breakdown",
          { params }
        );

        if (res.data && res.data.data) {
          return res.data.data;
        } else if (res.data) {
          return res.data;
        } else {
          return [];
        }
      } catch (error) {
        return [];
      }
    },
  });
};
