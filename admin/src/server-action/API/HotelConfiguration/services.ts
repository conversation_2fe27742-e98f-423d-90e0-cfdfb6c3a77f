import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-toastify";
import { apiClient } from "../../utils/ApiGateway";

interface Service {
  _id: string;
  name: string;
  price: number;
  description?: string;
  taxRate?: number;
  images?: string[];
}

interface CreateServiceData {
  name: string;
  price: number;
  description?: string;
  taxRate?: number;
  images?: string[];
}

interface UpdateServiceData {
  id: string;
  body: any;
}

// Get all services
export const useGetAllService = () => {
  return useQuery<Service[]>({
    queryKey: ["service"],
    queryFn: async () => {
      const res = await apiClient.get<{ data: Service[] }>("service");
      return res.data.data;
    },
  });
};

// Get service by ID
export const useGetServiceById = (id: string) => {
  return useQuery<Service>({
    queryKey: ["service", id],
    queryFn: async () => {
      const res = await apiClient.get<{ data: Service }>(`service/${id}`);
      return res.data.data;
    },
    enabled: !!id,
  });
};

// Create a service
export const useCreateService = () => {
  const queryClient = useQueryClient();
  return useMutation<Service, Error, CreateServiceData>({
    mutationFn: async (body: any) => {
      let config = {};
      if (body instanceof FormData) {
        config = {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        };
      }
      const res = await apiClient.post<{ data: Service }>(
        "service",
        body,
        config
      );
      return res?.data?.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["service"] });
      toast.success("Service created successfully");
    },
    onError: () => {
      toast.error("Error creating service");
    },
  });
};

// Update service
export const useUpdateService = () => {
  const queryClient = useQueryClient();
  return useMutation<Service, Error, UpdateServiceData>({
    mutationFn: async ({ id, body }) => {
      const config =
        body instanceof FormData
          ? { headers: { "Content-Type": "multipart/form-data" } }
          : { headers: { "Content-Type": "application/json" } };

      const res = await apiClient.patch<{ data: Service }>(
        `service/${id}`,
        body,
        config
      );
      return res.data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["service"] });
      toast.success("Service updated successfully");
    },
    onError: () => {
      toast.error("Error updating service");
    },
  });
};

// Delete service
export const useDeleteService = () => {
  const queryClient = useQueryClient();
  return useMutation<void, Error, string>({
    mutationFn: async (id) => {
      await apiClient.delete(`service/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["service"] });
      toast.success("Service deleted successfully");
    },
    onError: () => {
      toast.error("Error deleting service");
    },
  });
};
