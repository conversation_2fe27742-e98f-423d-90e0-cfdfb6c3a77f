import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";
import { toast } from "react-toastify";
import { IFloorPlan } from "../../../Interface/floorplan.interfacce";

export const useGetAllFloorPlans = () => {
  return useQuery<IFloorPlan[]>({
    queryKey: ["floorPlan"],
    queryFn: async () => {
      const response = await apiClient.get("floorplan");
      return response.data?.data;
    },
  });
};

export const useGetFloorPlanById = (id: string) => {
  return useQuery<IFloorPlan | null>({
    queryKey: ["floorPlan"],
    queryFn: async () => {
      if (!id) return null;
      const response = await apiClient.get(`floorplan/${id}`);
      return response.data?.data;
    },
    enabled: !!id,
  });
};

export const useCreateFloorPlan = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (floorPlanData: IFloorPlan) => {
      return apiClient.post("floorplan", floorPlanData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["floorPlan"] });
      toast.success("Floor Plan Created Successfully");
    },
    onError: () => {
      toast.error("Error Creating Floor Plan");
    },
  });
};

export const useUpdateFloorPlan = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      floorPlanData,
      _id,
    }: {
      floorPlanData: IFloorPlan;
      _id: string;
    }) => {
      if (!_id) throw new Error("Floor Plan ID is required");
      return apiClient.patch(`floorplan/${_id}`, floorPlanData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["floorPlan"] });
      toast.success("Floor Plan Updated Successfully");
    },
    onError: () => {
      toast.error("Error Updating Floor Plan");
    },
  });
};

export const useDeleteFloorPlan = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (_id: string) => {
      if (!_id) throw new Error("Floor Plan ID is required");
      return apiClient.delete(`floorplan/${_id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["floorPlan"] });
      toast.success("Floor Plan Deleted Successfully");
    },
    onError: () => {
      toast.error("Error Deleting Floor Plan");
    },
  });
};
