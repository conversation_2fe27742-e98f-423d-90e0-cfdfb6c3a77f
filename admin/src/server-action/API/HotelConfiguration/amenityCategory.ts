import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";
import { toast } from "react-toastify";

interface IAmenityCategory {
  _id: string;
  name: string;
  description?: string;
}

export const useGetAmenityCategories = () => {
  return useQuery<IAmenityCategory[]>({
    queryKey: ["amenity-categories"],
    queryFn: async () => {
      const response = await apiClient.get("/amenity-category");
      return response?.data?.data;
    },
  });
};

export const useCreateAmenityCategory = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (categoryData: { name: string; description?: string }) => {
      const response = await apiClient.post("/amenity-category", categoryData);
      return response?.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["amenity-categories"] });
      toast.success("Amenity category created successfully");
    },
    onError: () => {
      toast.error("Error creating amenity category");
    },
  });
};

export const useUpdateAmenityCategory = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      categoryData,
      _id,
    }: {
      categoryData: any;
      _id: string;
    }) => {
      if (!_id) throw new Error("Category ID is required");
      return apiClient.patch(`amenity-category/${_id}`, categoryData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["amenity-categories"] });
      toast.success("Amenity category updated successfully");
    },
    onError: () => {
      toast.error("Error updating amenity category");
    },
  });
};

export const useDeleteAmenityCategory = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: string) => {
      return apiClient.delete(`amenity-category/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["amenity-categories"] });
      toast.success("Amenity category deleted successfully");
    },
    onError: () => {
      toast.error("Error deleting amenity category");
    },
  });
};
