import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";
import { toast } from "react-toastify";
import { IBed } from "../../../Interface/bed.interface";

export const useGetBeds = () => {
  return useQuery<IBed[]>({
    queryKey: ["bed"],
    queryFn: async () => {
      const response = await apiClient.get("/bed");
      return response?.data?.data;
    },
  });
};

export const useGetBedById = (id: string) => {
  return useQuery({
    queryKey: ["bed", id],
    queryFn: async () => {
      const { data } = await apiClient.get(`/bed/${id}`);
      return data?.data;
    },
    enabled: !!id,
  });
};

export const useCreateBed = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (bedData: IBed) => {
      return apiClient.post("bed", bedData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["bed"] });
      toast.success("Bed Created Successfully");
    },
    onError: () => {
      toast.error("Error Creating Bed");
    },
  });
};

export const useUpdateBed = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ bedData, _id }: { bedData: IBed; _id: string }) => {
      return apiClient.patch(`bed/${_id}`, bedData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["bed"] });
      toast.success("Bed Updated Successfully");
    },
    onError: () => {
      toast.error("Error Updating Bed");
    },
  });
};

export const useDeleteBed = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: string) => {
      await apiClient.delete(`bed/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["bed"] });
      toast.success("Bed Deleted Successfully");
    },
    onError: () => {
      toast.error("Error Deleting Bed");
    },
  });
};
