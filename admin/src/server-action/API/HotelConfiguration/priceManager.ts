import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";
import { toast } from "react-toastify";
import { IDateBasedPrice } from "../../../Interface/room.interface";
import { calculateMembershipDiscount } from "../../utils/membershipDiscount";
/*eslint-disable @typescript-eslint/no-explicit-any */
// Interface for date-based price entry
export interface IDateBasedPriceInput {
  fromDate: string;
  fromTime?: string;
  toDate: string;
  toTime?: string;
  price: number;
  isPercentage?: boolean;
  status?: "elevated" | "offered";
}

// Interface for room price update request
export interface IRoomPriceUpdate {
  roomTypeId?: string;
  roomTypeIds?: string[];
  fromDate: string;
  fromTime?: string;
  toDate: string;
  toTime?: string;
  price: number;
  isPercentage?: boolean;
  status?: "elevated" | "offered";
}

// Interface for price calculation request
export interface IPriceCalculationRequest {
  roomId: string;
  checkInDate: string;
  checkOutDate: string;
  userId?: string; // Optional user ID to apply membership discount
}

// Interface for price calculation response
export interface IPriceCalculationResponse {
  roomId: string;
  roomNo: string;
  basePrice: number;
  totalPrice: number;
  nights: number;
  checkIn: string;
  checkOut: string;
  membershipDiscount?: {
    name: string;
    discountPercentage: number;
    discountAmount: number;
  };
  priceBreakdown: {
    date: string;
    price: number;
    rule: string;
  }[];
}

// Interface for room date-based prices response
export interface IRoomDateBasedPricesResponse {
  roomId: string;
  roomNo: string;
  dateBasedPrices: IDateBasedPrice[];
}

// Legacy interface for backward compatibility
export interface IPriceManager {
  _id?: string;
  roomType: string;
  acNon?: "none" | "central" | "split" | "window";
  totalPrice?: number;
  vaccantOffer?: string;
  offer?: "elevated" | "offered";
  rate?: number;
  date?: string;
  time?: string;
  dateFrom?: string;
  dateTo?: string;
  offeredPrice?: number;
  validityDate?: string;
  isActive?: boolean;
}

// Update room prices (single or bulk)
export const useUpdateRoomPrices = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (priceData: IRoomPriceUpdate) => {
      return apiClient.post("price", priceData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["room-prices"] });
      toast.success("Room prices updated successfully");
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Error updating room prices"
      );
    },
  });
};

// Get all date-based prices for a room
export const useGetRoomDateBasedPrices = (roomId: string) => {
  return useQuery<IRoomDateBasedPricesResponse>({
    queryKey: ["room-prices", roomId],
    queryFn: async () => {
      if (!roomId) throw new Error("Room ID is required");
      const response = await apiClient.get(`${roomId}/prices`);
      return response.data?.data;
    },
    enabled: !!roomId,
  });
};

// Delete a specific date-based price entry
export const useDeleteDateBasedPrice = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      roomId,
      priceId,
    }: {
      roomId: string;
      priceId: string;
    }) => {
      if (!roomId || !priceId)
        throw new Error("Room ID and Price ID are required");
      return apiClient.delete(`${roomId}/prices/${priceId}`);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ["room-prices", variables.roomId],
      });
      toast.success("Price entry deleted successfully");
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Error deleting price entry"
      );
    },
  });
};

// Update status of a date-based price entry
export const useUpdateDateBasedPriceStatus = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      roomId,
      priceId,
      status,
    }: {
      roomId: string;
      priceId: string;
      status: "elevated" | "offered";
    }) => {
      if (!roomId || !priceId)
        throw new Error("Room ID and Price ID are required");
      if (!status) throw new Error("Status is required");
      return apiClient.patch(`room/${roomId}/prices/${priceId}/status`, {
        status,
      });
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ["room-prices", variables.roomId],
      });
      toast.success("Price status updated successfully");
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Error updating price status"
      );
    },
  });
};

// Calculate room price with membership discount

// Calculate room price for a specific date range
export const useCalculateRoomPrice = () => {
  return useMutation({
    mutationFn: async (calculationData: IPriceCalculationRequest) => {
      // First get the base price calculation from the API
      const response = await apiClient.post("price/calculate", calculationData);
      const priceData = response.data?.data as IPriceCalculationResponse;

      // If a user ID is provided, check for membership discount
      if (calculationData.userId && priceData) {
        try {
          const membershipDiscount = await calculateMembershipDiscount(
            calculationData.userId,
            priceData.totalPrice
          );

          // If a discount applies, update the total price and add discount info
          if (membershipDiscount) {
            // Apply the discount to the total price
            priceData.totalPrice -= membershipDiscount.discountAmount;
            // Add membership discount info to the response
            priceData.membershipDiscount = membershipDiscount;
          }
        } catch (error) {
          console.error("Error applying membership discount:", error);
        }
      }

      return priceData;
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Error calculating room price"
      );
    },
  });
};

// Legacy functions for backward compatibility

// Get all price configurations (legacy)
export const useGetAllPrices = () => {
  return useQuery<IPriceManager[]>({
    queryKey: ["price-manager"],
    queryFn: async () => {
      const response = await apiClient.get("price");
      return response.data?.data || [];
    },
  });
};

// Get price configuration by ID (legacy)
export const useGetPriceById = (id: string) => {
  return useQuery<IPriceManager | null>({
    queryKey: ["price-manager", id],
    queryFn: async () => {
      if (!id) return null;
      const response = await apiClient.get(`price/${id}`);
      return response.data?.data;
    },
    enabled: !!id,
  });
};

// Create a new price configuration (legacy)
export const useCreatePrice = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (priceData: IPriceManager) => {
      return apiClient.post("price", priceData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["price-manager"] });
      toast.success("Price Configuration Created Successfully");
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Error Creating Price Configuration"
      );
    },
  });
};

// Update an existing price configuration (legacy)
export const useUpdatePrice = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      priceData,
      _id,
    }: {
      priceData: IPriceManager;
      _id: string;
    }) => {
      if (!_id) throw new Error("Price Configuration ID is required");
      return apiClient.patch(`price/${_id}`, priceData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["price-manager"] });
      toast.success("Price Configuration Updated Successfully");
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Error Updating Price Configuration"
      );
    },
  });
};

// Delete a price configuration (legacy)
export const useDeletePrice = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (_id: string) => {
      if (!_id) throw new Error("Price Configuration ID is required");
      return apiClient.delete(`price/${_id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["price-manager"] });
      toast.success("Price Configuration Deleted Successfully");
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Error Deleting Price Configuration"
      );
    },
  });
};
