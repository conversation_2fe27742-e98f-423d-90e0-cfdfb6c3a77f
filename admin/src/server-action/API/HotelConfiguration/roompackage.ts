import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-toastify";
import { IPackage } from "../../../Interface/package.interface";
import { apiClient } from "../../utils/ApiGateway";

export const useGetAllPackages = () => {
  return useQuery<IPackage[]>({
    queryKey: ["package"],
    queryFn: async () => {
      const response = await apiClient.get("package");
      return response.data?.data;
    },
  });
};

export const useGetPackageById = (id: string) => {
  return useQuery<IPackage | null>({
    queryKey: ["package", id],
    queryFn: async () => {
      if (!id) return null;
      const response = await apiClient.get(`package/${id}`);
      return response.data?.data;
    },
    enabled: !!id,
  });
};

export const useCreatePackage = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (packageData: any) => {
      let config = {};
      if (packageData instanceof FormData) {
        config = {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        };
      }
      const res = await apiClient.post("package", packageData, config);
      return res?.data?.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["package"] });
      toast.success("Package Created Successfully");
    },
    onError: () => {
      toast.error("Error Creating Package");
    },
  });
};

export const useUpdatePackage = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      packageData,
      _id,
    }: {
      packageData: FormData;
      _id: string;
    }) => {
      if (!_id) throw new Error("Package ID is required");

      // For FormData, don't set Content-Type header - let the browser set it
      const res = await apiClient.patch(`package/${_id}`, packageData);
      return res?.data?.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["package"] });
      toast.success("Package Updated Successfully");
    },
    onError: (error) => {
      console.error("Update error:", error);
      toast.error("Error Updating Package");
    },
  });
};

export const useDeletePackage = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (_id: string) => {
      if (!_id) throw new Error("Package ID is required");
      return apiClient.delete(`package/${_id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["package"] });
      toast.success("Package Deleted Successfully");
    },
    onError: () => {
      toast.error("Error Deleting Package");
    },
  });
};
