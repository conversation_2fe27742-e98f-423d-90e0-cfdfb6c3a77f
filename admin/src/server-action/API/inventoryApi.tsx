import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../utils/ApiGateway";
import { toast } from "react-toastify";
import {
  IHousekeepingInventoryItem,
  IInventoryStatusSummary,
  IAddInventoryItemRequest,
  IStoreWithItems,
} from "../../Interface/housekeepingInventory.interface";

// Define the response type for inventory status
interface InventoryStatusResponse {
  data: IStoreWithItems[];
  total: number;
  page: number;
  limit: number;
}

export const useGetInventory = ({ params = {} }) => {
  return useQuery({
    queryKey: ["inventory", params],
    queryFn: async () => {
      const res = await apiClient.get("/inventory", { params });
      return res.data.data;
    },
  });
};

export const useCreateInventory = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["inventory"],
    mutationFn: async (inventoryData: any) => {
      const res = await apiClient.post("/inventory", inventoryData);
      return res.data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["inventory"] });
      toast.success("Inventory created successfully");
    },
    onError: (error: any) => {
      toast.error(error || "Error creating inventory");
    },
  });
};

export const useDeleteInventory = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["inventory"],
    mutationFn: async (id: any) => {
      const res = await apiClient.delete(`/inventory/${id}`);
      return res.data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["inventory"] });
      toast.success("Inventory deleted successfully");
    },
    onError: (error: any) => {
      toast.error(error || "Error deleting inventory");
    },
  });
};

export const useUpdateInventory = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["inventory"],
    mutationFn: async ({
      id,
      inventoryData,
    }: {
      id: string;
      inventoryData: any;
    }) => {
      const res = await apiClient.patch(`/inventory/${id}`, inventoryData);
      return res.data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["inventory"] });
      toast.success("Inventory updated successfully");
    },
    onError: (error: any) => {
      toast.error(error || "Error updating inventory");
    },
  });
};

// Inventory API Functions

// Get inventory status summary - this is what we'll use for all inventory views
export const useGetInventoryStatus = (
  params: { page?: number; limit?: number; storeType?: string } = {}
) => {
  return useQuery<InventoryStatusResponse>({
    queryKey: ["inventory-status", params],
    queryFn: async () => {
      try {
        console.log("Fetching inventory status with params:", params);
        // Check if token exists
        const token = localStorage.getItem("_UPLFMMATRIX");
        if (!token) {
          console.error("No authentication token found");
          throw new Error("Authentication required");
        }

        // Make sure we're using the correct endpoint path
        const res = await apiClient.get("/laundry/inventory/status", {
          params,
        });
        console.log("Inventory status API response received");
        console.log("Response status:", res.status);
        console.log("Response data type:", typeof res.data);
        console.log("Response data is array?", Array.isArray(res.data));
        console.log("Full response data:", res.data);

        // Handle different response structures
        if (
          res.data &&
          res.data.data &&
          res.data.data.inventorySummary &&
          Array.isArray(res.data.data.inventorySummary)
        ) {
          console.log("Using new inventorySummary structure");
          console.log(
            "inventorySummary type:",
            typeof res.data.data.inventorySummary
          );
          console.log(
            "inventorySummary is array?",
            Array.isArray(res.data.data.inventorySummary)
          );
          console.log("inventorySummary:", res.data.data.inventorySummary);
          console.log("Pagination data:", res.data.data.pagination);

          return {
            data: res.data.data.inventorySummary,
            total:
              res.data.data.pagination?.total ||
              res.data.data.inventorySummary.length,
            page: res.data.data.pagination?.page || params.page || 1,
            limit: res.data.data.pagination?.limit || params.limit || 10,
          };
        } else if (res.data && res.data.data) {
          console.log("Using res.data.data structure");
          console.log("res.data.data type:", typeof res.data.data);
          console.log("res.data.data is array?", Array.isArray(res.data.data));
          console.log("res.data.data:", res.data.data);
          return {
            data: res.data.data,
            total: res.data.total || res.data.data.length,
            page: res.data.page || params.page || 1,
            limit: res.data.limit || params.limit || 10,
          };
        } else if (res.data && Array.isArray(res.data)) {
          console.log("Using res.data array structure");
          return {
            data: res.data,
            total: res.data.length,
            page: params.page || 1,
            limit: params.limit || 10,
          };
        } else {
          console.error("Unexpected response structure:", res.data);
          return {
            data: [],
            total: 0,
            page: params.page || 1,
            limit: params.limit || 10,
          };
        }
      } catch (error) {
        console.error("Error fetching inventory status:", error);
        throw error;
      }
    },
  });
};

// Add a new inventory item
export const useAddInventoryItem = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["add-inventory-item"],
    mutationFn: async (data: IAddInventoryItemRequest) => {
      const res = await apiClient.post("inventory", data);
      return res.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["inventory-status"] });
      queryClient.invalidateQueries({ queryKey: ["inventory"] });
      toast.success("Inventory item added successfully");
    },
    onError: (error: any) => {
      console.error("Error adding inventory item:", error);
      toast.error(
        error?.response?.data?.message || "Error adding inventory item"
      );
    },
  });
};

// Take clean items from inventory
export const useTakeCleanItems = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["take-clean-items"],
    mutationFn: async (data: { itemId: string; quantity: number }) => {
      const res = await apiClient.post("inventory/take-clean", data);
      return res.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["inventory-status"] });
      queryClient.invalidateQueries({ queryKey: ["inventory"] });
      toast.success("Items taken successfully");
    },
    onError: (error: any) => {
      console.error("Error taking clean items:", error);
      toast.error(error?.response?.data?.message || "Error taking clean items");
    },
  });
};

// Return dirty items to laundry
export const useReturnDirtyItems = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["return-dirty-items"],
    mutationFn: async (data: { itemId: string; quantity: number }) => {
      const res = await apiClient.post("inventory/return-dirty", data);
      return res.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["inventory-status"] });
      queryClient.invalidateQueries({ queryKey: ["inventory"] });
      toast.success("Dirty items returned to laundry successfully");
    },
    onError: (error: any) => {
      console.error("Error returning dirty items:", error);
      toast.error(
        error?.response?.data?.message || "Error returning dirty items"
      );
    },
  });
};

// Process laundry items
export const useProcessLaundryItems = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["process-laundry"],
    mutationFn: async (data: { itemId: string; quantity: number }) => {
      const res = await apiClient.post("inventory/process-laundry", data);
      return res.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["inventory-status"] });
      queryClient.invalidateQueries({ queryKey: ["inventory"] });
      toast.success("Laundry processed successfully");
    },
    onError: (error: any) => {
      console.error("Error processing laundry:", error);
      toast.error(error?.response?.data?.message || "Error processing laundry");
    },
  });
};

// Return clean items from laundry
export const useReturnCleanItems = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["return-clean-items"],
    mutationFn: async (data: { itemId: string; quantity: number }) => {
      const res = await apiClient.post("inventory/return-clean", data);
      return res.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["inventory-status"] });
      queryClient.invalidateQueries({ queryKey: ["inventory"] });
      toast.success("Clean items returned successfully");
    },
    onError: (error: any) => {
      console.error("Error returning clean items:", error);
      toast.error(
        error?.response?.data?.message || "Error returning clean items"
      );
    },
  });
};

// Get inventory categories
export const useGetInventoryCategories = () => {
  return useQuery({
    queryKey: ["inventory-categories"],
    queryFn: async () => {
      try {
        console.log("Fetching inventory categories...");
        const token = localStorage.getItem("_UPLFMMATRIX");
        if (!token) {
          console.error("No authentication token found");
          throw new Error("Authentication required");
        }

        const res = await apiClient.get("item/category");
        console.log("Inventory categories response:", res.data);

        if (res.data && res.data.data) {
          return res.data.data;
        } else {
          console.error("Unexpected response structure:", res.data);
          return [];
        }
      } catch (error) {
        console.error("Error fetching inventory categories:", error);
        throw error;
      }
    },
  });
};

// Get inventory stores
export const useGetInventoryStores = () => {
  return useQuery({
    queryKey: ["inventory-stores"],
    queryFn: async () => {
      try {
        console.log("Fetching inventory stores...");
        const token = localStorage.getItem("_UPLFMMATRIX");
        if (!token) {
          console.error("No authentication token found");
          throw new Error("Authentication required");
        }

        const res = await apiClient.get("store");
        console.log("Inventory stores response:", res.data);

        if (res.data && res.data.data) {
          return res.data.data;
        } else {
          console.error("Unexpected response structure:", res.data);
          return [];
        }
      } catch (error) {
        console.error("Error fetching inventory stores:", error);
        throw error;
      }
    },
  });
};
