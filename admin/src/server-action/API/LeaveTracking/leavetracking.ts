import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";
import { toast } from "react-toastify";
import { ILeave, ILeaveTypes } from "../../../Interface/leave.interface";

// Leave tracking
export const useGetAllLeaveTypes = () => {
  return useQuery<ILeave[]>({
    queryKey: ["leaveType"],
    queryFn: async () => {
      const response = await apiClient.get("leave/type");
      return response.data?.data;
    },
  });
};

export const useCreateLeaveType = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (leaveTypeData: ILeaveTypes) => {
      return apiClient.post("leave/type", leaveTypeData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["leaveType"] });
      toast.success("Leave Type Created Successfully");
    },
    onError: (error: any) => {
      toast.error(error || `Error Creating Leave Type `);
    },
  });
};

export const useUpdateLeaveType = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      leaveTypeData,
      _id,
    }: {
      leaveTypeData: ILeaveTypes;
      _id: string;
    }) => {
      if (!_id) throw new Error("Leave Type ID is required");
      return apiClient.patch(`leave/type/${_id}`, leaveTypeData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["leaveType"] });
      toast.success("Leave Type Updated Successfully");
    },
    onError: () => {
      toast.error("Error Updating Leave Type");
    },
  });
};

export const useDeleteLeaveType = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (_id: string) => {
      if (!_id) throw new Error("Leave Type ID is required");
      return apiClient.delete(`leave/type/${_id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["leaveType"] });
      toast.success("Leave Type Deleted Successfully");
    },
    onError: () => {
      toast.error("Error Deleting Leave Type");
    },
  });
};

// leaves
export const useGetLeaves = () => {
  return useQuery<ILeave[] | any>({
    queryKey: ["leave"],
    queryFn: async () => {
      const response = await apiClient.get("leave");
      return response.data?.data;
    },
  });
};

// get leave by id
export const useGetLeavesById = (id: string) => {
  return useQuery<ILeave[] | any>({
    queryKey: ["leave"],
    queryFn: async () => {
      if (!id) return null;
      const response = await apiClient.get(`leave/${id}`);
      return response?.data?.data;
    },
    enabled: !!id,
  });
};

//  Create Leave
export const useCreateLeave = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (leaveData: any) => {
      return apiClient.post("leave", leaveData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["leave"] });
      toast.success("Leave Request Submitted Successfully");
    },
    onError: (error: any) => {
      toast.error(error || `Error Creating Leave `);
    },
  });
};

// ✅ Update Leave
export const useUpdateLeave = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ leaveData, _id }: { leaveData: any; _id: string }) => {
      if (!_id) throw new Error("Leave ID is required");
      return apiClient.patch(`leave/${_id}`, leaveData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["leave"] });
      toast.success("Leave Updated Successfully");
    },
    onError: () => {
      toast.error("Error Updating Leave");
    },
  });
};

// ✅ Delete Leave
export const useDeleteLeave = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (_id: string) => {
      if (!_id) throw new Error("Leave ID is required");
      return apiClient.delete(`leave/${_id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["leave"] });
      toast.success("Leave Deleted Successfully");
    },
    onError: () => {
      toast.error("Error Deleting Leave");
    },
  });
};
