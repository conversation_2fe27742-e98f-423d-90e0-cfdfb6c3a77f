import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-toastify";
import { apiClient } from "../utils/ApiGateway";
export interface ICredit {
  _id?: string; // Optional for creation, required for updates/deletes
  user?: {
    name: string;
    designation: string;
    phoneNumber: string;
  }; // ObjectId as string
  hotel?: string; // ObjectId as string
  creditAmount: number;
  paidAmount?: number;
  description?: string;
  installmentAmt?: number;
  reason?: string;
  transaction?: {
    paidAmount: number;
    date?: string;
  }[];
  date?: string;
  creditRepaymentMethod?: string | null; // ObjectId
  creditRepaymentFrequency?: string | null; // ObjectId
  repaymentStartDate?: string;
  status?: string;
}

// Get all credits
export const useGetCredit = () => {
  return useQuery<ICredit[]>({
    queryKey: ["credit"],
    queryFn: async () => {
      const response = await apiClient.get("credit");
      return response?.data?.data;
    },
  });
};

// Create a credit
export const useCreateCredit = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (creditData: ICredit) => {
      return apiClient.post("credit", creditData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["credit"] });
      toast.success("Credit Created Successfully");
    },
    onError: (error: any) => {
      toast.error(error || "Error Creating Credit");
    },
  });
};

// Update a credit
export const useUpdateCredit = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      creditData,
      _id,
    }: {
      creditData: ICredit;
      _id: string;
    }) => {
      if (!_id) throw new Error("Credit ID is required");
      return apiClient.patch(`credit/${_id}`, creditData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["credit"] });
      toast.success("Credit Updated Successfully");
    },
    onError: (eorror: any) => {
      toast.error(eorror || "Error Updating Credit");
    },
  });
};

// Delete a credit
export const useDeleteCredit = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (_id: string) => {
      if (!_id) throw new Error("Credit ID is required");
      return apiClient.delete(`credit/${_id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["credit"] });
      toast.success("Credit Deleted Successfully");
    },
    onError: () => {
      toast.error("Error Deleting Credit");
    },
  });
};
