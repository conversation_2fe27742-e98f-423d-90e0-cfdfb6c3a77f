import { useQuery } from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";
import { IBooking } from "../../../Interface/booking.interface";
import { IUser } from "../../../Interface/user.interface";
import { BookingStatus } from "../../../Interface/booking.interface";

// Interface for booking summary data
export interface BookingStatusSummary {
  pending: {
    count: number;
    bookings: IBooking[];
  };
  confirmed: {
    count: number;
    bookings: IBooking[];
  };
}

// Interface for staff summary data
export interface StaffSummary {
  staff: {
    count: number;
    users: IUser[];
  };
  housekeeping: {
    count: number;
    users: IUser[];
  };
}

// Hook to fetch pending and confirmed bookings
export const useGetBookingStatusSummary = (limit: number = 5) => {
  return useQuery<BookingStatusSummary>({
    queryKey: ["booking-status-summary", limit],
    queryFn: async () => {
      try {
        // Fetch pending bookings
        const pendingResponse = await apiClient.get("/booking", { 
          params: { 
            status: BookingStatus.PENDING,
            limit: limit
          } 
        });
        
        // Fetch confirmed bookings
        const confirmedResponse = await apiClient.get("/booking", { 
          params: { 
            status: BookingStatus.CONFIRMED,
            limit: limit
          } 
        });

        return {
          pending: {
            count: pendingResponse.data.total || pendingResponse.data.data.length,
            bookings: pendingResponse.data.data || []
          },
          confirmed: {
            count: confirmedResponse.data.total || confirmedResponse.data.data.length,
            bookings: confirmedResponse.data.data || []
          }
        };
      } catch (error) {
        console.error("Error fetching booking status summary:", error);
        // Return default values if API fails
        return {
          pending: { count: 0, bookings: [] },
          confirmed: { count: 0, bookings: [] }
        };
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook to fetch staff and housekeeping users
export const useGetStaffSummary = (limit: number = 5) => {
  return useQuery<StaffSummary>({
    queryKey: ["staff-summary", limit],
    queryFn: async () => {
      try {
        // Fetch staff users
        const staffResponse = await apiClient.get("/user", { 
          params: { 
            role: "staff",
            limit: limit
          } 
        });
        
        // Fetch housekeeping users
        const housekeepingResponse = await apiClient.get("/user", { 
          params: { 
            role: "housekeeper",
            limit: limit
          } 
        });

        return {
          staff: {
            count: staffResponse.data.total || staffResponse.data.data.length,
            users: staffResponse.data.data || []
          },
          housekeeping: {
            count: housekeepingResponse.data.total || housekeepingResponse.data.data.length,
            users: housekeepingResponse.data.data || []
          }
        };
      } catch (error) {
        console.error("Error fetching staff summary:", error);
        // Return default values if API fails
        return {
          staff: { count: 0, users: [] },
          housekeeping: { count: 0, users: [] }
        };
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};
