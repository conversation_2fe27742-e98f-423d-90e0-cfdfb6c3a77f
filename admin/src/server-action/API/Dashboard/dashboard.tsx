import { useQuery } from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";

// Dashboard Summary Interfaces
export interface DashboardSummary {
  inHouseGuests: {
    count: number;
    totalPax: number;
  };
  expectedArrivals: {
    count: number;
    totalPax: number;
  };
  expectedCheckouts: {
    count: number;
    totalPax: number;
  };
  noShow: {
    count: number;
    totalPax: number;
  };
}

export interface TodayCheckout {
  room: string;
  roomType: string;
  time: string;
  guestName?: string;
  bookingId?: string;
}

export interface RoomServiceRequest {
  room: string;
  items: string[];
  status: string;
  requestTime?: string;
  guestName?: string;
}

export interface HousekeepingStatusItem {
  title: string;
  status: string;
  value: number;
  color: string;
}

export interface StaffTask {
  date: string;
  department: string;
  shift: string;
  name: string;
  role: string;
  time: string;
  taskId?: string;
}

export interface RecentBooking {
  guestName: string;
  roomType: string;
  checkIn: string;
  checkOut: string;
  paidAmount: number;
  dueAmount: number;
  billingStatus: string;
  bookingId?: string;
}

export interface MembershipStat {
  name: string;
  count: number;
  percentage: number;
  color: string;
}

export interface MembershipSummary {
  totalMembers: number;
  distribution: MembershipStat[];
  recentUpgrades: number;
  savingsGenerated: number;
}

export interface GuestOverview {
  inHouseGuests: {
    count: number;
    rooms: number;
    totalPax: number;
  };
  expectedArrivals: {
    count: number;
    rooms: number;
    totalPax: number;
  };
  expectedCheckouts: {
    count: number;
    rooms: number;
    totalPax: number;
  };
  noShows: {
    count: number;
    rooms: number;
    totalPax: number;
  };
}

export interface TicketSummary {
  totalTickets: number;
  openTickets: number;
  resolvedTickets: number;
  averageResolutionTime: number; // in hours
}

export interface TicketDistribution {
  name: string;
  count: number;
  percentage: number;
  color: string;
}

export interface RecentTicket {
  id: string;
  title: string;
  category: string | { _id: string; name: string };
  priority: string | { _id: string; name: string };
  status: string | { _id: string; name: string };
  createdAt: string;
  assignedTo?: string | { _id: string; name: string };
  reportedBy?: string;
}

export interface TicketTrend {
  date: string;
  count: number;
  formattedDate?: string;
}

export interface TicketReportData {
  summary: TicketSummary;
  byStatus: {
    status: string;
    count: number;
    percentage: number;
  }[];
  byCategory: {
    category: string;
    count: number;
    percentage: number;
  }[];
  byPriority: {
    priority: string;
    count: number;
    percentage: number;
  }[];
  recentTickets: {
    _id: string;
    ticketCategory: string;
    ticketStatus: string;
    priority: string;
    notes?: string;
    room?: {
      _id: string;
      roomNumber: string;
    };
    reportedBy: any;
    assignedTo?: {
      _id: string;
      name: string;
    };
    date: string;
    time?: string;
  }[];
  trend: TicketTrend[];
  dateRange?: {
    startDate: string;
    endDate: string;
  };
}

// Dashboard Summary Hook
export const useGetDashboardSummary = () => {
  return useQuery<DashboardSummary>({
    queryKey: ["dashboard-summary"],
    queryFn: async () => {
      try {
        const response = await apiClient.get("dashboard/summary");
        return response.data.data;
      } catch (error) {
        console.error("Error fetching dashboard summary:", error);
        // Return default values if API fails
        return {
          inHouseGuests: { count: 0, totalPax: 0 },
          expectedArrivals: { count: 0, totalPax: 0 },
          expectedCheckouts: { count: 0, totalPax: 0 },
          noShow: { count: 0, totalPax: 0 }
        };
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Today's Checkout Hook
export const useGetTodayCheckouts = () => {
  return useQuery<TodayCheckout[]>({
    queryKey: ["today-checkouts"],
    queryFn: async () => {
      try {
        const response = await apiClient.get("dashboard/today-checkouts");
        return response.data.data;
      } catch (error) {
        console.error("Error fetching today's checkouts:", error);
        return [];
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Room Service Requests Hook
export const useGetRoomServiceRequests = () => {
  return useQuery<RoomServiceRequest[]>({
    queryKey: ["room-service-requests"],
    queryFn: async () => {
      try {
        const response = await apiClient.get("dashboard/room-service");
        return response.data.data;
      } catch (error) {
        console.error("Error fetching room service requests:", error);
        return [];
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Housekeeping Status Hook
export const useGetHousekeepingStatus = () => {
  return useQuery<HousekeepingStatusItem[]>({
    queryKey: ["housekeeping-status"],
    queryFn: async () => {
      try {
        const response = await apiClient.get("dashboard/housekeeping-status");
        return response.data.data;
      } catch (error) {
        console.error("Error fetching housekeeping status:", error);
        return [];
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Staff Tasks Hook
export const useGetStaffTasks = () => {
  return useQuery<StaffTask[]>({
    queryKey: ["staff-tasks"],
    queryFn: async () => {
      try {
        const response = await apiClient.get("dashboard/staff-tasks");
        return response.data.data;
      } catch (error) {
        console.error("Error fetching staff tasks:", error);
        return [];
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Recent Bookings Hook
export const useGetRecentBookings = () => {
  return useQuery<RecentBooking[]>({
    queryKey: ["recent-bookings"],
    queryFn: async () => {
      try {
        const response = await apiClient.get("dashboard/recent-bookings");
        return response.data.data;
      } catch (error) {
        console.error("Error fetching recent bookings:", error);
        return [];
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Membership Statistics Hook
export const useGetMembershipStats = () => {
  return useQuery<MembershipSummary>({
    queryKey: ["membership-stats"],
    queryFn: async () => {
      try {
        const response = await apiClient.get("dashboard/membership-stats");
        return response.data.data;
      } catch (error) {
        console.error("Error fetching membership statistics:", error);
        // Return default values if API fails
        return {
          totalMembers: 0,
          distribution: [
            { name: "Silver", count: 45, percentage: 45, color: "#C0C0C0" },
            { name: "Gold", count: 30, percentage: 30, color: "#FFD700" },
            { name: "Platinum", count: 15, percentage: 15, color: "#E5E4E2" },
            { name: "Diamond", count: 8, percentage: 8, color: "#B9F2FF" },
            { name: "Ace", count: 2, percentage: 2, color: "#6047E4" }
          ],
          recentUpgrades: 0,
          savingsGenerated: 0
        };
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Guest Overview Hook
export const useGetGuestOverview = () => {
  return useQuery<GuestOverview>({
    queryKey: ["guest-overview"],
    queryFn: async () => {
      try {
        const response = await apiClient.get("dashboard/guest-overview");
        return response.data.data;
      } catch (error) {
        console.error("Error fetching guest overview:", error);
        // Return default values if API fails
        return {
          inHouseGuests: { count: 100, rooms: 100, totalPax: 120 },
          expectedArrivals: { count: 6, rooms: 6, totalPax: 12 },
          expectedCheckouts: { count: 15, rooms: 15, totalPax: 30 },
          noShows: { count: 9, rooms: 9, totalPax: 18 }
        };
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Ticket Reports Hook
export const useGetTicketReports = (params?: {
  startDate?: string;
  endDate?: string;
  category?: string;
  priority?: string;
  status?: string;
}) => {
  return useQuery<TicketReportData>({
    queryKey: ["ticket-reports", params],
    queryFn: async () => {
      try {
        const response = await apiClient.get("dashboard/ticket-reports", { params });
        return response.data.data;
      } catch (error) {
        console.error("Error fetching ticket reports:", error);
        // Return empty data structure instead of dummy data
        return {
          summary: {
            totalTickets: 0,
            openTickets: 0,
            resolvedTickets: 0,
            averageResolutionTime: 0
          },
          byStatus: [],
          byCategory: [],
          byPriority: [],
          recentTickets: [],
          trend: [],
          dateRange: {
            startDate: new Date().toISOString(),
            endDate: new Date().toISOString()
          }
        };
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};
