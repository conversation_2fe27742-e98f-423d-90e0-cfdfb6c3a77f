import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-toastify";
import { apiClient } from "../utils/ApiGateway";

//procut category api
export const useGetAllCategory = () => {
  return useQuery({
    queryKey: ["category"],
    queryFn: async () => {
      const res = await apiClient.get("item/category");
      return res.data.data;
    },
  });
};

export const useCreateCategory = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["category"],
    mutationFn: async (categoryData: any) => {
      const res = await apiClient.post("item/category", categoryData);
      return res.data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["category"] });
      toast.success("Category created successfully");
    },
    onError: (error: any) => {
      console.log("error", error);
      toast.error(error || "Error creating category");
    },
  });
};

export const useUpdateCategory = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["category"],
    mutationFn: async ({ id, body }: any) => {
      const res = await apiClient.patch(`item/category/${id}`, body);
      return res.data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["category"] });
      toast.success("Category updated successfully");
    },
    onError: () => {
      toast.error("Error updating category");
    },
  });
};

export const useDeleteCategory = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["category"],
    mutationFn: async (id: string) => {
      const res = await apiClient.delete(`item/category/${id}`);
      return res.data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["category"] });
      toast.success("Category deleted successfully");
    },
    onError: () => {
      toast.error("Error deleting category");
    },
  });
};

//product api
export const useGetAllProduct = () => {
  return useQuery({
    queryKey: ["product"],
    queryFn: async () => {
      const res = await apiClient.get("item");
      return res.data.data;
    },
  });
};

export const useCreateProduct = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["product"],
    mutationFn: async (productData: any) => {
      const res = await apiClient.post("item", productData);
      return res.data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["product"] });
      toast.success("Product created successfully");
    },
    onError: (error: any) => {
      toast.error(error || "Error creating product");
    },
  });
};

export const useUpdateProduct = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["product"],
    mutationFn: async ({ id, body }: any) => {
      const res = await apiClient.patch(`item/${id}`, body);
      return res.data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["product"] });
      toast.success("Product updated successfully");
    },
    onError: () => {
      toast.error("Error updating product");
    },
  });
};

export const useDeleteProduct = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["product"],
    mutationFn: async (id: string) => {
      const res = await apiClient.delete(`item/${id}`);
      return res.data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["product"] });
      toast.success("Product deleted successfully");
    },
    onError: () => {
      toast.error("Error deleting product");
    },
  });
};
