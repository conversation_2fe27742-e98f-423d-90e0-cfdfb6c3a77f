import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";
import { toast } from "react-toastify";

/**
 * Hook to check and upgrade user membership after a booking
 */
export const useCheckAndUpgradeMembership = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ userId, bookingAmount }: { userId: string; bookingAmount: number }) => {
      if (!userId || !bookingAmount) {
        throw new Error("User ID and booking amount are required");
      }
      
      const response = await apiClient.post("membership/upgrade", {
        userId,
        bookingAmount
      });
      
      return response.data?.data;
    },
    onSuccess: (data) => {
      // Invalidate user queries to refresh membership data
      queryClient.invalidateQueries({ queryKey: ["user"] });
      
      // Show toast notification if membership was upgraded
      if (data?.upgraded) {
        toast.success(`Membership upgraded to ${data.newMembership.name}!`);
      }
    },
    onError: (error: any) => {
      console.error("Error upgrading membership:", error);
      // Don't show error toast as this is a background operation
    }
  });
};
