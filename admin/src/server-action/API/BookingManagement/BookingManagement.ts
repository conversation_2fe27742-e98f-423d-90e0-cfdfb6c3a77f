import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-toastify";
import {
  apiClient,
  BookingApiResponse,
  SingleBookingApiResponse,
} from "../../utils/ApiGateway";
import { useCheckAndUpgradeMembership } from "./useMembershipUpgrade";
import moment from "moment";
import { IBooking } from "../../../Interface/booking.interface";

// Interface for booking payment
export interface IBookingPayment {
  bookingId: string;
  amount: number;
  paymentMethod: "cash" | "online";
  notes?: string;
}

/** 🚀 Get All Bookings */
export const useGetBookings = (params = {}) => {
  return useQuery<{
    data: IBooking[];
    success: boolean;
    message: string;
    responseTime: string;
  }>({
    queryKey: ["booking", params],
    queryFn: async () => {
      const response = await apiClient.get<BookingApiResponse<IBooking>>(
        "/booking",
        { params }
      );
      const apiData = response.data;

      // Return normalized structure with bookings array in data field
      return {
        success: apiData.success,
        message: apiData.message,
        responseTime: apiData.responseTime,
        data: apiData.data.bookings || [], // Extract bookings array
      };
    },
  });
};

/** 🚀 Get Single Booking */
export const useGetBookingById = (id: string) => {
  return useQuery<{
    data: IBooking | undefined;
    success: boolean;
    message: string;
    responseTime?: string;
  }>({
    queryKey: ["booking", id],
    queryFn: async () => {
      const response = await apiClient.get<SingleBookingApiResponse<IBooking>>(
        `/booking/${id}`
      );
      const apiData = response.data;

      // For single booking, return the booking data directly
      return {
        success: apiData.success,
        message: apiData.message,
        responseTime: apiData.responseTime,
        data: apiData.data, // Single booking should be directly in data.data
      };
    },
    enabled: !!id, // Only run when ID is available
  });
};

/** 🚀 Create Booking */
export const useCreateBooking = () => {
  const queryClient = useQueryClient();
  const { mutateAsync: checkAndUpgradeMembership } =
    useCheckAndUpgradeMembership();

  return useMutation({
    mutationFn: async (bookingData: FormData) => {
      // Validate required fields
      const checkIn = bookingData.get("checkIn");
      const reservationDate = bookingData.get("reservationDate");
      const expectedCheckOut = bookingData.get("expectedCheckOut");
      const room = bookingData.get("room");

      // Either checkIn or reservationDate is required
      if ((!checkIn && !reservationDate) || !expectedCheckOut || !room) {
        throw new Error(
          "Missing required fields: Either Check-in date or Reservation date, Expected checkout date, and Room are required"
        );
      }

      // Log the form data for debugging
      console.log("Booking form data:");
      for (const pair of (bookingData as any).entries()) {
        console.log(pair[0], pair[1]);
      }

      try {
        console.log("Sending booking data to API...");
        const { data } = await apiClient.post("/booking", bookingData, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        });
        console.log("Booking API response:", data);
        return data;
      } catch (error: any) {
        console.error("API error creating booking:", error);
        console.error("Response data:", error?.response?.data);
        console.error("Status code:", error?.response?.status);
        const errorMessage =
          error?.response?.data?.message ||
          error?.message ||
          "Failed to create booking";
        throw new Error(errorMessage);
      }
    },
    onSuccess: async (response) => {
      queryClient.invalidateQueries({ queryKey: ["booking"] });
      toast.success("Booking Created Successfully");

      // Check if we need to upgrade membership
      const bookingData = response?.data;
      if (
        bookingData &&
        bookingData.guest &&
        typeof bookingData.guest === "string"
      ) {
        try {
          // Call the membership upgrade function with user ID and booking amount
          await checkAndUpgradeMembership({
            userId: bookingData.guest,
            bookingAmount: bookingData.amount || 0,
          });
        } catch (error) {
          console.error("Error checking membership upgrade:", error);
          // Don't show error toast as this is a background operation
        }
      }

      return response;
    },
    onError: (error: any) => {
      toast.error(error || "Error Creating Booking");

      // Check if it's a booking conflict error
      if (error?.isBookingConflict) {
        // Don't show toast for booking conflict - we'll handle it in the form
        return error;
      }

      const errorMessage = error?.message || "Error Creating Booking";
      toast.error(errorMessage);
      return error;
    },
  });
};

/** 🚀 Update Booking */
export const useUpdateBooking = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      bookingData,
      _id,
    }: {
      bookingData: any;
      _id: string;
    }) => {
      if (!_id) {
        throw new Error("Booking ID is required for updating a booking");
      }

      // Check if bookingData is FormData
      const isFormData = bookingData instanceof FormData;

      // Validate required fields if it's FormData
      if (isFormData) {
        const checkIn = bookingData.get("checkIn");
        const reservationDate = bookingData.get("reservationDate");
        const expectedCheckOut = bookingData.get("expectedCheckOut");
        const room = bookingData.get("room");

        // Either checkIn or reservationDate is required
        if ((!checkIn && !reservationDate) || !expectedCheckOut || !room) {
          throw new Error(
            "Missing required fields: Either Check-in date or Reservation date, Expected checkout date, and Room are required"
          );
        }
      }

      // Log the data being sent for debugging
      console.log("Updating booking with ID:", _id);
      if (isFormData) {
        console.log("Updating booking with FormData:");
        for (const pair of (bookingData as FormData).entries()) {
          console.log(pair[0], pair[1]);
        }
      } else {
        console.log("Updating booking with JSON data:", bookingData);
      }

      try {
        const { data } = await apiClient.patch(`/booking/${_id}`, bookingData, {
          headers: isFormData
            ? {
                "Content-Type": "multipart/form-data",
              }
            : {
                "Content-Type": "application/json",
              },
        });
        console.log("Booking update API response:", data);
        return data;
      } catch (error: any) {
        console.error("API error updating booking:", error);
        console.error("Response data:", error?.response?.data);
        console.error("Status code:", error?.response?.status);
        const errorMessage =
          error?.response?.data?.message ||
          error?.message ||
          "Failed to update booking";
        throw new Error(errorMessage);
      }
    },
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["booking"] });
      toast.success("Booking Updated Successfully");
      return response;
    },
    onError: (error: any) => {
      console.error("Booking update error:", error);

      // Check if it's a booking conflict error
      if (error?.isBookingConflict) {
        // Don't show toast for booking conflict - we'll handle it in the form
        return error;
      }

      const errorMessage = error?.message || "Error Updating Booking";
      toast.error(errorMessage);
      return error;
    },
  });
};

/** 🚀 Delete Booking */
export const useDeleteBooking = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["booking"],
    mutationFn: async (id: string) => {
      await apiClient.delete(`/booking/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["booking"] });
      toast.success("Booking Deleted Successfully");
    },
    onError: () => {
      toast.error("Error Deleting Booking");
    },
  });
};

/**
 * Check if a booking already exists for a room during a specific date range
 * @param roomId - The ID of the room to check
 * @param checkIn - Check-in date in YYYY-MM-DD format
 * @param checkOut - Check-out date in YYYY-MM-DD format
 * @returns Promise<boolean> - True if a booking exists, false otherwise
 */
export const checkBookingExists = async (
  roomId: string,
  checkIn: string,
  checkOut: string
): Promise<boolean> => {
  try {
    if (!roomId || !checkIn || !checkOut) {
      console.warn("Missing required parameters for booking check");
      return false;
    }

    console.log(
      `Checking booking for room ${roomId} from ${checkIn} to ${checkOut}`
    );

    // Format dates to ensure consistency
    const formattedCheckIn = moment(checkIn).format("YYYY-MM-DD");
    const formattedCheckOut = moment(checkOut).format("YYYY-MM-DD");

    // Call the API to check for existing bookings
    const response = await apiClient.get(`/booking/check-availability`, {
      params: {
        roomId,
        checkIn: formattedCheckIn,
        checkOut: formattedCheckOut,
      },
    });

    // The API should return a boolean or an object indicating if the room is available
    // Adjust this logic based on your actual API response structure
    const isBooked = response.data?.isBooked || false;

    console.log(
      `Room ${roomId} availability check result:`,
      isBooked ? "Booked" : "Available"
    );

    return isBooked;
  } catch (error) {
    console.error("Error checking booking existence:", error);
    // In case of error, assume no booking exists to avoid blocking the user
    return false;
  }
};

/** 🚀 Make Payment for Booking */
export const useMakeBookingPayment = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (paymentData: IBookingPayment) => {
      const { data } = await apiClient.post("/booking/payment", paymentData);
      return data;
    },
    onSuccess: (response, variables) => {
      queryClient.invalidateQueries({ queryKey: ["booking"] });
      queryClient.invalidateQueries({
        queryKey: ["booking", variables.bookingId],
      });
      queryClient.invalidateQueries({
        queryKey: ["booking-transactions", variables.bookingId],
      });
      queryClient.invalidateQueries({
        queryKey: ["booking-balance", variables.bookingId],
      });
      toast.success("Payment processed successfully");
      return response;
    },
    onError: (error: any) => {
      console.error("Payment processing error:", error);
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        "Error processing payment";
      toast.error(errorMessage);
      return error;
    },
  });
};

/** 🚀 Get Payment History for Booking */
export const useGetBookingPaymentHistory = (bookingId: string) => {
  return useQuery({
    queryKey: ["booking-payment-history", bookingId],
    queryFn: async () => {
      const { data } = await apiClient.get(
        `/booking/${bookingId}/payment-history`
      );
      return data;
    },
    enabled: !!bookingId,
  });
};

/** 🚀 Get Booking Balance */
export const useGetBookingBalance = (bookingId: string) => {
  return useQuery({
    queryKey: ["booking-balance", bookingId],
    queryFn: async () => {
      const { data } = await apiClient.get(`/booking/${bookingId}/balance`);
      return data;
    },
    enabled: !!bookingId,
  });
};
