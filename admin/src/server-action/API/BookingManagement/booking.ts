import {
  UseQueryResult,
  UseMutationResult,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";
import { toast } from "react-toastify";
import { IRoom } from "../../../Interface/room.interface";

export type IBooking = {
  _id: string;
  id: string;
  name: string;
  email: string;
  phone: string;
  checkin: string;
  checkout: string;
  adults: number;
  children: number;
  rooms?: number;
  roomCatogery: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  guest: any;
  bookingId: string;

  RoomCategory?: any;
  Address?: any;
};

/**
 * Hook to fetch all bookings with detailed information
 * @returns Query result with booking data including room information
 */
export const useGetAllBooking = (): UseQueryResult<IBooking[]> => {
  return useQuery<IBooking[]>({
    queryKey: ["booking"],
    queryFn: async () => {
      console.log("Fetching all bookings");
      const response = await apiClient.get("booking");
      // Handle the new API response structure with bookings array
      const bookings =
        response?.data?.data?.bookings || response?.data?.data || [];
      console.log(`Found ${bookings.length} bookings`);
      return bookings;
    },
  });
};

/**
 * Hook to fetch a booking by ID with room information
 * @param id - The ID of the booking to fetch
 * @returns Query result with booking data including room information
 */
export const useGetBookingById = (
  id: string
): UseQueryResult<IBooking | null> => {
  return useQuery<IBooking | null>({
    queryKey: ["booking", id],
    queryFn: async () => {
      if (!id) return null;
      console.log(`Fetching booking with ID: ${id}`);
      const response = await apiClient.get(`booking/${id}`);
      const booking = response?.data?.data;

      if (booking) {
        // Ensure room information is available
        if (!booking.roomNo) {
          booking.roomNo = getRoomNoFromBooking(booking);
        }
        console.log(
          `Found booking with ID: ${id}, Room: ${booking.roomNo || "N/A"}`
        );
      }

      return booking;
    },
    enabled: !!id,
  });
};

export const useDeleteBooking = (): UseMutationResult<
  void,
  unknown,
  string,
  unknown
> => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: string) => {
      return apiClient.delete(`booking/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["booking"] });
      toast.success("Booking Deleted Successfully");
    },
    onError: () => {
      toast.error("Error Deleting Booking");
    },
  });
};

export const useUpdateBooking = (): UseMutationResult<
  void,
  unknown,
  { id: string; body: IBooking },
  unknown
> => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ id, body }) => {
      if (!id) {
        throw new Error("Booking ID is required for updating a booking");
      }
      console.log("Updating booking with ID:", id);
      console.log("Updating booking with data:", body);

      try {
        const response = await apiClient.patch(`booking/${id}`, body, {
          headers: {
            "Content-Type": "application/json",
          },
        });
        console.log("Booking update API response:", response.data);
        return response.data;
      } catch (error: any) {
        console.error("API error updating booking:", error);
        console.error("Response data:", error?.response?.data);
        console.error("Status code:", error?.response?.status);
        const errorMessage =
          error?.response?.data?.message ||
          error?.message ||
          "Failed to update booking";
        throw new Error(errorMessage);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["booking"] });
      toast.success("Booking Updated Successfully");
    },
    onError: (error: any) => {
      console.error("Booking update error:", error);
      const errorMessage = error?.message || "Error updating booking";
      toast.error(errorMessage);
    },
  });
};

export const useCreateBooking = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (body: FormData) => {
      return apiClient.post(`booking`, body, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["booking"] });
      toast.success("Booking Created Successfully");
    },
    onError: (error: any) => {
      toast.error(error || "Error creating booking");
    },
  });
};

/**
 * Hook to fetch bookings by room ID
 * @param roomId - The ID of the room to fetch bookings for
 * @returns Query result with booking data
 */
// export const useGetBookingsByRoom = (roomId: string) => {
//   return useQuery({
//     queryKey: ["booking", "room", roomId],
//     queryFn: async () => {
//       if (!roomId) return [];
//       const response = await apiClient.get(`booking/room/${roomId}`);
//       return response?.data?.data;
//     },
//     enabled: !!roomId,
//     staleTime: 5 * 60 * 1000, // 5 minutes
//     gcTime: 10 * 60 * 1000, // 10 minutes
//   });
// };
/**
 * Utility function to extract room number from booking data
 * @param booking - The booking object
 * @returns The room number as a string, or "N/A" if not found
 */
export const getRoomNoFromBooking = (booking: any): string => {
  if (!booking) return "N/A";

  let roomNo = "N/A";

  // Case 1: Room object with roomNo property
  if (booking.room && typeof booking.room === "object" && booking.room.roomNo) {
    return booking.room.roomNo;
  }

  // Case 2: Room is a string ID, but booking has roomNo property
  if (booking.roomNo) {
    return booking.roomNo;
  }

  // Case 3: Room is nested in another property
  if (booking.booking && booking.booking.room) {
    const bookingRoom = booking.booking.room;
    if (typeof bookingRoom === "object" && bookingRoom.roomNo) {
      return bookingRoom.roomNo;
    }
  }

  // Case 4: Direct roomNo property in nested booking
  if (booking.booking && booking.booking.roomNo) {
    return booking.booking.roomNo;
  }

  return roomNo;
};

export const useGetBookingsByRoom = (roomId: string) => {
  return useQuery({
    queryKey: ["booking", "room", roomId],
    queryFn: async () => {
      try {
        // If roomId is provided, fetch bookings for that specific room
        if (roomId) {
          console.log(`Fetching bookings for room ID: ${roomId}`);
          const response = await apiClient.get(`booking/room/${roomId}`);
          // Handle the new API response structure
          const bookings =
            response?.data?.data?.bookings || response?.data?.data || [];
          console.log(`Found ${bookings.length} bookings for room ${roomId}`);

          // Process bookings to ensure room information is available
          return bookings.map((booking: any) => {
            // If booking already has room information, return as is
            if (
              booking.room &&
              typeof booking.room === "object" &&
              booking.room.roomNo
            ) {
              return booking;
            }

            // Add roomNo property if it doesn't exist
            if (!booking.roomNo && roomId) {
              booking.roomNo = getRoomNoFromBooking(booking) || "N/A";
            }

            return booking;
          });
        }
        // If no roomId is provided, fetch all active bookings
        else {
          console.log("Fetching all active bookings");
          const response = await apiClient.get("booking", {
            params: {
              status: ["checked-in", "confirmed"].join(","),
            },
          });
          // Handle the new API response structure
          const bookings =
            response?.data?.data?.bookings || response?.data?.data || [];
          console.log(`Found ${bookings.length} active bookings`);

          // Process bookings to ensure room information is available
          return bookings.map((booking: any) => {
            booking.roomNo = getRoomNoFromBooking(booking) || "N/A";
            return booking;
          });
        }
      } catch (error) {
        console.error(
          `Error fetching ${roomId ? "room" : "all"} bookings:`,
          error
        );
        return [];
      }
    },
    enabled: true, // Always enable the query, will fetch all bookings when roomId is empty
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};
