import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-toastify";
import { apiClient } from "../utils/ApiGateway";

export interface IAttendance {
  _id: string;
  user?: {
    _id: string;
  };
  date: string;
  attendanceStatus: string;
  checkInTime: string;
  checkOutTime: string;
  remarks: string;
  createdAt: string;
  updatedAt: string;
}
export const useGetAttendance = (
  page: number = 1,
  limit: number = 10,
  search: string = "",
  date = ""
) => {
  return useQuery({
    queryKey: ["attendance", page, limit, search, date],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });
      if (search) params.append("search", search);
      if (date) params.append("date", date);

      const response = await apiClient.get("attendance", { params });
      return response.data?.data;
    },
  });
};
// ✅ Get Attendance By ID
export const useGetAttendanceById = (id: string) => {
  return useQuery<IAttendance | null>({
    queryKey: ["attendance", id],
    queryFn: async () => {
      if (!id) return null;
      const response = await apiClient.get(`attendance/${id}`);
      return response.data?.data;
    },
    enabled: !!id,
  });
};

// ✅ Create Attendance
export const useCreateAttendance = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (attendanceData: IAttendance) => {
      return apiClient.post("attendance", attendanceData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["attendance"] });
      toast.success("Attendance Created Successfully");
    },
    onError: (error: any) => {
      toast.error(error || "Error Creating Attendance");
    },
  });
};

export const useUpdateAttendance = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      attendanceData,
      _id,
    }: {
      attendanceData: IAttendance;
      _id: string;
    }) => {
      if (!_id) throw new Error("Attendance ID is required");
      return apiClient.patch(`attendance/${_id}`, attendanceData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["attendance"] });
      toast.success("Attendance Updated Successfully");
    },
    onError: (error: any) => {
      toast.error(error || "Error Updating Attendance");
    },
  });
};

export const useDeleteAttendance = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (_id: string) => {
      if (!_id) throw new Error("Attendance ID is required");

      return apiClient.delete(`attendance/${_id}`, {
        params: {
          id: JSON.stringify([_id]),
        },
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["attendance"] });
      toast.success("Attendance Deleted Successfully");
    },
    onError: (error: any) => {
      toast.error(error || "Error Deleting Attendance");
    },
  });
};
