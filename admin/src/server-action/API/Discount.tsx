import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-toastify';
import { apiClient } from '../utils/ApiGateway';

export interface discountTypes {
  name: string;
  discount: number;
  range: number; // Keeping for backward compatibility
  minSpend: number; // New field for minimum spending threshold
  _id?: string;
}

//product api
export const useGetAllDiscount = () => {
  return useQuery({
    queryKey: ['membership'],
    queryFn: async () => {
      const res = await apiClient.get('membership');
      return res.data.data;
    },
  });
};

export const useCreateDiscount = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ['membership'],
    mutationFn: async (productData: discountTypes) => {
      const res = await apiClient.post('membership', productData);
      return res.data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['membership'] });
      toast.success('Membership created successfully');
    },
    onError: () => {
      toast.error('Error creating membership');
    },
  });
};

export const useUpdateDiscount = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ['membership'],
    mutationFn: async ({ id, body }: any) => {
      const res = await apiClient.patch(`membership/${id}`, body);
      return res.data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['membership'] });
      toast.success('Membership updated successfully');
    },
    onError: () => {
      toast.error('Error updating membership');
    },
  });
};

export const useDeleteDiscount = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ['membership'],
    mutationFn: async (id: string) => {
      const res = await apiClient.delete(`membership/${id}`);
      return res.data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['membership'] });
      toast.success('Membership deleted successfully');
    },
    onError: () => {
      toast.error('Error deleting membership');
    },
  });
};
