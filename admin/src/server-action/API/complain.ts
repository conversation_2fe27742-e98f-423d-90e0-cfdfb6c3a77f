import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-toastify";
import { apiClient } from "../utils/ApiGateway";

export const useGetcomplains = () => {
  return useQuery({
    queryKey: ["complain"],
    queryFn: async () => {
      const { data } = await apiClient.get("/complain");
      return data?.data;
    },
  });
};

export const useGetcomplainById = (id: string) => {
  return useQuery({
    queryKey: ["complain", id],
    queryFn: async () => {
      const { data } = await apiClient.get(`/complain/${id}`);
      return data?.data;
    },
    enabled: !!id,
  });
};

export const useCreatecomplain = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (complainData: any) => {
      return apiClient.post("/complain", complainData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["complain"] });
      toast.success("complain Created Successfully");
    },
    onError: (error: any) => {
      toast.error(error || "Error Creating complain");
    },
  });
};

export const useUpdatecomplain = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      complainData,
      _id,
    }: {
      complainData: any;
      _id: string;
    }) => {
      return apiClient.patch(`/complain/${_id}`, complainData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["complain"] });
      toast.success("complain Updated Successfully");
    },
    onError: () => {
      toast.error("Error Updating complain");
    },
  });
};

export const useDeletecomplain = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: string) => {
      await apiClient.delete(`/complain/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["complain"] });
      toast.success("complain Deleted Successfully");
    },
    onError: () => {
      toast.error("Error Deleting complain");
    },
  });
};
