import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";
import { toast } from "react-toastify";

interface WakeUpModule {
  id: string;
  name?: string;
  description?: string;
  [key: string]: any;
}

interface WakeUpModuleBody {
  name?: string;
  description?: string;

  status: "active" | "inactive";
}

// Fetch all wakeup modules
export const useGetAllWakeUp = () => {
  return useQuery<WakeUpModule[]>({
    queryKey: ["wakeup"],
    queryFn: async () => {
      const res = await apiClient.get<{ data: WakeUpModule[] }>("wakeup");
      return res.data.data;
    },
  });
};

// Create a new wakeup module
export const useCreateWakeUpModule = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (body: any) => {
      return apiClient.post("wakeup", body);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["wakeup"] });
      toast.success("Wakeup Module Created Successfully");
    },
    onError: () => {
      toast.error("Error Creating Wakeup module");
    },
  });
};

// Delete a wakeup module
export const useDeleteWakeUpModule = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: string) => {
      return apiClient.delete(`wakeup/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["wakeup"] });
      toast.success("Wakeup Module Deleted Successfully");
    },
    onError: () => {
      toast.error("Error Deleting Wakeup module");
    },
  });
};

// Update a wakeup module
export const useUpdateWakeUpModule = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      body,
      id,
    }: {
      body: WakeUpModuleBody;
      id: string;
    }) => {
      return apiClient.patch(`wakeup/${id}`, body);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["wakeup"] });
      toast.success("Wakeup Module Updated Successfully");
    },
    onError: () => {
      toast.error("Error Updating Wakeup module");
    },
  });
};
