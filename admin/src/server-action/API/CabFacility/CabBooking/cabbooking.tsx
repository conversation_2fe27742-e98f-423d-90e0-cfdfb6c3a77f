import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-toastify";
import { apiClient } from "../../../utils/ApiGateway";

interface ICabModule {
  [key: string]: any;
}
export const useGetAllCabBooking = () => {
  return useQuery<ICabModule>({
    queryKey: ["cab-booking"],
    queryFn: async () => {
      const res = await apiClient.get("cab/booking");
      return res.data.data;
    },
  });
};

export const useCreateCabBooking = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (body: ICabModule) => {
      return apiClient.post("cab/booking", body);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["cab-booking"] });
      toast.success("Cab booking Created Successfully");
    },
    onError: () => {
      toast.error("Error Creating Cab booking");
    },
  });
};

export const useDeleteCabBooking = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: string) => {
      return apiClient.delete(`cab/booking/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["cab-booking"] });
      toast.success("Cab booking Deleted Successfully");
    },
    onError: () => {
      toast.error("Error Deleting Cab booking");
    },
  });
};

export const useUpdateCabBooking = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ body, id }: { body: ICabModule; id: string }) => {
      return apiClient.patch(`cab/booking/${id}`, body);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["cab-booking"] });
      toast.success("Cab booking Updated Successfully");
    },
    onError: () => {
      toast.error("Error Updating Cab booking");
    },
  });
};
