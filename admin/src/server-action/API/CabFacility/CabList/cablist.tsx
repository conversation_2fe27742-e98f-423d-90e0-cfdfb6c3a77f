import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-toastify";
import { apiClient } from "../../../utils/ApiGateway";
// interface ICab {
//   _id: string;
//   name: string;
//   description?: string;
//   [key: string]: any;
// }

// interface WakeUpModuleBody {
//   name: string;
//   description?: string;
// }
// using any type for now (temporary)

// Fetch all Laundry
export const useGetAllCabList = () => {
  return useQuery<any>({
    //laundrymodule type
    queryKey: ["cablist"],
    queryFn: async () => {
      const res = await apiClient.get("cab");
      return res.data.data;
    },
  });
};

// Create a new Laundry Ticket
export const useCreateCabList = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (body: any) => {
      //laundryTicketbody type
      return apiClient.post("cab", body);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["cablist"] });
      toast.success("Cab List Created Successfully");
    },
    onError: () => {
      toast.error("Error Creating Cab List");
    },
  });
};

// Delete a Laundry Ticket
export const useDeleteCabList = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: string) => {
      return apiClient.delete(`cab/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["cablist"] });
      toast.success("Cab List Deleted Successfully");
    },
    onError: () => {
      toast.error("Error Deleting Cab List");
    },
  });
};

// Update a wakeup module
export const useUpdateCabList = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      body,
      id,
    }: {
      body: any; //laundrymodulebody type
      id: string;
    }) => {
      return apiClient.patch(`cab/${id}`, body);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["cablist"] });
      toast.success("Cab List Updated Successfully");
    },
    onError: () => {
      toast.error("Error Updating Cab List");
    },
  });
};
