import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../utils/ApiGateway";
import { toast } from "react-toastify";
import { IPushNotification } from "../../Interface/IPushNotification";

export const useCreateSendPushNotification = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (notificationData: IPushNotification) => {
      const res = await apiClient.post("push-notification", notificationData);
      console.log(res.data);
      return res;
    },

    onError: (error: any) => {
      console.log(error);
      if (error === "FCM Token not found") {
        setTimeout(() => {
          toast.info(`User hasn't registered properly`);
        }, 1900);
        return;
      } else {
        toast.info("Something went wrong");
        return;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["push-notification"] });
      toast.success("Notification send Succesfully");
    },
  });
};

export const useUpdatePushNotification = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      notificationData,
      id,
    }: {
      notificationData: IPushNotification;
      id: string;
    }) => {
      const res = await apiClient.patch(
        `push-notification/${id}`,
        notificationData
      );
      return res;
    },

    onError: (error: any) => {
      toast.error(error || "Something went wrong");
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["push-notification"] });
    },
  });
};

export const useCreateSendBulkPushNotification = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (notificationData: IPushNotification) =>
      apiClient.post("push-notification", notificationData),
    onError: (error: any) => {
      toast.error("Something went wrong");
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["push-notification"] });
      toast.success("Notification send Succesfully");
    },
  });
};

export const useGetNotifications = (page: number = 1, limit: number = 10) => {
  return useQuery({
    queryKey: ["push-notification", page, limit],
    queryFn: async () => {
      const { data } = await apiClient.get("push-notification", {
        params: { page, limit },
      });
      return data;
    },
    staleTime: 60 * 1000,
  });
};

export const useGetAllNotifications = () => {
  return useQuery({
    queryKey: ["push-notification"],
    queryFn: async () => {
      const { data } = await apiClient.get(`push-notification`);
      return data;
    },
  });
};
