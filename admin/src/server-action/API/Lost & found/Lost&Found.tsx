import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";
import { toast } from "react-toastify";

interface LostFoundModel {
  id: string;
  description?: string;
  [key: string]: any;
  hotel: string;
  date: string;
  time: string;
  room: {
    roomNo: string | number;
    [key: string]: any;
  };
  reportedBy: string;
  item: string;
  images: string[];
  takenBy: string;
  type: string;
  status: string;
  formData: any;
}

interface LostFoundModelBody {
  description?: string;
  [key: string]: any;
  hotel?: string;
  date?: string;
  time?: string;
  room?: string;
  reportedBy?: string;
  item?: string;
  images?: string[];
  takenBy?: string;
  type?: string;
  status?: string;
  formData?: any;
}

//Fetch all Lost & Found Module
export const useGetAllLostFoundModule = () => {
  return useQuery<LostFoundModel[]>({
    queryKey: ["lostfound"],
    queryFn: async () => {
      const res = await apiClient.get<{ data: LostFoundModel[] }>("lostfound");
      return res.data.data;
    },
  });
};

//Create a new Lost & Found Module
export const useCreateLostFoundModule = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (body: FormData) => {
      return apiClient.post("lostfound", body, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
    },

    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["lostfound"] });
      toast.success("Lost & Found Module is created Sucessfullay");
    },

    onError: () => {
      toast.error("Error Creating Lost & Found Module");
    },
  });
};

//Delete a ticket module
export const useDeleteLostFoundModule = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: string) => {
      return apiClient.delete(`lostfound/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["lostfound"] });
      toast.success("Lost & Found Modules Deleted Sucessfully");
    },
    onError: () => {
      toast.error("Error Deleting Lost & Found Module");
    },
  });
};

export const useUpdateLostFoundModule = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      body,
      id,
    }: {
      body: FormData;
      id: string;
    }) => {
      return apiClient.patch(`lostfound/${id}`, body, {
         headers: {
          "Content-Type": "multipart/form-data",
        },
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["lostfound"] });
      toast.success("Lost & Found Module Updated Sucessfually");
    },
    onError: () => {
      toast.error("Error updating Lost & Found module");
    },
  });
};
