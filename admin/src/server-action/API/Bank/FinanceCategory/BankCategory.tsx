import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../../../utils/ApiGateway";
import { toast } from "react-toastify";
import { FinanceData } from "../../../../pages/FinanceManagement/FinacnceCategory/Component/BankForm";

interface BankModel {
  _id: string;
  bankName: string;
  accountNumber: string;
  AccountHolderName: string;
  accountQr: [string];
  phoneNumber: string;
  currentBalance: string;
  hotel: string;
  createdAt: string;
  updateAt: string;
}

export const useGetAllBank = () => {
  return useQuery<BankModel[]>({
    queryKey: ["bank"],
    queryFn: async () => {
      const res = await apiClient.get<{ data: BankModel[] }>("bank");
      return res.data.data;
    },
  });
};

export const useCreateBankModule = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (body: FinanceData) => {
      return apiClient.post("bank", body);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["bank"] });
      toast.success("Bank Module is Created Sucessfully");
    },
    onError: (error: any) => {
      // Check if it's a duplicate bank error
      console.log("Create Bank Error:", error);

      if (typeof error === "string" && error.includes("bank already exists")) {
        // Don't show toast for duplicate error - we'll handle it in the form
        return { message: "bank already exists.", isDuplicate: true };
      } else {
        toast.error(error || "Error Creating Bank module");
      }
      return error;
    },
  });
};

export const useDeletBankModule = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: string) => {
      return apiClient.delete(`bank/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["bank"] });
      toast.success("Bank Modules Deleted Sucessfully");
    },
    onError: () => {
      toast.error("Error Deleting Bank Module");
    },
  });
};

export const useUpdateBankModule = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ body, id }: { body: FinanceData; id: string }) => {
      return apiClient.patch(`Bank/${id}`, body);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["bank"] });
      toast.success("Bank Module Updated Successfully");
    },
    onError: (error: any) => {
      // Check if it's a duplicate bank error
      console.log("Update Bank Error:", error);

      if (typeof error === "string" && error.includes("bank already exists")) {
        // Don't show toast for duplicate error - we'll handle it in the form
        return { message: "bank already exists.", isDuplicate: true };
      } else {
        toast.error(error || "Error updating Bank");
      }
      return error;
    },
  });
};
