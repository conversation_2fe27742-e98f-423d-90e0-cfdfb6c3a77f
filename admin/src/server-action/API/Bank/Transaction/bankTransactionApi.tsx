import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../../../utils/ApiGateway";
import { toast } from "react-toastify";

export interface IBankTransaction {
  _id: string;
  hotel: string;
  bank:
    | {
        _id: string;
        bankName: string;
        accountNumber: string;
        accountHolderName: string;
        currentBalance: number;
      }
    | string
    | null;
  date: string;
  time: string;
  description?: string;
  to?:
    | {
        _id: string;
        bankName: string;
        accountNumber: string;
        accountHolderName: string;
        currentBalance: number;
      }
    | string
    | null;
  transactionNo: string;
  transactionType: "deposit" | "withdraw" | "transfer";
  transactionReference: string;
  transactionAmount: number;
  closingBalance: number;
  serviceCharge?: number;
  createdBy?:
    | {
        _id: string;
        name: string;
      }
    | string
    | null;
  wasProcessed?: boolean;
  createdAt?: string;
  updatedAt?: string;
  __v?: number;
}

interface BankTransactionFilters {
  bank?: string;
  transactionType?: string;
  date?: string;
  endDate?: string;
  page?: number;
  limit?: number;
}

// Get all bank transactions with optional filters
export const useGetBankTransactions = (
  filters: BankTransactionFilters = {}
) => {
  const queryParams = new URLSearchParams();

  // Add filters to query params
  if (filters.bank) queryParams.append("bank", filters.bank);
  if (filters.transactionType)
    queryParams.append("transactionType", filters.transactionType);
  if (filters.date) queryParams.append("date", filters.date);
  if (filters.endDate) queryParams.append("endDate", filters.endDate);
  if (filters.page) queryParams.append("page", filters.page.toString());
  if (filters.limit) queryParams.append("limit", filters.limit.toString());

  const queryString = queryParams.toString();
  const endpoint = `/bankTransaction${queryString ? `?${queryString}` : ""}`;

  return useQuery({
    queryKey: ["bankTransactions", filters],
    queryFn: async () => {
      const response = await apiClient.get(endpoint);
      // Format the response to match our expected structure
      return {
        transactions: response.data.data || [],
        pagination: response.data.pagination || {
          total: 0,
          page: 1,
          limit: 10,
          pages: 0,
        },
      };
    },
    refetchOnWindowFocus: false,
  });
};

// Get a single bank transaction by ID
export const useGetBankTransactionById = (id: string) => {
  return useQuery({
    queryKey: ["bankTransaction", id],
    queryFn: async () => {
      const response = await apiClient.get(`/bankTransaction/${id}`);
      return response.data;
    },
    enabled: !!id,
    refetchOnWindowFocus: false,
  });
};

// Create a new bank transaction
export interface CreateBankTransactionData {
  bank: string;
  date: string;
  time: string;
  description?: string;
  to?: string;
  transactionType: "deposit" | "withdraw" | "transfer";
  transactionNo: string;
  transactionReference?: string;
  transactionAmount: number;
  serviceCharge?: number;
}

export const useCreateBankTransaction = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateBankTransactionData) => {
      const response = await apiClient.post("/bankTransaction", data);
      return response.data;
    },
    onSuccess: () => {
      toast.success("Bank transaction created successfully");
      queryClient.invalidateQueries({ queryKey: ["bankTransactions"] });
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || "Failed to create bank transaction"
      );
    },
  });
};

// Delete a bank transaction
export const useDeleteBankTransaction = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await apiClient.delete(`/bankTransaction/${id}`);
      return response.data;
    },
    onSuccess: () => {
      toast.success("Bank transaction deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["bankTransactions"] });
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || "Failed to delete bank transaction"
      );
    },
  });
};
