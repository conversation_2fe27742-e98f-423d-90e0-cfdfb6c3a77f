import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../../../utils/ApiGateway";
import { toast } from "react-toastify";
import { TransactionData } from "../../../../pages/FinanceManagement/TransactionAdjustment/Component/BankTransactionForm";

interface TransactionModel {
  id: string;
  name: string;
  description?: string;
  hotel: string;
  bank: string;
  date: string;
  time: string;
  to: string;
  transactionNo: string;
  transactionType: string;
  transactionReference: string;
  transactionAmount: number;
  closingBalance: number;
  wasProcessed: boolean;
  createdBy: string;
  createdAt: string;
  updateAt: string;
  serviceCharge: number;
  [key: string]: any;
}

//Featch all Transaction Modules
export const useGetAllTransaction = () => {
  return useQuery<TransactionModel[]>({
    queryKey: ["transaction"],
    queryFn: async () => {
      const res = await apiClient.get<{ data: TransactionModel[] }>(
        "transaction"
      );
      return res.data.data;
    },
  });
};

// Create a new Transaction module
export const useCreateTransactionModule = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (body: TransactionData) => {
      return apiClient.post("transaction", body);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["transaction"] });
      toast.success("Transaction Module is Created Sucessfully");
    },
    onError: () => {
      toast.error("Error Creating Transaction module");
    },
  });
};

// Delete a Transaction module
export const useDeletTransactionModule = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: string) => {
      return apiClient.delete(`transaction/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["transaction"] });
      toast.success("Transaction Modules Deleted Sucessfully");
    },
    onError: () => {
      toast.error("Error Deleting Transaction Module");
    },
  });
};

//Update a Transaction Module
export const useUpdateTransactionModule = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ body, id }: { body: TransactionData; id: string }) => {
      return apiClient.patch(`transaction/${id}`, body);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["transaction"] });
      toast.success("Transaction Module Updated Sucessfually");
    },
    onError: () => {
      toast.error("Error updating Transaction module");
    },
  });
};
