import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-toastify";
import { apiClient } from "../../utils/ApiGateway";

// interface WakeUpModule {
//   id: string;
//   name: string;
//   description?: string;
//   [key: string]: any;
// }

// interface WakeUpModuleBody {
//   name: string;
//   description?: string;
// }
// using any type for now (temporary)

// Fetch all Laundry
export const useGetAllGuestLaundry = () => {
  return useQuery<any>({
    //laundrymodule type
    queryKey: ["guest-laundry"],
    queryFn: async () => {
      try {
        console.log("Fetching guest laundry data...");
        const res = await apiClient.get<{ data: any }>("laundry/guest");
        console.log("Guest laundry data fetched successfully:", res.data);
        return res.data.data;
      } catch (error: any) {
        console.error("Error fetching guest laundry data:", error);
        if (error.response) {
          console.error("Error response:", error.response.data);
          console.error("Error status:", error.response.status);
          console.error("Error headers:", error.response.headers);
        }
        throw error;
      }
    },
  });
};

// Create a new Laundry Ticket
export const useCreateGuestLaundry = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (body: any) => {
      console.log(
        "Creating guest laundry with data:",
        JSON.stringify(body, null, 2)
      );

      // Ensure items array is properly formatted
      if (body.items && Array.isArray(body.items)) {
        // Make sure each item has the required fields
        body.items = body.items.map((item: any) => ({
          category: item.category,
          service: item.service,
          quantity: item.quantity || 1,
          price: item.price || 0,
        }));
      } else {
        body.items = [];
      }

      // Ensure user field is properly set
      if (!body.user && body.guest) {
        body.user = body.guest;
      } else if (!body.guest && body.user) {
        body.guest = body.user;
      }

      // Debug room field
      console.log("Room field in request:", {
        room: body.room,
        roomType: typeof body.room,
        booking: body.booking,
      });

      try {
        console.log("Sending POST request to laundry/guest");
        const response = await apiClient.post("laundry/guest", body);
        console.log("Guest laundry created successfully:", response.data);

        // Debug response data
        if (response.data && response.data.data) {
          console.log("Response data details:", {
            room: response.data.data.room,
            roomType: typeof response.data.data.room,
            booking: response.data.data.booking,
            bookingRoom: response.data.data.booking?.room,
          });
        }

        return response.data;
      } catch (error: any) {
        console.error("API error creating guest laundry:", error);
        if (error.response) {
          console.error("Error response:", error.response.data);
        }
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["guest-laundry"] });
      toast.success("Guest Laundry Created Successfully");
    },
    onError: (error: any) => {
      console.error("Error creating guest laundry:", error);
      toast.error(
        error?.response?.data?.message || "Error Creating Guest Laundry"
      );
    },
  });
};

// Delete a Laundry Ticket
export const useDeleteGuestLaundry = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: string) => {
      console.log(`Deleting guest laundry with ID: ${id}`);
      try {
        console.log(`Sending DELETE request to laundry/guest/${id}`);
        const response = await apiClient.delete(`laundry/guest/${id}`);
        console.log("Guest laundry deleted successfully:", response.data);
        return response.data;
      } catch (error: any) {
        console.error("API error deleting guest laundry:", error);
        if (error.response) {
          console.error("Error response:", error.response.data);
        }
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["guest-laundry"] });
      toast.success("Guest Laundry Deleted Successfully");
    },
    onError: (error: any) => {
      console.error("Error deleting guest laundry:", error);
      toast.error(
        error?.response?.data?.message || "Error Deleting Guest Laundry"
      );
    },
  });
};

// Note: The useUpdateGuestLaundry hook has been moved to guestLaundry.ts
// Import it from there instead of using this file
