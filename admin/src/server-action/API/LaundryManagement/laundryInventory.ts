import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";
import { toast } from "react-toastify";

// Get inventory status across all stores
export const useGetLaundryInventoryStatus = (params: { page?: number; limit?: number; storeType?: string } = {}) => {
  return useQuery({
    queryKey: ["laundry-inventory-status", params],
    queryFn: async () => {
      try {
        const res = await apiClient.get("laundry/inventory/status", { params });
        if (res.data && res.data.data && res.data.data.inventorySummary && Array.isArray(res.data.data.inventorySummary)) {
          return {
            data: res.data.data.inventorySummary,
            total: res.data.data.pagination?.total || res.data.data.inventorySummary.length,
            page: res.data.data.pagination?.page || params.page || 1,
            limit: res.data.data.pagination?.limit || params.limit || 10
          };
        } else if (res.data && res.data.data) {
          return {
            data: res.data.data,
            total: res.data.total || res.data.data.length,
            page: res.data.page || params.page || 1,
            limit: res.data.limit || params.limit || 10
          };
        } else {
          console.error('Unexpected response structure:', res.data);
          return {
            data: [],
            total: 0,
            page: params.page || 1,
            limit: params.limit || 10
          };
        }
      } catch (error) {
        console.error('Error fetching laundry inventory status:', error);
        throw error;
      }
    },
  });
};

// Send items to laundry
export const useSendToLaundry = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["send-to-laundry"],
    mutationFn: async (data: { itemId: string; quantity: number; fromStoreId: string }) => {
      try {
        // Validate required fields
        if (!data.itemId || !data.fromStoreId) {
          console.error('Missing required fields for send to laundry:', data);
          throw new Error('Missing required fields: itemId and fromStoreId are required');
        }

        console.log('Sending items to laundry with data:', data);
        const res = await apiClient.post("laundry/inventory/send-to-laundry", data);
        console.log('Send to laundry response:', res.data);
        return res.data.data;
      } catch (error) {
        console.error('Error sending items to laundry:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["laundry-inventory-status"] });
      toast.success("Items sent to laundry successfully");
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message ||
                         (error instanceof Error ? error.message : "Error sending items to laundry");
      toast.error(errorMessage);
    },
  });
};

// Process dirty items in laundry
export const useProcessLaundryItems = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["process-laundry-items"],
    mutationFn: async (data: { itemId: string; quantity: number }) => {
      try {
        // Validate required fields
        if (!data.itemId) {
          console.error('Missing required field for process laundry items:', data);
          throw new Error('Missing required field: itemId is required');
        }

        console.log('Processing laundry items with data:', data);
        const res = await apiClient.post("laundry/inventory/process", data);
        console.log('Process laundry items response:', res.data);
        return res.data.data;
      } catch (error) {
        console.error('Error processing laundry items:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["laundry-inventory-status"] });
      toast.success("Items processed successfully");
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || "Error processing items");
    },
  });
};

// Return clean items from laundry to stores
export const useReturnCleanItems = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["return-clean-items"],
    mutationFn: async (data: { itemId: string; quantity: number; toStoreId: string }) => {
      try {
        // Validate required fields
        if (!data.itemId || !data.toStoreId) {
          console.error('Missing required fields for return clean items:', data);
          throw new Error('Missing required fields: itemId and toStoreId are required');
        }

        console.log('Returning clean items with data:', data);
        const res = await apiClient.post("laundry/inventory/return-clean", data);
        console.log('Return clean items response:', res.data);
        return res.data.data;
      } catch (error) {
        console.error('Error returning clean items:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["laundry-inventory-status"] });
      toast.success("Clean items returned successfully");
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || "Error returning clean items");
    },
  });
};

// Get all stores
export const useGetAllStores = () => {
  return useQuery({
    queryKey: ["all-stores"],
    queryFn: async () => {
      try {
        const res = await apiClient.get("store");
        console.log('All stores response:', res.data);

        if (res.data && res.data.data) {
          return res.data.data;
        } else {
          console.error('Unexpected response structure:', res.data);
          return [];
        }
      } catch (error) {
        console.error('Error fetching all stores:', error);
        throw error;
      }
    },
  });
};
