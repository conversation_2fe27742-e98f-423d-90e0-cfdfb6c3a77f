import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";
import { toast } from "react-toastify";
import { IHotel } from "../../../Interface/hotel.interface";

interface ILaundryService {
  hotel?: IHotel;
  _id?: string;
  name?: string;
  services?: any;
  isActive?: boolean;
}

export const useGetAllLaundryCategory = () => {
  return useQuery({
    queryKey: ["laundry-category"],
    queryFn: async () => {
      const res = await apiClient.get<{ data: ILaundryService[] }>(
        "laundry/category"
      );
      return res.data.data;
    },
  });
};

export const useCreateLaundryCategory = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (body: any) => {
      const res = await apiClient.post<{ data: ILaundryService }>(
        "laundry/category",
        body
      );
      return res.data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["laundry-category"] });
      toast.success(" Laundry Category created successfully");
    },
    onError: (error: any) => {
      toast.error(error || "Error creating  Laundry Category");
    },
  });
};

export const useUpdateLaundryCategory = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ id, body }: { id: string; body: ILaundryService }) => {
      const res = await apiClient.patch<{ data: ILaundryService }>(
        `laundry/category/${id}`,
        body
      );
      console.log("response", res);
      return res.data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["laundry-category"] });
      toast.success(" Laundry Category updated successfully");
    },
    onError: (error) => {
      console.log("error", error);
      toast.error("Error updating Laundry Category");
    },
  });
};

export const useDeleteLaundryCategory = () => {
  const queryClient = useQueryClient();
  return useMutation<void, Error, string>({
    mutationFn: async (id) => {
      await apiClient.delete(`laundry/category/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["laundry-category"] });
      toast.success(" Laundry Category deleted successfully");
    },
    onError: () => {
      toast.error("Error deleting Laundry Category");
    },
  });
};
