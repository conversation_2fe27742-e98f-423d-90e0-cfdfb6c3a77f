import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";
import { toast } from "react-toastify";

/**
 * Hook to get all guest laundry records
 * @returns Query result with guest laundry data
 */
export const useGetAllGuestLaundry = () => {
  return useQuery({
    queryKey: ["guestLaundry"],
    queryFn: async () => {
      const response = await apiClient.get("laundry/guest");
      return response?.data?.data;
    },
  });
};

/**
 * Hook to get a specific guest laundry record by ID
 * @param id - The ID of the guest laundry record to fetch
 * @returns Query result with guest laundry data
 */
export const useGetGuestLaundryById = (id: string) => {
  return useQuery({
    queryKey: ["guestLaundry", id],
    queryFn: async () => {
      if (!id) return null;
      const response = await apiClient.get(`laundry/guest/${id}`);
      return response?.data?.data;
    },
    enabled: !!id,
  });
};

/**
 * Hook to create a new guest laundry record
 * @returns Mutation function to create a guest laundry record
 */
export const useCreateGuestLaundry = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (body: any) => {
      console.log(
        "Creating guest laundry with data:",
        JSON.stringify(body, null, 2)
      );

      // Ensure items array is properly formatted
      if (!body.items || !Array.isArray(body.items)) {
        console.warn(
          "No laundry items found in the request body or not an array"
        );
        // Add an empty items array if it's missing
        body.items = [];
      }

      // Ensure booking field is present
      if (!body.booking) {
        console.warn("No booking field found in request body");
        if (body.room) {
          console.log("Using room ID as booking reference");
          body.booking = body.room;
        }
      }

      // Ensure reportedBy is a string (the guest user ID)
      if (typeof body.reportedBy === "object" && body.reportedBy?._id) {
        body.reportedBy = body.reportedBy._id;
      }

      // Ensure user is a string (the housekeeper name)
      if (typeof body.user === "object" && body.user?.name) {
        body.user = body.user.name;
      }

      // Remove reportedTo if it exists (we're using user instead)
      if (body.reportedTo) {
        delete body.reportedTo;
      }

      // Remove complaintBy if it exists (we're using reportedBy instead)
      if (body.complaintBy) {
        delete body.complaintBy;
      }

      try {
        // Make the API request
        console.log("Sending API request to laundry/guest");
        const response = await apiClient.post("laundry/guest", body);
        console.log("Guest laundry created successfully:", response.data);
        return response.data;
      } catch (error: any) {
        console.error("API error creating guest laundry:", error);
        if (error.response) {
          console.error("Error response:", error.response.data);
        }
        throw error;
      }
    },
    onSuccess: (data) => {
      // Invalidate queries to refresh the data
      queryClient.invalidateQueries({ queryKey: ["guestLaundry"] });
      queryClient.invalidateQueries({ queryKey: ["guest-laundry"] });

      // Show success message
      toast.success("Guest Laundry Created Successfully");

      // Return the created data
      return data;
    },
    onError: (error: any) => {
      // Log the error for debugging
      console.error("Error creating guest laundry:", error);

      // Extract error message from response if available
      const errorMessage =
        error?.response?.data?.message || "Error creating guest laundry";

      // Show error message to the user
      toast.error(errorMessage);

      // Throw the error to be caught by the component
      throw error;
    },
  });
};

/**
 * Hook to update an existing guest laundry record
 * @returns Mutation function to update a guest laundry record
 */
export const useUpdateGuestLaundry = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ id, body }: { id: string; body: any }) => {
      console.log(
        `[DEBUG] useUpdateGuestLaundry - Updating guest laundry with ID ${id}:`,
        JSON.stringify(body, null, 2)
      );

      // Validate ID
      if (!id) {
        console.error("[DEBUG] useUpdateGuestLaundry - Missing ID parameter");
        throw new Error("Missing ID parameter for update operation");
      }

      // Ensure items array is properly formatted
      if (!body.items || !Array.isArray(body.items)) {
        console.warn(
          "[DEBUG] useUpdateGuestLaundry - No laundry items found in the request body or not an array"
        );
        // Add an empty items array if it's missing
        body.items = [];
      }

      // Ensure booking field is present
      if (!body.booking) {
        console.warn("No booking field found in request body");
        if (body.room) {
          console.log("Using room ID as booking reference");
          body.booking = body.room;
        }
      }

      // Ensure guest field is properly set
      if (body.guest === "") {
        console.warn(
          "Empty guest field found, removing it to prevent MongoDB casting error"
        );
        delete body.guest;
      } else if (!body.guest && body.user) {
        console.log("Setting guest field to user value");
        body.guest = body.user;
      }

      // Log important fields for debugging
      console.log("Room field:", body.room);
      console.log("ReportedBy field:", body.reportedBy);
      console.log("User field:", body.user);
      console.log("Guest field:", body.guest);
      console.log("Booking field:", body.booking);

      // Ensure reportedBy is a string (the guest user ID)
      if (typeof body.reportedBy === "object" && body.reportedBy?._id) {
        body.reportedBy = body.reportedBy._id;
      }

      // Ensure user is a string (the housekeeper name)
      if (typeof body.user === "object" && body.user?.name) {
        body.user = body.user.name;
      }

      // Remove reportedTo if it exists (we're using user instead)
      if (body.reportedTo) {
        delete body.reportedTo;
      }

      // Remove complaintBy if it exists (we're using reportedBy instead)
      if (body.complaintBy) {
        delete body.complaintBy;
      }

      try {
        console.log(
          `[DEBUG] useUpdateGuestLaundry - Making PATCH request to laundry/guest/${id}`
        );

        // Make the API call with explicit URL and body
        const response = await apiClient.patch(`laundry/guest/${id}`, body);

        console.log(
          "[DEBUG] useUpdateGuestLaundry - Guest laundry updated successfully:",
          response.data
        );
        return response.data;
      } catch (error: any) {
        console.error(
          "[DEBUG] useUpdateGuestLaundry - API error updating guest laundry:",
          error
        );

        if (error.response) {
          console.error("[DEBUG] useUpdateGuestLaundry - Error response:", {
            status: error.response.status,
            statusText: error.response.statusText,
            data: error.response.data,
          });
        } else if (error.request) {
          console.error(
            "[DEBUG] useUpdateGuestLaundry - No response received:",
            error.request
          );
        } else {
          console.error(
            "[DEBUG] useUpdateGuestLaundry - Error setting up request:",
            error.message
          );
        }

        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["guestLaundry"] });
      queryClient.invalidateQueries({ queryKey: ["guest-laundry"] });
      toast.success("Guest Laundry Updated Successfully");
    },
    onError: (error: any) => {
      console.error("Error updating guest laundry:", error);
      toast.error(
        error?.response?.data?.message || "Error updating guest laundry"
      );
    },
  });
};

/**
 * Hook to delete a guest laundry record
 * @returns Mutation function to delete a guest laundry record
 */
export const useDeleteGuestLaundry = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: string) => {
      return apiClient.delete(`laundry/guest/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["guestLaundry"] });
      toast.success("Guest Laundry Deleted Successfully");
    },
    onError: (error: any) => {
      console.error("Error deleting guest laundry:", error);
      toast.error(
        error?.response?.data?.message || "Error deleting guest laundry"
      );
    },
  });
};
