import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";
import { toast } from "react-toastify";
import { IHotel } from "../../../Interface/hotel.interface";

interface ILaundryService {
  hotel?: IHotel;
  _id?: string;
  name?: string;
  type?: Array<string>;
  isActive?: boolean;
}

export const useGetAllLaundryService = () => {
  return useQuery({
    queryKey: ["laundry-service"],
    queryFn: async () => {
      const res = await apiClient.get<{ data: ILaundryService[] }>(
        "laundry/service"
      );
      return res.data.data;
    },
  });
};

export const useCreateLaundryService = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (body: any) => {
      const res = await apiClient.post<{ data: ILaundryService }>(
        "laundry/service",
        body
      );
      return res.data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["laundry-service"] });
      toast.success(" Laundry Service created successfully");
    },
    onError: () => {
      toast.error("Error creating  Laundry Service");
    },
  });
};

export const useUpdateLaundryService = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ id, body }: { id: string; body: ILaundryService }) => {
      const res = await apiClient.patch<{ data: ILaundryService }>(
        `laundry/service/${id}`,
        body
      );
      console.log("response", res);
      return res.data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["laundry-service"] });
      toast.success(" Laundry Service updated successfully");
    },
    onError: (error) => {
      console.log("error", error);
      toast.error("Error updating Laundry Service");
    },
  });
};

export const useDeleteLaundryService = () => {
  const queryClient = useQueryClient();
  return useMutation<void, Error, string>({
    mutationFn: async (id) => {
      await apiClient.delete(`laundry/service/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["laundry-service"] });
      toast.success(" Laundry Service deleted successfully");
    },
    onError: () => {
      toast.error("Error deleting Laundry Service");
    },
  });
};
