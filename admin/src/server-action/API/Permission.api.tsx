import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../utils/ApiGateway";
import { toast } from "react-toastify";


// Get all packages
export const useGetPermission = (params?: Record<string, any>) => {
  return useQuery({
    queryKey: ["permission", params],
    queryFn: async () => {
      const res = await apiClient.get("permission", { params });
      return res.data.data;
    },
  });
};


// Update (PATCH) a package
export const useUpdatePermission = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ id, body }:{ id: string; body: any}) => {
      const res = await apiClient.patch(
        `permission/${id}`,
        body
      );
      return res.data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["permission"] });
      toast.success("Permission updated successfully");
    },
    onError: () => {
      toast.error("Error updating permission");
    },
  });
};

// Delete a package
export const useDeletePermission = () => {
  const queryClient = useQueryClient();
  return useMutation<void, Error, string>({
    mutationFn: async (id) => {
      await apiClient.delete(`permission/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["permission"] });
      toast.success("Permission deleted successfully");
    },
    onError: () => {
      toast.error("Error deleting permission");
    },
  });
};
