import { useQuery } from "@tanstack/react-query";
import { apiClient } from "../utils/ApiGateway";

// Guest Report APIs
export const useGetGuestReportSummary = (params: { startDate: string; endDate: string; hotelId?: string }) => {
  return useQuery({
    queryKey: ["guest-report-summary", params],
    queryFn: async () => {
      try {
        const res = await apiClient.get("/reports/guests/summary", { params });
        return res.data.data;
      } catch (error) {
        console.error("Error fetching guest report summary:", error);
        return {
          totalGuests: 0,
          activeGuests: 0,
          checkInsToday: 0,
          checkOutsToday: 0,
          averageStayDuration: 0,
          returnGuestPercentage: 0,
          topCountries: [],
          guestTrend: [],
          period: {
            startDate: params.startDate,
            endDate: params.endDate
          }
        };
      }
    },
    enabled: !!params.startDate && !!params.endDate,
  });
};

export const useGetGuestDetailedReport = (params: {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: string;
  name?: string;
  email?: string;
  country?: string;
  minSpend?: number;
  maxSpend?: number;
  minVisits?: number;
  maxVisits?: number;
  lastVisitStart?: string;
  lastVisitEnd?: string;
}) => {
  return useQuery({
    queryKey: ["guest-detailed-report", params],
    queryFn: async () => {
      try {
        const res = await apiClient.get("/reports/guests", { params });
        return res.data.data;
      } catch (error) {
        console.error("Error fetching guest detailed report:", error);
        return {
          guests: [],
          pagination: {
            total: 0,
            page: params.page || 1,
            limit: params.limit || 10,
            pages: 0
          }
        };
      }
    },
  });
};

export const useGetGuestActivityReport = (params: {
  guestId?: string;
  startDate: string;
  endDate: string;
}) => {
  return useQuery({
    queryKey: ["guest-activity-report", params],
    queryFn: async () => {
      try {
        const res = await apiClient.get("/reports/guests/activity", { params });
        return res.data.data;
      } catch (error) {
        console.error("Error fetching guest activity report:", error);
        return {
          activities: [],
          period: {
            startDate: params.startDate,
            endDate: params.endDate
          }
        };
      }
    },
    enabled: !!params.startDate && !!params.endDate,
  });
};

export const useGetGuestSpendingAnalysis = (params: {
  guestId?: string;
  startDate: string;
  endDate: string;
  category?: string;
}) => {
  return useQuery({
    queryKey: ["guest-spending-analysis", params],
    queryFn: async () => {
      try {
        const res = await apiClient.get("/reports/guests/spending", { params });
        return res.data.data;
      } catch (error) {
        console.error("Error fetching guest spending analysis:", error);
        return {
          guest: null,
          totalSpending: 0,
          averageDailySpend: 0,
          spendingByCategory: {},
          spendingTrend: [],
          period: {
            startDate: params.startDate,
            endDate: params.endDate
          }
        };
      }
    },
    enabled: !!params.startDate && !!params.endDate,
  });
};

// Expense Report APIs
export const useGetExpenseSummaryReport = (params: {
  startDate: string;
  endDate: string;
  groupBy?: string;
  hotelId?: string;
}) => {
  return useQuery({
    queryKey: ["expense-summary-report", params],
    queryFn: async () => {
      try {
        const res = await apiClient.get("/reports/expenses/summary", { params });
        return res.data.data;
      } catch (error) {
        console.error("Error fetching expense summary report:", error);
        return {
          totalExpenses: 0,
          expensesByCategory: [],
          expensesTrend: [],
          comparisonWithPreviousPeriod: {
            percentage: 0,
            isIncrease: false
          },
          period: {
            startDate: params.startDate,
            endDate: params.endDate,
            groupBy: params.groupBy || "day"
          }
        };
      }
    },
    enabled: !!params.startDate && !!params.endDate,
  });
};

export const useGetDetailedExpenseReport = (params: {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: string;
  startDate?: string;
  endDate?: string;
  category?: string;
  minAmount?: number;
  maxAmount?: number;
  title?: string;
}) => {
  return useQuery({
    queryKey: ["detailed-expense-report", params],
    queryFn: async () => {
      try {
        const res = await apiClient.get("/reports/expenses", { params });
        return res.data.data;
      } catch (error) {
        console.error("Error fetching detailed expense report:", error);
        return {
          expenses: [],
          pagination: {
            total: 0,
            page: params.page || 1,
            limit: params.limit || 10,
            pages: 0
          }
        };
      }
    },
  });
};

export const useGetExpenseCategoryAnalysis = (params: {
  startDate: string;
  endDate: string;
  categoryId?: string;
}) => {
  return useQuery({
    queryKey: ["expense-category-analysis", params],
    queryFn: async () => {
      try {
        const res = await apiClient.get("/reports/expenses/categories", { params });
        return res.data.data;
      } catch (error) {
        console.error("Error fetching expense category analysis:", error);
        return {
          totalExpenses: 0,
          categories: [],
          period: {
            startDate: params.startDate,
            endDate: params.endDate
          }
        };
      }
    },
    enabled: !!params.startDate && !!params.endDate,
  });
};

export const useGetExpenseTrendAnalysis = (params: {
  startDate: string;
  endDate: string;
  period?: string;
  categoryId?: string;
}) => {
  return useQuery({
    queryKey: ["expense-trend-analysis", params],
    queryFn: async () => {
      try {
        const res = await apiClient.get("/reports/expenses/trends", { params });
        return res.data.data;
      } catch (error) {
        console.error("Error fetching expense trend analysis:", error);
        return {
          trends: [],
          category: null,
          seasonalPatterns: {
            highestMonth: 0,
            lowestMonth: 0,
            monthlyAverages: {}
          },
          period: {
            startDate: params.startDate,
            endDate: params.endDate,
            groupBy: params.period || "month"
          }
        };
      }
    },
    enabled: !!params.startDate && !!params.endDate,
  });
};
