import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";
import { toast } from "react-toastify";
import { IOrder } from "../../../Interface/order.interface";

// Get all orders
export const useGetOrders = (bookingId?: string) => {
  return useQuery({
    queryKey: ["orders", bookingId],
    queryFn: async () => {
      const params = bookingId ? { booking: bookingId } : {};
      const res = await apiClient.get<{ data: IOrder[] }>("/restaurant/order", { params });
      return res.data.data;
    },
    enabled: !!bookingId, // Only fetch if bookingId is provided
  });
};

// Get order by ID
export const useGetOrderById = (id: string) => {
  return useQuery({
    queryKey: ["order", id],
    queryFn: async () => {
      const res = await apiClient.get<{ data: IOrder }>(`/restaurant/order/${id}`);
      return res.data.data;
    },
    enabled: !!id, // Only fetch if id is provided
  });
};

// Create order
export const useCreateOrder = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (orderData: Omit<IOrder, "_id" | "date">) => {
      const res = await apiClient.post<{ data: IOrder }>("/restaurant/order", orderData);
      return res.data.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["orders"] });
      queryClient.invalidateQueries({ queryKey: ["order", data._id] });
      toast.success("Order created successfully");
    },
    onError: (error: any) => {
      toast.error(`Error creating order: ${error}`);
    },
  });
};

// Add items to existing order
export const useAddItemsToOrder = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      orderId,
      menuItems,
    }: {
      orderId: string;
      menuItems: any[];
    }) => {
      const res = await apiClient.put<{ data: IOrder }>(
        `/restaurant/order/additems/${orderId}`,
        { menuItems }
      );
      return res.data.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["orders"] });
      queryClient.invalidateQueries({ queryKey: ["order", data._id] });
      toast.success("Items added to order successfully");
    },
    onError: (error: any) => {
      toast.error(`Error adding items to order: ${error}`);
    },
  });
};

// Remove items from order
export const useRemoveItemsFromOrder = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      orderId,
      menuItems,
    }: {
      orderId: string;
      menuItems: { item: string; quantity: number }[];
    }) => {
      const res = await apiClient.delete<{ data: IOrder }>(
        `/restaurant/order/removeitems/${orderId}`,
        { data: { menuItems } }
      );
      return res.data.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["orders"] });
      queryClient.invalidateQueries({ queryKey: ["order", data._id] });
      toast.success("Items removed from order successfully");
    },
    onError: (error: any) => {
      toast.error(`Error removing items from order: ${error}`);
    },
  });
};

// Update order
export const useUpdateOrder = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      orderId,
      orderData,
    }: {
      orderId: string;
      orderData: Partial<IOrder>;
    }) => {
      const res = await apiClient.put<{ data: IOrder }>(
        `/restaurant/order/${orderId}`,
        orderData
      );
      return res.data.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["orders"] });
      queryClient.invalidateQueries({ queryKey: ["order", data._id] });
      toast.success("Order updated successfully");
    },
    onError: (error: any) => {
      toast.error(`Error updating order: ${error}`);
    },
  });
};
