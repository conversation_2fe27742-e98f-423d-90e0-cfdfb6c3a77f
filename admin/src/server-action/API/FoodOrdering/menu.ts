import {  useQuery } from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";
import { IMenuCategory, IMenuItem } from "../../../Interface/menu.interface";

// Get all menu categories
export const useGetMenuCategories = () => {
  return useQuery({
    queryKey: ["menu-categories"],
    queryFn: async () => {
      const res = await apiClient.get<{ data: IMenuCategory[] }>(
        "/restaurant/menu/category"
      );
      return res.data.data;
    },
  });
};

// Get all menu items
export const useGetMenuItems = (categoryId?: string) => {
  return useQuery({
    queryKey: ["menu-items", categoryId],
    queryFn: async () => {
      const params = categoryId ? { categoryId } : {};
      const res = await apiClient.get<{ data: IMenuItem[] }>(
        "/restaurant/menu",
        { params }
      );
      return res.data.data;
    },
  });
};

// Get menu item by ID
export const useGetMenuItemById = (id: string) => {
  return useQuery({
    queryKey: ["menu-item", id],
    queryFn: async () => {
      const res = await apiClient.get<{ data: IMenuItem }>(
        `/restaurant/menu/${id}`
      );
      return res.data.data;
    },
    enabled: !!id, // Only fetch if id is provided
  });
};
