import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";
import { toast } from "react-toastify";

export type Priority = "high" | "medium" | "low";

export interface Ticket {
  _id?: string | number | undefined;
  name?: string;
  description?: string;
  room?: string;
  ticketCategory?: string;
  user?: string;
  priority?: Priority;
  ticketStatus?: string;
  attachment?: string[];
  date?: string;
  time?: string;
  notes?: string;
  issueType?: string;
  isMissing?: boolean;
  isDamaged?: boolean;
  isAlright?: boolean;
}

export type TicketRequestBody = Omit<Ticket, "id">;

export const useGetAllTickets = (params?: {
  status?: string;
  priority?: string;
  page?: number;
  limit?: number;
  ticketCategory?: string;
  // Add any other parameters you might need
}) => {
  return useQuery<Ticket[]>({
    queryKey: ["tickets", params], // Include params in queryKey for proper caching
    queryFn: async () => {
      const res = await apiClient.get<{ data: Ticket[] }>("ticket", {
        params: params,
      });
      return res.data.data;
    },
    // Only run the query if params are not provided or if they're valid
    enabled:
      !params ||
      Object.keys(params).length === 0 ||
      Object.values(params).some((val) => val !== undefined),
  });
};

export const useCreateTicket = () => {
  const queryClient = useQueryClient();
  return useMutation<Ticket, Error, FormData | Ticket>({
    mutationFn: async (body) => {
      const response = await apiClient.post<Ticket>("ticket", body, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["tickets"] });
      toast.success("Ticket created successfully");
    },
    onError: () => {
      toast.error("Error creating ticket");
    },
  });
};

export const useDeleteTicket = () => {
  const queryClient = useQueryClient();
  return useMutation<void, Error, string>({
    mutationFn: async (id) => {
      console.log(`Deleting ticket with ID: ${id}`);
      // Log the full URL being called
      const url = `ticket/${id}`;
      console.log(`Full API URL: ${apiClient.defaults.baseURL}${url}`);

      try {
        const response = await apiClient.delete(url);
        console.log("Delete API response:", response);
        return response.data;
      } catch (error) {
        console.error("Delete API error:", error);
        throw error;
      }
    },
    onSuccess: () => {
      console.log("Delete mutation succeeded");
      queryClient.invalidateQueries({ queryKey: ["tickets"] });
      toast.success("Ticket deleted successfully");
    },
    onError: (error) => {
      console.error("Delete mutation error:", error);
      toast.error("Error deleting ticket");
    },
  });
};

export const useUpdateTicket = () => {
  const queryClient = useQueryClient();
  return useMutation<Ticket, Error, { id: string; body: FormData | Ticket }>({
    mutationFn: async ({ id, body }) => {
      const response = await apiClient.patch<Ticket>(`ticket/${id}`, body, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["tickets"] });
      toast.success("Ticket updated successfully");
    },
    onError: () => {
      toast.error("Error updating ticket");
    },
  });
};
