import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../utils/ApiGateway";
import { toast } from "react-toastify";

export interface IPurchaseItems {
  item: string;
  quantity: number;
  unitCost: number;
  unit: string;
}
export interface IPurchaseReturn {
  returnItems: IPurchaseItems[];
  purchase: string;
  totalCost: number;
}

export const useGetAllPurchase = () => {
  return useQuery({
    queryKey: ["purchase"],
    queryFn: async () => {
      const res = await apiClient.get("purchase");
      return res.data.data;
    },
  });
};

export const useCreatePurchaseMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["purchase"],
    mutationFn: async (body: any) => {
      const res = await apiClient.post("purchase", body);
      return res.data.data;
    },
    onSuccess: () => {
      toast.success("Purchase created successfully");
      queryClient.invalidateQueries({ queryKey: ["purchase"] }); // 🔥 Refresh cache
    },
    onError: (err: any) => {
      toast.error(err || "Error creating purchase");
    },
  });
};

export const useUpdatePurchaseMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["purchase"],
    mutationFn: async ({ id, body }: any) => {
      const res = await apiClient.patch(`purchase/${id}`, body);
      return res.data.data;
    },
    onSuccess: () => {
      toast.success("Purchase updated successfully");
      queryClient.invalidateQueries({ queryKey: ["purchase"] }); // 🔥 Refresh cache
    },
    onError: (err: any) => {
      toast.error(err || "Error updating purchase");
    },
  });
};

export const useDeletePurchaseMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["purchase"],
    mutationFn: async (_id: string) => {
      if (!_id) throw new Error("Purchase ID is required");
      const res = await apiClient.delete(`purchase/${_id}`);
      return res.data.data;
    },
    onSuccess: () => {
      toast.success("Purchase deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["purchase"] }); // 🔥 Refresh cache
    },
    onError: () => {
      toast.error("Error deleting purchase");
    },
  });
};

//purchase return api
export const useGetAllPurchaseReturn = () => {
  return useQuery({
    queryKey: ["purchase-return"],
    queryFn: async () => {
      const res = await apiClient.get("purchase/return");
      return res.data.data;
    },
  });
};

export const useCreatePurchaseReturnMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["purchase-return"],
    mutationFn: async (body: IPurchaseReturn) => {
      console.log("Creating purchase return with data:", body);

      try {
        const res = await apiClient.post("purchase/return", body);
        return res.data.data;
      } catch (error: any) {
        console.error("Purchase return creation error:", error);

        // Extract error message from response if available
        const errorMessage =
          error?.response?.data?.message ||
          error?.message ||
          "Error creating purchase return";

        throw errorMessage;
      }
    },
    onSuccess: () => {
      toast.success("Purchase Return created successfully");
      queryClient.invalidateQueries({ queryKey: ["purchase-return"] }); // 🔥 Refresh cache
    },
    onError: (err: any) => {
      const errorMessage =
        typeof err === "string" ? err : "Error creating purchase return";
      toast.error(errorMessage);
    },
  });
};

export const useUpdatePurchaseReturnMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["purchase-return"],
    mutationFn: async ({ id, body }: { id: string; body: IPurchaseReturn }) => {
      console.log(`Updating purchase return with ID: ${id}`);
      console.log("Request body:", body);

      try {
        const res = await apiClient.patch(`purchase/return/${id}`, body);
        return res.data.data;
      } catch (error: any) {
        console.error("Purchase return update error:", error);

        // Extract error message from response if available
        const errorMessage =
          error?.response?.data?.message ||
          error?.message ||
          "Error updating purchase return";

        throw errorMessage;
      }
    },
    onSuccess: () => {
      toast.success("Purchase Return updated successfully");
      queryClient.invalidateQueries({ queryKey: ["purchase-return"] }); // 🔥 Refresh cache
    },
    onError: (err: any) => {
      const errorMessage =
        typeof err === "string" ? err : "Error updating purchase return";
      toast.error(errorMessage);
    },
  });
};

export const useDeletePurchaseReturnMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["purchase-return"],
    mutationFn: async (_id: string) => {
      if (!_id) throw new Error("Purchase Return ID is required");
      const res = await apiClient.delete(`purchase/return/${_id}`);
      return res.data.data;
    },
    onSuccess: () => {
      toast.success("Purchase Return deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["purchase-return"] }); // 🔥 Refresh cache
    },
    onError: () => {
      toast.error("Error deleting purchase return");
    },
  });
};
