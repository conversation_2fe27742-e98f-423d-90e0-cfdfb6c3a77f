import { useQuery } from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";

// Base interface for common fields
export interface Base {
  _id: string;
  createdAt?: string;
  updatedAt?: string;
  isActive?: boolean;
  hotel?: string;
}

// Use the main API client with restaurant prefix
const getRestaurantEndpoint = (endpoint: string) => `restaurant/${endpoint}`;

// Helper function to make restaurant API calls
const callRestaurantApi = async <T>(endpoint: string, params?: any): Promise<T> => {
  try {
    const response = await apiClient.get<T>(getRestaurantEndpoint(endpoint), { params });
    return response.data;
  } catch (error) {
    console.error(`Error calling restaurant API endpoint ${endpoint}:`, error);
    throw error;
  }
};

// Menu Sales Report
export interface MenuSalesItem {
  itemId: string;
  itemName: string;
  category: string;
  totalQuantityOrdered: number;
  numberOfOrders: number;
  averagePerOrder: number;
  price: number;
}

export interface MenuSalesResponse {
  success: boolean;
  message: string;
  data: MenuSalesItem[];
}

export const useGetMenuSalesReport = (params: {
  startDate?: string;
  endDate?: string;
  restaurantId?: string;
  menuItemId?: string;
}) => {
  // Convert the parameter names to match what the API expects
  const apiParams = {
    from: params.startDate,
    to: params.endDate,
    restaurantId: params.restaurantId,
    menuItemId: params.menuItemId
  };

  return useQuery<MenuSalesItem[]>({
    queryKey: ["restaurant-menu-sales", params],
    queryFn: async () => {
      try {
        const response = await callRestaurantApi<MenuSalesResponse>("most-ordered", apiParams);
        console.log('API Response for menu sales:', response);
        if (response && response.data && Array.isArray(response.data)) {
          return response.data;
        } else {
          console.error('Invalid response format for menu sales:', response);
          return [];
        }
      } catch (error) {
        console.error("Error fetching menu sales report:", error);
        return [];
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Today's Order Stats
export interface TodayOrderStats {
  totalOrders: number;
  inProgressOrders: number;
  complaintOrders: number;
  canceledOrders: number;
}

export interface TodayOrderStatsResponse {
  success: boolean;
  message: string;
  data: TodayOrderStats;
}

export const useGetTodayOrderStats = () => {
  return useQuery<TodayOrderStats>({
    queryKey: ["restaurant-today-order-stats"],
    queryFn: async () => {
      try {
        const response = await callRestaurantApi<TodayOrderStatsResponse>("today-order-stats");
        return response.data;
      } catch (error) {
        console.error("Error fetching today's order stats:", error);
        return {
          totalOrders: 0,
          inProgressOrders: 0,
          complaintOrders: 0,
          canceledOrders: 0,
        };
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Daily Cash Flow Report
export interface DailyCashFlowItem {
  date: string;
  totalCashIn: number;
  totalCashOut: number;
  totalBankCash: number;
  orderRevenue: number;
  netCashFlow: number;
  totalExpenses: number;
  transactions: any[];
}

export interface DailyCashFlowResponse {
  success: boolean;
  message: string;
  data: DailyCashFlowItem[];
}

export const useGetDailyCashFlow = (params: {
  fromDate?: string;
  toDate?: string;
  counterId?: string;
}) => {
  return useQuery<DailyCashFlowItem[]>({
    queryKey: ["restaurant-daily-cash-flow", params],
    queryFn: async () => {
      try {
        const response = await callRestaurantApi<DailyCashFlowResponse>("daily-cash", params);
        return response.data
      } catch (error) {
        console.error("Error fetching daily cash flow:", error);
        return [];
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Counter History
export interface CounterSummary {
  counterId: string;
  counterName: string;
  openingBalance: number;
  cashIn: number;
  cashOut: number;
  bankTransactions: number;
  closingBalance: number;
}

export interface CounterHistoryResponse {
  success: boolean;
  message: string;
  data: {
    counterSummaries: CounterSummary[];
    overallSummary: {
      totalOpeningBalance: number;
      totalCashIn: number;
      totalCashOut: number;
      totalBankTransactions: number;
      totalClosingBalance: number;
    };
  };
}

export const useGetCounterHistory = (date?: string) => {
  const endpoint = date ? `counter-history/${date}` : "counter-history";

  return useQuery<CounterHistoryResponse["data"]>({
    queryKey: ["restaurant-counter-history", date],
    queryFn: async () => {
      try {
        const response = await callRestaurantApi<CounterHistoryResponse>(endpoint);
        return response.data;
      } catch (error) {
        console.error("Error fetching counter history:", error);
        return {
          counterSummaries: [],
          overallSummary: {
            totalOpeningBalance: 0,
            totalCashIn: 0,
            totalCashOut: 0,
            totalBankTransactions: 0,
            totalClosingBalance: 0,
          },
        };
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Cash Totals
export interface CashTotals {
  weekly: {
    totalCash: number;
    cashIn: number;
    cashOut: number;
  };
  monthly: {
    totalCash: number;
    cashIn: number;
    cashOut: number;
  };
  yearly: {
    totalCash: number;
    cashIn: number;
    cashOut: number;
  };
}

export interface CashTotalsResponse {
  success: boolean;
  message: string;
  data: CashTotals;
}

export const useGetCashTotals = () => {
  return useQuery<CashTotals>({
    queryKey: ["restaurant-cash-totals"],
    queryFn: async () => {
      try {
        const response = await callRestaurantApi<CashTotalsResponse>("cashtotal");
        return response.data;
      } catch (error) {
        console.error("Error fetching cash totals:", error);
        return {
          weekly: { totalCash: 0, cashIn: 0, cashOut: 0 },
          monthly: { totalCash: 0, cashIn: 0, cashOut: 0 },
          yearly: { totalCash: 0, cashIn: 0, cashOut: 0 },
        };
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Guest Statistics
export interface GuestStats {
  totalGuests: number;
  totalOrders: number;
  totalSales: number;
}

export interface GuestStatsResponse {
  success: boolean;
  message: string;
  data: GuestStats;
}

export const useGetGuestStats = () => {
  return useQuery<GuestStats>({
    queryKey: ["restaurant-guest-stats"],
    queryFn: async () => {
      try {
        const response = await callRestaurantApi<GuestStatsResponse>("total");
        return response.data;
      } catch (error) {
        console.error("Error fetching guest statistics:", error);
        return {
          totalGuests: 0,
          totalOrders: 0,
          totalSales: 0,
        };
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Table Utilization
export interface TableUtilization {
  totalGuestsToday: number;
  totalGuestsCurrently: number;
  totalReservation: number;
}

export interface TableUtilizationResponse {
  success: boolean;
  data: TableUtilization;
}

export const useGetTableUtilization = () => {
  return useQuery<TableUtilization>({
    queryKey: ["restaurant-table-utilization"],
    queryFn: async () => {
      try {
        const response = await callRestaurantApi<TableUtilizationResponse>("table-avg");
        return response.data;
      } catch (error) {
        console.error("Error fetching table utilization:", error);
        return {
          totalGuestsToday: 0,
          totalGuestsCurrently: 0,
          totalReservation: 0,
        };
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Customer Time Flow Data
export interface TimeFlowDataItem {
  hour: number;
  totalOrders: number;
  orderTypes: {
    dineIn: number;
    takeaway: number;
    delivery: number;
  };
  averageOrderValue: number;
  popularItems: {
    name: string;
    count: number;
  }[];
  customerCount: number;
}

export interface PeakHour {
  start: number;
  end: number;
  intensity: string;
}

export interface CustomerTimeFlowData {
  timeFlowData: TimeFlowDataItem[];
  peakHours: PeakHour[];
  timezone: string;
}

export interface CustomerTimeFlowResponse {
  success: boolean;
  message: string;
  data: {
    data: CustomerTimeFlowData;
    meta: {
      restaurantId: string;
      dateRange: {
        start: string;
        end: string;
      };
      timezone: string;
    };
  };
}

export const useGetCustomerTimeFlowData = (params: {
  restaurantId?: string;
  startDate?: string;
  endDate?: string;
}) => {
  // Convert the parameter names to match what the API expects
  const apiParams = {
    from: params.startDate,
    to: params.endDate,
    restaurantId: params.restaurantId
  };

  return useQuery<CustomerTimeFlowData>({
    queryKey: ["restaurant-customer-time-flow", params],
    queryFn: async () => {
      try {
        const response = await callRestaurantApi<CustomerTimeFlowResponse>("time-flow", apiParams);
        // Handle the nested data structure
        if (response.success && response.data && response.data.data) {
          return response.data.data;
        }
        return {
          timeFlowData: [],
          peakHours: [],
          timezone: "Asia/Kathmandu (UTC+5:45)",
        };
      } catch (error) {
        console.error("Error fetching customer time flow data:", error);
        return {
          timeFlowData: [],
          peakHours: [],
          timezone: "Asia/Kathmandu (UTC+5:45)",
        };
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Inventory Stock Levels
export interface InventoryItem {
  itemId: string;
  name: string;
  category: string;
  currentStock: number;
  minStockLevel: number;
  maxStockLevel: number;
  unitPrice: number;
  totalValue: number;
}

export interface InventoryStockResponse {
  success: boolean;
  message: string;
  data: InventoryItem[];
}

export const useGetInventoryStock = (params?: {
  category?: string;
  lowStockOnly?: boolean;
}) => {
  return useQuery<InventoryItem[]>({
    queryKey: ["restaurant-inventory-stock", params],
    queryFn: async () => {
      try {
        const response = await callRestaurantApi<InventoryStockResponse>("inventory/stock", params);
        return response.data;
      } catch (error) {
        console.error("Error fetching inventory stock:", error);
        return [];
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Restaurant Item Interface
export interface IItem extends Base {
  type: string | null;
  threshold: number;
  category: string | { _id: string; name: string; restaurant: string; isActive: boolean; __v: number };
  interval?: string;
  initialStock: number;
  usedStock: number;
  remainingStock: number;
  name: string;
  restaurant: string;
}

export interface RestaurantItemResponse {
  success: boolean;
  message: string;
  data: IItem[];
}

// Function to get restaurant items from /item endpoint
export const useGetRestaurantItems = (params?: {
  category?: string;
  type?: string;
}) => {
  return useQuery<IItem[]>({
    queryKey: ["restaurant-items", params],
    queryFn: async () => {
      try {
        const response = await callRestaurantApi<RestaurantItemResponse>("item", params);

        // Process the data to handle the specific response format
        if (response.data && Array.isArray(response.data)) {
          return response.data.map(item => ({
            ...item,
            // Set default values for missing fields
            initialStock: typeof item.initialStock === 'number' ? item.initialStock : 0,
            usedStock: typeof item.usedStock === 'number' ? item.usedStock : 0,
            remainingStock: typeof item.remainingStock === 'number' ? item.remainingStock : 0,
            threshold: typeof item.threshold === 'number' ? item.threshold : 0,
            interval: item.interval || 'Daily',
            // Ensure type is a string or null
            type: item.type || 'General'
          }));
        }
        return [];
      } catch (error) {
        console.error("Error fetching restaurant items:", error);
        return [];
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};



// Waiter Performance Report
export interface WaiterPerformance {
  waiterId: string;
  name: string;
  ordersCompleted: number;
  tablesServed: number;
  averageServiceTime: number;
  totalSales: number;
  customerRating: number;
}

export interface WaiterPerformanceResponse {
  success: boolean;
  message: string;
  data: WaiterPerformance[];
}

export const useGetWaiterPerformance = (params?: {
  startDate?: string;
  endDate?: string;
}) => {
  return useQuery<WaiterPerformance[]>({
    queryKey: ["restaurant-waiter-performance", params],
    queryFn: async () => {
      try {
        const response = await callRestaurantApi<WaiterPerformanceResponse>("employee/waiter-performance", params);
        return response.data;
      } catch (error) {
        console.error("Error fetching waiter performance:", error);
        return [];
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Employee Attendance Report
export interface AttendanceRecord {
  employeeId: string;
  name: string;
  present: number;
  absent: number;
  late: number;
  leave: number;
  totalDays: number;
}

export interface AttendanceReportResponse {
  success: boolean;
  message: string;
  data: {
    records: AttendanceRecord[];
    period: {
      startDate: string;
      endDate: string;
    };
  };
}

export const useGetAttendanceReport = (params: {
  startDate?: string;
  endDate?: string;
}) => {
  return useQuery<AttendanceReportResponse["data"]>({
    queryKey: ["restaurant-attendance-report", params],
    queryFn: async () => {
      try {
        const response = await callRestaurantApi<AttendanceReportResponse>("employee/attendance", params);
        return response.data;
      } catch (error) {
        console.error("Error fetching attendance report:", error);
        return {
          records: [],
          period: {
            startDate: params.startDate || "",
            endDate: params.endDate || "",
          },
        };
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};
