import { toast } from "react-toastify";
import {
  IRepaymentFrequency,
  IRepaymentMethod,
} from "../../../Interface/employeeconfig.interface";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";

// Get repayment methods
export const useGetRepaymentMethod = () => {
  return useQuery<IRepaymentMethod[]>({
    queryKey: ["repaymentmethod"],
    queryFn: async () => {
      const response = await apiClient.get("repaymentmethod");
      return response?.data?.data;
    },
  });
};

// Create a repayment method
export const useCreateRepaymentMethod = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (repaymentMethodData: IRepaymentMethod) => {
      return apiClient.post("repaymentmethod", repaymentMethodData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["repaymentmethod"] });
      toast.success("Repayment Method Created Successfully");
    },
    onError: (error: any) => {
      toast.error(error || `Error Creating Repayment Method `);
    },
  });
};

// Update a repayment method
export const useUpdateRepaymentMethod = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      repaymentMethodData,
      _id,
    }: {
      repaymentMethodData: IRepaymentMethod;
      _id: string;
    }) => {
      if (!_id) throw new Error("Repayment Method ID is required");
      return apiClient.patch(`repaymentmethod/${_id}`, repaymentMethodData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["repaymentmethod"] });
      toast.success("Repayment Method Updated Successfully");
    },
    onError: () => {
      toast.error("Error Updating Repayment Method");
    },
  });
};

// Delete a repayment method
export const useDeleteRepaymentMethod = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (_id: string) => {
      if (!_id) throw new Error("Repayment Method ID is required");
      return apiClient.delete(`repaymentmethod/${_id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["repaymentmethod"] });
      toast.success("Repayment Method Deleted Successfully");
    },
    onError: () => {
      toast.error("Error Deleting Repayment Method");
    },
  });
};

export const useGetRepaymentFrequency = () => {
  return useQuery<IRepaymentFrequency[]>({
    queryKey: ["repaymentfrequency"],
    queryFn: async () => {
      const response = await apiClient.get("repaymentfrequency");
      return response?.data?.data;
    },
  });
};

// Create a repayment frequency
export const useCreateRepaymentFrequency = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (repaymentFrequencyData: IRepaymentFrequency) => {
      return apiClient.post("repaymentfrequency", repaymentFrequencyData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["repaymentfrequency"] });
      toast.success("Repayment Frequency Created Successfully");
    },
    onError: (error: any) => {
      toast.error(error || `Error Creating Repayment Frequency `);
    },
  });
};

// Update a repayment frequency
export const useUpdateRepaymentFrequency = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      repaymentFrequencyData,
      _id,
    }: {
      repaymentFrequencyData: IRepaymentFrequency;
      _id: string;
    }) => {
      if (!_id) throw new Error("Repayment Frequency ID is required");
      return apiClient.patch(
        `repaymentfrequency/${_id}`,
        repaymentFrequencyData
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["repaymentfrequency"] });
      toast.success("Repayment Frequency Updated Successfully");
    },
    onError: () => {
      toast.error("Error Updating Repayment Frequency");
    },
  });
};

// Delete a repayment frequency
export const useDeleteRepaymentFrequency = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (_id: string) => {
      if (!_id) throw new Error("Repayment Frequency ID is required");
      return apiClient.delete(`repaymentfrequency/${_id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["repaymentfrequency"] });
      toast.success("Repayment Frequency Deleted Successfully");
    },
    onError: () => {
      toast.error("Error Deleting Repayment Frequency");
    },
  });
};
