import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";
import { toast } from "react-toastify";
import {
  IDepartment,
  IDesignation,
  IReligion,
  IShiftType,
} from "../../../Interface/employeeconfig.interface";

// Department section
export const useGetDepartment = () => {
  return useQuery<IDepartment[]>({
    queryKey: ["department"],
    queryFn: async () => {
      const response = await apiClient.get("department");
      return response?.data?.data;
    },
  });
};

export const useCreateDepartment = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (departmentData: IDepartment) =>
      apiClient.post("department", departmentData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["department"] });
      toast.success("Department Created Successfully");
    },
    onError: (error: any) => {
      toast.error(error || `Error Creating Department`);
    },
  });
};

export const useUpdateDepartMent = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      departmentData,
      _id,
    }: {
      departmentData: IDepartment;
      _id: string;
    }) => {
      return apiClient.patch(`department/${_id}`, departmentData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["department"] });
      toast.success("Department Updated Successfully");
    },
    onError: () => {
      toast.error("Error Updating Department");
    },
  });
};

export const useDeleteDepartment = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (_id: string) => {
      return apiClient.delete(`department/${_id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["department"] });
      toast.success("Department Deleted Successfully");
    },
    onError: () => {
      toast.error("Error Deleting Department");
    },
  });
};

// Designaion
export const useGetDesignation = () => {
  return useQuery<IDesignation[]>({
    queryKey: ["designation"],
    queryFn: async () => {
      const response = await apiClient.get("designation");
      return response?.data?.data;
    },
  });
};

export const useCreateDesignation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (designationData: IDesignation) => {
      return apiClient.post("designation", designationData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["designation"] });
      toast.success("Designation Created Successfully");
    },
    onError: (error: any) => {
      toast.error(error || `Error Creating Designation`);
    },
  });
};

export const useUpdateDesignation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      designationData,
      _id,
    }: {
      designationData: IDesignation;
      _id: string;
    }) => {
      if (!_id) throw new Error("Designation ID is required");
      return apiClient.patch(`designation/${_id}`, designationData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["designation"] });
      toast.success("Designation Updated Successfully");
    },
    onError: () => {
      toast.error("Error Updating Designation");
    },
  });
};

export const useDeleteDesignation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (_id: string) => {
      if (!_id) throw new Error("Designation ID is required");
      return apiClient.delete(`designation/${_id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["designation"] });
      toast.success("Designation Deleted Successfully");
    },

    onError: () => {
      toast.error("Error Deleting Designation");
    },
  });
};

// shift Type
export const useGetShiftType = () => {
  return useQuery<IShiftType[]>({
    queryKey: ["shiftType"],
    queryFn: async () => {
      const response = await apiClient.get("shiftType");
      return response?.data?.data;
    },
  });
};

export const useCreateShiftType = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (shiftTypeData: IShiftType) => {
      return apiClient.post("shiftType", shiftTypeData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["shiftType"] });
      toast.success("Shift Type Created Successfully");
    },
    onError: (error: any) => {
      toast.error(error || `Error Creating Shift Type `);
    },
  });
};

export const useUpdateShiftType = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      shiftTypeData,
      _id,
    }: {
      shiftTypeData: IShiftType;
      _id: string;
    }) => {
      if (!_id) throw new Error("Shift Type ID is required");
      return apiClient.patch(`shiftType/${_id}`, shiftTypeData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["shiftType"] });
      toast.success("Shift Type Updated Successfully");
    },
    onError: () => {
      toast.error("Error Updating Shift Type");
    },
  });
};

export const useDeleteShiftType = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (_id: string) => {
      if (!_id) throw new Error("Shift Type ID is required");
      return apiClient.delete(`shiftType/${_id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["shiftType"] });
      toast.success("Shift Type Deleted Successfully");
    },
    onError: () => {
      toast.error("Error Deleting Shift Type");
    },
  });
};

// Religion
export const useGetReligion = () => {
  return useQuery<IReligion[]>({
    queryKey: ["religion"],
    queryFn: async () => {
      const response = await apiClient.get("religion");
      return response?.data?.data;
    },
  });
};

export const useCreateReligion = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (religionData: IReligion) => {
      return apiClient.post("religion", religionData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["religion"] });
      toast.success("Religion Created Successfully");
    },
    onError: (error: any) => {
      toast.error(error || `Error Creating Religion `);
    },
  });
};

export const useUpdateReligion = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      religionData,
      _id,
    }: {
      religionData: IReligion;
      _id: string;
    }) => {
      if (!_id) throw new Error("Religion ID is required");
      return apiClient.patch(`religion/${_id}`, religionData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["religion"] });
      toast.success("Religion Updated Successfully");
    },
    onError: () => {
      toast.error("Error Updating Religion");
    },
  });
};

export const useDeleteReligion = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (_id: string) => {
      if (!_id) throw new Error("Religion ID is required");
      return apiClient.delete(`religion/${_id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["religion"] });
      toast.success("Religion Deleted Successfully");
    },
    onError: () => {
      toast.error("Error Deleting Religion");
    },
  });
};
