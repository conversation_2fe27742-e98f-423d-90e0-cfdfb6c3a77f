import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";
import { toast } from "react-toastify";

export interface IEmployeeCredit {
  _id?: string;
  name: string;
  phoneNumber: string;
  date: string;
  creditAmount: number;
  reason: string;
  repaymentMethod: string;
  installment: number;
  repaymentFrequency: string;
  // Added required fields from validation error
  repaymentStartDate: string;
  installmentAmt: number;
  description: string;
  user: string; // This should be the user ID
}

// Get all employee credits
export const useGetEmployeeCredits = (page = 1, limit = 10) => {
  return useQuery({
    queryKey: ["employeecredits", page, limit],
    queryFn: async () => {
      try {
        // Try different endpoints to see which one works
        let response;
        try {
          response = await apiClient.get("credit", {
            params: { page, limit },
          });
        } catch (e) {
          console.log("First endpoint failed, trying alternative");
          try {
            response = await apiClient.get("credits", {
              params: { page, limit },
            });
          } catch (e2) {
            console.log("Second endpoint failed, trying another alternative");
            response = await apiClient.get("employee-credit", {
              params: { page, limit },
            });
          }
        }

        // Log the response for debugging
        console.log("Credit API Response:", response.data);

        // Return the data in the expected format
        return response?.data;
      } catch (error) {
        console.error("Error fetching credits:", error);
        // Return empty data structure instead of throwing
        return { data: [], pagination: { totalItems: 0 } };
      }
    },
    // Retry failed requests
    retry: 1,
    // Don't refetch on window focus
    refetchOnWindowFocus: false,
  });
};

// Get employee credit by ID
export const useGetEmployeeCreditById = (id: string) => {
  return useQuery({
    queryKey: ["employeecredit", id],
    queryFn: async () => {
      if (!id) return null;
      const response = await apiClient.get(`credit/${id}`);
      return response?.data?.data;
    },
    enabled: !!id,
  });
};

// Create employee credit
export const useCreateEmployeeCredit = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (creditData: IEmployeeCredit) => {
      return apiClient.post("credit", creditData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["employeecredits"] });
      toast.success("Employee Credit Created Successfully");
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Error Creating Employee Credit"
      );
    },
  });
};

// Update employee credit
export const useUpdateEmployeeCredit = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      creditData,
      _id,
    }: {
      creditData: IEmployeeCredit;
      _id: string;
    }) => {
      if (!_id) throw new Error("Employee Credit ID is required");
      return apiClient.patch(`credit/${_id}`, creditData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["employeecredits"] });
      toast.success("Employee Credit Updated Successfully");
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Error Updating Employee Credit"
      );
    },
  });
};

// Delete employee credit
export const useDeleteEmployeeCredit = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (_id: string) => {
      if (!_id) throw new Error("Employee Credit ID is required");
      return apiClient.delete(`credit/${_id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["employeecredits"] });
      toast.success("Employee Credit Deleted Successfully");
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Error Deleting Employee Credit"
      );
    },
  });
};
