import { toast } from "react-toastify";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";
import {
  IAllowance,
  IDeduction,
} from "../../../Interface/employeeconfig.interface";

// Get allowances
export const useGetAllowance = () => {
  return useQuery<IAllowance[]>({
    queryKey: ["allowance"],
    queryFn: async () => {
      const response = await apiClient.get("allowance");
      return response?.data?.data;
    },
  });
};

// Create an allowance
export const useCreateAllowance = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (allowanceData: IAllowance) => {
      return apiClient.post("allowance", allowanceData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["allowance"] });
      toast.success("Allowance Created Successfully");
    },
    onError: (error: any) => {
      toast.error(error || `Error Creating Allowance `);
    },
  });
};

// Update an allowance
export const useUpdateAllowance = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      allowanceData,
      _id,
    }: {
      allowanceData: IAllowance;
      _id: string;
    }) => {
      if (!_id) throw new Error("Allowance ID is required");
      return apiClient.patch(`allowance/${_id}`, allowanceData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["allowance"] });
      toast.success("Allowance Updated Successfully");
    },
    onError: () => {
      toast.error("Error Updating Allowance");
    },
  });
};

// Delete an allowance
export const useDeleteAllowance = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (_id: string) => {
      if (!_id) throw new Error("Allowance ID is required");
      return apiClient.delete(`allowance/${_id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["allowance"] });
      toast.success("Allowance Deleted Successfully");
    },
    onError: () => {
      toast.error("Error Deleting Allowance");
    },
  });
};

// Deduction
export const useGetDeduction = () => {
  return useQuery<IDeduction[]>({
    queryKey: ["deduction"],
    queryFn: async () => {
      const response = await apiClient.get("deduction");
      return response?.data?.data;
    },
  });
};

// Create a deduction
export const useCreateDeduction = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (deductionData: IDeduction) => {
      return apiClient.post("deduction", deductionData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["deduction"] });
      toast.success("Deduction Created Successfully");
    },
    onError: (error: any) => {
      toast.error(error || `Error Creating Deduction `);
    },
  });
};

// Update a deduction
export const useUpdateDeduction = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      deductionData,
      _id,
    }: {
      deductionData: IDeduction;
      _id: string;
    }) => {
      if (!_id) throw new Error("Deduction ID is required");
      return apiClient.patch(`deduction/${_id}`, deductionData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["deduction"] });
      toast.success("Deduction Updated Successfully");
    },
    onError: () => {
      toast.error("Error Updating Deduction");
    },
  });
};

// Delete a deduction
export const useDeleteDeduction = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (_id: string) => {
      if (!_id) throw new Error("Deduction ID is required");
      return apiClient.delete(`deduction/${_id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["deduction"] });
      toast.success("Deduction Deleted Successfully");
    },
    onError: () => {
      toast.error("Error Deleting Deduction");
    },
  });
};
