import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-toastify";
import { apiClient } from "../utils/ApiGateway";
import { ISalary, ISalaryResponse } from "../../Interface/salary.interface";
/*eslint-disable @typescript-eslint/no-explicit-any */
export const useGetSalaries = (
  page: number = 1,
  limit: number = 10,
  search: string = "",
  targetMonth: string = "",
  targetYear: string = "",
  role: string = ""
) => {
  return useQuery<ISalaryResponse["data"]>({
    queryKey: ["salaries", page, limit, search, targetMonth, targetYear, role],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });
      if (search) params.append("search", search);
      if (role) params.append("role", role);
      if (targetMonth) params.append("targetMonth", targetMonth);
      if (targetYear) params.append("targetYear", targetYear);

      const response = await apiClient.get<ISalaryResponse>("salary", {
        params,
      });
      return response.data?.data;
    },
  });
};
export const useGetSalaryById = (id: string) => {
  return useQuery<ISalary[]>({
    queryKey: ["salary", id],
    queryFn: async () => {
      if (!id) return [];
      const response = await apiClient.get<ISalaryResponse>(`salary`, {
        params: { user: id },
      });
      // Return the array of salary records for this user
      return response?.data?.data?.salary || [];
    },
    enabled: !!id,
  });
};

export const useCreateSalary = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (salaryData: Partial<ISalary>) => {
      return apiClient.post("salary", salaryData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["salaries"] });
      toast.success("Salary Record Created Successfully");
    },
    onError: () => {
      toast.error("Error Creating Salary Record");
    },
  });
};

export const useUpdateSalary = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      salaryData,
      _id,
    }: {
      salaryData: Partial<ISalary>;
      _id: string;
    }) => {
      if (!_id) throw new Error("Salary ID is required");
      return apiClient.patch(`salary/${_id}`, salaryData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["salaries"] });
      toast.success("Salary Record Updated Successfully");
    },
    onError: () => {
      toast.error("Error Updating Salary Record");
    },
  });
};

export const useDeleteSalary = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (_id: string) => {
      if (!_id) throw new Error("Salary ID is required");
      return apiClient.delete(`salary/${_id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["salaries"] });
      toast.success("Salary Record Deleted Successfully");
    },
    onError: () => {
      toast.error("Error Deleting Salary Record");
    },
  });
};

export const useGenerateSalary = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: { targetMonth: any; targetYear: any }) => {
      return apiClient.post("generateSalary", data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["salaries"] });
      toast.success("Monthly Salary Generated Successfully");
    },
    onError: () => {
      toast.error("Error Generating Monthly Salary");
    },
  });
};
