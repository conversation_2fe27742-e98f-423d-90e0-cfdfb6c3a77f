import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";
import { toast } from "react-toastify";

interface StoreType {
  _id: string;
  name: string;
  hotel?: string;
  type: string;
  floor?: {
    _id: string;
    name: string;
  };
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
  [key: string]: any;
}

export const useGetAllStore = () => {
  return useQuery<StoreType[]>({
    queryKey: ["store"],
    queryFn: async () => {
      const response = await apiClient.get("store");
      return response?.data?.data;
    },
  });
};

export const useCreateStore = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["store"],
    mutationFn: async (storeData: any) => {
      const res = await apiClient.post("/store", storeData);
      return res.data.data;
    },
    onSuccess: () => {
      // Invalidate both store and all-stores queries to ensure all components refetch the latest data
      queryClient.invalidateQueries({ queryKey: ["store"] });
      queryClient.invalidateQueries({ queryKey: ["all-stores"] });
      // Also invalidate store-inventory-status to refresh the inventory status with the new store
      queryClient.invalidateQueries({ queryKey: ["store-inventory-status"] });
      toast.success("Store created successfully");
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || "Error creating Store");
    },
  });
};

export const useUpdateStore = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["update-store"],
    mutationFn: async ({ id, data }: { id: string; data: any }) => {
      const res = await apiClient.patch(`/store/${id}`, data);
      return res.data.data;
    },
    onSuccess: () => {
      // Invalidate all store-related queries to ensure all components refetch the latest data
      queryClient.invalidateQueries({ queryKey: ["store"] });
      queryClient.invalidateQueries({ queryKey: ["all-stores"] });
      queryClient.invalidateQueries({ queryKey: ["store-inventory-status"] });
      toast.success("Store updated successfully");
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || "Error updating Store");
    },
  });
};

export const useDeleteStore = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["delete-store"],
    mutationFn: async (id: string) => {
      const res = await apiClient.delete(`/store/${id}`);
      return res.data.data;
    },
    onSuccess: () => {
      // Invalidate all store-related queries to ensure all components refetch the latest data
      queryClient.invalidateQueries({ queryKey: ["store"] });
      queryClient.invalidateQueries({ queryKey: ["all-stores"] });
      queryClient.invalidateQueries({ queryKey: ["store-inventory-status"] });
      toast.success("Store deleted successfully");
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || "Error deleting Store");
    },
  });
};
