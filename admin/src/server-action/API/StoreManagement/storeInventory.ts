import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../../utils/ApiGateway";
import { toast } from "react-toastify";
import { IStoreWithItems } from "../../../Interface/storeInventory.interface";

// Define the response type for inventory status
interface InventoryStatusResponse {
  data: IStoreWithItems[];
  total: number;
  page: number;
  limit: number;
}

// Get inventory status across all stores
export const useGetStoreInventoryStatus = (params: { page?: number; limit?: number; storeId?: string } = {}) => {
  return useQuery<InventoryStatusResponse>({
    queryKey: ["store-inventory-status", params],
    queryFn: async () => {
      try {
        console.log('Making API request to laundry/inventory/status with params:', params);
        const res = await apiClient.get("laundry/inventory/status", { params });
        console.log('Store inventory status API response received:', res.data);

        // Check if the response data is in the expected format
        if (res.data && res.data.data && res.data.data.inventorySummary && Array.isArray(res.data.data.inventorySummary)) {
          console.log('Found inventory summary data:', res.data.data.inventorySummary);
          console.log('Pagination data:', res.data.data.pagination);

          return {
            data: res.data.data.inventorySummary,
            total: res.data.data.pagination?.total || res.data.data.inventorySummary.length,
            page: res.data.data.pagination?.page || params.page || 1,
            limit: res.data.data.pagination?.limit || params.limit || 10
          };
        } else if (res.data && res.data.data && Array.isArray(res.data.data)) {
          // Fallback to old format if needed
          return {
            data: res.data.data,
            total: res.data.total || res.data.data.length,
            page: res.data.page || params.page || 1,
            limit: res.data.limit || params.limit || 10
          };
        } else {
          console.error('Unexpected response format:', res.data);
          return {
            data: [],
            total: 0,
            page: params.page || 1,
            limit: params.limit || 10
          };
        }
      } catch (error) {
        console.error('Error fetching store inventory status:', error);
        throw error;
      }
    },
  });
};

// Get all stores
export const useGetAllStores = () => {
  return useQuery({
    queryKey: ["all-stores"],
    queryFn: async () => {
      try {
        const res = await apiClient.get("store");
        console.log('All stores response:', res.data);

        if (res.data && res.data.data) {
          return res.data.data;
        } else {
          console.error('Unexpected response structure:', res.data);
          return [];
        }
      } catch (error) {
        console.error('Error fetching all stores:', error);
        throw error;
      }
    },
  });
};

// Transfer items between stores
export const useTransferStoreItems = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["transfer-store-items"],
    mutationFn: async (data: {
      itemId: string;
      quantity: number;
      fromStoreId: string;
      toStoreId: string;
      itemStatus?: 'clean' | 'dirty';
    }) => {
      try {
        // Validate required fields with detailed logging
        console.log('Transfer store items data received:', data);

        if (!data.itemId) {
          console.error('Missing itemId for transfer store items');
          throw new Error('Missing required field: itemId is required');
        }

        if (!data.fromStoreId) {
          console.error('Missing fromStoreId for transfer store items');
          throw new Error('Missing required field: fromStoreId is required');
        }

        if (!data.toStoreId) {
          console.error('Missing toStoreId for transfer store items');
          throw new Error('Missing required field: toStoreId is required');
        }

        console.log('Transferring store items with validated data:', {
          itemId: data.itemId,
          quantity: data.quantity,
          fromStoreId: data.fromStoreId,
          toStoreId: data.toStoreId,
          itemStatus: data.itemStatus || 'clean'
        });

        // Use the store/transfer endpoint from the backend
        const res = await apiClient.post("store/transfer", {
          itemId: data.itemId,
          quantity: data.quantity,
          fromStoreId: data.fromStoreId,
          toStoreId: data.toStoreId,
          itemStatus: data.itemStatus || 'clean'
        });

        console.log('Transfer store items response:', res.data);
        return res.data.data;
      } catch (error) {
        console.error('Error transferring store items:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["store-inventory-status"] });
      queryClient.invalidateQueries({ queryKey: ["inventory"] });
      toast.success("Items transferred successfully");
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message ||
                         (error instanceof Error ? error.message : "Error transferring items");
      console.error('Transfer items error details:', error);
      toast.error(errorMessage);
    },
  });
};

// Change item status in store (clean to dirty or vice versa)
export const useProcessStoreItems = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["process-store-items"],
    mutationFn: async (data: {
      itemId: string;
      quantity: number;
      storeId: string;
      targetStatus: 'clean' | 'dirty';
    }) => {
      try {
        // Validate required fields with detailed logging
        console.log('Change item status data received:', data);

        if (!data.itemId) {
          console.error('Missing itemId for changing item status');
          throw new Error('Missing required field: itemId is required');
        }

        if (!data.storeId) {
          console.error('Missing storeId for changing item status');
          throw new Error('Missing required field: storeId is required');
        }

        console.log('Changing item status with validated data:', {
          itemId: data.itemId,
          quantity: data.quantity,
          storeId: data.storeId,
          targetStatus: data.targetStatus
        });

        // Use the store/transfer endpoint with transferType=status_change
        const res = await apiClient.post("store/process", {
          itemId: data.itemId,
          quantity: data.quantity,
          storeId: data.storeId,
          transferType: 'status_change',
          itemStatus: data.targetStatus
        });

        console.log('Change item status response:', res.data);
        return res.data.data;
      } catch (error) {
        console.error('Error changing item status:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["store-inventory-status"] });
      queryClient.invalidateQueries({ queryKey: ["inventory"] });
      toast.success("Item status changed successfully");
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message ||
                         (error instanceof Error ? error.message : "Error changing item status");
      console.error('Change item status error details:', error);
      toast.error(errorMessage);
    },
  });
};
