import { Store } from "@tanstack/react-store";

export interface BookingData {
  [key: string]: any;
}
export interface BookingDataState {
  bookingData: BookingData | null;
}

export const reservationStore = new Store<BookingDataState>({
  bookingData: null,
});

export const setBookingData = (data: BookingData): void => {
  reservationStore.setState((prev) => ({ ...prev, bookingData: data }));
};

export const clearBookingData = (): void => {
  reservationStore.setState((prev) => ({ ...prev, bookingData: null }));
};
