@tailwind base;
@tailwind components;
@tailwind utilities;

::-webkit-scrollbar {
  display: none;
}

@font-face {
  font-family: "Poppins";
  src: url("/font/Poppins-Light.ttf") format("truetype");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Poppins";
  src: url("/font/Poppins-Regular.ttf") format("truetype");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Poppins";
  src: url("/font/Poppins-Medium.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Poppins";
  src: url("/font/Poppins-SemiBold.ttf") format("truetype");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Poppins";
  src: url("/font/Poppins-Bold.ttf") format("truetype");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

/* css reset */

*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}
.text-[black] {
  color: #2b2b2b;
}
* {
  margin: 0;
  font-family: "Poppins";
  color: "#2B2B2B";
  /* overflow: auto; */
  /* background-color:#f8f7fa; */
  padding: 0;
}

*::-webkit-scrollbar {
  display: none;
}

ol,
ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

html,
body {
  height: 100%;
}
body {
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
}
img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
  cursor: pointer;
}
input,
button,
textarea,
select {
  font: inherit;
}
blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  word-wrap: break-word;
  margin: 0;
  box-sizing: border-box;
  text-decoration: none !important;
}
blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  word-wrap: break-word;
  margin: 0;
  box-sizing: border-box;
  text-decoration: none !important;
}

ul,
li {
  margin: 0;
  padding: 0;
}
li {
  list-style: disc;
}

#root,
#__next {
  isolation: isolate;
}

.center {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* Assuming this is in your CSS file or a styled component */
.master-table-container {
  width: 100%;
  overflow-x: auto; /* Allows horizontal scrolling if needed */
}

.master-table {
  width: 100%;
  table-layout: auto; /* Allows columns to adjust based on content */
  border-collapse: collapse;
}

.master-table th,
.master-table td {
  white-space: normal; /* Allows text to wrap */
  word-wrap: break-word; /* Ensures long words break to the next line */
  padding: 8px;
  text-align: center;
  min-width: 100px; /* Optional: Set a minimum width for columns */
}

/* Ensure table respects the container's width when sidebar is open */
@media (min-width: 768px) {
  .master-table {
    max-width: calc(
      100vw - 72px - 1rem
    ); /* Adjust for sidebar (72px) and padding */
  }
}

@media (max-width: 767px) {
  .master-table {
    max-width: 100%; /* Full width on smaller screens */
  }
}

/* Screen styles */
.printable-content {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.printable-content p {
  margin: 0.5rem 0;
}

.no-print {
  display: flex;
}

/* Print-specific styles */
@media print {
  .no-print {
    display: none !important;
  }

  .printable-content {
    box-shadow: none;
    padding: 20px;
    font-size: 14px;
  }

  .printable-content h2 {
    font-size: 24px;
    margin-bottom: 16px;
  }

  .printable-content .border {
    border: 1px solid #000 !important;
  }

  body {
    margin: 0;
  }
}
