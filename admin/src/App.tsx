import { Outlet } from "react-router-dom";
import { SideBar, Navbar } from "./layouts";
import { useEffect, useState } from "react";
import { initializeAuth } from "./hooks/useAuth";
import { useQueryClient } from "@tanstack/react-query";
import SetupRedirect from "./components/SetupRedirect";

export default function App() {
  const queryClient = useQueryClient();
  const [miniSidebar, setMiniSidebar] = useState(true); // Controls sidebar visibility
  const [isCompact, setIsCompact] = useState(false); // Controls compact (icons-only) mode

  useEffect(() => {
    const authData = initializeAuth();
    queryClient.setQueryData(["auth"], authData);
  }, [queryClient]);

  // Handle resize to reset compact mode on mobile
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 900) {
        setIsCompact(false); // Disable compact mode on mobile
        setMiniSidebar(false); // Close sidebar on mobile by default
      } else {
        setMiniSidebar(true); // Show sidebar on desktop by default
      }
    };

    window.addEventListener("resize", handleResize);
    handleResize();

    return () => window.removeEventListener("resize", handleResize);
  }, [setMiniSidebar, setIsCompact]);

  return (
    <SetupRedirect>
      <div className="flex bg-[#f8f7fa] overflow-x-hidden min-h-screen">
        <SideBar
          miniSidebar={miniSidebar}
          setMiniSidebar={setMiniSidebar}
          isCompact={isCompact}
          setIsCompact={setIsCompact}
        />

        <div
          className={`flex-1 flex flex-col min-h-screen transition-all duration-300 ${
            miniSidebar
              ? isCompact
                ? "ml-16" // Narrower margin for compact mode
                : "ml-72" // Full sidebar width
              : "ml-0" // No sidebar
          }`}
        >
          <header
            className={`fixed top-0 right-0 z-20 transition-all duration-300 ${
              miniSidebar ? (isCompact ? "left-16" : "left-72") : "left-0"
            }`}
          >
            <Navbar
              miniSidebar={miniSidebar}
              setMiniSidebar={setMiniSidebar}
              isCompact={isCompact}
              setIsCompact={setIsCompact}
            />
          </header>

          <main
            className={`flex-1 pt-20 px-3 overflow-y-auto overflow-x-hidden bg-bg ${
              miniSidebar ? "ml-0" : "ml-0"
            }`}
          >
            <div className="max-w-full overflow-x-auto">
              <Outlet />
            </div>
          </main>
        </div>
      </div>
    </SetupRedirect>
  );
}
