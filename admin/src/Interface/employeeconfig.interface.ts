export interface IDepartment {
  _id?: string;
  name: string;
  isActive: boolean;
}

export interface IDesignation {
  _id?: string;
  name: string;
  isActive: boolean;
  department: {
    _id?: string;
    name?: string;
  };
}

export interface IShiftType {
  _id?: string;
  hotel?: number;
  name: string;
  isActive?: boolean;
  to?: string;
  from?: string;
}

export interface IReligion {
  _id?: string;
  name: string;
  isActive?: boolean;
}

export interface IAllowance {
  _id?: string;
  name: string;
  category: string;
  value: number | string;
}

export interface IDeduction {
  _id?: string;
  name: string;
  category: string;
  value: number | string;
}

export interface IRepaymentMethod {
  _id?: string;
  name: string;
  isActive?: boolean;
}

export interface IRepaymentFrequency {
  _id?: string;
  name: string;
  isActive?: boolean;
}
