import { IHotel } from "./hotel.interface";
import { IRoom } from "./room.interface";
import { IUser } from "./user.interface";

// Interface for laundry category
export interface ILaundryCategory {
  _id: string;
  name: string;
  hotel?: IHotel;
  isActive?: boolean;
}

// Interface for laundry service
export interface ILaundryService {
  _id: string;
  name: string;
  type?: string[];
  hotel?: IHotel;
  isActive?: boolean;
}

// Interface for laundry item
export interface ILaundryItem {
  category: string | ILaundryCategory;
  service: string | ILaundryService;
  serviceType?: string;
  quantity: number;
  price: number; // Per-item price
  totalPrice?: number; // Total price (price × quantity)
}

// Interface for guest laundry
export interface IGuestLaundry {
  _id?: string;
  user: string | IUser;
  room: string | IRoom;
  status: "pending" | "in-progress" | "completed";
  priority: "low" | "medium" | "high";
  date?: string;
  completionDate?: string;
  items: ILaundryItem[];
  totalCost: number;
  reportedBy?: string | IUser;
  reportedTo?: string | IUser;
  ticketCategory?: string;
  isLaundry?: boolean;
}

// Interface for create/update guest laundry request
export interface IGuestLaundryRequest {
  user: string;
  room: string;
  status: string;
  priority: string;
  items: ILaundryItem[];
  totalCost?: number;
}
