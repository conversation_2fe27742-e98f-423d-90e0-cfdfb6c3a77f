import { IBooking } from "./booking.interface";
import { IHotel } from "./hotel.interface";
import { IMenuItem } from "./menu.interface";
import { IUser } from "./user.interface";

export type OrderType = "dineIn" | "takeaway" | "delivery" | "hotel";
export type OrderStatus = "inprogress" | "ready" | "server" | "cancel" | "complaint";
export type PaymentMethod = "cash" | "online" | "split";

export interface IOrderItem {
  item: string | IMenuItem;
  quantity: number;
  foodSize?: string;
  sizePrice?: number;
  note?: string;
  toppings?: {
    name: string;
    price: number;
  }[];
  status: OrderStatus;
  orderType?: OrderType;
  takeAway: number;
}

export interface ICustomerDetails {
  name?: string;
  phone?: string;
  address?: string;
  email?: string;
}

export interface IOrder {
  _id: string;
  booking: string | IBooking;
  restaurant: string;
  orderType: OrderType;
  menuItems: IOrderItem[];
  table?: string[];
  customerDetails: ICustomerDetails;
  waiter: string | IUser;
  date: Date;
  status: OrderStatus;
  pax: number;
  discount: number;
  paidAmount: number;
  paidBy?: PaymentMethod;
  hotel: string | IHotel;
}
