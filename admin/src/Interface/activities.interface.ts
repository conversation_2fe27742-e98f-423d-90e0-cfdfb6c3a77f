import { IBooking } from "./booking.interface";
import { IHotel } from "./hotel.interface";
import { IUser } from "./user.interface";

export interface IActivity {
  time: string;
  maxCapacity: number;
  price: number;
  availability: boolean;
  days: string[];
  start: Date;
  end: Date;
  hotel: IHotel;
  name: string;
  isActive: boolean;
  _id: string;
}

export interface IActivityBooking {
  paymentStatus: string;
  guest: IUser;
  activity: IActivity;
  bookingDate: string;
  booking: IBooking;
  price: number;
  totalPrice: number;
  _id: string;
  name: string;
}
