import { IUser } from "./user.interface";
import { IHotel } from "./hotel.interface";

export interface ISalaryAllowance {
  _id?: string;
  name: string;
  category: string;
  value: number;
  amount: number;
  allowance: {
    _id?: string;
    name?: string;
  };
}

export interface ISalaryDeduction {
  _id?: string;
  name: string;
  category: string;
  value: number;
  amount: number;
  deductive: {
    _id?: string;
    name?: string;
  };
}

export interface IAttendance {
  presentDays: number;
  absentDays: number;
  leaveDays: number;
  totalWorkingDays: number;
}

export interface ITaxBreakdown {
  range?: string;
  rate?: number;
  amount: number;
}

export interface IIncomeTax {
  totalTax: number;
  breakdown: ITaxBreakdown[];
  _id?: string;
}

export interface ISalary {
  _id?: string;
  hotel?: IHotel;
  user: IUser | string;
  targetMonth: string;
  targetYear: string;
  calculatedSalary: number;
  allowance: ISalaryAllowance[];
  allowances?: ISalaryAllowance[];
  deductance: ISalaryDeduction[];
  deductions?: ISalaryDeduction[];
  totalAllowance?: number;
  totalDeduction?: number;
  incomeTax?: IIncomeTax;
  dispatchedSalary?: number;
  sstPercentage?: number;
  paymentType: "CASH" | "BANK" | "ONLINE" | "DEBIT";
  paymentStatus?: "paid" | "partially-paid" | "pending";
  paymentDate?: string;
  attendance?: IAttendance;
  remarks?: string;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
  pagination?: any;
}

export interface ISalaryResponse {
  success: boolean;
  data: {
    salary: ISalary[];
    totalCount: number;
    pagination?: any;
  };
}

export interface ISalaryDetailResponse {
  success: boolean;
  data: ISalary;
}
