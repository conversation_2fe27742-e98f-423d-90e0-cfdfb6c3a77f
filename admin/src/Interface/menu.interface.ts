import { IHotel } from "./hotel.interface";

export interface IMenuCategory {
  _id: string;
  name: string;
  description?: string;
  hotel: string | IHotel;
  isActive: boolean;
}

export interface IMenuItem {
  _id: string;
  name: string;
  description: string;
  photos: string[];
  price: number;
  isNonVeg: boolean;
  size?: { 
    _id: string;
    size: string;
    price: number 
  }[];
  toppings?: { 
    _id: string;
    name: string;
    price: number 
  }[];
  category: string | IMenuCategory;
  hotel: string | IHotel;
  isActive: boolean;
}
