import { IHotel } from "./hotel.interface";

export interface IUser {
  _id: string;
  hotel: IHotel;
  name: string;
  email: string;
  password: string;
  role: string;
  isSuperAdmin?: boolean;
  // Basic details
  phoneNumber: string;
  DOB: string;
  gender: string;
  country?: string;
  address?: string;
  tempAddress: {
    district: string;
    municipality: string;
    tole: string;
    country: string;
  };
  permanentAddress: {
    district: string;
    municipality: string;
    tole: string;
    country: string;
  };
  religion: string;
  martialStatus: string;

  // Job details
  staffStatus: string;
  joinDate: string;
  department?: any;
  designation?: any;
  shiftType?: any;

  // Salary details
  applyTDS: boolean;
  basicSalary: number;
  annualSalary: number;
  allowance?: any;
  deduction?: any;
  festivalAllowanceMonth: string;
  overTimePerHr: number;
  paidLeave: number;
  bonus: number;
  extraDeduction: number;
  extraAllowance: number;

  // Documents
  photo: string[];
  citizenship: string[];
  license: string[];
  resume: string[];
  documents: [
    {
      IdentityType: string;
      IdNo: number;
      images: [string];
    }
  ];
  //token
  verificationToken: string | undefined;
  verificationTokenExpiresAt: Date | undefined;
  resetPasswordToken: string;
  resetPasswordTokenExpiresAt: Date;

  FCMToken: string;
}
