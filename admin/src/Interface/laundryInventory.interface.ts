import { IHotel } from "./hotel.interface";

// Interface for inventory item
export interface IInventoryItem {
  _id: string;
  name: string;
  description?: string;
  category?: {
    _id: string;
    name: string;
    isActive: boolean;
  };
  unit?: string;
  hotel?: string;
}

// Interface for store
export interface IStore {
  _id?: string;
  id?: string;  // Some API responses use id instead of _id
  name?: string;
  type: "main" | "laundry" | "floor" | string;
  hotel?: string | IHotel;
  isActive?: boolean;
}

// Interface for inventory item with quantities and status
export interface ILaundryInventoryItem {
  _id?: string;
  id?: string;  // Some API responses use id instead of _id
  item: IInventoryItem;
  store: IStore;
  quantity: number;
  cleanQuantity: number;
  dirtyQuantity: number;
  status: "clean" | "dirty" | "mixed";
}

// Interface for store with items
export interface IStoreWithItems {
  store: IStore;
  items: ILaundryInventoryItem[];
}

// Interface for inventory status summary
export interface ILaundryInventoryStatusSummary {
  stores: IStoreWithItems[];
}

// Interface for send to laundry request
export interface ISendToLaundryRequest {
  itemId: string;
  quantity: number;
  fromStoreId: string;
}

// Interface for process laundry request
export interface IProcessLaundryRequest {
  itemId: string;
  quantity: number;
}

// Interface for return clean items request
export interface IReturnCleanItemsRequest {
  itemId: string;
  quantity: number;
  toStoreId: string;
}
