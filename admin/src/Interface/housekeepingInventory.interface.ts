import { boolean } from "yup";
import { IHotel } from "./hotel.interface";

// Interface for inventory item
export interface IInventoryItem {
  _id: string;
  name: string;
  description?: string;
  category?: {
    _id: string;
    name: string;
    isActive: boolean;
  };
  unit?: string;
  hotel?: string;
}

// Interface for store
export interface IStore {
  id: string;
  name?: string;
  type: "main" | "laundry" | "floor" | string;
}

// Interface for inventory item with quantities
export interface IHousekeepingInventoryItem {
  id: string;
  item: IInventoryItem | null;
  quantity: number;
  cleanQuantity: number;
  dirtyQuantity: number;
}

// Interface for store with items
export interface IStoreWithItems {
  store: IStore;
  items: IHousekeepingInventoryItem[];
}

// Interface for inventory status summary
export interface IInventoryStatusSummary extends Array<IStoreWithItems> {}

// Interface for take clean items request
export interface ITakeCleanItemsRequest {
  itemId: string;
  quantity: number;
}

// Interface for return dirty items request
export interface IReturnDirtyItemsRequest {
  itemId: string;
  quantity: number;
}

// Interface for process laundry items request
export interface IProcessLaundryItemsRequest {
  itemId: string;
  quantity: number;
}

// Interface for return clean items request
export interface IReturnCleanItemsRequest {
  itemId: string;
  quantity: number;
}

// Interface for add inventory item request
export interface IAddInventoryItemRequest {
  name: string;
  description?: string;
  categoryId: string;
  storeId: string;
  quantity: number;
  unit?: string;
  cleanQuantity?: number;
  dirtyQuantity?: number;
  status?: string;
}
