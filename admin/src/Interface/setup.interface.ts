/**
 * Interface for SaaS configuration
 */
export interface ISaasConfig {
  setupCompleted: boolean;
  hotelId?: string;
  userId?: string;
  subscriptionId?: string;
  subscriptionPlan?: string;
  subscriptionStatus?: 'active' | 'inactive' | 'pending' | 'expired';
  subscriptionStartDate?: string;
  subscriptionEndDate?: string;
  paymentMethod?: string;
  paymentStatus?: 'paid' | 'pending' | 'failed';
}

/**
 * Interface for setup form data
 */
export interface ISetupFormData {
  hotelName: string;
  hotelAddress: string;
  hotelPhone: string;
  hotelDescription?: string;
  hotelLogo?: File;
  adminName: string;
  adminEmail: string;
  adminPassword: string;
  confirmPassword?: string;
  subscriptionPlan: string;
  billingCycle: 'monthly' | 'yearly';
  paymentMethod: string;
  cardNumber?: string;
  cardholderName?: string;
  expiryDate?: string;
  cvv?: string;
  billingAddress?: string;
  step?: string | number;
  data?: any;
}
