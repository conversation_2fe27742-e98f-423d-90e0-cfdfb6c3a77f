import { IAmenities } from "./amenities.interface";
import { IHotel } from "./hotel.interface";
import { IRoom, IRoomType } from "./room.interface";
import { IService } from "./service.interface";

export interface IPackage {
  hotel: string | IHotel;
  _id: string;
  name: string;
  description?: string;
  inclusions?: {
    roomTypes: IRoomType[];
    services: IService[];
    amenities: IAmenities[];
    rooms?: IRoom[];
  };
  price: number;
  priceType: "perNight" | "total";
  durationNights: number;
  maxGuests?: number;
  minGuests?: number;
  startDate?: Date;
  endDate?: Date;
  isActive?: boolean;
  images?: string[];
  termsAndConditions?: string;
  createdAt: Date;
  updatedAt: Date;
}
