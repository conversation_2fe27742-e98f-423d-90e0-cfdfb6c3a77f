import { IAmenities } from "./amenities.interface";
import { IService } from "./service.interface";

export enum RoomStatus {
  AVAILABLE = "available",
  OCCUPIED = "occupied",
  MAINTENANCE = "maintenance",
  CLEANING = "cleaning",
  RESERVED = "reserved",
}
export interface IRoomType {
  _id?: string;
  name: string;
  basePrice?: number;
  capacity?: IRoomCapacity;
}

export interface IRoomCapacity {
  standard: number;
  maximum: number;
}

export interface IDateBasedPrice {
  fromDate: string;
  toDate: string;
  price: number;
  status: string;
  isPercentage: boolean;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
}

export interface IRoomPrice {
  base: number;
  weekend?: number;
  seasonal?: {
    seasonId: string;
    price: number;
  }[];
  dateBasedPrices?: IDateBasedPrice[];
}

export interface IRoomFeatures {
  acType: "none" | "central" | "split" | "window";
  viewType?: string;
  balcony: boolean;
  smoking: boolean;
}

export interface IBedDetails {
  count: number;
  types: [
    {
      _id: string;
    }
  ]; // ObjectId strings representing bed types
}

export interface IMaintenanceHistory {
  date: string;
  description: string;
  resolvedDate?: string;
}

export interface IRoom {
  _id?: string;
  roomNo: string;
  hotel?: string;
  isActive?: boolean;
  roomType?: IRoomType;
  status?: RoomStatus;
  images?: string[];
  capacity?: IRoomCapacity;
  description?: string;
  roomPrice: IRoomPrice;
  amenities: IAmenities[];
  features?: IRoomFeatures;
  dimensions?: {
    areaSquareFeet: number;
    length: number;
    width: number;
  };
  beds?: IBedDetails;
  isVip?: boolean;
  floorPlan?: {
    _id: string;
  };
  floor?: number;
  hasSmoking?: boolean;
  maintenanceHistory?: IMaintenanceHistory[];
  lastCleaned?: string;
  notes?: string;
  createdBy?: string;
  updatedBy?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface IRoomPackage {
  _id?: string;
  hotel: string;
  name: string;
  description?: string;
  inclusions?: {
    roomTypes: IRoomType[];
    services: IService[];
    amenities: IAmenities[];
    rooms?: IRoom[];
  };
  price: number;
  priceType: "perNight" | "total";
  durationNights: number;
  maxGuests?: number;
  minGuests?: number;
  startDate?: string; // or Date, depending on usage
  endDate?: string; // or Date
  isActive?: boolean;
  images?: string[];
  termsAndConditions?: string;
  createdAt?: string;
  updatedAt?: string;
}
