/**
 * Interface for registration data during SaaS setup
 */
export interface IRegistrationData {
  adminName: string;
  adminEmail: string;
  adminPassword: string;
  confirmPassword?: string;
}

/**
 * Interface for hotel details during SaaS setup
 */
export interface IHotelDetails {
  name: string;
  address: string;
  phone: string;
  description?: string;
  logo?: File;
}

/**
 * Interface for payment information during SaaS setup
 */
export interface IPaymentInfo {
  cardNumber: string;
  cardholderName: string;
  expiryDate: string;
  cvv: string;
  billingAddress: string;
}

/**
 * Interface for subscription plan during SaaS setup
 */
export interface ISubscriptionPlan {
  id: string;
  name: string;
  price: number;
  billingCycle: 'monthly' | 'yearly';
  features: string[];
  isRecommended?: boolean;
}
