import { IHotel } from "./hotel.interface";

// Interface for inventory item
export interface IInventoryItem {
  _id: string;
  name: string;
  description?: string;
  category?: {
    _id: string;
    name: string;
    isActive: boolean;
  };
  unit?: string;
  hotel?: string;
}

// Interface for store
export interface IStore {
  _id?: string;
  id?: string;  // Some API responses use id instead of _id
  name?: string;
  type: "main" | "laundry" | "floor" | string;
  hotel?: string | IHotel;
  isActive?: boolean;
}

// Interface for inventory item with quantities and status
export interface IStoreInventoryItem {
  _id?: string;
  id?: string;  // Some API responses use id instead of _id
  item: IInventoryItem;
  store: IStore;
  quantity: number;
  cleanQuantity?: number;
  dirtyQuantity?: number;
  status?: string;
}

// Interface for store with items
export interface IStoreWithItems {
  store: IStore;
  items: IStoreInventoryItem[];
}

// Interface for inventory status summary
export interface IStoreInventoryStatusSummary extends Array<IStoreWithItems> {}

// Interface for transfer items request
export interface ITransferItemsRequest {
  itemId: string;
  quantity: number;
  fromStoreId: string;
  toStoreId: string;
  itemStatus?: 'clean' | 'dirty';
}

// Interface for process items request
export interface IProcessItemsRequest {
  itemId: string;
  quantity: number;
  storeId: string;
}
