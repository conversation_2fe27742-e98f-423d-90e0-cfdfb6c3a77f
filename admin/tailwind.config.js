export default {
  content: [
    './src/**/*.{js,ts,jsx,tsx,mdx}', // General scan across src folder
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {

    extend: {
      // fontFamily: {
      //   sans:["Poppins", "sans-serif"]
      // },
      colors: {
        'secondary':"#2A3A6D",
        "dark-secondary":"#070C44",
        "light-secondary":"#163381",
        'primary': 'white',
        'white': "#fff",
        'yellow': '#A2AD00',
        'green': '#34C759',
        'blue': "#3A86FF",
        'blue-100': "#DBEAFE",
        'blue-200': "#BFDBFE",
        'blue-600': "#2563EB",
        'blue-700': "#1D4ED8",
        'red': '#FF3B30',
        black: "#000000",
        'fade-black':"#2B2B2B"
      },
      gridTemplateColumns: {
        // Simple 32 column grid for room availability calendar
        '32': 'repeat(32, minmax(0, 1fr))',
      },
    },
  },
  plugins: [],
};
